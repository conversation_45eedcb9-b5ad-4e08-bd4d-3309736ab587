using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.Cache
{
    /// <summary>
    /// 缓存键装饰器
    /// </summary>
    public interface ICacheKeyDecorator
    {
         
        /// <summary>
        /// 缓存分区
        /// </summary>
        string RegionId
        {
            get; set;
        }

        /// <summary>
        /// 缓存标识装饰
        /// </summary>
        /// <returns></returns>
        string GetCacheKey(string cacheKey);

        /// <summary>
        /// 缓存标识还原
        /// </summary>
        /// <param name="cacheKey"></param>
        /// <returns></returns>
        string RestoreCacheKey(string cacheKey);

        /// <summary>
        /// 键匹配值装饰
        /// </summary>
        /// <param name="keyPattern"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        string GetKeyPattern(string keyPattern, Enu_SearchType type = Enu_SearchType.Default);
    }
}
