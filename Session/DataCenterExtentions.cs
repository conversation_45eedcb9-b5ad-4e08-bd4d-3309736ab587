using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Const;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.SSO;
using JieNor.Framework.IoC;
using Newtonsoft.Json;
using ServiceStack;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormMeta;
using Newtonsoft.Json.Linq;
using ServiceStack.VirtualPath;


namespace JieNor.Framework.Interface
{

    /// <summary>
    /// 企业数据中心扩展类
    /// </summary>
    public static class DataCenterExtentions
    {
        private static readonly DataCenterInfo dataCenterInfo = new DataCenterInfo();
        private static readonly SSOConfiguration ssoConfig = new SSOConfiguration();
        private static readonly FileServerConfig fileServerConfig = new FileServerConfig();
        private static readonly ChatServerConfig chatServerConfig = new ChatServerConfig();
        static ConcurrentDictionary<string, CompanyDCInfo> _dctCompanyDCInfos = new ConcurrentDictionary<string, CompanyDCInfo>(StringComparer.OrdinalIgnoreCase);

        private static ConcurrentDictionary<string, QueryDbConfig> _dctQueryDbInfos = new ConcurrentDictionary<string, QueryDbConfig>();

        static System.Threading.Timer _timer = null;

        static DataCenterExtentions()
        {
            try
            {
                var appPath = PathUtils.GetStartupPath();
                if (appPath.EndsWith(@"\bin"))
                {
                    appPath = appPath.ParentDirectory();
                }
                var strHostFile = Path.Combine(appPath, "DataCenter.json");
                dataCenterInfo = strHostFile.ReadObjectFromFile<DataCenterInfo>();
                if (dataCenterInfo == null)
                {
                    dataCenterInfo = new DataCenterInfo();
                }

                AddSlaveDb();
                AddQueryDb();

                UpdateCompanyConfig();

                UpdateDCTCompanyDCInfos();

                ssoConfig = new SSOConfiguration();

                var strConfigFile = Path.Combine(PathUtils.GetStartupPath(), "ssoconfig.json");
                var txt = strConfigFile.ReadObjectFromFile<string>();
                if (!txt.IsNullOrEmptyOrWhiteSpace())
                {
                    var jObj = Newtonsoft.Json.Linq.JObject.Parse(txt);
                    var ssoServer = jObj?["SsoServer"];
                    var lstSSO = ssoServer?.ToJson().FromJson<List<SSOServerInfo>>();
                    if (lstSSO != null)
                    {
                        foreach (var item in lstSSO)
                        {
                            ssoConfig.SSOServer.Add(item);
                        }
                    }
                }
                #region 加载文件服务器配置项
                var strFsConfig = Path.Combine(PathUtils.GetStartupPath(), "fileserverlist.json");
                fileServerConfig = strFsConfig.ReadObjectFromFile<FileServerConfig>();

                if (fileServerConfig == null)
                {
                    fileServerConfig = new FileServerConfig();
                }
                #endregion

                #region 聊天咨询配置项
                var chatFilePath = Path.Combine(PathUtils.GetStartupPath(), "chatserver.json");
                if (File.Exists(chatFilePath))
                {
                    var chatJson = File.ReadAllText(chatFilePath, Encoding.UTF8);
                    if (!string.IsNullOrWhiteSpace(chatJson) && chatJson.Length > 10)
                    {
                        chatServerConfig = JsonConvert.DeserializeObject<ChatServerConfig>(chatJson);
                    }
                }
                if (chatServerConfig == null)
                {
                    chatServerConfig = new ChatServerConfig();
                }
                #endregion

                IntervalUpdateDCT();
            }
            catch
            {

            }
        }

        /// <summary>
        /// 定期更新数据中心字典
        /// </summary>
        private static void IntervalUpdateDCT()
        {
            TimerCallback action = (state) =>
            {
                try
                {
                    UpdateDCTCompanyDCInfos();

                    WriteLogToFile("更新数据中心字典成功");
                }
                catch (Exception ex)
                {
                    WinEventUtil.WriteError($"更新数据中心字典失败:{ex.Message}", ex);
                }
            };

            var dueTime = new TimeSpan(0, 0, 0); //延迟时间，用于指定延迟多少秒开始执行定时任务
            var period = new TimeSpan(0, 10, 0); //定时周期，用于指定每隔多长时间执行一次，10分钟执行一次 

            _timer = new System.Threading.Timer(action, null, dueTime, period);
        }

        public static void WriteLogToFile(string message)
        {
            try
            {
                string fileName = "DataCenter";

                var files = new FileSystemVirtualPathProvider(HostContext.AppHost, HostContext.Config.WebHostPhysicalPath);
                string path = "app_data/debuglog/" + DateTime.Now.ToString("yyyy-MM-dd") + "/" + fileName + ".txt";

                StringBuilder content = new StringBuilder();
                content.AppendLine("时间:" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                content.AppendLine("内容:" + message);
                content.AppendLine();

                if (!files.FileExists(path))
                {
                    files.WriteFile(path, content.ToString());
                }
                else
                {
                    files.AppendFile(path, content.ToString());
                }
            }
            catch (Exception)
            {
            }
        }

        /// <summary>
        /// 更新配置文件中的组织到数据中心字典
        /// </summary>
        private static void UpdateCompanyConfig()
        {
            if (dataCenterInfo.CompanyDC == null) return;

            foreach (var company in dataCenterInfo.CompanyDC)
            {
                _dctCompanyDCInfos[company.CompanyId] = company;
            }
        }

        /// <summary>
        /// 更新数据中心字典
        /// </summary>
        private static void UpdateDCTCompanyDCInfos()
        {
            UserContext ctx = null;

            var topOrgId = "".GetAppConfig(HostConfigKeys.FW.TopOrgId) ?? "";
            if (topOrgId.IsNullOrEmptyOrWhiteSpace())
            {
                var defaultCompany = "".GetDefaultCompany();
                ctx = "".CreateContextByCompanyInfo(defaultCompany);
            }
            else
            {
                ctx = "".CreateDBContextByCompanyId(topOrgId);
            }

            var companyInfos = GetAllCompanyInfo(ctx);

            foreach (var companyInfo in companyInfos)
            {
                _dctCompanyDCInfos[companyInfo.CompanyId] = companyInfo;
            }

            #region 补偿：从DataCenter里添加

            var strHostFile = Path.Combine(PathUtils.GetStartupPath(), "DataCenter.json");
            if (File.Exists(strHostFile))
            {
                var hostCfg = File.ReadAllText(strHostFile, Encoding.UTF8);

                var dc = hostCfg?.FromJson<DataCenterInfo>();

                if (dc?.CompanyDC != null)
                {
                    foreach (var companyDcInfo in dc.CompanyDC)
                    {
                        if (!dataCenterInfo.CompanyDC.Any(s =>
                            s.CompanyId.EqualsIgnoreCase(companyDcInfo.CompanyId)))
                        {
                            dataCenterInfo.CompanyDC.Add(companyDcInfo);
                        }
                        _dctCompanyDCInfos.TryAdd(companyDcInfo.CompanyId, companyDcInfo);
                    }
                }
            }

            #endregion
        }

        /// <summary>
        /// 添加从库
        /// </summary>
        private static void AddSlaveDb()
        {
            if (!EnableSlaveDb())
            {
                return;
            }

            string dbHost = "".GetAppConfig("fw.db.slave.dbHost");
            string dbUser = "".GetAppConfig("fw.db.slave.dbUser");
            string dbPwd = "".GetAppConfig("fw.db.slave.dbPwd");
            string dbName = "".GetAppConfig("fw.db.slave.dbName");

            var dbServer = dataCenterInfo.DBServer.FirstOrDefault(s => s.ServerId.EqualsIgnoreCase("slave"));
            if (dbServer == null)
            {
                dbServer = new SQLDBServerInfo();
                dbServer.ServerId = "slave";
                dataCenterInfo.DBServer.Insert(0, dbServer);
            }

            dbServer.ServerIP = dbHost;
            dbServer.UserName = dbUser;
            dbServer.Psw = dbPwd;
        }

        /// <summary>
        /// 添加查询库
        /// </summary>
        private static void AddQueryDb()
        {
            if (!EnableQueryDbNode())
            {
                return;
            }

            string config = "".GetAppConfig("fw.db.query.config");
            if (config.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }

            var queryDbInfos = config.FromJson<List<QueryDbConfig>>();

            foreach (var item in queryDbInfos)
            {
                _dctQueryDbInfos.TryAdd(item.ServerId, item);

                var dbServer = dataCenterInfo.DBServer.FirstOrDefault(s => s.ServerId.EqualsIgnoreCase(item.ServerId));
                if (dbServer == null)
                {
                    dbServer = new SQLDBServerInfo();
                    dbServer.ServerId = item.ServerId;
                    dataCenterInfo.DBServer.Insert(0, dbServer);
                }

                dbServer.ServerIP = item.ServerIP;
                dbServer.UserName = item.UserName;
                dbServer.Psw = item.Psw;
            }
        }

        /// <summary>
        /// 是否开启从库
        /// </summary>
        /// <returns></returns>
        private static bool EnableSlaveDb()
        {
            string enableValue = "".GetAppConfig("fw.db.slave.enable");
            var enable = enableValue.EqualsIgnoreCase("1") || enableValue.EqualsIgnoreCase("true");
            return enable;
        }

        /// <summary>
        /// 是否开启查询库
        /// </summary>
        /// <returns></returns>
        private static bool EnableQueryDbNode()
        {
            string enableValue = "".GetAppConfig("fw.db.query.enable");
            var enable = enableValue.EqualsIgnoreCase("1") || enableValue.EqualsIgnoreCase("true");
            return enable;
        }

        /// <summary>
        /// 获取聊天咨询配置
        /// </summary>
        /// <param name="objInst"></param>
        /// <returns></returns>
        public static List<ChatInfo> GetChatServerConfig(this object objInst)
        {
            if (chatServerConfig != null && chatServerConfig.ChatServers != null && chatServerConfig.ChatServers.Count > 0)
            {
                return chatServerConfig.ChatServers;
            }
            return null;
        }

        /// <summary>
        /// 获取文件服务器配置列表中的第一个正常工作的文件服务器信息
        /// </summary>
        /// <returns></returns>
        public static FileServerInfo GetFirstWorkFileServer(this object objInst)
        {
            if (fileServerConfig != null
                && fileServerConfig.FileServers != null
                && fileServerConfig.FileServers.Count > 0)
            {
                return fileServerConfig.FileServers.Where(f => f.IsWork == true).FirstOrDefault();
            }
            return null;
        }

        /// <summary>
        /// 获取文件服务API地址
        /// </summary>
        /// <returns></returns>
        public static string GetFileServerUrl(this object objInst)
        {
            if (fileServerConfig != null
                && fileServerConfig.FileServers != null
                && fileServerConfig.FileServers.Count > 0)
            {
                var fsConfig = fileServerConfig.FileServers.Where(f => f.IsWork == true && !string.IsNullOrEmpty(f.Url)).ToList().FirstOrDefault();
                return fsConfig?.Url;
            }
            return "";
        }

        /// <summary>
        /// 获取内网文件服务器API地址
        /// </summary>
        /// <returns></returns>
        public static string GetLocalFileServerUrl(this object objInst)
        {
            if (fileServerConfig != null
                && fileServerConfig.FileServers != null
                && fileServerConfig.FileServers.Count > 0)
            {
                var fsConfig = fileServerConfig.FileServers.Where(f => f.IsWork == true && !string.IsNullOrEmpty(f.Url)).ToList().FirstOrDefault();
                return fsConfig?.LocalUrl == null ? fsConfig.Url : fsConfig?.LocalUrl;
            }
            return "";
        }

        /// <summary>
        /// 获取单点登录服务器信息
        /// </summary>
        /// <param name="objInst"></param>
        /// <param name="serverId"></param>
        /// <returns></returns>
        public static SSOServerInfo GetSSOServerInfo(this object objInst, string serverId)
        {
            var curSSOSerInfo = ssoConfig?.SSOServer?.FirstOrDefault(x => x.ServerId.EqualsIgnoreCase(serverId));
            if (!curSSOSerInfo.IsNullOrEmptyOrWhiteSpace())
            {
                return curSSOSerInfo;
            }
            var strConfigFile = Path.Combine(PathUtils.GetStartupPath(), "ssoconfig.json");
            if (File.Exists(strConfigFile ?? ""))
            {
                var config = File.ReadAllText(strConfigFile);
                var xx = config?.FromJson<SSOConfigurationToBag>();

                if (xx.IsNullOrEmptyOrWhiteSpace() || xx.SSOServer.IsNullOrEmptyOrWhiteSpace())
                {
                    return null;
                }
                foreach (var sso in xx.SSOServer)
                {
                    ssoConfig.SSOServer.Add(sso);
                }
            }
            return ssoConfig?.SSOServer?.FirstOrDefault(x => x.ServerId.EqualsIgnoreCase(serverId));
        }




        /// <summary>
        /// 获取所有企业信息
        /// </summary>
        /// <param name="objInst"></param>
        /// <returns></returns>
        public static IDictionary<string, CompanyDCInfo> GetAllCompanys(this object objInst)
        {
            foreach (var item in dataCenterInfo.CompanyDC)
            {
                if (_dctCompanyDCInfos.ContainsKey(item.CompanyId))
                {
                    continue;
                }
                _dctCompanyDCInfos.TryAdd(item.CompanyId, item);
            }

            return _dctCompanyDCInfos;
        }

        /// <summary>
        /// 获取默认组织
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        public static CompanyDCInfo GetCompany(this string companyId)
        {
            if (_dctCompanyDCInfos.TryGetValue(companyId, out var companyDcInfo))
            {
                return companyDcInfo;
            }

            #region 尝试从DataCenter里获取

            var strHostFile = Path.Combine(PathUtils.GetStartupPath(), "DataCenter.json");
            if (File.Exists(strHostFile))
            {
                var hostCfg = File.ReadAllText(strHostFile, Encoding.UTF8);

                var dc = hostCfg?.FromJson<DataCenterInfo>();

                if (dc?.CompanyDC != null)
                {
                    foreach (var dcInfo in dc.CompanyDC)
                    {
                        if (!dataCenterInfo.CompanyDC.Any(s =>
                            s.CompanyId.EqualsIgnoreCase(dcInfo.CompanyId)))
                        {
                            dataCenterInfo.CompanyDC.Add(dcInfo);
                            _dctCompanyDCInfos.TryAdd(dcInfo.CompanyId, dcInfo);
                        }
                    }
                }

                if (_dctCompanyDCInfos.TryGetValue(companyId, out companyDcInfo))
                {
                    return companyDcInfo;
                }
            }

            #endregion

            return null;
        }

        /// <summary>
        /// 获取默认组织
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        public static List<CompanyDCInfo> GetCompanys(this string companyId)
        {
            var company = GetCompany(companyId);
            var companys = new List<CompanyDCInfo>();
            if (company != null)
            {
                companys.Add(company);
            }

            return companys;
        }

        /// <summary>
        /// 获取默认组织
        /// </summary>
        /// <param name="objInst"></param>
        /// <returns></returns>
        public static CompanyDCInfo GetDefaultCompany(this object objInst)
        {
            CompanyDCInfo defaultCompany = null;
            var topCompanyId = "".GetAppConfig("fw.topcompanyid") ?? "";
            if (!topCompanyId.IsNullOrEmptyOrWhiteSpace())
            {
                defaultCompany = dataCenterInfo.CompanyDC.FirstOrDefault(f =>
                     !f.CompanyId.IsNullOrEmptyOrWhiteSpace() && f.CompanyId.EqualsIgnoreCase(topCompanyId) && !f.DbServer.EqualsIgnoreCase("slave"));
            }
            if (defaultCompany == null)
            {
                defaultCompany = dataCenterInfo.CompanyDC.FirstOrDefault(f =>
                     !f.CompanyId.IsNullOrEmptyOrWhiteSpace() || !f.DbServer.EqualsIgnoreCase("slave"));
            }
            return defaultCompany;

            //return dataCenterInfo.CompanyDC.FirstOrDefault(f =>
            //    !f.CompanyId.IsNullOrEmptyOrWhiteSpace() || !f.DbServer.EqualsIgnoreCase("slave"));
        }

        /// <summary>
        /// 获取默认数据服务器
        /// </summary>
        /// <param name="objInst"></param>
        /// <returns></returns>
        public static SQLDBServerInfo GetDefaultDBServer(this object objInst)
        {
            var defaultCompany = objInst.GetDefaultCompany();
            if (defaultCompany == null)
            {
                return null;
            }

            return GetDBServerInfor(defaultCompany.DbServer);
        }

        ///// <summary>
        ///// 重设上下文里企业信息
        ///// </summary>
        ///// <param name="userCtx"></param>
        //public static void ResetUserCompanys(this UserContext userCtx)
        //{
        //    userCtx.UserSession.ResetUserCompanys();
        //}

        /// <summary>
        /// 根据当前会话对象重设用户关联的企业信息
        /// </summary>
        /// <param name="userTicket"></param>
        public static void ResetUserCompanys(this UserAuthTicket userTicket)
        {
            //userTicket.Companys = userTicket.Companys.FilterUserCompanysByLocalConfig()
            //    .ToList();
        }

        /// <summary>
        /// 按本地配置进行过滤有效企业
        /// </summary>
        /// <param name="companys"></param>
        /// <returns></returns>
        public static IEnumerable<CompanyDCInfo> FilterUserCompanysByLocalConfig(this IEnumerable<CompanyDCInfo> companys)
        {
            if (companys == null) return new CompanyDCInfo[] { };

            var ctx = "".CreateContextByCompanyInfo(companys.First());
            var allDbCompanys = GetAllCompanyInfo(ctx);
            Dictionary<string, CompanyDCInfo> dctUsedCompanys = new Dictionary<string, CompanyDCInfo>();
            foreach (var companyInfo in companys)
            {
                var localMatchCompany = GetCompany(companyInfo.CompanyId);
                if (localMatchCompany == null)
                {
                    //本地配置中没找到的直接查数据库
                    if (allDbCompanys != null && allDbCompanys.Any(t => t.CompanyId.EqualsIgnoreCase(companyInfo.CompanyId)))
                    {
                        localMatchCompany = allDbCompanys.Where(t => t.CompanyId.EqualsIgnoreCase(companyInfo.CompanyId)).FirstNonDefault();
                        dataCenterInfo.CompanyDC.Add(localMatchCompany);
                        _dctCompanyDCInfos.TryAdd(companyInfo.CompanyId, localMatchCompany);
                        if (!dataCenterInfo.DBServer.Any(t => t.ServerId.EqualsIgnoreCase(companyInfo.CompanyId)))
                        {
                            var dbServer = "".GetDefaultDBServer();
                            SQLDBServerInfo currDbServer = new SQLDBServerInfo()
                            {
                                ServerId = companyInfo.CompanyId,
                                ServerIP = dbServer.ServerIP,
                                UserName = dbServer.UserName,
                                Psw = dbServer.Psw
                            };
                            dataCenterInfo.DBServer.Add(currDbServer);
                        }
                    }
                }
                if (localMatchCompany != null)
                {
                    //由于dbserver信息属于本地配置信息，因此需要重新根据本地设置，其它属性以ac站点返回为准
                    if (!localMatchCompany.DbServer.IsNullOrEmptyOrWhiteSpace())
                    {
                        companyInfo.DbServer = localMatchCompany.DbServer;
                        companyInfo.DBName = localMatchCompany.DBName;
                    }
                    if (!localMatchCompany.ReadOnlyDbServer.IsNullOrEmptyOrWhiteSpace())
                    {
                        companyInfo.ReadOnlyDbServer = localMatchCompany.ReadOnlyDbServer;
                        companyInfo.ReadOnlyDBName = localMatchCompany.ReadOnlyDBName;
                    }

                    if (localMatchCompany.AccountId.IsNullOrEmptyOrWhiteSpace()
                        && companyInfo.AccountId.IsNullOrEmptyOrWhiteSpace())
                    {
                        companyInfo.AccountId = localMatchCompany.AccountId;
                        companyInfo.AccountStatus = localMatchCompany.AccountStatus;
                    }

                    dctUsedCompanys[companyInfo.CompanyId] = companyInfo;
                }
            }
            return dctUsedCompanys.Values;
        }

        /// <summary>
        /// 根据当前会话对象重设用户关联的企业数据中心信息
        /// </summary>
        /// <param name="userTicket"></param>
        public static void ResetUserCompanysDBServer(this UserAuthTicket userTicket)
        {
            var defaultCompany = "".GetDefaultCompany();
            foreach (var company in userTicket.Companys)
            {
                if (!defaultCompany.DbServer.IsNullOrEmptyOrWhiteSpace())
                {
                    company.DbServer = defaultCompany.DbServer;
                    company.DBName = defaultCompany.DBName;
                }
                if (!defaultCompany.ReadOnlyDbServer.IsNullOrEmptyOrWhiteSpace())
                {
                    company.ReadOnlyDbServer = defaultCompany.ReadOnlyDbServer;
                    company.ReadOnlyDBName = defaultCompany.ReadOnlyDBName;
                }

                //if (!defaultCompany.AccountId.IsNullOrEmptyOrWhiteSpace()
                //    && company.AccountId.IsNullOrEmptyOrWhiteSpace())
                //{
                //    company.AccountId = defaultCompany.AccountId;
                //    company.AccountStatus = defaultCompany.AccountStatus;
                //}
            }
        }

        /// <summary>
        /// 是否多租户站点
        /// </summary>
        /// <param name="objInst"></param>
        /// <returns></returns>
        public static bool IsMultiTenant(this object objInst)
        {
            var tenantType = objInst.GetAppConfig("ms.site.tenanttype");
            return !tenantType.EqualsIgnoreCase("single");
        }

        /// <summary>
        /// 创建指定企业对应的数据库的上下文信息
        /// </summary>
        /// <param name="objInst"></param>
        /// <returns></returns>
        public static UserContext CreateDBContextByCompanyId(this object objInst, string companyId)
        {
            var companyInfo = GetCompany(companyId);
            if (companyInfo == null)
            {
                var defaultCompanyInfo = objInst.GetDefaultCompany();
                companyInfo = new CompanyDCInfo
                {
                    CompanyId = companyId,
                    DBName = defaultCompanyInfo.DBName,
                    DbServer = companyId,
                    ReadOnlyDBName = defaultCompanyInfo.ReadOnlyDBName,
                    ReadOnlyDbServer = companyId
                };
                //throw HttpError.Unauthorized("未指定认证对应的数据库实体，请修改配置文件，在 Datacenter.json 中指定认证数据库实体");
            }
            return objInst.CreateContextByCompanyInfo(companyInfo);
        }

        /// <summary>
        /// 创建用户认证对应的数据库的上下文信息
        /// </summary>
        /// <param name="objInst"></param>
        /// <returns></returns>
        public static UserContext CreateAuthDBContext(this object objInst)
        {
            var companyInfo = GetCompany("jn.sz.saas.auth");
            if (companyInfo == null)
            {
                throw HttpError.Unauthorized("未指定认证对应的数据库实体，请修改配置文件，在 Datacenter.json 中指定认证数据库实体");
            }
            return objInst.CreateContextByCompanyInfo(companyInfo);
        }

        /// <summary>
        /// 创建产品认证对应的数据库的上下文信息
        /// </summary>
        /// <param name="objInst"></param>
        /// <returns></returns>
        public static UserContext CreateEisDBContext(this object objInst)
        {
            var companyInfo = GetCompany("jn.sz.saas.eis");
            if (companyInfo == null)
            {
                throw HttpError.Unauthorized("未指定认证对应的数据库实体，请修改配置文件，在 Datacenter.json 中指定认证数据库实体");
            }
            return objInst.CreateContextByCompanyInfo(companyInfo);
        }

        /// <summary>
        /// 创建单租户时的用户上下文
        /// </summary>
        /// <returns></returns>
        public static UserContext CreateSingleTenantContext(this object objInst)
        {
            if (objInst.IsMultiTenant()) return null;

            var companyInfo = objInst.GetDefaultCompany();
            return objInst.CreateContextByCompanyInfo(companyInfo);
        }

        /// <summary>
        /// 根据指定企业信息创建上下文
        /// </summary>
        /// <param name="objInst"></param>
        /// <param name="companyInfo"></param>
        /// <returns></returns>
        public static UserContext CreateContextByCompanyInfo(this object objInst, CompanyDCInfo companyInfo)
        {
            if (companyInfo == null) return null;
            if (companyInfo.CompanyId.IsNullOrEmptyOrWhiteSpace()) return null;

            var userSession = new UserAuthTicket();
            userSession.Company = companyInfo.CompanyId;
            userSession.Companys.Add(companyInfo);
            userSession.UserId = "sysadmin";
            userSession.Product = "".ProductId();

            // 初始化SessionId
            userSession.Id = Guid.NewGuid().ToString("N");

            var oldCtx = objInst as UserContext;
            if (oldCtx != null)
            {
                userSession.TopCompanyId = oldCtx.TopCompanyId;
                userSession.ParentCompanyId = oldCtx.ParentCompanyId;
            }

            userSession.IsDirectSale = CommonUtil.GetCurrentAgentIsDirectSale(userSession.Company);
            var userCtx = new UserContext();
            userCtx.SetUserSession(userSession);
            userCtx.Container = HostContext.TryResolve<IServiceContainer>().BeginLifetimeScope(Guid.NewGuid().ToString());

            return userCtx;
        }

        /// <summary>
        /// 创建从库数据库的上下文信息
        /// </summary>
        /// <param name="isAdmin">是否使用管理员</param>
        /// <param name="companyId">指定企业，不指定时使用当前用户上下文的企业</param>
        /// <returns></returns>
        public static UserContext CreateSlaveDBContext(this UserContext userCtx, bool isAdmin = false, string companyId = null)
        {
            if (!EnableSlaveDb())
            {
                return isAdmin ? userCtx.CreateAdminDbContext(companyId) : userCtx.CreateCompanyDbContext(companyId);
            }

            var bizOrgId = companyId ?? userCtx.BizOrgId;
            var curCompanyId = companyId ?? userCtx.Company;

            UserContext slaveCtx = new UserContext();
            UserAuthTicket session = new UserAuthTicket();

            session.UserId = isAdmin ? "sysadmin" : userCtx.UserId;
            session.DisplayName = isAdmin ? "系统管理员" : userCtx.DisplayName;
            session.UserName = isAdmin ? "系统管理员" : userCtx.UserName;

            session.Product = userCtx.UserSession.Product;
            session.Company = curCompanyId;
            session.BizOrgId = bizOrgId;
            session.TopCompanyId = userCtx.UserSession.TopCompanyId;
            session.ParentCompanyId = userCtx.UserSession.ParentCompanyId;
            session.AppId = userCtx.UserSession.AppId;
            session.AppServer = userCtx.UserSession.AppServer;
            //session.AccountId = userCtx.UserSession.AccountId;
            //session.AccountStatus = userCtx.UserSession.AccountStatus;
            //session.CallerContext = userCtx.UserSession.CallerContext;
            session.CreatedDate = userCtx.UserSession.CreatedDate;
            session.Device = userCtx.UserSession.Device;

            string dbName = "".GetAppConfig("fw.db.slave.dbName");
            var companyDC = GetCompany(curCompanyId);

            // 从库
            session.Companys = new List<CompanyDCInfo>();
            session.Companys.Add(new CompanyDCInfo
            {
                AccountId = companyDC?.AccountId,
                AccountStatus = companyDC?.AccountStatus,
                CompanyId = companyDC?.CompanyId ?? curCompanyId,
                CompanyName = companyDC?.CompanyName,
                CompanyNumber = companyDC?.CompanyNumber,
                DBName = dbName,
                DbServer = "slave",
                ReadOnlyDBName = dbName,
                ReadOnlyDbServer = "slave",
                RegistType = companyDC?.RegistType ?? OrgAccountType.Org
            });

            // 初始化SessionId
            session.Id = Guid.NewGuid().ToString("N");

            slaveCtx.SetUserSession(session);
            slaveCtx.RequestId = userCtx.RequestId;
            slaveCtx.Container = ServiceStack.HostContext.TryResolve<IServiceContainer>().BeginLifetimeScope(userCtx.RequestId);

            return slaveCtx;
        }

        /// <summary>
        /// 创建查询数据库的上下文信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <returns></returns>
        public static UserContext CreateQueryDBContext(this UserContext userCtx, HtmlForm htmlForm, bool resetpage = false)
        {
            if (htmlForm == null)
            {
                return userCtx;
            }

            // 如果是从库，就不再使用查询数据库
            if (userCtx.IsSlaveDBContext())
            {
                return userCtx;
            }

            var queryDatabaseNode = htmlForm.QueryDatabaseNode;
            if (queryDatabaseNode.IsNullOrEmptyOrWhiteSpace())
            {
                return userCtx;
            }

            if (!EnableQueryDbNode())
            {
                return userCtx;
            }

            if (!_dctQueryDbInfos.TryGetValue(queryDatabaseNode, out var queryDbNode))
            {
                return userCtx;
            }

            // 基础资料或单据，如果前端指定不重置分页，表示是在某些操作后刷新，例如删除、保存。此时按主库查询返回数据
            if (htmlForm.ElementType == (int)HtmlElementType.HtmlForm_BaseForm ||
                htmlForm.ElementType == (int)HtmlElementType.HtmlForm_BillForm)
            {
                if (!resetpage)
                {
                    return userCtx;
                }
            }

            UserContext queryCtx = new UserContext();
            UserAuthTicket session = new UserAuthTicket
            {
                UserId = userCtx.UserId,
                DisplayName = userCtx.DisplayName,
                UserName = userCtx.UserName,

                Product = userCtx.UserSession.Product,
                Company = userCtx.Company,
                BizOrgId = userCtx.BizOrgId,
                TopCompanyId = userCtx.UserSession.TopCompanyId,
                ParentCompanyId = userCtx.UserSession.ParentCompanyId,
                AppId = userCtx.UserSession.AppId,
                AppServer = userCtx.UserSession.AppServer,
                CreatedDate = userCtx.UserSession.CreatedDate,
                Device = userCtx.UserSession.Device,
                Meta = userCtx.Meta,
                AccountId = userCtx.AccountId,
                AccountStatus = userCtx.AccountStatus,
            };

            // 从库
            session.Companys = new List<CompanyDCInfo>();
            session.Companys.Add(userCtx.GetQueryCompanyDCInfo(queryDatabaseNode));

            // 初始化SessionId
            //session.Id = Guid.NewGuid().ToString("N");
            session.Id = userCtx.UserSession.Id;

            queryCtx.SetUserSession(session);
            queryCtx.RequestId = userCtx.RequestId;
            queryCtx.Container = ServiceStack.HostContext.TryResolve<IServiceContainer>().BeginLifetimeScope(userCtx.RequestId);

            return queryCtx;
        }

        public static CompanyDCInfo GetQueryCompanyDCInfo(this UserContext userCtx, string queryDataNode)
        {
            var defaultCompany = userCtx.Companys.FirstOrDefault(s => s.CompanyId.EqualsIgnoreCase(userCtx.Company));

            if (_dctQueryDbInfos.TryGetValue(queryDataNode, out var queryDbNode))
            {
                var company = new CompanyDCInfo
                {
                    AccountId = defaultCompany?.AccountId,
                    AccountStatus = defaultCompany?.AccountStatus,
                    CompanyId = defaultCompany?.CompanyId ?? userCtx.Company,
                    CompanyName = defaultCompany?.CompanyName,
                    CompanyNumber = defaultCompany?.CompanyNumber,
                    RegistType = defaultCompany?.RegistType ?? OrgAccountType.Org
                };

                company.DBName = queryDbNode.DbName;
                company.DbServer = queryDbNode.ServerId;
                company.ReadOnlyDBName = queryDbNode.DbName;
                company.ReadOnlyDbServer = queryDbNode.ServerId;
                return company;
            }

            // 默认使用主数据库
            return defaultCompany;
        }

        /// <summary>
        /// 是否从库上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static bool IsSlaveDBContext(this UserContext userCtx)
        {
            var result = userCtx.Companys.Any(s =>
                s.CompanyId.EqualsIgnoreCase(userCtx.Company) && s.DbServer.EqualsIgnoreCase("slave"));
            return result;
        }

        /// <summary>
        /// 创建指定企业的数据库的上下文信息
        /// </summary>
        /// <returns></returns>
        public static UserContext CreateCompanyDbContext(this UserContext userCtx, string companyId = null)
        {
            if (companyId.IsNullOrEmptyOrWhiteSpace())
            {
                return userCtx;
            }

            UserContext ctx = new UserContext();
            UserAuthTicket session = new UserAuthTicket();

            session.UserId = userCtx.UserId;
            session.DisplayName = userCtx.DisplayName;
            session.UserName = userCtx.UserName;

            session.Product = userCtx.ProductId();
            session.Company = companyId;
            session.BizOrgId = companyId;
            session.TopCompanyId = userCtx.TopCompanyId;
            session.ParentCompanyId = userCtx.ParentCompanyId;

            session.Companys = GetCompanys(companyId);

            // 初始化SessionId
            session.Id = Guid.NewGuid().ToString("N");
            session.IsDirectSale = CommonUtil.GetCurrentAgentIsDirectSale(session.BizOrgId);
            ctx.SetUserSession(session);
            ctx.RequestId = userCtx.RequestId;
            ctx.Container = ServiceStack.HostContext.TryResolve<IServiceContainer>().BeginLifetimeScope(userCtx.RequestId);

            return ctx;
        }

        /// <summary>
        /// 创建系统管理员的数据库的上下文信息
        /// </summary>
        /// <returns></returns>
        public static UserContext CreateAdminDbContext(this UserContext userCtx, string companyId = null)
        {
            UserContext ctx = new UserContext();
            UserAuthTicket session = new UserAuthTicket();

            // 用系统预设的管理员身份操作
            session.UserId = "sysadmin";
            session.DisplayName = "系统管理员";
            session.UserName = "系统管理员";

            companyId = companyId ?? userCtx.Company;

            session.Product = userCtx.ProductId();
            session.Company = companyId;
            session.BizOrgId = companyId;
            session.TopCompanyId = userCtx.TopCompanyId;
            session.ParentCompanyId = userCtx.ParentCompanyId;
            session.AppId = userCtx.UserSession.AppId;
            session.AppServer = userCtx.UserSession.AppServer;
            //session.AccountId = userCtx.UserSession.AccountId;
            //session.AccountStatus = userCtx.UserSession.AccountStatus;
            //session.CallerContext = userCtx.UserSession.CallerContext;
            session.CreatedDate = userCtx.UserSession.CreatedDate;
            session.Device = userCtx.UserSession.Device;

            session.Companys = GetCompanys(companyId);
            session.IsDirectSale = CommonUtil.GetCurrentAgentIsDirectSale(session.BizOrgId);
            // 初始化SessionId
            session.Id = Guid.NewGuid().ToString("N");

            ctx.SetUserSession(session);
            ctx.RequestId = userCtx.RequestId;
            ctx.Container = ServiceStack.HostContext.TryResolve<IServiceContainer>().BeginLifetimeScope(userCtx.RequestId);

            return ctx;
        }

        /// <summary>
        /// 创建总部用户上下文
        /// </summary>
        /// <param name="userCtx">当前用户上下文</param> 
        /// <returns></returns>
        public static UserContext CreateTopOrgDBContext(this UserContext userCtx)
        {
            // 企业主体=总部，返回当前用户上下文
            if (userCtx.IsTopOrg) return userCtx;

            var topCompanyId = userCtx.TopCompanyId;
            if (userCtx.TopCompanyId.IsNullOrEmptyOrWhiteSpace() && !userCtx.Company.IsNullOrEmptyOrWhiteSpace())
            {
                //后续如果多个总部的话这边得考虑关联变动
                topCompanyId = Convert.ToString(userCtx.LoadBizBillHeadDataById("bas_organization", userCtx.Company, "ftopcompanyid")?["ftopcompanyid"]);
            }
            if (topCompanyId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("当前用户上下文没有关联总部，请检查！");

            UserContext ctx = new UserContext();
            UserAuthTicket session = new UserAuthTicket();

            //用系统预设的管理员身份操作
            session.UserId = "sysadmin";
            session.DisplayName = "系统管理员";
            session.UserName = "系统管理员";

            session.Product = userCtx.ProductId();
            session.Company = topCompanyId;
            session.BizOrgId = topCompanyId;
            session.TopCompanyId = topCompanyId;
            session.ParentCompanyId = topCompanyId;
            session.Companys = GetCompanys(topCompanyId);

            // 初始化SessionId
            session.Id = Guid.NewGuid().ToString("N");
            //总部不是直营模式，所以这里设置为false
            session.IsDirectSale = false;

            ctx.SetUserSession(session);
            ctx.RequestId = userCtx.RequestId;
            ctx.Container = ServiceStack.HostContext.TryResolve<IServiceContainer>().BeginLifetimeScope(userCtx.RequestId);

            return ctx;
        }

        /// <summary>
        /// 获取所有企业主体信息（站点启动时获取）
        /// </summary> 
        /// <returns></returns>
        public static void InitMultiTenantInfo(IServiceContainer container)
        {
            //如果是单租户，则不查找租户信息
            if (!container.IsMultiTenant()) return;

            var svc = container.GetService<IHttpServiceInvoker>();

            //auth站点里面保存的数据中心信息
            var para = new CommonBillDTO()
            {
                FormId = "auth_company",
                OperationNo = "GetUserAllCompanyInfo",
            };
            para.SimpleData.Add("productid", para.ProductId());
            para.SimpleData.Add("userid", null);
            var req = svc.Invoke(null, TargetSEP.AuthService, para) as DynamicDTOResponse;
            var result = req?.OperationResult?.SrvData?.ToString();
            if (!result.IsNullOrEmptyOrWhiteSpace())
            {
                var dcs = result.FromJson<List<CompanyDCInfo>>();
                foreach (var item in dcs)
                {
                    var ex = dataCenterInfo.CompanyDC.FirstOrDefault(f => f.CompanyId.EqualsIgnoreCase(item.CompanyId));
                    if (ex == null)
                    {
                        dataCenterInfo.CompanyDC.Add(item);
                    }
                    else
                    {
                        if (ex.DbServer.IsNullOrEmptyOrWhiteSpace())
                        {
                            ex.DbServer = item.DbServer;
                        }
                        if (ex.DBName.IsNullOrEmptyOrWhiteSpace())
                        {
                            ex.DBName = item.DBName;
                        }
                    }
                }
                //去掉未设置数据库信息的企业
                dataCenterInfo.CompanyDC.RemoveAll(f => f.DbServer.IsNullOrEmptyOrWhiteSpace() || f.DBName.IsNullOrEmptyOrWhiteSpace());

                UpdateDCTCompanyDCInfos();
            }
        }

        /// <summary>
        /// 刷新企业
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="companyName"></param>
        public static void RefreshDataCenter(string companyId, string companyName)
        {
            var strHostFile = Path.Combine(PathUtils.GetStartupPath(), "DataCenter.json");
            if (File.Exists(strHostFile))
            {
                var hostCfg = File.ReadAllText(strHostFile, Encoding.UTF8);
                var dc = hostCfg?.FromJson<DataCenterInfo>();
                if (dc != null && dc.CompanyDC != null)
                {
                    var exists = dataCenterInfo.CompanyDC.FirstOrDefault(f => f.CompanyId.EqualsIgnoreCase(companyId));
                    if (exists != null)
                    {
                        exists.CompanyName = companyName;
                        SaveDataCenterConfig();
                    }
                }
            }
        }

        /// <summary>
        /// 刷新企业名称
        /// </summary>
        /// <param name="companyIdNameKv">企业ID名称键值对</param>
        public static void RefreshDataCenter(Dictionary<string, string> companyIdNameKv)
        {
            if (companyIdNameKv == null || !companyIdNameKv.Any()) return;

            var strHostFile = Path.Combine(PathUtils.GetStartupPath(), "DataCenter.json");
            if (!File.Exists(strHostFile)) return;

            var hostCfg = File.ReadAllText(strHostFile, Encoding.UTF8);
            var dc = hostCfg?.FromJson<DataCenterInfo>();
            if (dc == null || dc.CompanyDC == null) return;

            var needSave = false;
            foreach (var item in companyIdNameKv)
            {
                // 企业ID相等 且 企业名称 不相等时才需要处理，否则没意义
                var exists = dataCenterInfo.CompanyDC.FirstOrDefault(f =>
                    f.CompanyId.EqualsIgnoreCase(item.Key) &&
                    !f.CompanyName.EqualsIgnoreCase(item.Value));
                if (exists != null)
                {
                    exists.CompanyName = item.Value;
                    needSave = true;
                }
            }
            if (needSave)
            {
                SaveDataCenterConfig();
            }
        }

        /// <summary>
        /// 刷新企业信息：在企业注册时调用更新
        /// </summary>
        /// <param name="container"></param>
        public static void RefreshDataCenter(IServiceContainer container)
        {
            dataCenterInfo.CompanyDC.Clear();
            var strHostFile = Path.Combine(PathUtils.GetStartupPath(), "DataCenter.json");
            if (File.Exists(strHostFile))
            {
                var hostCfg = File.ReadAllText(strHostFile, Encoding.UTF8);

                var dc = hostCfg?.FromJson<DataCenterInfo>();
                if (dc != null && dc.CompanyDC != null)
                {
                    dataCenterInfo.CompanyDC.AddRange(dc.CompanyDC);
                }
            }

            InitMultiTenantInfo(container);
        }

        /// <summary>
        /// 获取数据库信息
        /// </summary> 
        /// <returns></returns>
        public static SQLDBServerInfo GetDBServerInfor(string dbId)
        {
            var db = dataCenterInfo.DBServer.FirstOrDefault(f => f.ServerId.EqualsIgnoreCase(dbId));
            return db;
        }

        /// <summary>
        /// 获取所有数据库信息
        /// </summary>
        /// <returns></returns>
        public static List<SQLDBServerInfo> GetALLDBServerInfor()
        {
            return dataCenterInfo.DBServer;
        }

        private static bool isUpdating = false;
        public static void BeginUpdate()
        {
            isUpdating = true;
        }

        public static void EndUpdate()
        {
            isUpdating = false;
        }

        /// <summary>
        /// 增加数据库服务器配置信息
        /// </summary>
        /// <param name="dbServerInfo"></param>
        public static void AddDBServerInfo(SQLDBServerInfo dbServerInfo)
        {
            if (dbServerInfo == null) return;

            if (!dataCenterInfo.DBServer.Any(f => f.ServerId == dbServerInfo.ServerId))
            {
                dataCenterInfo.DBServer.Add(dbServerInfo);
            }

            if (!isUpdating)
            {
                SaveDataCenterConfig();
            }
        }

        /// <summary>
        /// 移除指定数据库服务器信息
        /// </summary>
        /// <param name="dbServerInfo"></param>
        public static void RemoveDBServerInfo(SQLDBServerInfo dbServerInfo)
        {
            if (dbServerInfo == null) return;

            if (dataCenterInfo.DBServer.Contains(dbServerInfo))
            {
                dataCenterInfo.DBServer.Remove(dbServerInfo);
            }
        }


        /// <summary>
        /// 向当前应用站点添加企业信息
        /// </summary>
        /// <param name="companyInfo"></param>
        public static void AddCompanyDCInfo(CompanyDCInfo companyInfo)
        {
            if (companyInfo == null) return;

            if (!dataCenterInfo.CompanyDC.Contains(companyInfo))
            {
                dataCenterInfo.CompanyDC.Add(companyInfo);
            }

            if (!isUpdating)
            {
                SaveDataCenterConfig();
            }
        }

        /// <summary>
        /// 移除指定企业主体信息
        /// </summary>
        /// <param name="companyInfo"></param>
        public static void RemoveCompanyDCInfo(CompanyDCInfo companyInfo)
        {
            if (companyInfo == null) return;

            if (dataCenterInfo.CompanyDC.Contains(companyInfo))
            {
                dataCenterInfo.CompanyDC.Remove(companyInfo);

                UpdateDCTCompanyDCInfos();
            }
        }


        /// <summary>
        /// 保存数据中心配置文件
        /// </summary>
        public static void SaveDataCenterConfig()
        {
            var strHostFile = Path.Combine(PathUtils.GetStartupPath(), "DataCenter.json");
            dataCenterInfo.CompanyDC = dataCenterInfo.CompanyDC.GroupBy(f => f.CompanyId).Select(f => f.Last()).ToList();
            dataCenterInfo.DBServer = dataCenterInfo.DBServer.GroupBy(f => f.ServerId).Select(f => f.Last()).ToList();
            File.WriteAllText(strHostFile, dataCenterInfo.ToJson(true));

            var container = HostContext.TryResolve<IServiceContainer>().BeginNewLifetimeScope();
            InitMultiTenantInfo(container);
        }

        private static List<CompanyDCInfo> GetAllCompanyInfo(UserContext ctx)
        {
            var productId = "".ProductId();
            var dbService = ctx.Container.GetService<IDBService>();
            string sql = @" 
select distinct b.fcompanyid,c.fnumber as fcompanynumber,c.fname as fcompanyname,b.fdbserverid,
    b.fdbentity, b.fdbrserverid, b.fdbrentity, d.fid as faccountid,d.fpayaccountstatus
from v_auth_companyproduct b with (nolock)
inner join v_auth_company c  with (nolock)on c.fid=b.fcompanyid
left join v_pay_accountinfo d  with (nolock) on d.fsettleitem=c.fid 
where b.fproductid=@fproductid ";

            List<SqlParam> paras = new List<SqlParam>()
            {
                new SqlParam("@fproductid", System.Data.DbType.String, productId )
            };
            var defaultCompany = "".GetDefaultCompany();
            //取出对应的数据中心
            var defaultDBserver = GetDBServerInfor(defaultCompany.CompanyId);

            List<CompanyDCInfo> orgInfo = new List<CompanyDCInfo>();
            using (var reader = dbService.ExecuteReader(ctx, sql, paras))
            {
                while (reader.Read())
                {
                    var org = new CompanyDCInfo();
                    org.CompanyId = reader.GetString("fcompanyid");
                    org.CompanyNumber = reader.GetString("fcompanynumber");
                    org.CompanyName = reader.GetString("fcompanyname");
                    org.DbServer = reader["fdbserverid"] is DBNull ? "" : reader.GetString("fdbserverid");
                    org.DBName = defaultCompany.DBName;
                    org.ReadOnlyDbServer = reader["fdbrserverid"] is DBNull ? "" : reader.GetString("fdbrserverid");
                    org.ReadOnlyDBName = defaultCompany.ReadOnlyDBName;
                    org.AccountId = reader["faccountid"] is DBNull ? "" : reader.GetString("faccountid");
                    org.AccountStatus = reader["fpayaccountstatus"] is DBNull ? "" : reader.GetString("fpayaccountstatus");
                    orgInfo.Add(org);
                    if (!dataCenterInfo.DBServer.Any(f => f.ServerId == org.CompanyId))
                    {
                        var currDbServer = new SQLDBServerInfo();
                        currDbServer.ServerId = org.CompanyId;
                        currDbServer.ServerIP = defaultDBserver.ServerIP;
                        currDbServer.UserName = defaultDBserver.UserName;
                        currDbServer.Psw = defaultDBserver.Psw;
                        dataCenterInfo.DBServer.Add(currDbServer);
                    }
                }
            }

            return orgInfo;
        }

        private class QueryDbConfig
        {
            public string ServerId { get; set; }

            public string ServerIP { get; set; }

            public string UserName { get; set; }

            public string Psw { get; set; }

            public string DbName { get; set; }
        }
    }


    /// <summary>
    /// 处理企业注册、经销商注册时，更新相关负载均衡站点的配置信息
    /// </summary>
    [InjectService]
    public class UpdateDatacenterConfigInfo : IReceivedPubMessage
    {

        public string ChannelName
        {
            get { return PubSubChannel.DatacenterConfig; }
        }

        public void OnReceivedMessage(string msg)
        {
            var obj = msg?.FromJson<PublishSubDBCenterMsgInfo>();
            if (obj == null)
            {
                return;
            }

            //更新站点的datacenter.json文件
            if (obj.Add)
            {
                DataCenterExtentions.AddDBServerInfo(obj.dbServerInfo);
                DataCenterExtentions.AddCompanyDCInfo(obj.companyInfo);
            }
            else
            {
                DataCenterExtentions.RemoveCompanyDCInfo(obj.companyInfo);
                DataCenterExtentions.RemoveDBServerInfo(obj.dbServerInfo);
            }
            DataCenterExtentions.SaveDataCenterConfig();
        }



    }

    /// <summary>
    /// 经销商更换名称时，更新相关负载均衡站点的配置信息
    /// </summary>
    [InjectService]
    public class UpdateDataCenterCompanyName : IReceivedPubMessage
    {
        public string ChannelName
        {
            get { return PubSubChannel.UpdateDataCenterCompanyName; }
        }

        public void OnReceivedMessage(string msg)
        {
            var companyIdNameKv = msg?.FromJson<Dictionary<string, string>>();

            DataCenterExtentions.RefreshDataCenter(companyIdNameKv);
        }
    }
}
