using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.SuperOrm;

namespace JieNor.Framework.AppService.ScheduledTask
{
    /// <summary>
    /// 用户个性化设置重置任务
    /// </summary>
    [InjectService(AliasName = "Task")]
    [TaskSvrId("UsersProfileResetTask")]
    [Caption("用户个性化设置重置任务")]
    [TaskMultiInstance()]
    [Browsable(false)]
    public class UsersProfileResetTask : AbstractScheduleWorker
    {

        /// <summary>
        /// 缓存服务服务：内存缓存，负载均衡或分布式部署时，需要按id导向分流，否则要换成分布式的redis缓存
        /// </summary>
        [InjectProperty]
        protected IMemoryCache UserFrofileCache { get; set; }
        /// <summary>
        /// 执行任务逻辑
        /// </summary>
        protected override async Task DoExecute()
        {
            var logServiceEx = this.UserContext.Container.GetService<ILogServiceEx>();
            var metaSrv = this.UserContext.Container?.TryGetService<IMetaModelService>();
            var userFrofileCache = this.UserContext.Container?.TryGetService<IMemoryCache>();
            var userProfileMeta = metaSrv?.LoadFormModel(this.UserContext, "sys_userprofile");
            if (userProfileMeta == null)
            {
                return;
            }
            var userprofiledm = this.UserContext.Container?.TryGetService<IDataManager>();
            userprofiledm.InitDbContext(this.UserContext, userProfileMeta.GetDynamicObjectType(this.UserContext));
            userProfileMeta.Isolate = "0";
            var pkIdReader = this.UserContext.GetPkIdDataReader(userProfileMeta, " fprofile like '%\"sortOrder\":3%'", new List<SqlParam>(), true);
            var dcUserProfiles = userprofiledm.SelectBy(pkIdReader);
            logServiceEx.Info($"用户个性化设置中排序字段超过3个的有：{dcUserProfiles.Count}条");
            if (dcUserProfiles.Count < 1) return;
            //var jobParameter = Convert.ToString(this.TaskObject.Parameter).FromJson<Dictionary<string, object>>() ?? new Dictionary<string, object>();
            //var backup = jobParameter?.GetValue("backup", "") as string;
            //if (backup == "true")
            //{
            //    var svcX = this.UserContext.Container.GetService<IDBServiceEx>();
            //    var backupTable = $"T_SYS_USERPROFILE_Bak_{DateTime.Now:yyyyMMddHHmmss}";
            //    var count = svcX.Execute(this.UserContext, $"select [fid],[FFormId],[fprofile],[fcategory],[fuserid],[fbillformid],[fisactive],[fmainorgid] into {backupTable}  from T_SYS_USERPROFILE;");
            //    logServiceEx.Info($"用户个性化设置备份成功，共：{count}条，备份表：{backupTable}");
            //}
            var userProfiles = dcUserProfiles.OfType<DynamicObject>();
            foreach (var userProfile in userProfiles)
            {
                try
                {
                    var profile = Convert.ToString(userProfile["fprofile"] ?? "{}").FromJson<FormUserProfile>();
                    foreach (var layout in profile.ListLayout.Where(l => !string.IsNullOrEmpty(l.SortDirection) || l.SortOrder > 0))
                    {
                        layout.SortOrder = 0;
                        layout.SortDirection = "";
                    }
                    var key = $"UserProfile:{ userProfile["fmainorgid"]}:{userProfile["fuserid"]?.ToString()}:{userProfileMeta.FormCacheId}.{ userProfile["fcategory"]?.ToString()}";
                    var profileOld = userFrofileCache.Get<FormUserProfile>(UserContext, key);
                    if (profileOld != null)
                        userFrofileCache.Remove(this.UserContext, key);
                    var option = OperateOption.Create();
                    userProfile["fprofile"] = profile.ToJson();
                    userprofiledm.Save(userProfile, null, option);
                }
                catch (Exception ex)
                {
                    logServiceEx.Error($"用户个性化设置清除排序设置异常：用户个性化设置Id:{userProfile["fid"]}", ex);

                }
            }
            logServiceEx.Info("用户个性化设置重置任务执行完成。");
            await Task.CompletedTask;
        }
    }
}