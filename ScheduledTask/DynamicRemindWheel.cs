using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Timers;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using Autofac.Features.Metadata;
using System.Text.RegularExpressions;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.IoC;

namespace JieNor.Framework.AppService.ScheduledTask
{
    /// <summary>
    /// 动态提醒轮盘
    /// </summary>
    [Serializable]
    public class DynamicRemindWheel
    {
        private static DynamicRemindWheel Instance = null;
        private static object lockObj = new object();

        /// <summary>
        /// 轮盘格子（将轮盘分为86400个格子表示一天86400秒，每秒一个格子，每个格子里存放了一个缓存Key队列）
        /// </summary>
        private List<Queue<string>> Tootheds { get; set; }

        /// <summary>
        /// 定时器（用于定时扫描轮盘中的格子）
        /// </summary>
        private Timer Timer { get; set; }

        /// <summary>
        /// 定时器执行的回调函数
        /// </summary>
        private Action<DateTime, string> CallBack = null;

        /// <summary>
        /// 构造函数
        /// </summary>
        private DynamicRemindWheel()
        {
            //初始化轮盘格子
            this.Tootheds = new List<Queue<string>>();
            var seconds = 60 * 60 * 24;
            for (int i = 0; i < seconds; i++)
            {
                this.Tootheds.Add(new Queue<string>());
            }
        }

        /// <summary>
        /// 获取动态提醒轮盘实例（单例实现）
        /// </summary>
        /// <returns></returns>
        public static DynamicRemindWheel GetInstance()
        {
            if (Instance == null)
            {
                lock (lockObj)
                {
                    if (Instance == null)
                    {
                        Instance = new DynamicRemindWheel();
                    }
                }
            }
            return Instance;
        }

        /// <summary>
        /// 缓存服务
        /// </summary>
        [InjectProperty]
        private IMemoryCache RedisCache { get; set; }

        /// <summary>
        /// 向轮盘格子中添加项
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dynObj"></param>
        public void Add(UserContext userCtx, DynamicObject dynObj)
        {
            if (userCtx == null || dynObj == null || dynObj["freminddate"].IsNullOrEmptyOrWhiteSpace()) return;

            var today = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day);
            var remindDate = Convert.ToDateTime(dynObj["freminddate"]);
            if (remindDate >= DateTime.Now && remindDate.ToString("yyyy-MM-dd").EqualsIgnoreCase(today.ToString("yyyy-MM-dd")))
            {
                if (this.RedisCache == null)
                {
                    this.RedisCache = userCtx.Container.GetService<IMemoryCache>();
                }

                var expireDate = DateTime.UtcNow.AddDays(HostConfigView.Jwt.ExpireTokensInDays);
                var dynDic = new Dictionary<string, object>();
                dynDic["id"] = dynObj["id"];
                dynDic["fbizformid"] = dynObj["fbizformid"];
                dynDic["fcontent"] = dynObj["fcontent"];
                dynDic["fmainorgid"] = dynObj["fmainorgid"];
                dynDic["fusercontext"] = JwtUtil.CreateJwtToken(userCtx, HostConfigView.Jwt.PrivateKeyXml, expireDate);
                dynDic["fnotremind"] = false;

                var cacheKey = $"dyn:{dynObj["fbizformid"]}_lg:{dynObj["id"]}";
                this.RedisCache.Set(cacheKey, JsonConvert.SerializeObject(dynDic), new TimeSpan(24, 0, 0)); //缓存24小时

                //从今天0时0分0秒 到 指定日期之间的总秒数
                var currentSecond = (int)(remindDate - today).TotalSeconds;
                if (currentSecond > 0) currentSecond--;

                var cacheKeys = this.Tootheds[currentSecond];
                cacheKeys.Enqueue(cacheKey);
            }
        }

        /// <summary>
        /// 将指定的动态记录设置为不再提醒
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="bizFormId"></param>
        /// <param name="logId"></param>
        /// <param name="notRemind"></param>
        public void SetNotRemind(UserContext userCtx, string bizFormId, string logId, bool notRemind = true)
        {
            if (this.RedisCache == null)
            {
                this.RedisCache = userCtx.Container.GetService<IMemoryCache>();
            }
            var cacheKey = $"dyn:{bizFormId}_lg:{logId}";
            var dynDic = this.RedisCache.Get<string>(cacheKey).FromJson<Dictionary<string, object>>();
            if (dynDic == null || dynDic.Count <= 0) return;

            dynDic["fnotremind"] = notRemind;

            this.RedisCache.Set(cacheKey, JsonConvert.SerializeObject(dynDic), new TimeSpan(24, 0, 0)); //缓存24小时
        }

        /// <summary>
        /// 清除所有轮盘格子中的项
        /// </summary>
        public void Clear()
        {
            foreach (var item in this.Tootheds)
            {
                item.Clear();
            }
        }

        /// <summary>
        /// 启动一个定时器定时轮询轮盘格子中的动态记录
        /// </summary>
        /// <param name="callBack"></param>
        public void StartTimer(Action<DateTime, string> callBack)
        {
            if (this.Timer == null)
            {
                this.CallBack = callBack;
                this.Timer = new Timer(1000);
                this.Timer.AutoReset = true;
                this.Timer.Enabled = true;
                this.Timer.Elapsed += Timer_Elapsed;
            }
        }

        /// <summary>
        /// 定时触发的事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Timer_Elapsed(object sender, ElapsedEventArgs e)
        {
            //从今天0时0分0秒 到 定时器触发这一刻之间的总秒数
            var currentSecond = (int)(e.SignalTime - new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day)).TotalSeconds;
            if (currentSecond > 0) currentSecond--;

            var cacheKeys = this.Tootheds[currentSecond];
            while (cacheKeys.Count > 0)
            {
                var cacheKey = cacheKeys.Dequeue();

                //执行回调
                this.CallBack?.Invoke(e.SignalTime, cacheKey);
            }
        }

        /// <summary>
        /// 加载动态提醒记录
        /// </summary>
        /// <param name="userCtx"></param>
        public void Load(UserContext userCtx)
        {
            string[] remindFormIds = null;

            var sysProfile = userCtx.Container.GetService<ISystemProfile>();
            var strValue = sysProfile.GetProfile(userCtx, "fw", "dyn_remind_formid");
            if (!strValue.IsNullOrEmptyOrWhiteSpace())
            {
                remindFormIds = strValue.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            }
            if (remindFormIds == null || remindFormIds.Length <= 0)
            {
                var baseFormProvider = userCtx.Container.GetService<IBaseFormProvider>();
                remindFormIds = baseFormProvider?.GetDefaultRemindFormIds(userCtx)?.ToArray();
            }
            if (remindFormIds == null || remindFormIds.Length <= 0) return;

            var logServiceEx = userCtx.Container.GetService<ILogServiceEx>();
            var metaService = userCtx.Container.GetService<IMetaModelService>();
            var dbService = userCtx.Container.GetService<IDBService>();
            var bizLogDm = userCtx.Container?.TryGetService<IDataManager>();

            foreach (var formId in remindFormIds)
            {
                HtmlForm form = null;
                try
                {
                    form = metaService.LoadFormModel(userCtx, formId);
                }
                catch { }
                if (form == null)
                {
                    logServiceEx.Error($"加载动态提醒记录时：没有找到 {formId} 表单模型！");
                    continue;
                }
                try
                {
                    //初始化业务对象日志表结构
                    var bizLogDt = logServiceEx.LoadFormLogType(userCtx, form.Id);
                    bizLogDm.InitDbContext(userCtx, bizLogDt);

                    var plugIn = getPlugIn(userCtx, form);

                    LoadDynamicLog(userCtx, form, dbService, plugIn);

                    bizLogDt = null;
                }
                catch (BusinessException ex)
                {
                    logServiceEx.Error($"表单Id：{form.Id}，错误消息：{ex.Message}");
                }
                catch (Exception ex)
                {
                    logServiceEx.Error($"加载动态日志时出现未知错误，表单Id：{form.Id}，错误消息：{ex.Message}, stacktrace:{ex.StackTrace}");
                }
            }
        }

        /// <summary>
        /// 加载动态日志
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="form"></param>
        /// <param name="dbService"></param>
        /// <returns></returns>
        private void LoadDynamicLog(UserContext userCtx, HtmlForm form, IDBService dbService, List<IDynamicRemindPlugIn> plugIn)
        {
            Task.Run(() =>
            {
                var sqlText = $@"
                select fid id,fbizformid,freminddate,fcontent,fmainorgid from {form.BillHeadTableName}_lg 
                where fmainorgid='{userCtx.Company}' and ftype='recordtype_01' and fnotremind='0' 
                and freminddate>='{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}.000' 
                and freminddate<='{DateTime.Now.ToString("yyyy-MM-dd")} 23:59:59.999'";

                var permiSvc = userCtx.Container.GetService<IPermissionService>();
                var isDevOpsUser = permiSvc.IsDevOpsUser(userCtx, new MetaCore.PermData.PermAuth(userCtx));
                if (!isDevOpsUser)
                {
                    sqlText += " and fisdevops='0' ";
                }

                var args = new OnDynamicRemindLoadLogArgs();
                OnLoadDynamicLog(plugIn, args);

                if (args.ChangedSql && false == string.IsNullOrWhiteSpace(args.SqlText))
                {
                    sqlText = args.SqlText;
                }

                var dynObjs = dbService.ExecuteDynamicObject(userCtx, sqlText);
                if (dynObjs == null || dynObjs.Count <= 0) return;

                foreach (var dynObj in dynObjs)
                {
                    this.Add(userCtx, dynObj);
                }
            });
        }

        /// <summary>
        /// 调用插件OnLoadDynamicLog事件
        /// </summary>
        /// <param name="plugIn"></param>
        /// <param name="args"></param>
        private void OnLoadDynamicLog(List<IDynamicRemindPlugIn> plugIn, OnDynamicRemindLoadLogArgs args)
        {
            if (plugIn == null && plugIn.Count <= 0)
            {
                return;
            }

            foreach(var p in plugIn)
            {
                p.OnLoadDynamicLog(args);
            }
        }

        /// <summary>
        /// 获取插件并初始化插件
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="htmlForm"></param>
        /// <returns></returns>
        private List<IDynamicRemindPlugIn> getPlugIn(UserContext userContext, HtmlForm htmlForm)
        {
            var plugIn = userContext.Container.GetService<IEnumerable<Meta<Lazy<IDynamicRemindPlugIn>>>>()
                            .Where(o => (o.Metadata.GetString("FormId").EqualsIgnoreCase(htmlForm.Id) ||
                                        o.Metadata.GetString("FormId").EqualsIgnoreCase("*") ||
                                        o.Metadata.GetString("FormId").StartsWith(htmlForm.Id + "|") ||
                                        o.Metadata.GetString("FormId").EndsWith("|" + htmlForm.Id) ||
                                        o.Metadata.GetString("FormId").IndexOf("|" + htmlForm.Id + "|") > 0))
                            .Select(o => o.Value.Value)
                            .ToList();

            if (plugIn != null && plugIn.Count > 0)
            {
                foreach (var p in plugIn)
                {
                    p.InitPlugIn(userContext, htmlForm);
                }
            }

            return plugIn;
        }

    }
}