using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.ComponentModel;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.Interface.Log;

namespace JieNor.Framework.AppService.ScheduledTask
{
    /// <summary>
    /// 动态提醒任务
    /// </summary>
    [InjectService(AliasName = "Task")]
    [TaskSvrId("dynamicremindtask")]
    [Caption("动态提醒")]
    [TaskMultiInstance()]
    [Browsable(false)]
    public class DynamicRemindTask : AbstractScheduleWorker
    {
        /// <summary>
        /// 执行任务逻辑
        /// </summary>
        protected override async Task DoExecute()
        {
            var drw = DynamicRemindWheel.GetInstance();

            await Task.Run(() => 
            {
                drw.Load(this.UserContext);
            });

            var logServiceEx = this.UserContext.Container.GetService<ILogServiceEx>();
            logServiceEx.Info("动态提醒轮盘通过【计划任务】初始化完成。");
        }
    }
}