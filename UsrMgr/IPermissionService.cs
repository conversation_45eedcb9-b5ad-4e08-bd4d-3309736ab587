using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.PermData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.UsrMgr
{
    /// <summary>
    /// 权限服务接口
    /// </summary>
    public interface IPermissionService
    {
        /// <summary>
        /// 返回当前用户是否具有某表单某操作的权限
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="permData"></param>
        /// <returns></returns>
        bool HasPermission(UserContext ctx, PermAuth permData);

        /// <summary>
        /// 判断当前用户是否具有某表单某操作的权限，无权时会直接报错
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="permData"></param>
        void CheckPermission(UserContext ctx, PermAuth permData);


        /// <summary>
        /// 获取拥有某个业务对象的对应权限的用户列表，比如获取【拥有查看招标信息】的用户列表,
        /// 多用于发送消息时确定那些用户能收到此类消息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="permData"></param>
        /// <returns></returns>
        List<string> HaveBizObPermitUsers(UserContext ctx, PermAuth permData);


        /// <summary>
        /// 获取当前用户对某个具体业务对象拥有的权限信息,
        /// 多用于UI页面的菜单按钮显示控制
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        List<string> HaveBizPermitInfo(UserContext ctx, string formId);


        /// <summary>
        /// 判断用户是否属于管理员角色组
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="permData"></param>
        /// <returns></returns>
        bool IsRoleAdmin(UserContext ctx, PermAuth permData);

        /// <summary>
        /// 判断用户是否属于系统运维角色组
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="permData"></param>
        /// <returns></returns>
        bool IsDevOpsUser(UserContext ctx, PermAuth permData);

        /// <summary>
        /// 获取系统发布的业务对象信息（主要用于授权相关功能）
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        List<Tuple<string, List<HtmlPermItem>, string, string>> GetBizObjPermitItem(UserContext ctx);




        /// <summary>
        /// 创建用户角色映射
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="dctUserRoleMap"></param>
        /// <param name="forceRebuild"></param>
        void CreateUserRoleMap(UserContext ctx, Dictionary<string, IEnumerable<string>> dctUserRoleMap, bool forceRebuild = false);

        /// <summary>
        /// 保存角色权限
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleACLDetail"></param>
        void SaveRolePermission(UserContext ctx, RolePermitInfo roleACLDetail);

        /// <summary>
        /// 获取角色权限
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="roleId"></param>
        /// <param name="fromRoleAuth">是否来源于角色授权的调用</param>
        /// <returns></returns>
        RolePermitInfo GetRolePermission(UserContext userCtx, string roleId, bool fromRoleAuth = false, string billformid = "");

        /// <summary>
        /// 检查角色是否有配置权限
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        bool IsRolePermissionSet(UserContext userCtx, string roleId);


        /// <summary>
        /// 业务单据自定义的数据隔离条件
        /// </summary>
        /// <param name="ctx">上下文信息</param>
        /// <param name="para">当前业务对象</param> 
        /// <returns>数据行范围sql</returns>
        IEnumerable<FilterRowObject> GetDataRowACLFilter(UserContext ctx, DataQueryRuleParaInfo para);

        /// <summary>
        /// 数据查询规则：主要应用在各业务系统自定义的数据隔离（数据授权）功能
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="para">当前业务对象</param> 
        /// <returns>数据行范围sql</returns>
        IEnumerable<FilterRowObject> GetDataQueryRuleFilter(UserContext ctx, DataQueryRuleParaInfo para);


        /// <summary>
        /// 获取表单的没有权限查看的字段列表
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        IEnumerable<string> GetInvisibleFieldByFormId(UserContext ctx, string formId);

        /// <summary>
        /// 获取角色关联的权限
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleId">角色</param>
        /// <returns>用户列表 key：用户id，value：用户名  </returns>
        Dictionary<string, string> GetRoleUser(UserContext ctx, string roleId);


        /// <summary>
        /// 更新用户档案上的角色字段(在角色授权中关联用户或者导入角色用户时调用)
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleId"></param>
        void UpdateUserRoleFldInfo(UserContext ctx, string roleId);

        /// <summary>
        /// 清空权限相关缓存
        /// </summary>
        /// <param name="ctx"></param>
        void ClearCache(UserContext ctx);
    }
}
