//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using JieNor.Framework.SuperOrm.DataManager;
//using JieNor.Framework.SuperOrm.DataEntity;
//using JieNor.Framework.SuperOrm;
//using JieNor.Framework.Interface.BizTask;

//namespace JieNor.Framework.AppService.BizTask
//{
//    [InjectService]
//    [FormId("bas_task")]
//    [OperationNo("query")]
//    public class ViewTask: AbstractOperationServicePlugIn
//    {
//        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
//        {
//            base.AfterExecuteOperationTransaction(e);
//            var Task = this.Context.Container.GetService<IJobScheduleService>();
//            List<ScheduleTaskObject> mdlDatas = Task.GetAllTaskItems(this.Context);
//            if (mdlDatas.Count() == 0)
//            {
//                return;
//            }
//            List<string> ids = new List<string>();
//            foreach (var item in mdlDatas)
//            {
//                ids.Add(item.Id);
//            }
//            Task task = new Task(() =>
//            {
//                var meta = this.Context.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "bas_task");
//                var dm = this.Context.Container.GetService<IDataManager>();
//                dm.InitDbContext(this.Context, meta.GetDynamicObjectType(this.Context));
//                List<DynamicObject> dy = dm.Select(ids).OfType<DynamicObject>().ToList();

//                List<DynamicObject> data = new List<DynamicObject>();   //待保存的数据
//                foreach (var item in mdlDatas)
//                {
//                    List<DynamicObject> listMdl = dy.Where(s => s["Id"]?.ToString() == item.Id).ToList();
//                    if (listMdl.Count > 0)
//                    {
//                        //item["fname"] = listMdl[0].Caption;
//                        listMdl[0]["fpluginid"] = item.PluginId;
                        
//                    }
//                    else
//                    {
//                        DynamicObject taskdata = meta.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;
//                        taskdata["Id"] = item.Id;
//                        taskdata["fname"] = item.Name;
//                        taskdata["fexecuteplan"] = item.DefCronExp;
//                        taskdata["fstartstatus"] = "ew_start002";
//                        taskdata["fpluginid"] = item.PluginId;//对应插件名称
//                        //taskdata["fworkobject"] = item.WorkObject;
//                        dy.Add(taskdata);
//                    }
//                }

//                var defCalulator = this.Context.Container.GetService<IDefaultValueCalculator>();
//                defCalulator.Execute(this.Context, meta, data.ToArray());
//                var option = OperateOption.Create();
//                option.SetBulkCopy(true);
//                option.SetCacheData(false);
//                dm.Save(dy, null, option);
//            });

//            ThreadWorker.QuequeTask(task, result =>
//            {
//                if (result?.Exception != null)
//                {
//                    WinEventUtil.WriteError("线程执行错误：", result.Exception, 2018);
//                }
//            });
//        }
//    }
//}
