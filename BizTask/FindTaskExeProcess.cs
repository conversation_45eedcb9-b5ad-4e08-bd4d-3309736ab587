using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.Framework.AppService.SystemPlugIn.MainFw
{
    /// <summary>
    /// 查询任务的执行进度
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("FindTaskExeProcess")]
    public class FindTaskExeProcess : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var logId = this.GetQueryOrSimpleParam<string>("TaskExeProcess", "");
            if(logId.IsNullOrEmptyOrWhiteSpace ())
            {
                return;
            }

            this.Result.IsSuccess = false;
            this.Result.SrvData = null;

            var progressResult = this.TaskProgressService.GetTaskProgress(this.Context, logId);
            this.Result.IsSuccess = true;
            this.Result.SrvData = new
            {
                Id=logId,
                TotalCount = 100m,
                FinishCount = progressResult.ProgressValue,
                IsFinish = progressResult.ProgressValue >= 100m,
            };
        }
    }
}
