using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface.IM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.DataTransferObject.IM;
using ServiceStack;
using System.Reflection;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.DataTransferObject.Const;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.CustomException;

namespace JieNor.Framework.AppService.MessageHandler
{
    /// <summary>
    /// 标准任务消息处理器
    /// </summary>
    [InjectService]
    [MessageChannel(MsgHandlerConsts.CST_ChannelName_StandardRequest)]
    public class StandardRequestHandler : AbstractMsgProcPlugIn<DynamicDTOWrapper>
    {
        static MethodInfo fromJsonMethod = null;

        static StandardRequestHandler()
        {
            fromJsonMethod = typeof(ObjectUtils).GetMethod("FromJson");
        }

        /// <summary>
        /// 约定消息处理规则
        /// </summary>
        /// <param name="option"></param>
        public override void InitializeOption(MessageHandlerOption option)
        {
            base.InitializeOption(option);

            option.AckMode = 1;
            option.RequeueWhenFailed = true;
        }

        /// <summary>
        /// 处理标准请求的消息任务
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="bizMsg"></param>
        protected override void OnProcessMessage(IMMessage msg, DynamicDTOWrapper bizMsg)
        {
            if (bizMsg == null) return;

            //设置当前操作为后台操作
            bizMsg.SetOptionFlag((long)Enu_OpFlags.RunInBackground);
            var resp = this.Gateway.InvokeLocal<DynamicDTOResponse>(this.Context, bizMsg, Enu_HttpMethod.Post);

            if (resp?.OperationResult?.IsSuccess == true)
            {
                //明确告诉引擎消息处理成功
                this.Result.Ack = true;

                //如果消息需要回复，则自动向消息来源方回复
                if (bizMsg.HasOptionFlag((long)Enu_OpFlags.RequestToReply))
                {
                    if (bizMsg.ReplyTo == null
                        || bizMsg.ReplyTo.CompanyId.IsNullOrEmptyOrWhiteSpace()
                        || bizMsg.ReplyTo.FormId.IsNullOrEmptyOrWhiteSpace()
                        || bizMsg.ReplyTo.OperationNo.IsNullOrEmptyOrWhiteSpace())
                    {
                        throw new ApplicationException("请求异步任务消息通知时必须提供应答方企业代码与业务标识！(companydId,formId)");
                    }
                    var replyDto = bizMsg.GetType().CreateInstance() as DynamicDTOWrapper;
                    replyDto = replyDto.SetFormId(bizMsg.ReplyTo.FormId)
                        .SetOperationNo(bizMsg.ReplyTo.OperationNo)
                        .SetResponseError(resp.ResponseStatus)
                        .SetSimpleData(resp.OperationResult.SimpleData)
                        .SetOption(resp.OperationResult.OptionData)
                        .SetOption("_srvdata", resp.OperationResult.SrvData?.ToJson())
                        //.SetReplyTo(new ReplyObject()
                        //{
                        //    CompanyId = this.Context.Company,
                        //    ProductId = this.Context.Product,
                        //    FormId = resp.FormId,
                        //    OperationNo = resp.OperationNo
                        //})
                        .Runbackground();

                    object respReply = null;
                    if (bizMsg.ReplyTo.CompanyId.EqualsIgnoreCase(this.Context.Company)
                        && bizMsg.ReplyTo.ProductId.EqualsIgnoreCase(this.Context.Product))
                    {
                        respReply = this.Gateway.InvokeLocal<DynamicDTOResponse>(this.Context, replyDto);
                    }
                    else
                    {
                        respReply = this.Gateway.Invoke(this.Context, new TargetSEP(bizMsg.ReplyTo.CompanyId, bizMsg.ReplyTo.ProductId ?? this.Context.Product), replyDto);
                    }
                    if (respReply is DynamicDTOResponse)
                    {
                        var retCsv = (respReply as DynamicDTOResponse).OperationResult.SrvData as string;
                        var dctRet = retCsv?.FromJson<Dictionary<string, string>>() ?? new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
                        var taskId = "";
                        dctRet.TryGetValue("taskId", out taskId);
                        resp.OperationResult.SimpleData["_replyTaskId"] = taskId;
                    }
                }
            }

            this.Result.OperationResult.MergeResult(resp.OperationResult);

        }

        /// <summary>
        /// 反序列化请求实体按操作名
        /// </summary>
        /// <param name="msg"></param>
        /// <returns></returns>
        protected override DynamicDTOWrapper OnDeserializedMessage(IMMessage msg)
        {
            if (msg == null) return null;
            object operationName = "";
            if (msg.Head.Tags.TryGetValue("operationName", out operationName))
            {
                var opType = HostContext.Metadata.GetOperationType(operationName?.ToString()??"");
                if (opType == null) return null;

                var msgBody = msg.Body.FirstOrDefault(x => x.BodyType == IMMessageBodyType.Json);
                if (msgBody == null || msgBody.Content.IsNullOrEmptyOrWhiteSpace()) return null;

                if (fromJsonMethod != null)
                {
                    var deserializeMethod = fromJsonMethod.MakeGenericMethod(opType);
                    var dto = deserializeMethod.Invoke(null, new object[] { msgBody.Content, true }) as DynamicDTOWrapper;
                    if (dto != null) return dto;
                }
            }
            return base.OnDeserializedMessage(msg);
        }
    }
}
