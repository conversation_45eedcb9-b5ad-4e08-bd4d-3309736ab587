using JieNor.AMS.YDJ.Store.AppService.Service.PDA;
using JieNor.Framework;
using JieNor.Framework.Interface;
using ServiceStack;
using System;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth.PDA
{/// <summary>
 /// 获取最新版本---作废
 /// </summary>
 /// <param name="e"></param>
    public class GetPDAVersion : ServiceStack.Service
    {

        /// <summary>
        /// 接口数据传输对象
        /// </summary>
        [Route("/GetPDAVersion")]
        public class SysConfigDTO : IReturn<PadResponse>
        {

        }
        public object Any(SysConfigDTO dto)
        {
            var container = this.TryResolve<IServiceContainer>().BeginLifetimeScope(Guid.NewGuid().ToString());
            var resp = new PadResponse();
            var company = this.GetAllCompanys().Values.LastOrDefault();
            var userCtx = this.CreateContextByCompanyInfo(company);
            var topCtx = userCtx.CreateTopOrgDBContext();

            var dbService = container.GetService<IDBService>();
            //根据版本号获取最新审批结束的版本
            var sql = $@"select top 1 fversionnumber,fversionnumbershow,fversionpackage,fdescription,fisforcedupdate from t_pda_version where fstatus='E' and fcancelstatus=0 order by fversionnumber desc  ";
            var resversion = dbService.ExecuteDynamicObject(topCtx, sql);
            resp.OperationResult.SrvData = null;
            resp.OperationResult.IsSuccess = true;


            if (resversion != null && resversion.Count > 0)
            {

                resp.OperationResult.SrvData = new
                {
                    Versionnumber = resversion[0]["fversionnumber"],
                    Actualversionnumber = resversion[0]["fversionnumbershow"],
                    Versionpackageurl = resversion[0]["fversionpackage"].ToString().GetSignedFileUrl(),
                    Description = resversion[0]["fdescription"],
                    Isforcedupdate = resversion[0]["fisforcedupdate"]
                };
            }
            return resp;

        }

    }
}
