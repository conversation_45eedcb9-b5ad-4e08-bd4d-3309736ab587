using JieNor.AMS.YDJ.Store.AppService.Plugin.MP;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Permission;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth
{
    public partial class PreRolePermInfo
    {
        /// <summary>
        /// 经销商角色--财务数据权限
        /// </summary>
        /// <returns>列表： item1：表单标识，item2：字段标识，item3：字段名称，item4：是否可见，item5：是否可修改 </returns>
        public static List<Tuple<string, List<DataRowAuthInfo>>> GetAgentDataRowAuth_BigCustomerFin()
        {
            /* 系统中预设角色权限，然后通过下面的这个sql查询到这个预设数据
            select distinct fbizobjid ,ffiltertype,'"' + fbizobjid + '|' + ffiltertype + '|' + fexpress + '|'+ fdesc + '|' + fsuperiorperm  + '|' + fbdfldfilter  + '|' + fbdfldfilter_txt    +  '",'  
            from t_sec_roledatarowacl 
            where fcompanyid = '企业id' and froleId = '角色id'
            order by  fbizobjid ,ffieldid
            */
            var fldItems = new List<string>()
            {
                "ydj_dept| | | |0| | ",
                "ydj_staff| | | |0| | ",
            };
            var result = GetDataRowPermItems(fldItems);

            return result;
        }

        /// <summary>
        /// 经销商角色--财务字段权限
        /// </summary>
        /// <returns>列表： item1：表单标识，item2：字段标识，item3：字段名称，item4：是否可见，item5：是否可修改 </returns>
        public static List<Tuple<string, List<FieldAuthInfo>>> GetAgentRoleFldPerm_BigCustomerFin()
        {
            /* 系统中预设角色权限，然后通过下面的这个sql查询到这个预设数据
            select distinct fbizobjid ,ffieldid,'"' + fbizobjid + ';' + ffieldid + ';' + ffieldname + ';'+ fvisible + ';' + fmodify   +  '",'  
            from t_sec_rolefieldacl  where fcompanyid = '企业id' and froleId = '角色id'
            order by  fbizobjid ,ffieldid
            */
            var fldItems = new List<string>()
            {
"rpt_pricesynthesize;fpurfacprice;采购单价（折前）;0;1",
"rpt_pricesynthesize;fpurdealprice;采购单价（折后）;0;1",
"rpt_pricesynthesize;freprice;分销价（折前）;0;1",
"rpt_pricesynthesize;funitcostprice;单位成本;0;1",

"ydj_transferorderapply;fcostprice;成本价格;0;1",

"stk_inventorytransfer;fcostprice;单位成本(加权平均);0;1",
"stk_inventorytransfer;fcostamt;总成本(加权平均);0;1",

"rpt_purchasedetail;fdealamount;金额;0;1",
"rpt_purchasedetail;fdealprice;单价;0;1",


"rpt_profitanalysis;fsumcost;总成本;0;1",
"rpt_profitanalysis;fgain;毛利;0;1",
"rpt_profitanalysis;faginrate;毛利率;0; 1",

"ydj_order;fcost;成本金额;0;1",
"ydj_order;fcostprice;成本价;0;1",
"ydj_order;ftransfercostprice;成本价格;0;1",
"ydj_order;ftargetagentamount;发货结算金额;0;1",
"ydj_order;fotherfee;其他费用;0;1",
"ydj_order;fpurfacamount;采购折前金额;0;1",
"ydj_order;fpurfacprice;采购单价（折前）;0;1",

"ydj_order_chg;fcost;成本金额;0;1",
"ydj_order_chg;fcostprice;成本价;0;1",
"ydj_order_chg;fpurfacamount;采购折前金额;0;1",
"ydj_order_chg;fpurfacamount_chg;采购折前金额（新）;0;1",
"ydj_order_chg;fpurfacprice;采购单价（折前）;0;1",
"ydj_order_chg;fpurfacprice_chg;采购单价（折前）（新）;0;1",
"ydj_order_chg;ftransfercostprice;成本价格;0;1",

"stk_postockin;fcostamt;总成本(加权平均);0;1",
"stk_postockin;fcostprice;单位成本(加权平均);0;1",

"stk_postockreturn;fcostamt;总成本(加权平均);0;1",
"stk_postockreturn;fcostprice;单位成本(加权平均);0;1",

"stk_sostockout;fcostamt;总成本(加权平均);0;1",
"stk_sostockout;fcostprice;单位成本(加权平均);0;1",
"stk_sostockout;fpurfacamount;采购折前金额;0;1",
"stk_sostockout;fpurfacprice;采购单价（折前）;0;1",

"stk_sostockreturn;fcostamt;总成本(加权平均);0;1",
"stk_sostockreturn;fcostprice;单位成本(加权平均);0;1",

"stk_inventorylist;fcostamt;成本;0;1",
"stk_inventorylist;fcostprice;成本价;0;1",

"rpt_stocksynthesize;fcostamt;成本;0;1",
"rpt_stocksynthesize;fcostprice;成本价;0;1",
"rpt_stocksynthesize;fpurfacprice;采购单价（折前）;0;1",
"rpt_stocksynthesize;fpurfacamount;采购折前金额;0;1",
"rpt_stocksynthesize;fpurdealprice;采购单价（折后）;0;1",
"rpt_stocksynthesize;fpurdealamount;采购折后金额;0;1",

"stk_otherstockin;fcostamt;总成本(加权平均);0;1",
"stk_otherstockin;fcostprice;单位成本(加权平均);0;1",

"stk_otherstockout;fcostamt;总成本(加权平均);0;1",
"stk_otherstockout;fcostprice;单位成本(加权平均);0;1",

"stk_inventoryverify;fcostamt;总成本(加权平均);0;1",
"stk_inventoryverify;fcostprice;单位成本(加权平均);0;1",
"stk_inventoryverify;fbizpdprice;盘点单价;0;1",
"stk_inventoryverify;fpdprice;基本单位盘点单价;0;1",
"stk_inventoryverify;fbizprice;账存单价;0;1",
"stk_inventoryverify;fprice;基本单位账存单价;0;1",
"stk_inventoryverify;famount;账存金额;0;1",
"stk_inventoryverify;fpdamount;盘点金额;0;1",
"stk_inventoryverify;fpyamount;盘盈金额;0;1",
"stk_inventoryverify;fpkamount;盘亏金额;0;1",
"stk_inventoryverify;fbugunitprice;采购单价(折前);0;1",
"stk_inventoryverify;fpkbuyamount;盈亏采购总额;0;1",

"stk_inventorybalance;fcostamt;成本;0;1",
"stk_inventorybalance;fcostprice;成本价;0;1",
"stk_inventorybalance;finicostamt;期初总成本;0;1",
"stk_inventorybalance;finicostprice;期初成本价;0;1",

"rpt_orderbalance;fcostprice;单位成本;0;1",
"rpt_orderbalance;fcost;总成本;0;1",
"rpt_orderbalance;fpurfacprice;采购单价(折前);0;1",
"rpt_orderbalance;fpurfacamount;采购折前金额;0;1",
"rpt_orderbalance;fpurdealprice;采购单价（折后）;0;1",
"rpt_orderbalance;fpurdealamount;采购折后金额;0;1",

"rpt_orderdetail;fcostprice;单位成本;0;1",
"rpt_orderdetail;fcost;总成本;0;1",

"ydj_stockoutreport;fcost;单位成本;0;1",
"ydj_stockoutreport;fcostamt;总成本;0;1",
"ydj_stockoutreport;fpurfacprice;采购单价(折前);0;1",
"ydj_stockoutreport;fpurfacamount;采购折前金额;0;1",
"ydj_stockoutreport;fpurdealprice;采购单价（折后）;0;1",
"ydj_stockoutreport;fpurdealamount;采购折后金额;0;1",

"rpt_stocksummary;fincostamt;收入总成本(加权平均);0;1",
"rpt_stocksummary;foutcostamt;发出总成本(加权平均);0;1",
"rpt_stocksummary;fpoprice;采购单价(折前);0;1",
"rpt_stocksummary;fpopriceafter;采购单价(折后);0;1",
"rpt_stocksummary;fstockintotal;收入总成本(折前);0;1",
"rpt_stocksummary;fstockintotalafter;收入总成本(折后);0;1",
"rpt_stocksummary;fstockouttotal;发出总成本(折前);0;1",
"rpt_stocksummary;fstockouttotalafter;发出总成本(折后);0;1",

"rpt_stockdetail;fincostamt;收入总成本(加权平均);0;1",
"rpt_stockdetail;foutcostamt;发出总成本(加权平均);0;1",
"rpt_stockdetail;fpoprice;采购单价(折前);0;1",
"rpt_stockdetail;fpopriceafter;采购单价(折后);0;1",
"rpt_stockdetail;fstockintotal;收入总成本(折前);0;1",
"rpt_stockdetail;fstockintotalafter;收入总成本(折后);0;1",
"rpt_stockdetail;fstockouttotal;发出总成本(折前);0;1",
"rpt_stockdetail;fstockouttotalafter;发出总成本(折后);0;1",

"rpt_stockageanalysis;fpurfacprice;采购单价(折前);0;1",
"rpt_stockageanalysis;fpurfacamount;采购折前金额;0;1",
"rpt_stockageanalysis;fpurdealprice;采购单价（折后）;0;1",
"rpt_stockageanalysis;fpurdealamount;采购折后金额;0;1",
"rpt_stockageanalysis;funitcostprice;单位成本价;0;1",
"rpt_stockageanalysis;funitcostamount;总成本;0;1",
            };
            var result = GetFieldPermItems(fldItems);

            return result;
        }

        /// <summary>
        /// 经销商角色--财务权限
        /// </summary>
        /// <returns>列表： item1：表单标识，item2：权限项 </returns>
        public static List<Tuple<string, List<string>>> GetAgentRolePermItem_BigCustomerFin()
        {
            /* 系统中预设角色权限，然后通过下面的这个sql查询到这个预设数据
            select distinct t0.fbizobjid ,fpermititemid , '"' + fbizobjid + ';' + fpermititemid + '",'
            from t_sec_rolefuncacl t0
            where t0.froleId = '角色id'  and fallow = '1'
            order by t0.fbizobjid ,fpermititemid
            */
            var permItems = new List<string>()
            {
                "bas_store;fw_view",
"bd_billtype;fw_view",
"coo_incomedisburse;fw_audit",
"coo_incomedisburse;fw_delete",
"coo_incomedisburse;fw_export",
"coo_incomedisburse;fw_listattach",
"coo_incomedisburse;fw_modify",
"coo_incomedisburse;fw_new",
"coo_incomedisburse;fw_operatelog",
"coo_incomedisburse;fw_presetfilteredit",
"coo_incomedisburse;fw_print",
"coo_incomedisburse;fw_sharelayout",
"coo_incomedisburse;fw_submit",
"coo_incomedisburse;fw_unaudit",
"coo_incomedisburse;fw_unsubmit",
"coo_incomedisburse;fw_view",
"coo_incomedisburse;fw_viewrecord",
"dashboard_mytask;fw_view",
"rpt_orderbalance;fw_export",
"rpt_orderbalance;fw_presetfilteredit",
"rpt_orderbalance;fw_print",
"rpt_orderbalance;fw_view",
"rpt_orderdetail;fw_export",
"rpt_orderdetail;fw_presetfilteredit",
"rpt_orderdetail;fw_print",
"rpt_orderdetail;fw_view",
"rpt_purchaseprice;fw_view",
"rpt_stockdetail;fw_export",
"rpt_stockdetail;fw_presetfilteredit",
"rpt_stockdetail;fw_print",
"rpt_stockdetail;fw_view",
"rpt_stocksummary;fw_export",
"rpt_stocksummary;fw_presetfilteredit",
"rpt_stocksummary;fw_print",
"rpt_stocksummary;fw_view",
"rpt_stocksynthesize;fw_export",
"rpt_stocksynthesize;fw_presetfilteredit",
"rpt_stocksynthesize;fw_print",
"rpt_stocksynthesize;fw_view",
"sal_dealeraccount;fw_export",
"sal_dealeraccount;fw_print",
"sal_dealeraccount;fw_view",
"sal_dealerdetail;fw_export",
"sal_dealerdetail;fw_presetfilteredit",
"sal_dealerdetail;fw_print",
"sal_dealerdetail;fw_view",
"sel_propvalue;fw_new",
"sel_propvalue;fw_view",
"ste_registfee;fw_audit",
"ste_registfee;fw_delete",
"ste_registfee;fw_export",
"ste_registfee;fw_listattach",
"ste_registfee;fw_modify",
"ste_registfee;fw_new",
"ste_registfee;fw_operatelog",
"ste_registfee;fw_print",
"ste_registfee;fw_submit",
"ste_registfee;fw_unaudit",
"ste_registfee;fw_unsubmit",
"ste_registfee;fw_view",
"ste_registfee;fw_viewrecord",
"ste_registfee;ydj_payment",
"ste_registfee;ydj_refund",
"stk_invcloseaccount;fw_new",
"stk_inventorybase;fw_view",
"stk_inventorylist;fw_export",
"stk_inventorylist;fw_view",
"stk_inventoryverify;fw_audit",
"stk_inventoryverify;fw_delete",
"stk_inventoryverify;fw_export",
"stk_inventoryverify;fw_listattach",
"stk_inventoryverify;fw_modify",
"stk_inventoryverify;fw_new",
"stk_inventoryverify;fw_operatelog",
"stk_inventoryverify;fw_print",
"stk_inventoryverify;fw_queryinventory",
"stk_inventoryverify;fw_submit",
"stk_inventoryverify;fw_unaudit",
"stk_inventoryverify;fw_unsubmit",
"stk_inventoryverify;fw_view",
"stk_inventoryverify;fw_viewrecord",
"stk_otherstockin;fw_audit",
"stk_otherstockin;fw_export",
"stk_otherstockin;fw_listattach",
"stk_otherstockin;fw_modify",
"stk_otherstockin;fw_operatelog",
"stk_otherstockin;fw_print",
"stk_otherstockin;fw_queryinventory",
"stk_otherstockin;fw_submit",
"stk_otherstockin;fw_unaudit",
"stk_otherstockin;fw_unsubmit",
"stk_otherstockin;fw_view",
"stk_otherstockin;fw_viewrecord",
"stk_otherstockout;fw_audit",
"stk_otherstockout;fw_export",
"stk_otherstockout;fw_modify",
"stk_otherstockout;fw_operatelog",
"stk_otherstockout;fw_print",
"stk_otherstockout;fw_queryinventory",
"stk_otherstockout;fw_submit",
"stk_otherstockout;fw_unaudit",
"stk_otherstockout;fw_unsubmit",
"stk_otherstockout;fw_view",
"stk_otherstockout;fw_viewrecord",
"stk_postockin;fw_view",
"stk_sostockout;fw_view",
"sys_cfunction;fw_view",
"sys_datawidgetlist;fw_view",
"sys_datawidgettype;fw_view",
"ydj_bank;fw_delete",
"ydj_bank;fw_import",
"ydj_bank;fw_listattach",
"ydj_bank;fw_modify",
"ydj_bank;fw_new",
"ydj_bank;fw_operatelog",
"ydj_bank;fw_print",
"ydj_bank;fw_view",
"ydj_banknum;fw_delete",
"ydj_banknum;fw_export",
"ydj_banknum;fw_listattach",
"ydj_banknum;fw_modify",
"ydj_banknum;fw_new",
"ydj_banknum;fw_operatelog",
"ydj_banknum;fw_print",
"ydj_banknum;fw_view",
"ydj_brand;fw_view",
"ydj_category;fw_view",
"ydj_closedaccounts;fw_anticlosing",
"ydj_closedaccounts;fw_closing",
"ydj_closedaccounts;fw_view",
"ydj_collectreceipt;fw_audit",
"ydj_collectreceipt;fw_delete",
"ydj_collectreceipt;fw_export",
"ydj_collectreceipt;fw_import",
"ydj_collectreceipt;fw_listattach",
"ydj_collectreceipt;fw_operatelog",
"ydj_collectreceipt;fw_print",
"ydj_collectreceipt;fw_submit",
"ydj_collectreceipt;fw_unaudit",
"ydj_collectreceipt;fw_unsubmit",
"ydj_collectreceipt;fw_view",
"ydj_collectreceipt;fw_viewrecord",
"ydj_contactunit;fw_export",
"ydj_contactunit;fw_print",
"ydj_contactunit;fw_view",
"ydj_costaccounting;fw_audit",
"ydj_costaccounting;fw_delete",
"ydj_costaccounting;fw_export",
"ydj_costaccounting;fw_modify",
"ydj_costaccounting;fw_new",
"ydj_costaccounting;fw_operatelog",
"ydj_costaccounting;fw_print",
"ydj_costaccounting;fw_submit",
"ydj_costaccounting;fw_unaudit",
"ydj_costaccounting;fw_unsubmit",
"ydj_costaccounting;fw_view",
"ydj_customer;fw_view",
"ydj_dept;fw_view",
"ydj_employeesalesrevenue;fw_export",
"ydj_employeesalesrevenue;fw_print",
"ydj_employeesalesrevenue;fw_view",
"ydj_expenseitem;fw_delete",
"ydj_expenseitem;fw_modify",
"ydj_expenseitem;fw_new",
"ydj_expenseitem;fw_operatelog",
"ydj_expenseitem;fw_view",
"ydj_expenseitem;fw_viewrecord",
"ydj_headstatement;fw_audit",
"ydj_headstatement;fw_delete",
"ydj_headstatement;fw_export",
"ydj_headstatement;fw_import",
"ydj_headstatement;fw_listattach",
"ydj_headstatement;fw_modify",
"ydj_headstatement;fw_new",
"ydj_headstatement;fw_operatelog",
"ydj_headstatement;fw_print",
"ydj_headstatement;fw_submit",
"ydj_headstatement;fw_unaudit",
"ydj_headstatement;fw_unsubmit",
"ydj_headstatement;fw_view",
"ydj_headstatement;fw_viewrecord",
"ydj_headstatement;ydj_headstatement_confirmbill",
"ydj_headstatement;ydj_headstatement_unconfirmbill",
"ydj_marketstatement;fw_audit",
"ydj_marketstatement;fw_delete",
"ydj_marketstatement;fw_export",
"ydj_marketstatement;fw_import",
"ydj_marketstatement;fw_modify",
"ydj_marketstatement;fw_new",
"ydj_marketstatement;fw_operatelog",
"ydj_marketstatement;fw_submit",
"ydj_marketstatement;fw_unaudit",
"ydj_marketstatement;fw_unsubmit",
"ydj_marketstatement;fw_view",
"ydj_marketstatement;fw_viewrecord",
"ydj_marketstatement;verifyincomedisburse_p",
"ydj_order;fw_export",
"ydj_order;fw_print",
"ydj_order;fw_view",
"ydj_order;ydj_order_receipt",
"ydj_order;ydj_order_refund",
"ydj_order_chg;fw_view",
"ydj_payreceipt;fw_audit",
"ydj_payreceipt;fw_delete",
"ydj_payreceipt;fw_export",
"ydj_payreceipt;fw_listattach",
"ydj_payreceipt;fw_operatelog",
"ydj_payreceipt;fw_print",
"ydj_payreceipt;fw_submit",
"ydj_payreceipt;fw_unaudit",
"ydj_payreceipt;fw_unsubmit",
"ydj_payreceipt;fw_view",
"ydj_price;fw_delete",
"ydj_price;fw_modify",
"ydj_price;fw_new",
"ydj_price;fw_view",
"ydj_price;fw_viewrecord",
"ydj_product;fw_view",
"ydj_purchaseorder;fw_view",
"ydj_purchaseorder_chg;fw_view",
"ydj_series;fw_view",
"ydj_service;fw_view",
"ydj_staff;fw_view",
"ydj_stockoutreport;fw_export",
"ydj_stockoutreport;fw_presetfilteredit",
"ydj_stockoutreport;fw_print",
"ydj_stockoutreport;fw_view",
"ydj_stockstatus;fw_view",
"ydj_storehouse;fw_view",
"ydj_supplier;fw_view",
"ydj_unit;fw_view",
"ms_markingassistant;fw_view",
"ydj_collectreceipt_mid;fw_view",
"ydj_payreceipt_mid;fw_view",
            };

            var result = GetPermItems(permItems);

            return result;
        }

        public static List<MPTabbarModel> GetAgentRoleMPMenu_BigCustomerFin()
        {
            /*
             select '"' + ftabbar + ';' + fgroup + ';' + fmenuid + ';' + fmenuname  +  '",'  
            from t_mp_rolemenu t0  with(nolock) 
            where t0.froleId=@roleId and fisallow=1
             */

            var menuItems = new List<string>()
            {
                "工作台;订单管理;720565024824889427;合同单",
                "工作台;订单管理;720565024824889428;收款单",
                "工作台;订单管理;720565024824889429;退款单",
                "工作台;企业管理;720565024824889431;审批",
            };


            var mpMenu = GetMPTabbars(menuItems);

            return mpMenu;
        }

    }
}
