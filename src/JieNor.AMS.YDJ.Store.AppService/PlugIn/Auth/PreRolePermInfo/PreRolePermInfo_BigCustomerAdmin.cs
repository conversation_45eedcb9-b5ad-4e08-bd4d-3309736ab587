using JieNor.AMS.YDJ.Store.AppService.Plugin.MP;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Permission;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth
{
    public partial class PreRolePermInfo
    {
        /// <summary>
        /// 大客户渠道经销商角色--管理员数据权限
        /// </summary>
        /// <returns>列表： item1：表单标识，item2：字段标识，item3：字段名称，item4：是否可见，item5：是否可修改 </returns>
        public static List<Tuple<string, List<DataRowAuthInfo>>> GetAgentDataRowAuth_BigCustomerAdmin()
        {
            /* 系统中预设角色权限，然后通过下面的这个sql查询到这个预设数据
            select distinct fbizobjid ,ffiltertype,'"' + fbizobjid + '|' + ffiltertype + '|' + fexpress + '|'+ fdesc + '|' + fsuperiorperm  + '|' + fbdfldfilter  + '|' + fbdfldfilter_txt    +  '",'  
            from t_sec_roledatarowacl 
            where fcompanyid = '企业id' and froleId = '角色id'
            order by  fbizobjid ,ffieldid
            */
            var fldItems = new List<string>()
            {
            };
            var result = GetDataRowPermItems(fldItems);

            return result;
        }

        /// <summary>
        /// 大客户渠道经销商角色--管理员字段权限
        /// </summary>
        /// <returns>列表： item1：表单标识，item2：字段标识，item3：字段名称，item4：是否可见，item5：是否可修改 </returns>
        public static List<Tuple<string, List<FieldAuthInfo>>> GetAgentRoleFldPerm_BigCustomerAdmin()
        {
            /* 系统中预设角色权限，然后通过下面的这个sql查询到这个预设数据
            select distinct fbizobjid ,ffieldid,'"' + fbizobjid + ';' + ffieldid + ';' + ffieldname + ';'+ fvisible + ';' + fmodify   +  '",'  
            from t_sec_rolefieldacl  where fcompanyid = '企业id' and froleId = '角色id'
            order by  fbizobjid ,ffieldid
            */
            var fldItems = new List<string>()
            {
"rpt_pricesynthesize;fpurfacprice;采购单价（折前）;0;1",
"rpt_pricesynthesize;fpurdealprice;采购单价（折后）;0;1",
"rpt_pricesynthesize;freprice;分销价（折前）;0;1",
"rpt_pricesynthesize;funitcostprice;单位成本;0;1",

"ydj_transferorderapply;fcostprice;成本价格;0;1",

"ydj_order;fcost;成本金额;0;1",
"ydj_order;fcostprice;成本价;0;1",
"ydj_order;ftransfercostprice;成本价格;0;1",
"ydj_order;ftargetagentamount;发货结算金额;0;1",
"ydj_order;fotherfee;其他费用;0;1",
"ydj_order;fpurfacamount;采购折前金额;0;1",
"ydj_order;fpurfacprice;采购单价（折前）;0;1",

"ydj_order_chg;fcost;成本金额;0;1",
"ydj_order_chg;fcostprice;成本价;0;1",
"ydj_order_chg;fpurfacamount;采购折前金额;0;1",
"ydj_order_chg;fpurfacamount_chg;采购折前金额（新）;0;1",
"ydj_order_chg;fpurfacprice;采购单价（折前）;0;1",
"ydj_order_chg;fpurfacprice_chg;采购单价（折前）（新）;0;1",
"ydj_order_chg;ftransfercostprice;成本价格;0;1",

"stk_inventorytransfer;fcostprice;单位成本(加权平均);0;1",
"stk_inventorytransfer;fcostamt;总成本(加权平均);0;1",

"rpt_purchasedetail;fdealamount;金额;0;1",
"rpt_purchasedetail;fdealprice;单价;0;1",


"rpt_profitanalysis;fsumcost;总成本;0;1",
"rpt_profitanalysis;fgain;毛利;0;1",
"rpt_profitanalysis;faginrate;毛利率;0; 1",

"ydj_purchaseorder;fprice;采购单价;0;1",
"ydj_purchaseorder;famount;金额;0;1",
"ydj_purchaseorder;fdealprice;成交单价;0;1",
"ydj_purchaseorder;ffbillamount;成交金额;0;1",
"ydj_purchaseorder;fdealamount_e;成交金额;0;1",
"ydj_purchaseorder;ftaxprice;含税出厂价;0;1",
"ydj_purchaseorder;fdepthdiscount;深度护理折扣;0;1",
"ydj_purchaseorder;fnewdiscount;新品折扣;0;1",
"ydj_purchaseorder;fexpenserebate;费用支持返利;0;1",
"ydj_purchaseorder;fotherdiscount;其他折扣;0;1",
"ydj_purchaseorder;fsapdiscount;SAP折扣总额;0;1",
"ydj_purchaseorder;fdistamount;折扣额;0;1",
"ydj_purchaseorder;fdealamount;货品原值;0;1",
"ydj_purchaseorder;fsettleamount;已付金额;0;1",
"ydj_purchaseorder;fpayamount;待结算金额;0;1",
"ydj_purchaseorder;fpaidamount;已结算金额;0;1",
"ydj_purchaseorder;fconfirmamount;待确认金额;0;1",
"ydj_purchaseorder;factrefundamount;实退金额;0;1",
"ydj_purchaseorder;frefundamount;申请退货金额;0;1",



"ydj_purchaseorder_chg;famount;金额;0;1",
"ydj_purchaseorder_chg;famount_chg;金额(新;0;1",
"ydj_purchaseorder_chg;fdealprice;成交单价;0;1",
"ydj_purchaseorder_chg;fdealprice_chg;成交单价(新;0;1",
"ydj_purchaseorder_chg;ffbillamount;成交金额;0;1",
"ydj_purchaseorder_chg;ffbillamount_chg;订单金额(新;0;1",
"ydj_purchaseorder_chg;fprice;采购单价;0;1",
"ydj_purchaseorder_chg;fprice_chg;单价(新;0;1",
"ydj_purchaseorder_chg;fdealamount_e_chg;成交金额(新;0;1",
"ydj_purchaseorder_chg;fdealamount_e;成交金额;0;1",
"ydj_purchaseorder_chg;ftaxprice;含税出厂价;0;1",
"ydj_purchaseorder_chg;fdepthdiscount;深度护理折扣;0;1",
"ydj_purchaseorder_chg;fnewdiscount;新品折扣;0;1",
"ydj_purchaseorder_chg;fexpenserebate;费用支持返利;0;1",
"ydj_purchaseorder_chg;fotherdiscount;其他折扣;0;1",
"ydj_purchaseorder_chg;fsapdiscount;SAP折扣总额;0;1",
"ydj_purchaseorder_chg;fdistamount;折扣额;0;1",
"ydj_purchaseorder_chg;fdealamount;货品原值;0;1",
"ydj_purchaseorder_chg;fdealamount_chg;货品原值;0;1",
"ydj_purchaseorder_chg;fsettleamount;已付金额;0;1",
"ydj_purchaseorder_chg;fpayamount;待结算金额;0;1",
"ydj_purchaseorder_chg;fpayamount_chg;待结算金额(新);0;1",
"ydj_purchaseorder_chg;fpaidamount;已结算金额;0;1",
"ydj_purchaseorder_chg;fconfirmamount;待确认金额;0;1",
"ydj_purchaseorder_chg;factrefundamount;实退金额;0;1",
"ydj_purchaseorder_chg;frefundamount;申请退货金额;0;1",




"stk_postockin;famount;成交金额;0;1",
"stk_postockin;fpoamount;金额;0;1",
"stk_postockin;fpoprice;采购单价;0;1",
"stk_postockin;fprice;成交单价;0;1",
"stk_postockin;fcostamt;总成本(加权平均);0;1",
"stk_postockin;fcostprice;单位成本(加权平均);0;1",

"stk_postockreturn;famount;成交金额;0;1",
"stk_postockreturn;fpoamount;金额;0;1",
"stk_postockreturn;fpoprice;采购单价;0;1",
"stk_postockreturn;fprice;成交单价;0;1",
"stk_postockreturn;fcostamt;总成本(加权平均);0;1",
"stk_postockreturn;fcostprice;单位成本(加权平均);0;1",

"stk_sostockout;fcostamt;总成本(加权平均);0;1",
"stk_sostockout;fcostprice;单位成本(加权平均);0;1",
"stk_sostockout;fpurfacamount;采购折前金额;0;1",
"stk_sostockout;fpurfacprice;采购单价（折前）;0;1",

"stk_sostockreturn;fcostamt;总成本(加权平均);0;1",
"stk_sostockreturn;fcostprice;单位成本(加权平均);0;1",

"stk_inventorylist;fcostamt;成本;0;1",
"stk_inventorylist;fcostprice;成本价;0;1",

"rpt_stocksynthesize;fcostamt;成本;0;1",
"rpt_stocksynthesize;fcostprice;成本价;0;1",
"rpt_stocksynthesize;fpurfacprice;采购单价（折前）;0;1",
"rpt_stocksynthesize;fpurfacamount;采购折前金额;0;1",
"rpt_stocksynthesize;fpurdealprice;采购单价（折后）;0;1",
"rpt_stocksynthesize;fpurdealamount;采购折后金额;0;1",

"stk_otherstockin;fcostamt;总成本(加权平均);0;1",
"stk_otherstockin;fcostprice;单位成本(加权平均);0;1",

"stk_otherstockout;fcostamt;总成本(加权平均);0;1",
"stk_otherstockout;fcostprice;单位成本(加权平均);0;1",

"stk_inventoryverify;fcostamt;总成本(加权平均);0;1",
"stk_inventoryverify;fcostprice;单位成本(加权平均);0;1",
"stk_inventoryverify;fbizpdprice;盘点单价;0;1",
"stk_inventoryverify;fpdprice;基本单位盘点单价;0;1",
"stk_inventoryverify;fbizprice;账存单价;0;1",
"stk_inventoryverify;fprice;基本单位账存单价;0;1",
"stk_inventoryverify;famount;账存金额;0;1",
"stk_inventoryverify;fpdamount;盘点金额;0;1",
"stk_inventoryverify;fpyamount;盘盈金额;0;1",
"stk_inventoryverify;fpkamount;盘亏金额;0;1",
"stk_inventoryverify;fbugunitprice;采购单价(折前);0;1",
"stk_inventoryverify;fpkbuyamount;盈亏采购总额;0;1",

"stk_inventorybalance;fcostamt;成本;0;1",
"stk_inventorybalance;fcostprice;成本价;0;1",
"stk_inventorybalance;finicostamt;期初总成本;0;1",
"stk_inventorybalance;finicostprice;期初成本价;0;1",

"rpt_orderbalance;fcostprice;单位成本;0;1",
"rpt_orderbalance;fcost;总成本;0;1",
"rpt_orderbalance;fpurfacprice;采购单价(折前);0;1",
"rpt_orderbalance;fpurfacamount;采购折前金额;0;1",
"rpt_orderbalance;fpurdealprice;采购单价（折后）;0;1",
"rpt_orderbalance;fpurdealamount;采购折后金额;0;1",

"rpt_orderdetail;fcostprice;单位成本;0;1",
"rpt_orderdetail;fcost;总成本;0;1",

"ydj_stockoutreport;fcost;单位成本;0;1",
"ydj_stockoutreport;fcostamt;总成本;0;1",
"ydj_stockoutreport;fpurfacprice;采购单价(折前);0;1",
"ydj_stockoutreport;fpurfacamount;采购折前金额;0;1",
"ydj_stockoutreport;fpurdealprice;采购单价（折后）;0;1",
"ydj_stockoutreport;fpurdealamount;采购折后金额;0;1",

"rpt_stocksummary;fincostamt;收入总成本(加权平均);0;1",
"rpt_stocksummary;foutcostamt;发出总成本(加权平均);0;1",
"rpt_stocksummary;fpoprice;采购单价(折前);0;1",
"rpt_stocksummary;fpopriceafter;采购单价(折后);0;1",
"rpt_stocksummary;fstockintotal;收入总成本(折前);0;1",
"rpt_stocksummary;fstockintotalafter;收入总成本(折后);0;1",
"rpt_stocksummary;fstockouttotal;发出总成本(折前);0;1",
"rpt_stocksummary;fstockouttotalafter;发出总成本(折后);0;1",

"rpt_stockdetail;fincostamt;收入总成本(加权平均);0;1",
"rpt_stockdetail;foutcostamt;发出总成本(加权平均);0;1",
"rpt_stockdetail;fpoprice;采购单价(折前);0;1",
"rpt_stockdetail;fpopriceafter;采购单价(折后);0;1",
"rpt_stockdetail;fstockintotal;收入总成本(折前);0;1",
"rpt_stockdetail;fstockintotalafter;收入总成本(折后);0;1",
"rpt_stockdetail;fstockouttotal;发出总成本(折前);0;1",
"rpt_stockdetail;fstockouttotalafter;发出总成本(折后);0;1",

"rpt_stockageanalysis;fpurfacprice;采购单价(折前);0;1",
"rpt_stockageanalysis;fpurfacamount;采购折前金额;0;1",
"rpt_stockageanalysis;fpurdealprice;采购单价（折后）;0;1",
"rpt_stockageanalysis;fpurdealamount;采购折后金额;0;1",
"rpt_stockageanalysis;funitcostprice;单位成本价;0;1",
"rpt_stockageanalysis;funitcostamount;总成本;0;1",

            };
            var result = GetFieldPermItems(fldItems);

            return result;
        }

        /// <summary>
        /// 大客户渠道经销商角色--管理员权限
        /// </summary>
        /// <returns>列表： item1：表单标识，item2：权限项 </returns>
        public static List<Tuple<string, List<string>>> GetAgentRolePermItem_BigCustomerAdmin()
        {
            /* 系统中预设角色权限，然后通过下面的这个sql查询到这个预设数据
            select distinct t0.fbizobjid ,fpermititemid , '"' + fbizobjid + ';' + fpermititemid + '",'
            from t_sec_rolefuncacl t0
            where t0.froleId = '角色id'  and fallow = '1'
            order by t0.fbizobjid ,fpermititemid
            */
            var permItems = new List<string>()
            {
                "bas_billdataaccesscontrol;fw_audit",
"bas_billdataaccesscontrol;fw_change",
"bas_billdataaccesscontrol;fw_delete",
"bas_billdataaccesscontrol;fw_distribute",
"bas_billdataaccesscontrol;fw_export",
"bas_billdataaccesscontrol;fw_forbid",
"bas_billdataaccesscontrol;fw_import",
"bas_billdataaccesscontrol;fw_listattach",
"bas_billdataaccesscontrol;fw_modify",
"bas_billdataaccesscontrol;fw_new",
"bas_billdataaccesscontrol;fw_operatelog",
"bas_billdataaccesscontrol;fw_presetfilteredit",
"bas_billdataaccesscontrol;fw_print",
"bas_billdataaccesscontrol;fw_settopreset",
"bas_billdataaccesscontrol;fw_sharelayout",
"bas_billdataaccesscontrol;fw_submit",
"bas_billdataaccesscontrol;fw_submitchange",
"bas_billdataaccesscontrol;fw_tasktmpl",
"bas_billdataaccesscontrol;fw_unaudit",
"bas_billdataaccesscontrol;fw_unchange",
"bas_billdataaccesscontrol;fw_undistribute",
"bas_billdataaccesscontrol;fw_unforbid",
"bas_billdataaccesscontrol;fw_unsubmit",
"bas_billdataaccesscontrol;fw_view",
"bas_billdataaccesscontrol;fw_viewrecord",
"bas_billnosetting;fw_audit",
"bas_billnosetting;fw_change",
"bas_billnosetting;fw_delete",
"bas_billnosetting;fw_distribute",
"bas_billnosetting;fw_export",
"bas_billnosetting;fw_forbid",
"bas_billnosetting;fw_import",
"bas_billnosetting;fw_listattach",
"bas_billnosetting;fw_modify",
"bas_billnosetting;fw_new",
"bas_billnosetting;fw_presetfilteredit",
"bas_billnosetting;fw_print",
"bas_billnosetting;fw_settopreset",
"bas_billnosetting;fw_sharelayout",
"bas_billnosetting;fw_submit",
"bas_billnosetting;fw_submitchange",
"bas_billnosetting;fw_tasktmpl",
"bas_billnosetting;fw_unaudit",
"bas_billnosetting;fw_unchange",
"bas_billnosetting;fw_undistribute",
"bas_billnosetting;fw_unforbid",
"bas_billnosetting;fw_unsubmit",
"bas_billnosetting;fw_view",
"bas_billnosetting;fw_viewrecord",
"bas_bizwarnscheme;fw_audit",
"bas_bizwarnscheme;fw_change",
"bas_bizwarnscheme;fw_delete",
"bas_bizwarnscheme;fw_distribute",
"bas_bizwarnscheme;fw_export",
"bas_bizwarnscheme;fw_forbid",
"bas_bizwarnscheme;fw_import",
"bas_bizwarnscheme;fw_listattach",
"bas_bizwarnscheme;fw_modify",
"bas_bizwarnscheme;fw_new",
"bas_bizwarnscheme;fw_operatelog",
"bas_bizwarnscheme;fw_presetfilteredit",
"bas_bizwarnscheme;fw_print",
"bas_bizwarnscheme;fw_settopreset",
"bas_bizwarnscheme;fw_sharelayout",
"bas_bizwarnscheme;fw_submit",
"bas_bizwarnscheme;fw_submitchange",
"bas_bizwarnscheme;fw_tasktmpl",
"bas_bizwarnscheme;fw_unaudit",
"bas_bizwarnscheme;fw_unchange",
"bas_bizwarnscheme;fw_undistribute",
"bas_bizwarnscheme;fw_unforbid",
"bas_bizwarnscheme;fw_unsubmit",
"bas_bizwarnscheme;fw_view",
"bas_bizwarnscheme;fw_viewrecord",
"bas_datadictionary;fw_new",
"bas_deliver;fw_view",
"bas_msglog;fw_export",
"bas_msglog;fw_print",
"bas_msglog;fw_view",
"bas_priceadjust;fw_audit",
"bas_priceadjust;fw_cancel",
"bas_priceadjust;fw_change",
"bas_priceadjust;fw_close",
"bas_priceadjust;fw_delete",
"bas_priceadjust;fw_export",
"bas_priceadjust;fw_import",
"bas_priceadjust;fw_listattach",
"bas_priceadjust;fw_modify",
"bas_priceadjust;fw_new",
"bas_priceadjust;fw_operatelog",
"bas_priceadjust;fw_presetfilteredit",
"bas_priceadjust;fw_print",
"bas_priceadjust;fw_reserveinventory",
"bas_priceadjust;fw_sharelayout",
"bas_priceadjust;fw_submit",
"bas_priceadjust;fw_submitchange",
"bas_priceadjust;fw_syncchange",
"bas_priceadjust;fw_tasktmpl",
"bas_priceadjust;fw_unaudit",
"bas_priceadjust;fw_uncancel",
"bas_priceadjust;fw_unchange",
"bas_priceadjust;fw_unclose",
"bas_priceadjust;fw_unsubmit",
"bas_priceadjust;fw_view",
"bas_priceadjust;fw_viewrecord",
"bas_printpaper;fw_audit",
"bas_printpaper;fw_change",
"bas_printpaper;fw_delete",
"bas_printpaper;fw_distribute",
"bas_printpaper;fw_export",
"bas_printpaper;fw_forbid",
"bas_printpaper;fw_import",
"bas_printpaper;fw_listattach",
"bas_printpaper;fw_modify",
"bas_printpaper;fw_new",
"bas_printpaper;fw_operatelog",
"bas_printpaper;fw_presetfilteredit",
"bas_printpaper;fw_print",
"bas_printpaper;fw_settopreset",
"bas_printpaper;fw_sharelayout",
"bas_printpaper;fw_submit",
"bas_printpaper;fw_submitchange",
"bas_printpaper;fw_tasktmpl",
"bas_printpaper;fw_unaudit",
"bas_printpaper;fw_unchange",
"bas_printpaper;fw_undistribute",
"bas_printpaper;fw_unforbid",
"bas_printpaper;fw_unsubmit",
"bas_printpaper;fw_view",
"bas_printpaper;fw_viewrecord",
"bas_relatedinfo;fw_audit",
"bas_relatedinfo;fw_change",
"bas_relatedinfo;fw_delete",
"bas_relatedinfo;fw_distribute",
"bas_relatedinfo;fw_export",
"bas_relatedinfo;fw_forbid",
"bas_relatedinfo;fw_import",
"bas_relatedinfo;fw_listattach",
"bas_relatedinfo;fw_modify",
"bas_relatedinfo;fw_new",
"bas_relatedinfo;fw_operatelog",
"bas_relatedinfo;fw_presetfilteredit",
"bas_relatedinfo;fw_print",
"bas_relatedinfo;fw_settopreset",
"bas_relatedinfo;fw_sharelayout",
"bas_relatedinfo;fw_submit",
"bas_relatedinfo;fw_submitchange",
"bas_relatedinfo;fw_tasktmpl",
"bas_relatedinfo;fw_unaudit",
"bas_relatedinfo;fw_unchange",
"bas_relatedinfo;fw_undistribute",
"bas_relatedinfo;fw_unforbid",
"bas_relatedinfo;fw_unsubmit",
"bas_relatedinfo;fw_view",
"bas_relatedinfo;fw_viewrecord",
"bas_store;fw_view",
"bas_store;fw_viewrecord",
"bas_storesysparam;fw_modify",
"bas_storesysparam;fw_new",
"bas_storesysparam;fw_view",
"bas_task;fw_view",
"bas_tasklog;fw_export",
"bas_tasklog;fw_print",
"bas_tasklog;fw_view",
"bd_billnodedefine;fw_view",
"bd_billtype;fw_view",
"bd_enumdata;fw_audit",
"bd_enumdata;fw_change",
"bd_enumdata;fw_delete",
"bd_enumdata;fw_distribute",
"bd_enumdata;fw_export",
"bd_enumdata;fw_forbid",
"bd_enumdata;fw_import",
"bd_enumdata;fw_listattach",
"bd_enumdata;fw_modify",
"bd_enumdata;fw_new",
"bd_enumdata;fw_operatelog",
"bd_enumdata;fw_presetfilteredit",
"bd_enumdata;fw_print",
"bd_enumdata;fw_settopreset",
"bd_enumdata;fw_sharelayout",
"bd_enumdata;fw_submit",
"bd_enumdata;fw_submitchange",
"bd_enumdata;fw_tasktmpl",
"bd_enumdata;fw_unaudit",
"bd_enumdata;fw_unchange",
"bd_enumdata;fw_undistribute",
"bd_enumdata;fw_unforbid",
"bd_enumdata;fw_unsubmit",
"bd_enumdata;fw_view",
"bd_enumdata;fw_viewrecord",
"bd_enumeditor;fw_delete",
"bd_enumeditor;fw_new",
"bd_recordlist;fw_view",
"bpm_businessapproval;fw_audit",
"bpm_businessapproval;fw_change",
"bpm_businessapproval;fw_delete",
"bpm_businessapproval;fw_distribute",
"bpm_businessapproval;fw_export",
"bpm_businessapproval;fw_forbid",
"bpm_businessapproval;fw_import",
"bpm_businessapproval;fw_listattach",
"bpm_businessapproval;fw_modify",
"bpm_businessapproval;fw_new",
"bpm_businessapproval;fw_operatelog",
"bpm_businessapproval;fw_presetfilteredit",
"bpm_businessapproval;fw_print",
"bpm_businessapproval;fw_settopreset",
"bpm_businessapproval;fw_sharelayout",
"bpm_businessapproval;fw_submit",
"bpm_businessapproval;fw_submitchange",
"bpm_businessapproval;fw_tasktmpl",
"bpm_businessapproval;fw_unaudit",
"bpm_businessapproval;fw_unchange",
"bpm_businessapproval;fw_undistribute",
"bpm_businessapproval;fw_unforbid",
"bpm_businessapproval;fw_unsubmit",
"bpm_businessapproval;fw_view",
"bpm_businessapproval;fw_viewrecord",
"bpm_flowinstance;fw_audit",
"bpm_flowinstance;fw_cancel",
"bpm_flowinstance;fw_change",
"bpm_flowinstance;fw_close",
"bpm_flowinstance;fw_delete",
"bpm_flowinstance;fw_export",
"bpm_flowinstance;fw_import",
"bpm_flowinstance;fw_listattach",
"bpm_flowinstance;fw_modify",
"bpm_flowinstance;fw_new",
"bpm_flowinstance;fw_operatelog",
"bpm_flowinstance;fw_presetfilteredit",
"bpm_flowinstance;fw_print",
"bpm_flowinstance;fw_reserveinventory",
"bpm_flowinstance;fw_sharelayout",
"bpm_flowinstance;fw_submit",
"bpm_flowinstance;fw_submitchange",
"bpm_flowinstance;fw_syncchange",
"bpm_flowinstance;fw_tasktmpl",
"bpm_flowinstance;fw_unaudit",
"bpm_flowinstance;fw_uncancel",
"bpm_flowinstance;fw_unchange",
"bpm_flowinstance;fw_unclose",
"bpm_flowinstance;fw_unsubmit",
"bpm_flowinstance;fw_view",
"bpm_flowinstance;fw_viewrecord",
"bpm_flowmsg;fw_view",
"coo_incomedisburse;batchmodify_prem",
"coo_incomedisburse;confirm_prem",
"coo_incomedisburse;confirmsynergy_prem",
"coo_incomedisburse;deletesynergy_prem",
"coo_incomedisburse;fw_audit",
"coo_incomedisburse;fw_cancel",
"coo_incomedisburse;fw_change",
"coo_incomedisburse;fw_close",
"coo_incomedisburse;fw_delete",
"coo_incomedisburse;fw_edit",
"coo_incomedisburse;fw_export",
"coo_incomedisburse;fw_import",
"coo_incomedisburse;fw_listattach",
"coo_incomedisburse;fw_modify",
"coo_incomedisburse;fw_new",
"coo_incomedisburse;fw_operatelog",
"coo_incomedisburse;fw_presetfilteredit",
"coo_incomedisburse;fw_print",
"coo_incomedisburse;fw_reserveinventory",
"coo_incomedisburse;fw_sharelayout",
"coo_incomedisburse;fw_submit",
"coo_incomedisburse;fw_submitchange",
"coo_incomedisburse;fw_syncchange",
"coo_incomedisburse;fw_tasktmpl",
"coo_incomedisburse;fw_unaudit",
"coo_incomedisburse;fw_uncancel",
"coo_incomedisburse;fw_unchange",
"coo_incomedisburse;fw_unclose",
"coo_incomedisburse;fw_unsubmit",
"coo_incomedisburse;fw_view",
"coo_incomedisburse;fw_viewrecord",
"coo_incomedisburse;invalid_prem",
"coo_incomedisburse;invalidsynergy_prem",
"coo_incomedisburse;savesynergy_prem",
"coo_incomedisburse;unconfirm_prem",
"coo_incomedisburse;unverific_prem",
"coo_incomedisburse;verific_prem",
"coo_incomedisburse;ydj_purchaseorder_saveaudit",
"coo_incomedisburse;ydj_purchaseorder_savesubmit",
"coo_incomedisburserptsal;fw_export",
"coo_incomedisburserptsal;fw_presetfilteredit",
"coo_incomedisburserptsal;fw_print",
"coo_incomedisburserptsal;fw_view",
"dashboard_mytask;mobile_view",
"dashboard_mytask;fw_view",
"im_notice;fw_audit",
"im_notice;fw_cancel",
"im_notice;fw_change",
"im_notice;fw_close",
"im_notice;fw_delete",
"im_notice;fw_export",
"im_notice;fw_import",
"im_notice;fw_listattach",
"im_notice;fw_modify",
"im_notice;fw_new",
"im_notice;fw_operatelog",
"im_notice;fw_presetfilteredit",
"im_notice;fw_print",
"im_notice;fw_publish",
"im_notice;fw_reserveinventory",
"im_notice;fw_sharelayout",
"im_notice;fw_submit",
"im_notice;fw_submitchange",
"im_notice;fw_syncchange",
"im_notice;fw_tasktmpl",
"im_notice;fw_unaudit",
"im_notice;fw_uncancel",
"im_notice;fw_unchange",
"im_notice;fw_unclose",
"im_notice;fw_unsubmit",
"im_notice;fw_view",
"im_notice;fw_viewrecord",
"mp_assignright;fw_allotrole",
"mp_assignright;fw_new",
"pur_systemparam;fw_modify",
"pur_systemparam;fw_new",
"pur_systemparam;fw_view",
"rpt_billlock;fw_export",
"rpt_billlock;fw_presetfilteredit",
"rpt_billlock;fw_print",
"rpt_billlock;fw_unlock",
"rpt_billlock;fw_view",
"rpt_orderbalance;fw_export",
"rpt_orderbalance;fw_presetfilteredit",
"rpt_orderbalance;fw_print",
"rpt_orderbalance;fw_view",
"rpt_orderdetail;fw_export",
"rpt_orderdetail;fw_presetfilteredit",
"rpt_orderdetail;fw_print",
"rpt_orderdetail;fw_view",
"rpt_ordertrackingform;fw_export",
"rpt_ordertrackingform;fw_presetfilteredit",
"rpt_ordertrackingform;fw_print",
"rpt_ordertrackingform;fw_view",
"rpt_profitanalysis;fw_export",
"rpt_profitanalysis;fw_presetfilteredit",
"rpt_profitanalysis;fw_print",
"rpt_profitanalysis;fw_view",
"rpt_purchaseanalysis;fw_export",
"rpt_purchaseanalysis;fw_presetfilteredit",
"rpt_purchaseanalysis;fw_print",
"rpt_purchaseanalysis;fw_view",
"rpt_purchasedetail;fw_export",
"rpt_purchasedetail;fw_presetfilteredit",
"rpt_purchasedetail;fw_print",
"rpt_purchasedetail;fw_view",
"rpt_purchaseexecute;fw_export",
"rpt_purchaseexecute;fw_presetfilteredit",
"rpt_purchaseexecute;fw_print",
"rpt_purchaseexecute;fw_view",
"rpt_purchaseplan;fw_export",
"rpt_purchaseplan;fw_presetfilteredit",
"rpt_purchaseplan;fw_print",
"rpt_purchaseplan;fw_view",
"rpt_purchaseratesanalysis;fw_export",
"rpt_purchaseratesanalysis;fw_presetfilteredit",
"rpt_purchaseratesanalysis;fw_print",
"rpt_purchaseratesanalysis;fw_view",
"rpt_salarycommission;fw_export",
"rpt_salarycommission;fw_presetfilteredit",
"rpt_salarycommission;fw_print",
"rpt_salarycommission;fw_view",
"rpt_salesrpt;fw_export",
"rpt_salesrpt;fw_presetfilteredit",
"rpt_salesrpt;fw_print",
"rpt_salesrpt;fw_view",
"rpt_stockdetail;fw_export",
"rpt_stockdetail;fw_presetfilteredit",
"rpt_stockdetail;fw_print",
"rpt_stockdetail;fw_view",
"rpt_stocksummary;fw_export",
"rpt_stocksummary;fw_presetfilteredit",
"rpt_stocksummary;fw_print",
"rpt_stocksummary;fw_view",
"rpt_stocksynthesize;fw_export",
"rpt_stocksynthesize;fw_presetfilteredit",
"rpt_stocksynthesize;fw_print",
"rpt_stocksynthesize;fw_view",
"rpt_storesalesconversionrate;fw_export",
"rpt_storesalesconversionrate;fw_presetfilteredit",
"rpt_storesalesconversionrate;fw_print",
"rpt_storesalesconversionrate;fw_view",
"sal_dealeraccount;fw_export",
"sal_dealeraccount;fw_presetfilteredit",
"sal_dealeraccount;fw_print",
"sal_dealeraccount;fw_view",
"sal_dealerdetail;fw_view",
"sal_dealersetup;fw_modify",
"sal_dealersetup;fw_new",
"sal_dealersetup;fw_view",
"sec_assignright;fw_allotrole",
"sec_assignright;fw_new",
"sec_role;fw_audit",
"sec_role;fw_change",
"sec_role;fw_delete",
"sec_role;fw_distribute",
"sec_role;fw_export",
"sec_role;fw_forbid",
"sec_role;fw_import",
"sec_role;fw_listattach",
"sec_role;fw_modify",
"sec_role;fw_new",
"sec_role;fw_operatelog",
"sec_role;fw_presetfilteredit",
"sec_role;fw_print",
"sec_role;fw_settopreset",
"sec_role;fw_sharelayout",
"sec_role;fw_submit",
"sec_role;fw_submitchange",
"sec_role;fw_tasktmpl",
"sec_role;fw_unaudit",
"sec_role;fw_unchange",
"sec_role;fw_undistribute",
"sec_role;fw_unforbid",
"sec_role;fw_unsubmit",
"sec_role;fw_view",
"sec_role;fw_viewrecord",
"sec_user;fw_audit",
"sec_user;fw_change",
"sec_user;fw_delete",
"sec_user;fw_distribute",
"sec_user;fw_export",
"sec_user;fw_forbid",
"sec_user;fw_import",
"sec_user;fw_listattach",
"sec_user;fw_modify",
"sec_user;fw_new",
"sec_user;fw_operatelog",
"sec_user;fw_presetfilteredit",
"sec_user;fw_print",
"sec_user;fw_resetpwd",
"sec_user;fw_setloginway",
"sec_user;fw_settopreset",
"sec_user;fw_sharelayout",
"sec_user;fw_submit",
"sec_user;fw_submitchange",
"sec_user;fw_tasktmpl",
"sec_user;fw_unaudit",
"sec_user;fw_unbindqywx",
"sec_user;fw_unchange",
"sec_user;fw_undistribute",
"sec_user;fw_unforbid",
"sec_user;fw_unlock",
"sec_user;fw_unsubmit",
"sec_user;fw_view",
"sec_user;fw_viewrecord",
"sec_user;sec_user_changephone",
"sec_user_pwdpolicy;fw_modify",
"sec_user_pwdpolicy;fw_new",
"sec_user_pwdpolicy;fw_view",
"sel_category;fw_view",
"sel_constraint;fw_view",
"sel_prop;fw_view",
"sel_propvalue;fw_new",
"sel_propvalue;fw_view",
"sel_range;fw_view",
"sel_suite;fw_view",
"sel_type;fw_view",
"si_bizobjectfieldmap;fw_view",
"si_datasyncparam;fw_view",
"ste_afterfeedback;fw_view",
"ste_channel;addduty",
"ste_channel;changeduty",
"ste_channel;deleteduty",
"ste_channel;followerrecord",
"ste_channel;fw_audit",
"ste_channel;fw_change",
"ste_channel;fw_delete",
"ste_channel;fw_distribute",
"ste_channel;fw_export",
"ste_channel;fw_forbid",
"ste_channel;fw_import",
"ste_channel;fw_listattach",
"ste_channel;fw_modify",
"ste_channel;fw_new",
"ste_channel;fw_operatelog",
"ste_channel;fw_presetfilteredit",
"ste_channel;fw_print",
"ste_channel;fw_settopreset",
"ste_channel;fw_sharelayout",
"ste_channel;fw_submit",
"ste_channel;fw_submitchange",
"ste_channel;fw_tasktmpl",
"ste_channel;fw_unaudit",
"ste_channel;fw_unchange",
"ste_channel;fw_undistribute",
"ste_channel;fw_unforbid",
"ste_channel;fw_unsubmit",
"ste_channel;fw_view",
"ste_channel;fw_viewrecord",
"ste_goal;fw_audit",
"ste_goal;fw_cancel",
"ste_goal;fw_change",
"ste_goal;fw_close",
"ste_goal;fw_delete",
"ste_goal;fw_export",
"ste_goal;fw_import",
"ste_goal;fw_listattach",
"ste_goal;fw_modify",
"ste_goal;fw_new",
"ste_goal;fw_operatelog",
"ste_goal;fw_presetfilteredit",
"ste_goal;fw_print",
"ste_goal;fw_reserveinventory",
"ste_goal;fw_sharelayout",
"ste_goal;fw_submit",
"ste_goal;fw_submitchange",
"ste_goal;fw_syncchange",
"ste_goal;fw_tasktmpl",
"ste_goal;fw_unaudit",
"ste_goal;fw_uncancel",
"ste_goal;fw_unchange",
"ste_goal;fw_unclose",
"ste_goal;fw_unsubmit",
"ste_goal;fw_view",
"ste_goal;fw_viewrecord",
"stk_initstockbill;fw_view",
"stk_invcloseaccount;fw_new",
"stk_invcompleteinit;fw_new",
"stk_inventorybase;fw_audit",
"stk_inventorybase;fw_change",
"stk_inventorybase;fw_delete",
"stk_inventorybase;fw_distribute",
"stk_inventorybase;fw_export",
"stk_inventorybase;fw_forbid",
"stk_inventorybase;fw_import",
"stk_inventorybase;fw_listattach",
"stk_inventorybase;fw_modify",
"stk_inventorybase;fw_new",
"stk_inventorybase;fw_operatelog",
"stk_inventorybase;fw_presetfilteredit",
"stk_inventorybase;fw_print",
"stk_inventorybase;fw_settopreset",
"stk_inventorybase;fw_sharelayout",
"stk_inventorybase;fw_submit",
"stk_inventorybase;fw_submitchange",
"stk_inventorybase;fw_tasktmpl",
"stk_inventorybase;fw_unaudit",
"stk_inventorybase;fw_unchange",
"stk_inventorybase;fw_undistribute",
"stk_inventorybase;fw_unforbid",
"stk_inventorybase;fw_unsubmit",
"stk_inventorybase;fw_view",
"stk_inventorybase;fw_viewrecord",
"stk_inventorylist;fw_export",
"stk_inventorylist;fw_view",
"stk_inventorytransfer;fw_audit",
"stk_inventorytransfer;fw_cancel",
"stk_inventorytransfer;fw_change",
"stk_inventorytransfer;fw_close",
"stk_inventorytransfer;fw_delete",
"stk_inventorytransfer;fw_export",
"stk_inventorytransfer;fw_import",
"stk_inventorytransfer;fw_listattach",
"stk_inventorytransfer;fw_modify",
"stk_inventorytransfer;fw_new",
"stk_inventorytransfer;fw_operatelog",
"stk_inventorytransfer;fw_presetfilteredit",
"stk_inventorytransfer;fw_print",
"stk_inventorytransfer;fw_queryinventory",
"stk_inventorytransfer;fw_reserveinventory",
"stk_inventorytransfer;fw_sharelayout",
"stk_inventorytransfer;fw_submit",
"stk_inventorytransfer;fw_submitchange",
"stk_inventorytransfer;fw_syncchange",
"stk_inventorytransfer;fw_tasktmpl",
"stk_inventorytransfer;fw_unaudit",
"stk_inventorytransfer;fw_uncancel",
"stk_inventorytransfer;fw_unchange",
"stk_inventorytransfer;fw_unclose",
"stk_inventorytransfer;fw_unsubmit",
"stk_inventorytransfer;fw_view",
"stk_inventorytransfer;fw_viewrecord",
"stk_inventoryverify;fw_audit",
"stk_inventoryverify;fw_cancel",
"stk_inventoryverify;fw_change",
"stk_inventoryverify;fw_close",
"stk_inventoryverify;fw_delete",
"stk_inventoryverify;fw_export",
"stk_inventoryverify;fw_import",
"stk_inventoryverify;fw_listattach",
"stk_inventoryverify;fw_modify",
"stk_inventoryverify;fw_new",
"stk_inventoryverify;fw_operatelog",
"stk_inventoryverify;fw_presetfilteredit",
"stk_inventoryverify;fw_print",
"stk_inventoryverify;fw_queryinventory",
"stk_inventoryverify;fw_reserveinventory",
"stk_inventoryverify;fw_sharelayout",
"stk_inventoryverify;fw_submit",
"stk_inventoryverify;fw_submitchange",
"stk_inventoryverify;fw_syncchange",
"stk_inventoryverify;fw_tasktmpl",
"stk_inventoryverify;fw_unaudit",
"stk_inventoryverify;fw_uncancel",
"stk_inventoryverify;fw_unchange",
"stk_inventoryverify;fw_unclose",
"stk_inventoryverify;fw_unsubmit",
"stk_inventoryverify;fw_view",
"stk_inventoryverify;fw_viewrecord",
"stk_otherstockin;fw_audit",
"stk_otherstockin;fw_cancel",
"stk_otherstockin;fw_change",
"stk_otherstockin;fw_close",
"stk_otherstockin;fw_delete",
"stk_otherstockin;fw_export",
"stk_otherstockin;fw_import",
"stk_otherstockin;fw_listattach",
"stk_otherstockin;fw_modify",
"stk_otherstockin;fw_new",
"stk_otherstockin;fw_operatelog",
"stk_otherstockin;fw_presetfilteredit",
"stk_otherstockin;fw_print",
"stk_otherstockin;fw_queryinventory",
"stk_otherstockin;fw_reserveinventory",
"stk_otherstockin;fw_sharelayout",
"stk_otherstockin;fw_submit",
"stk_otherstockin;fw_submitchange",
"stk_otherstockin;fw_syncchange",
"stk_otherstockin;fw_tasktmpl",
"stk_otherstockin;fw_unaudit",
"stk_otherstockin;fw_uncancel",
"stk_otherstockin;fw_unchange",
"stk_otherstockin;fw_unclose",
"stk_otherstockin;fw_unsubmit",
"stk_otherstockin;fw_view",
"stk_otherstockin;fw_viewrecord",
"stk_otherstockout;fw_audit",
"stk_otherstockout;fw_cancel",
"stk_otherstockout;fw_change",
"stk_otherstockout;fw_close",
"stk_otherstockout;fw_delete",
"stk_otherstockout;fw_export",
"stk_otherstockout;fw_import",
"stk_otherstockout;fw_listattach",
"stk_otherstockout;fw_modify",
"stk_otherstockout;fw_new",
"stk_otherstockout;fw_operatelog",
"stk_otherstockout;fw_presetfilteredit",
"stk_otherstockout;fw_print",
"stk_otherstockout;fw_queryinventory",
"stk_otherstockout;fw_reserveinventory",
"stk_otherstockout;fw_sharelayout",
"stk_otherstockout;fw_submit",
"stk_otherstockout;fw_submitchange",
"stk_otherstockout;fw_syncchange",
"stk_otherstockout;fw_tasktmpl",
"stk_otherstockout;fw_unaudit",
"stk_otherstockout;fw_uncancel",
"stk_otherstockout;fw_unchange",
"stk_otherstockout;fw_unclose",
"stk_otherstockout;fw_unsubmit",
"stk_otherstockout;fw_view",
"stk_otherstockout;fw_viewrecord",
"stk_postockin;fw_audit",
"stk_postockin;fw_cancel",
"stk_postockin;fw_change",
"stk_postockin;fw_close",
"stk_postockin;fw_delete",
"stk_postockin;fw_export",
"stk_postockin;fw_import",
"stk_postockin;fw_listattach",
"stk_postockin;fw_modify",
"stk_postockin;fw_new",
"stk_postockin;fw_operatelog",
"stk_postockin;fw_presetfilteredit",
"stk_postockin;fw_print",
"stk_postockin;fw_queryinventory",
"stk_postockin;fw_reserveinventory",
"stk_postockin;fw_sharelayout",
"stk_postockin;fw_submit",
"stk_postockin;fw_submitchange",
"stk_postockin;fw_syncchange",
"stk_postockin;fw_tasktmpl",
"stk_postockin;fw_unaudit",
"stk_postockin;fw_uncancel",
"stk_postockin;fw_unchange",
"stk_postockin;fw_unclose",
"stk_postockin;fw_unsubmit",
"stk_postockin;fw_view",
"stk_postockin;fw_viewrecord",
"stk_postockreturn;fw_audit",
"stk_postockreturn;fw_cancel",
"stk_postockreturn;fw_change",
"stk_postockreturn;fw_close",
"stk_postockreturn;fw_delete",
"stk_postockreturn;fw_export",
"stk_postockreturn;fw_import",
"stk_postockreturn;fw_listattach",
"stk_postockreturn;fw_modify",
"stk_postockreturn;fw_new",
"stk_postockreturn;fw_operatelog",
"stk_postockreturn;fw_presetfilteredit",
"stk_postockreturn;fw_print",
"stk_postockreturn;fw_queryinventory",
"stk_postockreturn;fw_reserveinventory",
"stk_postockreturn;fw_sharelayout",
"stk_postockreturn;fw_submit",
"stk_postockreturn;fw_submitchange",
"stk_postockreturn;fw_syncchange",
"stk_postockreturn;fw_tasktmpl",
"stk_postockreturn;fw_unaudit",
"stk_postockreturn;fw_uncancel",
"stk_postockreturn;fw_unchange",
"stk_postockreturn;fw_unclose",
"stk_postockreturn;fw_unsubmit",
"stk_postockreturn;fw_view",
"stk_postockreturn;fw_viewrecord",
"stk_reservebill;fw_audit",
"stk_reservebill;fw_cancel",
"stk_reservebill;fw_change",
"stk_reservebill;fw_close",
"stk_reservebill;fw_delete",
"stk_reservebill;fw_export",
"stk_reservebill;fw_import",
"stk_reservebill;fw_listattach",
"stk_reservebill;fw_modify",
"stk_reservebill;fw_new",
"stk_reservebill;fw_operatelog",
"stk_reservebill;fw_presetfilteredit",
"stk_reservebill;fw_print",
"stk_reservebill;fw_reserveinventory",
"stk_reservebill;fw_sharelayout",
"stk_reservebill;fw_submit",
"stk_reservebill;fw_submitchange",
"stk_reservebill;fw_syncchange",
"stk_reservebill;fw_tasktmpl",
"stk_reservebill;fw_unaudit",
"stk_reservebill;fw_uncancel",
"stk_reservebill;fw_unchange",
"stk_reservebill;fw_unclose",
"stk_reservebill;fw_unsubmit",
"stk_reservebill;fw_view",
"stk_reservebill;fw_viewrecord",
"stk_reservebill;ydj_manualrelease",
"stk_reservebill;ydj_releaseexpirer",
"stk_reservebill;fw_reserveborrow",
"stk_sostockout;fw_audit",
"stk_sostockout;fw_cancel",
"stk_sostockout;fw_change",
"stk_sostockout;fw_close",
"stk_sostockout;fw_delete",
"stk_sostockout;fw_export",
"stk_sostockout;fw_import",
"stk_sostockout;fw_listattach",
"stk_sostockout;fw_modify",
"stk_sostockout;fw_new",
"stk_sostockout;fw_operatelog",
"stk_sostockout;fw_presetfilteredit",
"stk_sostockout;fw_print",
"stk_sostockout;fw_pushservice",
"stk_sostockout;fw_queryinventory",
"stk_sostockout;fw_reserveinventory",
"stk_sostockout;fw_sharelayout",
"stk_sostockout;fw_submit",
"stk_sostockout;fw_submitchange",
"stk_sostockout;fw_syncchange",
"stk_sostockout;fw_tasktmpl",
"stk_sostockout;fw_unaudit",
"stk_sostockout;fw_uncancel",
"stk_sostockout;fw_unchange",
"stk_sostockout;fw_unclose",
"stk_sostockout;fw_unsubmit",
"stk_sostockout;fw_view",
"stk_sostockout;fw_viewrecord",
"stk_sostockreturn;fw_audit",
"stk_sostockreturn;fw_cancel",
"stk_sostockreturn;fw_change",
"stk_sostockreturn;fw_close",
"stk_sostockreturn;fw_delete",
"stk_sostockreturn;fw_export",
"stk_sostockreturn;fw_import",
"stk_sostockreturn;fw_listattach",
"stk_sostockreturn;fw_modify",
"stk_sostockreturn;fw_new",
"stk_sostockreturn;fw_operatelog",
"stk_sostockreturn;fw_presetfilteredit",
"stk_sostockreturn;fw_print",
"stk_sostockreturn;fw_queryinventory",
"stk_sostockreturn;fw_reserveinventory",
"stk_sostockreturn;fw_sharelayout",
"stk_sostockreturn;fw_submit",
"stk_sostockreturn;fw_submitchange",
"stk_sostockreturn;fw_syncchange",
"stk_sostockreturn;fw_tasktmpl",
"stk_sostockreturn;fw_unaudit",
"stk_sostockreturn;fw_uncancel",
"stk_sostockreturn;fw_unchange",
"stk_sostockreturn;fw_unclose",
"stk_sostockreturn;fw_unsubmit",
"stk_sostockreturn;fw_view",
"stk_sostockreturn;fw_viewrecord",
"stk_stockparam;fw_modify",
"stk_stockparam;fw_new",
"stk_stockparam;fw_view",
"sys_cfunction;fw_view",
"sys_company;fw_view",
"sys_datawidgetlist;fw_audit",
"sys_datawidgetlist;fw_change",
"sys_datawidgetlist;fw_delete",
"sys_datawidgetlist;fw_distribute",
"sys_datawidgetlist;fw_export",
"sys_datawidgetlist;fw_forbid",
"sys_datawidgetlist;fw_import",
"sys_datawidgetlist;fw_listattach",
"sys_datawidgetlist;fw_modify",
"sys_datawidgetlist;fw_new",
"sys_datawidgetlist;fw_operatelog",
"sys_datawidgetlist;fw_presetfilteredit",
"sys_datawidgetlist;fw_print",
"sys_datawidgetlist;fw_settopreset",
"sys_datawidgetlist;fw_sharelayout",
"sys_datawidgetlist;fw_submit",
"sys_datawidgetlist;fw_submitchange",
"sys_datawidgetlist;fw_tasktmpl",
"sys_datawidgetlist;fw_unaudit",
"sys_datawidgetlist;fw_unchange",
"sys_datawidgetlist;fw_undistribute",
"sys_datawidgetlist;fw_unforbid",
"sys_datawidgetlist;fw_unsubmit",
"sys_datawidgetlist;fw_view",
"sys_datawidgetlist;fw_viewrecord",
"sys_datawidgettype;fw_audit",
"sys_datawidgettype;fw_change",
"sys_datawidgettype;fw_delete",
"sys_datawidgettype;fw_distribute",
"sys_datawidgettype;fw_export",
"sys_datawidgettype;fw_forbid",
"sys_datawidgettype;fw_import",
"sys_datawidgettype;fw_listattach",
"sys_datawidgettype;fw_modify",
"sys_datawidgettype;fw_new",
"sys_datawidgettype;fw_operatelog",
"sys_datawidgettype;fw_presetfilteredit",
"sys_datawidgettype;fw_print",
"sys_datawidgettype;fw_settopreset",
"sys_datawidgettype;fw_sharelayout",
"sys_datawidgettype;fw_submit",
"sys_datawidgettype;fw_submitchange",
"sys_datawidgettype;fw_tasktmpl",
"sys_datawidgettype;fw_unaudit",
"sys_datawidgettype;fw_unchange",
"sys_datawidgettype;fw_undistribute",
"sys_datawidgettype;fw_unforbid",
"sys_datawidgettype;fw_unsubmit",
"sys_datawidgettype;fw_view",
"sys_datawidgettype;fw_viewrecord",
"sys_opserconfig;fw_view",
"sys_reportshell;fw_new",
"sys_systemparam;fw_modify",
"sys_systemparam;fw_new",
"sys_systemparam;fw_view",
"ydj_achievement;fw_view",
"ydj_achievement;mobile_view",
"ydj_achievement;mycompany",
"ydj_achievement;myself",
"ydj_achievement;mysubordinates",
"ydj_activity;fw_audit",
"ydj_activity;fw_change",
"ydj_activity;fw_delete",
"ydj_activity;fw_distribute",
"ydj_activity;fw_export",
"ydj_activity;fw_forbid",
"ydj_activity;fw_import",
"ydj_activity;fw_listattach",
"ydj_activity;fw_modify",
"ydj_activity;fw_new",
"ydj_activity;fw_operatelog",
"ydj_activity;fw_presetfilteredit",
"ydj_activity;fw_print",
"ydj_activity;fw_settopreset",
"ydj_activity;fw_sharelayout",
"ydj_activity;fw_submit",
"ydj_activity;fw_submitchange",
"ydj_activity;fw_tasktmpl",
"ydj_activity;fw_unaudit",
"ydj_activity;fw_unchange",
"ydj_activity;fw_undistribute",
"ydj_activity;fw_unforbid",
"ydj_activity;fw_unsubmit",
"ydj_activity;fw_view",
"ydj_activity;fw_viewrecord",
"ydj_bank;fw_audit",
"ydj_bank;fw_change",
"ydj_bank;fw_delete",
"ydj_bank;fw_distribute",
"ydj_bank;fw_export",
"ydj_bank;fw_forbid",
"ydj_bank;fw_import",
"ydj_bank;fw_listattach",
"ydj_bank;fw_modify",
"ydj_bank;fw_new",
"ydj_bank;fw_operatelog",
"ydj_bank;fw_presetfilteredit",
"ydj_bank;fw_print",
"ydj_bank;fw_settopreset",
"ydj_bank;fw_sharelayout",
"ydj_bank;fw_submit",
"ydj_bank;fw_submitchange",
"ydj_bank;fw_tasktmpl",
"ydj_bank;fw_unaudit",
"ydj_bank;fw_unchange",
"ydj_bank;fw_undistribute",
"ydj_bank;fw_unforbid",
"ydj_bank;fw_unsubmit",
"ydj_bank;fw_view",
"ydj_bank;fw_viewrecord",
"ydj_banknum;fw_audit",
"ydj_banknum;fw_change",
"ydj_banknum;fw_delete",
"ydj_banknum;fw_distribute",
"ydj_banknum;fw_export",
"ydj_banknum;fw_forbid",
"ydj_banknum;fw_import",
"ydj_banknum;fw_listattach",
"ydj_banknum;fw_modify",
"ydj_banknum;fw_new",
"ydj_banknum;fw_operatelog",
"ydj_banknum;fw_presetfilteredit",
"ydj_banknum;fw_print",
"ydj_banknum;fw_settopreset",
"ydj_banknum;fw_sharelayout",
"ydj_banknum;fw_submit",
"ydj_banknum;fw_submitchange",
"ydj_banknum;fw_tasktmpl",
"ydj_banknum;fw_unaudit",
"ydj_banknum;fw_unchange",
"ydj_banknum;fw_undistribute",
"ydj_banknum;fw_unforbid",
"ydj_banknum;fw_unsubmit",
"ydj_banknum;fw_view",
"ydj_banknum;fw_viewrecord",
"ydj_brand;fw_audit",
"ydj_brand;fw_change",
"ydj_brand;fw_delete",
"ydj_brand;fw_distribute",
"ydj_brand;fw_export",
"ydj_brand;fw_forbid",
"ydj_brand;fw_import",
"ydj_brand;fw_listattach",
"ydj_brand;fw_modify",
"ydj_brand;fw_new",
"ydj_brand;fw_operatelog",
"ydj_brand;fw_presetfilteredit",
"ydj_brand;fw_print",
"ydj_brand;fw_settopreset",
"ydj_brand;fw_sharelayout",
"ydj_brand;fw_submit",
"ydj_brand;fw_submitchange",
"ydj_brand;fw_tasktmpl",
"ydj_brand;fw_unaudit",
"ydj_brand;fw_unchange",
"ydj_brand;fw_undistribute",
"ydj_brand;fw_unforbid",
"ydj_brand;fw_unsubmit",
"ydj_brand;fw_view",
"ydj_brand;fw_viewrecord",
"ydj_building;fw_audit",
"ydj_building;fw_change",
"ydj_building;fw_delete",
"ydj_building;fw_distribute",
"ydj_building;fw_export",
"ydj_building;fw_forbid",
"ydj_building;fw_import",
"ydj_building;fw_listattach",
"ydj_building;fw_modify",
"ydj_building;fw_new",
"ydj_building;fw_operatelog",
"ydj_building;fw_presetfilteredit",
"ydj_building;fw_print",
"ydj_building;fw_settopreset",
"ydj_building;fw_sharelayout",
"ydj_building;fw_submit",
"ydj_building;fw_submitchange",
"ydj_building;fw_tasktmpl",
"ydj_building;fw_unaudit",
"ydj_building;fw_unchange",
"ydj_building;fw_undistribute",
"ydj_building;fw_unforbid",
"ydj_building;fw_unsubmit",
"ydj_building;fw_view",
"ydj_building;fw_viewrecord",
"ydj_category;fw_audit",
"ydj_category;fw_change",
"ydj_category;fw_delete",
"ydj_category;fw_distribute",
"ydj_category;fw_export",
"ydj_category;fw_forbid",
"ydj_category;fw_import",
"ydj_category;fw_listattach",
"ydj_category;fw_modify",
"ydj_category;fw_new",
"ydj_category;fw_operatelog",
"ydj_category;fw_presetfilteredit",
"ydj_category;fw_print",
"ydj_category;fw_settopreset",
"ydj_category;fw_sharelayout",
"ydj_category;fw_submit",
"ydj_category;fw_submitchange",
"ydj_category;fw_tasktmpl",
"ydj_category;fw_unaudit",
"ydj_category;fw_unchange",
"ydj_category;fw_undistribute",
"ydj_category;fw_unforbid",
"ydj_category;fw_unsubmit",
"ydj_category;fw_view",
"ydj_category;fw_viewrecord",
"ydj_combopromotion;fw_audit",
"ydj_combopromotion;fw_delete",
"ydj_combopromotion;fw_export",
"ydj_combopromotion;fw_forbid",
"ydj_combopromotion;fw_import",
"ydj_combopromotion;fw_listattach",
"ydj_combopromotion;fw_modify",
"ydj_combopromotion;fw_new",
"ydj_combopromotion;fw_operatelog",
"ydj_combopromotion;fw_presetfilteredit",
"ydj_combopromotion;fw_print",
"ydj_combopromotion;fw_settopreset",
"ydj_combopromotion;fw_sharelayout",
"ydj_combopromotion;fw_submit",
"ydj_combopromotion;fw_unaudit",
"ydj_combopromotion;fw_unforbid",
"ydj_combopromotion;fw_unsubmit",
"ydj_combopromotion;fw_view",
"ydj_combopromotion;fw_viewrecord",
"ydj_combopromotion;fw_queryinventory",
"ydj_combopromotion;publish",
"ydj_contactunit;fw_export",
"ydj_contactunit;fw_print",
"ydj_contactunit;fw_view",
"ydj_costaccounting;fw_audit",
"ydj_costaccounting;fw_cancel",
"ydj_costaccounting;fw_change",
"ydj_costaccounting;fw_close",
"ydj_costaccounting;fw_delete",
"ydj_costaccounting;fw_export",
"ydj_costaccounting;fw_import",
"ydj_costaccounting;fw_listattach",
"ydj_costaccounting;fw_modify",
"ydj_costaccounting;fw_new",
"ydj_costaccounting;fw_operatelog",
"ydj_costaccounting;fw_presetfilteredit",
"ydj_costaccounting;fw_print",
"ydj_costaccounting;fw_reserveinventory",
"ydj_costaccounting;fw_sharelayout",
"ydj_costaccounting;fw_submit",
"ydj_costaccounting;fw_submitchange",
"ydj_costaccounting;fw_syncchange",
"ydj_costaccounting;fw_tasktmpl",
"ydj_costaccounting;fw_unaudit",
"ydj_costaccounting;fw_uncancel",
"ydj_costaccounting;fw_unchange",
"ydj_costaccounting;fw_unclose",
"ydj_costaccounting;fw_unsubmit",
"ydj_costaccounting;fw_view",
"ydj_costaccounting;fw_viewrecord",
"ydj_costaccounting;ydj_costaccounting_refreshentities",
"ydj_customer;followerrecord",
"ydj_customer;fw_audit",
"ydj_customer;fw_change",
"ydj_customer;fw_changedutybydept",
"ydj_customer;fw_delete",
"ydj_customer;fw_distribute",
"ydj_customer;fw_export",
"ydj_customer;fw_forbid",
"ydj_customer;fw_import",
"ydj_customer;fw_listattach",
"ydj_customer;fw_modify",
"ydj_customer;fw_new",
"ydj_customer;fw_opencompany",
"ydj_customer;fw_operatelog",
"ydj_customer;fw_presetfilteredit",
"ydj_customer;fw_print",
"ydj_customer;fw_pushservice",
"ydj_customer;fw_settopreset",
"ydj_customer;fw_sharelayout",
"ydj_customer;fw_submit",
"ydj_customer;fw_submitchange",
"ydj_customer;fw_tasktmpl",
"ydj_customer;fw_unaudit",
"ydj_customer;fw_unchange",
"ydj_customer;fw_undistribute",
"ydj_customer;fw_unforbid",
"ydj_customer;fw_unsubmit",
"ydj_customer;fw_view",
"ydj_customer;fw_viewrecord",
"ydj_customer;fw_ydj_customer_addduty",
"ydj_customer;fw_ydj_customer_removeduty",
"ydj_customer;fw_ydj_customer_replaceduty",
"ydj_customer;ydj_customerrecord_recycle",
"ydj_customerrecord;followerrecord",
"ydj_customerrecord;fw_audit",
"ydj_customerrecord;fw_cancel",
"ydj_customerrecord;fw_change",
"ydj_customerrecord;fw_close",
"ydj_customerrecord;fw_delete",
"ydj_customerrecord;fw_export",
"ydj_customerrecord;fw_import",
"ydj_customerrecord;fw_listattach",
"ydj_customerrecord;fw_modify",
"ydj_customerrecord;fw_new",
"ydj_customerrecord;fw_operatelog",
"ydj_customerrecord;fw_presetfilteredit",
"ydj_customerrecord;fw_print",
"ydj_customerrecord;fw_reserveinventory",
"ydj_customerrecord;fw_sharelayout",
"ydj_customerrecord;fw_submit",
"ydj_customerrecord;fw_submitchange",
"ydj_customerrecord;fw_syncchange",
"ydj_customerrecord;fw_tasktmpl",
"ydj_customerrecord;fw_unaudit",
"ydj_customerrecord;fw_uncancel",
"ydj_customerrecord;fw_unchange",
"ydj_customerrecord;fw_unclose",
"ydj_customerrecord;fw_unsubmit",
"ydj_customerrecord;fw_view",
"ydj_customerrecord;fw_viewrecord",
"ydj_customerrecord;ydj_customerrecord_allocate",
"ydj_customerrecord;ydj_customerrecord_close",
"ydj_customerrecord;ydj_customerrecord_createorder",
"ydj_customerrecord;ydj_customerrecord_createquotation",
"ydj_customerrecord;ydj_customerrecord_cusreturn",
"ydj_customerrecord;ydj_customerrecord_finish",
"ydj_customerrecord;ydj_customerrecord_replace",
"ydj_deliveryway;fw_audit",
"ydj_deliveryway;fw_change",
"ydj_deliveryway;fw_delete",
"ydj_deliveryway;fw_distribute",
"ydj_deliveryway;fw_export",
"ydj_deliveryway;fw_forbid",
"ydj_deliveryway;fw_import",
"ydj_deliveryway;fw_listattach",
"ydj_deliveryway;fw_modify",
"ydj_deliveryway;fw_new",
"ydj_deliveryway;fw_operatelog",
"ydj_deliveryway;fw_presetfilteredit",
"ydj_deliveryway;fw_print",
"ydj_deliveryway;fw_settopreset",
"ydj_deliveryway;fw_sharelayout",
"ydj_deliveryway;fw_submit",
"ydj_deliveryway;fw_submitchange",
"ydj_deliveryway;fw_tasktmpl",
"ydj_deliveryway;fw_unaudit",
"ydj_deliveryway;fw_unchange",
"ydj_deliveryway;fw_undistribute",
"ydj_deliveryway;fw_unforbid",
"ydj_deliveryway;fw_unsubmit",
"ydj_deliveryway;fw_view",
"ydj_deliveryway;fw_viewrecord",
"ydj_dept;fw_audit",
"ydj_dept;fw_change",
"ydj_dept;fw_delete",
"ydj_dept;fw_distribute",
"ydj_dept;fw_export",
"ydj_dept;fw_forbid",
"ydj_dept;fw_import",
"ydj_dept;fw_listattach",
"ydj_dept;fw_modify",
"ydj_dept;fw_new",
"ydj_dept;fw_operatelog",
"ydj_dept;fw_presetfilteredit",
"ydj_dept;fw_print",
"ydj_dept;fw_settopreset",
"ydj_dept;fw_sharelayout",
"ydj_dept;fw_submit",
"ydj_dept;fw_submitchange",
"ydj_dept;fw_tasktmpl",
"ydj_dept;fw_unaudit",
"ydj_dept;fw_unchange",
"ydj_dept;fw_undistribute",
"ydj_dept;fw_unforbid",
"ydj_dept;fw_unsubmit",
"ydj_dept;fw_view",
"ydj_dept;fw_viewrecord",
"ydj_designscheme;fw_audit",
"ydj_designscheme;fw_change",
"ydj_designscheme;fw_delete",
"ydj_designscheme;fw_distribute",
"ydj_designscheme;fw_export",
"ydj_designscheme;fw_forbid",
"ydj_designscheme;fw_import",
"ydj_designscheme;fw_listattach",
"ydj_designscheme;fw_modify",
"ydj_designscheme;fw_new",
"ydj_designscheme;fw_operatelog",
"ydj_designscheme;fw_presetfilteredit",
"ydj_designscheme;fw_print",
"ydj_designscheme;fw_settopreset",
"ydj_designscheme;fw_sharelayout",
"ydj_designscheme;fw_submit",
"ydj_designscheme;fw_submitchange",
"ydj_designscheme;fw_tasktmpl",
"ydj_designscheme;fw_unaudit",
"ydj_designscheme;fw_unchange",
"ydj_designscheme;fw_undistribute",
"ydj_designscheme;fw_unforbid",
"ydj_designscheme;fw_unsubmit",
"ydj_designscheme;fw_view",
"ydj_designscheme;fw_viewrecord",
"ydj_employeesalesrevenue;fw_export",
"ydj_employeesalesrevenue;fw_presetfilteredit",
"ydj_employeesalesrevenue;fw_print",
"ydj_employeesalesrevenue;fw_view",
"ydj_expenseitem;fw_view",
"ydj_followerrecord;fw_audit",
"ydj_followerrecord;fw_cancel",
"ydj_followerrecord;fw_change",
"ydj_followerrecord;fw_close",
"ydj_followerrecord;fw_delete",
"ydj_followerrecord;fw_export",
"ydj_followerrecord;fw_import",
"ydj_followerrecord;fw_listattach",
"ydj_followerrecord;fw_modify",
"ydj_followerrecord;fw_new",
"ydj_followerrecord;fw_operatelog",
"ydj_followerrecord;fw_presetfilteredit",
"ydj_followerrecord;fw_print",
"ydj_followerrecord;fw_reserveinventory",
"ydj_followerrecord;fw_sharelayout",
"ydj_followerrecord;fw_submit",
"ydj_followerrecord;fw_submitchange",
"ydj_followerrecord;fw_syncchange",
"ydj_followerrecord;fw_tasktmpl",
"ydj_followerrecord;fw_unaudit",
"ydj_followerrecord;fw_uncancel",
"ydj_followerrecord;fw_unchange",
"ydj_followerrecord;fw_unclose",
"ydj_followerrecord;fw_unsubmit",
"ydj_followerrecord;fw_view",
"ydj_followerrecord;fw_viewrecord",
"ydj_innerpartrank;fw_view",
"ydj_linkprogress;fw_view",
"ydj_marketstatement;fw_audit",
"ydj_marketstatement;fw_cancel",
"ydj_marketstatement;fw_change",
"ydj_marketstatement;fw_close",
"ydj_marketstatement;fw_delete",
"ydj_marketstatement;fw_export",
"ydj_marketstatement;fw_import",
"ydj_marketstatement;fw_listattach",
"ydj_marketstatement;fw_modify",
"ydj_marketstatement;fw_new",
"ydj_marketstatement;fw_operatelog",
"ydj_marketstatement;fw_presetfilteredit",
"ydj_marketstatement;fw_print",
"ydj_marketstatement;fw_reserveinventory",
"ydj_marketstatement;fw_sharelayout",
"ydj_marketstatement;fw_submit",
"ydj_marketstatement;fw_submitchange",
"ydj_marketstatement;fw_syncchange",
"ydj_marketstatement;fw_tasktmpl",
"ydj_marketstatement;fw_unaudit",
"ydj_marketstatement;fw_uncancel",
"ydj_marketstatement;fw_unchange",
"ydj_marketstatement;fw_unclose",
"ydj_marketstatement;fw_unsubmit",
"ydj_marketstatement;fw_view",
"ydj_marketstatement;fw_viewrecord",
"ydj_marketstatement;verifyincomedisburse_p",
"ydj_order;followerrecord",
"ydj_order;fw_audit",
"ydj_order;fw_cancel",
"ydj_order;fw_change",
"ydj_order;fw_close",
"ydj_order;fw_delete",
"ydj_order;fw_export",
"ydj_order;fw_import",
"ydj_order;fw_listattach",
"ydj_order;fw_modify",
"ydj_order;fw_new",
"ydj_order;fw_operatelog",
"ydj_order;fw_presetfilteredit",
"ydj_order;fw_print",
"ydj_order;fw_queryinventory",
"ydj_order;fw_reserveinventory",
"ydj_order;fw_sharelayout",
"ydj_order;fw_submit",
"ydj_order;fw_submitchange",
"ydj_order;fw_syncchange",
"ydj_order;fw_tasktmpl",
"ydj_order;fw_unaudit",
"ydj_order;fw_uncancel",
"ydj_order;fw_unchange",
"ydj_order;fw_unclose",
"ydj_order;fw_unsubmit",
"ydj_order;fw_view",
"ydj_order;fw_viewrecord",
"ydj_order;mergeregistfee",
"ydj_order;ydj_manualrelease",
"ydj_order;fw_dropshipment",
"ydj_order;fw_logisticsprogress",
"ydj_order;ydj_order_addattachment",
"ydj_order;ydj_order_cost",
"ydj_order;ydj_order_deleteattachment",
"ydj_order;ydj_order_receipt",
"ydj_order;ydj_order_refund",
"ydj_order;fw_reserveborrow",
"ydj_order;fw_reservetransferquery",
"ydj_order;fw_renewalreceipt",
"ydj_order_chg;followerrecord",
"ydj_order_chg;fw_audit",
"ydj_order_chg;fw_cancel",
"ydj_order_chg;fw_change",
"ydj_order_chg;fw_close",
"ydj_order_chg;fw_delete",
"ydj_order_chg;fw_export",
"ydj_order_chg;fw_import",
"ydj_order_chg;fw_listattach",
"ydj_order_chg;fw_modify",
"ydj_order_chg;fw_new",
"ydj_order_chg;fw_operatelog",
"ydj_order_chg;fw_presetfilteredit",
"ydj_order_chg;fw_print",
"ydj_order_chg;fw_queryinventory",
"ydj_order_chg;fw_reserveinventory",
"ydj_order_chg;fw_sharelayout",
"ydj_order_chg;fw_submit",
"ydj_order_chg;fw_submitchange",
"ydj_order_chg;fw_syncchange",
"ydj_order_chg;fw_tasktmpl",
"ydj_order_chg;fw_unaudit",
"ydj_order_chg;fw_uncancel",
"ydj_order_chg;fw_unchange",
"ydj_order_chg;fw_unclose",
"ydj_order_chg;fw_unsubmit",
"ydj_order_chg;fw_view",
"ydj_order_chg;fw_viewrecord",
"ydj_order_chg;mergeregistfee",
"ydj_order_chg;ydj_manualrelease",
"ydj_order_chg;ydj_order_addattachment",
"ydj_order_chg;ydj_order_cost",
"ydj_order_chg;ydj_order_deleteattachment",
"ydj_order_chg;ydj_order_receipt",
"ydj_order_chg;ydj_order_refund",
"ms_crmdistributor;fw_view",
"ms_markingassistant;fw_view",
"ydj_position;fw_audit",
"ydj_position;fw_change",
"ydj_position;fw_delete",
"ydj_position;fw_distribute",
"ydj_position;fw_export",
"ydj_position;fw_forbid",
"ydj_position;fw_import",
"ydj_position;fw_listattach",
"ydj_position;fw_modify",
"ydj_position;fw_new",
"ydj_position;fw_operatelog",
"ydj_position;fw_presetfilteredit",
"ydj_position;fw_print",
"ydj_position;fw_settopreset",
"ydj_position;fw_sharelayout",
"ydj_position;fw_submit",
"ydj_position;fw_submitchange",
"ydj_position;fw_tasktmpl",
"ydj_position;fw_unaudit",
"ydj_position;fw_unchange",
"ydj_position;fw_undistribute",
"ydj_position;fw_unforbid",
"ydj_position;fw_unsubmit",
"ydj_position;fw_view",
"ydj_position;fw_viewrecord",
"ydj_price;fw_audit",
"ydj_price;fw_change",
"ydj_price;fw_delete",
"ydj_price;fw_distribute",
"ydj_price;fw_export",
"ydj_price;fw_forbid",
"ydj_price;fw_import",
"ydj_price;fw_listattach",
"ydj_price;fw_modify",
"ydj_price;fw_new",
"ydj_price;fw_operatelog",
"ydj_price;fw_presetfilteredit",
"ydj_price;fw_print",
"ydj_price;fw_settopreset",
"ydj_price;fw_sharelayout",
"ydj_price;fw_submit",
"ydj_price;fw_submitchange",
"ydj_price;fw_tasktmpl",
"ydj_price;fw_unaudit",
"ydj_price;fw_unchange",
"ydj_price;fw_undistribute",
"ydj_price;fw_unforbid",
"ydj_price;fw_unsubmit",
"ydj_price;fw_view",
"ydj_price;fw_viewrecord",
"ydj_product;fw_audit",
"ydj_product;fw_change",
"ydj_product;fw_delete",
"ydj_product;fw_distribute",
"ydj_product;fw_export",
"ydj_product;fw_forbid",
"ydj_product;fw_import",
"ydj_product;fw_listattach",
"ydj_product;fw_modify",
"ydj_product;fw_new",
"ydj_product;fw_operatelog",
"ydj_product;fw_presetfilteredit",
"ydj_product;fw_print",
"ydj_product;fw_settopreset",
"ydj_product;fw_sharelayout",
"ydj_product;fw_submit",
"ydj_product;fw_submitchange",
"ydj_product;fw_tasktmpl",
"ydj_product;fw_unaudit",
"ydj_product;fw_unchange",
"ydj_product;fw_undistribute",
"ydj_product;fw_unforbid",
"ydj_product;fw_unsubmit",
"ydj_product;fw_view",
"ydj_product;fw_viewrecord",
"ydj_product;ydj_product_updateprice",
"ydj_productcategory;fw_view",
"ydj_productpromotion;fw_audit",
"ydj_productpromotion;fw_delete",
"ydj_productpromotion;fw_export",
"ydj_productpromotion;fw_forbid",
"ydj_productpromotion;fw_import",
"ydj_productpromotion;fw_listattach",
"ydj_productpromotion;fw_modify",
"ydj_productpromotion;fw_new",
"ydj_productpromotion;fw_operatelog",
"ydj_productpromotion;fw_presetfilteredit",
"ydj_productpromotion;fw_print",
"ydj_productpromotion;fw_settopreset",
"ydj_productpromotion;fw_sharelayout",
"ydj_productpromotion;fw_submit",
"ydj_productpromotion;fw_unaudit",
"ydj_productpromotion;fw_unforbid",
"ydj_productpromotion;fw_unsubmit",
"ydj_productpromotion;fw_view",
"ydj_productpromotion;fw_viewrecord",
"ydj_productpromotion;fw_queryinventory",
"ydj_productpromotion;publish",
"ydj_purchaseorder;fw_audit",
"ydj_purchaseorder;fw_cancel",
"ydj_purchaseorder;fw_change",
"ydj_purchaseorder;fw_close",
"ydj_purchaseorder;fw_delete",
"ydj_purchaseorder;fw_export",
"ydj_purchaseorder;fw_import",
"ydj_purchaseorder;fw_listattach",
"ydj_purchaseorder;fw_modify",
"ydj_purchaseorder;fw_new",
"ydj_purchaseorder;fw_operatelog",
"ydj_purchaseorder;fw_presetfilteredit",
"ydj_purchaseorder;fw_print",
"ydj_purchaseorder;fw_reserveinventory",
"ydj_purchaseorder;fw_sharelayout",
"ydj_purchaseorder;fw_submit",
"ydj_purchaseorder;fw_submitchange",
"ydj_purchaseorder;fw_syncchange",
"ydj_purchaseorder;fw_tasktmpl",
"ydj_purchaseorder;fw_unaudit",
"ydj_purchaseorder;fw_uncancel",
"ydj_purchaseorder;fw_unchange",
"ydj_purchaseorder;fw_unclose",
"ydj_purchaseorder;fw_unsubmit",
"ydj_purchaseorder;fw_view",
"ydj_purchaseorder;fw_viewrecord",
"ydj_purchaseorder;pur_cancelconfirm",
"ydj_purchaseorder;pur_cancelreceiv",
"ydj_purchaseorder;pur_confirmreceiv",
"ydj_purchaseorder;pur_payment",
"ydj_purchaseorder;pur_pricefirm",
"ydj_purchaseorder;pur_repealsynergy",
"ydj_purchaseorder;pur_submitsynergy",
"ydj_purchaseorder;pur_unbindcontract",
"ydj_purchaseorder;ydj_purchaseorder_refund",
"ydj_purchaseorder;ydj_purchaseorder_saveaudit",
"ydj_purchaseorder;ydj_purchaseorder_savesubmit",
"ydj_purchaseorder;ydj_purchaseorder_submithq",
"ydj_purchaseorder;ydj_purchaseorder_renewsubmithq",
"ydj_purchaseorder_chg;fw_audit",
"ydj_purchaseorder_chg;fw_cancel",
"ydj_purchaseorder_chg;fw_change",
"ydj_purchaseorder_chg;fw_close",
"ydj_purchaseorder_chg;fw_delete",
"ydj_purchaseorder_chg;fw_export",
"ydj_purchaseorder_chg;fw_import",
"ydj_purchaseorder_chg;fw_listattach",
"ydj_purchaseorder_chg;fw_modify",
"ydj_purchaseorder_chg;fw_new",
"ydj_purchaseorder_chg;fw_operatelog",
"ydj_purchaseorder_chg;fw_presetfilteredit",
"ydj_purchaseorder_chg;fw_print",
"ydj_purchaseorder_chg;fw_reserveinventory",
"ydj_purchaseorder_chg;fw_sharelayout",
"ydj_purchaseorder_chg;fw_submit",
"ydj_purchaseorder_chg;fw_submitchange",
"ydj_purchaseorder_chg;fw_syncchange",
"ydj_purchaseorder_chg;fw_tasktmpl",
"ydj_purchaseorder_chg;fw_unaudit",
"ydj_purchaseorder_chg;fw_uncancel",
"ydj_purchaseorder_chg;fw_unchange",
"ydj_purchaseorder_chg;fw_unclose",
"ydj_purchaseorder_chg;fw_unsubmit",
"ydj_purchaseorder_chg;fw_view",
"ydj_purchaseorder_chg;fw_viewrecord",
"ydj_purchaseorder_chg;pur_cancelconfirm",
"ydj_purchaseorder_chg;pur_cancelreceiv",
"ydj_purchaseorder_chg;pur_confirmreceiv",
"ydj_purchaseorder_chg;pur_payment",
"ydj_purchaseorder_chg;pur_pricefirm",
"ydj_purchaseorder_chg;pur_repealsynergy",
"ydj_purchaseorder_chg;pur_submitsynergy",
"ydj_purchaseorder_chg;pur_unbindcontract",
"ydj_purchaseorder_chg;ydj_purchaseorder_refund",
"ydj_purchaseorder_chg;ydj_purchaseorder_saveaudit",
"ydj_purchaseorder_chg;ydj_purchaseorder_savesubmit",
"ydj_purchaseorder_chg;ydj_purchaseorder_submithq",
"ydj_selfpurchaseprice;fw_audit",
"ydj_selfpurchaseprice;fw_change",
"ydj_selfpurchaseprice;fw_delete",
"ydj_selfpurchaseprice;fw_distribute",
"ydj_selfpurchaseprice;fw_export",
"ydj_selfpurchaseprice;fw_forbid",
"ydj_selfpurchaseprice;fw_import",
"ydj_selfpurchaseprice;fw_listattach",
"ydj_selfpurchaseprice;fw_modify",
"ydj_selfpurchaseprice;fw_new",
"ydj_selfpurchaseprice;fw_operatelog",
"ydj_selfpurchaseprice;fw_presetfilteredit",
"ydj_selfpurchaseprice;fw_print",
"ydj_selfpurchaseprice;fw_settopreset",
"ydj_selfpurchaseprice;fw_sharelayout",
"ydj_selfpurchaseprice;fw_submit",
"ydj_selfpurchaseprice;fw_submitchange",
"ydj_selfpurchaseprice;fw_tasktmpl",
"ydj_selfpurchaseprice;fw_unaudit",
"ydj_selfpurchaseprice;fw_unchange",
"ydj_selfpurchaseprice;fw_undistribute",
"ydj_selfpurchaseprice;fw_unforbid",
"ydj_selfpurchaseprice;fw_unsubmit",
"ydj_selfpurchaseprice;fw_view",
"ydj_selfpurchaseprice;fw_viewrecord",
"ydj_purpriceadjust;fw_audit",
"ydj_purpriceadjust;fw_cancel",
"ydj_purpriceadjust;fw_change",
"ydj_purpriceadjust;fw_close",
"ydj_purpriceadjust;fw_delete",
"ydj_purpriceadjust;fw_export",
"ydj_purpriceadjust;fw_import",
"ydj_purpriceadjust;fw_listattach",
"ydj_purpriceadjust;fw_modify",
"ydj_purpriceadjust;fw_new",
"ydj_purpriceadjust;fw_operatelog",
"ydj_purpriceadjust;fw_presetfilteredit",
"ydj_purpriceadjust;fw_print",
"ydj_purpriceadjust;fw_reserveinventory",
"ydj_purpriceadjust;fw_sharelayout",
"ydj_purpriceadjust;fw_submit",
"ydj_purpriceadjust;fw_submitchange",
"ydj_purpriceadjust;fw_syncchange",
"ydj_purpriceadjust;fw_tasktmpl",
"ydj_purpriceadjust;fw_unaudit",
"ydj_purpriceadjust;fw_uncancel",
"ydj_purpriceadjust;fw_unchange",
"ydj_purpriceadjust;fw_unclose",
"ydj_purpriceadjust;fw_unsubmit",
"ydj_purpriceadjust;fw_view",
"ydj_purpriceadjust;fw_viewrecord",
"ydj_rank;fw_view",
"ydj_salescontrol;fw_audit",
"ydj_salescontrol;fw_change",
"ydj_salescontrol;fw_delete",
"ydj_salescontrol;fw_distribute",
"ydj_salescontrol;fw_export",
"ydj_salescontrol;fw_forbid",
"ydj_salescontrol;fw_import",
"ydj_salescontrol;fw_listattach",
"ydj_salescontrol;fw_modify",
"ydj_salescontrol;fw_new",
"ydj_salescontrol;fw_operatelog",
"ydj_salescontrol;fw_presetfilteredit",
"ydj_salescontrol;fw_print",
"ydj_salescontrol;fw_settopreset",
"ydj_salescontrol;fw_sharelayout",
"ydj_salescontrol;fw_submit",
"ydj_salescontrol;fw_submitchange",
"ydj_salescontrol;fw_tasktmpl",
"ydj_salescontrol;fw_unaudit",
"ydj_salescontrol;fw_unchange",
"ydj_salescontrol;fw_undistribute",
"ydj_salescontrol;fw_unforbid",
"ydj_salescontrol;fw_unsubmit",
"ydj_salescontrol;fw_view",
"ydj_salescontrol;fw_viewrecord",
"ydj_series;fw_audit",
"ydj_series;fw_change",
"ydj_series;fw_delete",
"ydj_series;fw_distribute",
"ydj_series;fw_export",
"ydj_series;fw_forbid",
"ydj_series;fw_import",
"ydj_series;fw_listattach",
"ydj_series;fw_modify",
"ydj_series;fw_new",
"ydj_series;fw_operatelog",
"ydj_series;fw_presetfilteredit",
"ydj_series;fw_print",
"ydj_series;fw_settopreset",
"ydj_series;fw_sharelayout",
"ydj_series;fw_submit",
"ydj_series;fw_submitchange",
"ydj_series;fw_tasktmpl",
"ydj_series;fw_unaudit",
"ydj_series;fw_unchange",
"ydj_series;fw_undistribute",
"ydj_series;fw_unforbid",
"ydj_series;fw_unsubmit",
"ydj_series;fw_view",
"ydj_series;fw_viewrecord",
"ydj_service;fw_audit",
"ydj_service;fw_cancel",
"ydj_service;fw_change",
"ydj_service;fw_close",
"ydj_service;fw_delete",
"ydj_service;fw_export",
"ydj_service;fw_import",
"ydj_service;fw_listattach",
"ydj_service;fw_modify",
"ydj_service;fw_new",
"ydj_service;fw_operatelog",
"ydj_service;fw_presetfilteredit",
"ydj_service;fw_print",
"ydj_service;fw_reserveinventory",
"ydj_service;fw_sharelayout",
"ydj_service;fw_submit",
"ydj_service;fw_submitchange",
"ydj_service;fw_syncchange",
"ydj_service;fw_tasktmpl",
"ydj_service;fw_unaudit",
"ydj_service;fw_uncancel",
"ydj_service;fw_unchange",
"ydj_service;fw_unclose",
"ydj_service;fw_unsubmit",
"ydj_service;fw_view",
"ydj_service;fw_viewrecord",
"ydj_service;ydj_cancelservice",
"ydj_service;ydj_push",
"ydj_service;ydj_servicevist",
"ydj_service;ydj_setstatus01",
"ydj_service;ydj_setstatus02",
"ydj_service;ydj_setstatus03",
"ydj_service;ydj_setstatus04",
"ydj_serviceitem;fw_view",
"ydj_staff;fw_audit",
"ydj_staff;fw_change",
"ydj_staff;fw_delete",
"ydj_staff;fw_distribute",
"ydj_staff;fw_export",
"ydj_staff;fw_forbid",
"ydj_staff;fw_import",
"ydj_staff;fw_listattach",
"ydj_staff;fw_modify",
"ydj_staff;fw_new",
"ydj_staff;fw_operatelog",
"ydj_staff;fw_presetfilteredit",
"ydj_staff;fw_print",
"ydj_staff;fw_settopreset",
"ydj_staff;fw_sharelayout",
"ydj_staff;fw_submit",
"ydj_staff;fw_submitchange",
"ydj_staff;fw_tasktmpl",
"ydj_staff;fw_unaudit",
"ydj_staff;fw_unchange",
"ydj_staff;fw_undistribute",
"ydj_staff;fw_unforbid",
"ydj_staff;fw_unsubmit",
"ydj_staff;fw_view",
"ydj_staff;fw_viewrecord",
"ydj_staff;ydj_createuserlink",
"ydj_stockoutreport;fw_export",
"ydj_stockoutreport;fw_presetfilteredit",
"ydj_stockoutreport;fw_print",
"ydj_stockoutreport;fw_view",
"ydj_stockstatus;fw_view",
"ydj_storehouse;fw_audit",
"ydj_storehouse;fw_change",
"ydj_storehouse;fw_delete",
"ydj_storehouse;fw_distribute",
"ydj_storehouse;fw_export",
"ydj_storehouse;fw_forbid",
"ydj_storehouse;fw_import",
"ydj_storehouse;fw_listattach",
"ydj_storehouse;fw_modify",
"ydj_storehouse;fw_new",
"ydj_storehouse;fw_operatelog",
"ydj_storehouse;fw_presetfilteredit",
"ydj_storehouse;fw_print",
"ydj_storehouse;fw_settopreset",
"ydj_storehouse;fw_sharelayout",
"ydj_storehouse;fw_submit",
"ydj_storehouse;fw_submitchange",
"ydj_storehouse;fw_tasktmpl",
"ydj_storehouse;fw_unaudit",
"ydj_storehouse;fw_unchange",
"ydj_storehouse;fw_undistribute",
"ydj_storehouse;fw_unforbid",
"ydj_storehouse;fw_unsubmit",
"ydj_storehouse;fw_view",
"ydj_storehouse;fw_viewrecord",
"ydj_storestatement;fw_audit",
"ydj_storestatement;fw_cancel",
"ydj_storestatement;fw_change",
"ydj_storestatement;fw_close",
"ydj_storestatement;fw_delete",
"ydj_storestatement;fw_export",
"ydj_storestatement;fw_import",
"ydj_storestatement;fw_listattach",
"ydj_storestatement;fw_modify",
"ydj_storestatement;fw_new",
"ydj_storestatement;fw_operatelog",
"ydj_storestatement;fw_presetfilteredit",
"ydj_storestatement;fw_print",
"ydj_storestatement;fw_reserveinventory",
"ydj_storestatement;fw_sharelayout",
"ydj_storestatement;fw_submit",
"ydj_storestatement;fw_submitchange",
"ydj_storestatement;fw_syncchange",
"ydj_storestatement;fw_tasktmpl",
"ydj_storestatement;fw_unaudit",
"ydj_storestatement;fw_uncancel",
"ydj_storestatement;fw_unchange",
"ydj_storestatement;fw_unclose",
"ydj_storestatement;fw_unsubmit",
"ydj_storestatement;fw_view",
"ydj_storestatement;fw_viewrecord",
"ydj_storestatement;verifyincomedisburse_p",
"ydj_supplier;fw_audit",
"ydj_supplier;fw_change",
"ydj_supplier;fw_delete",
"ydj_supplier;fw_distribute",
"ydj_supplier;fw_export",
"ydj_supplier;fw_forbid",
"ydj_supplier;fw_import",
"ydj_supplier;fw_listattach",
"ydj_supplier;fw_modify",
"ydj_supplier;fw_new",
"ydj_supplier;fw_operatelog",
"ydj_supplier;fw_presetfilteredit",
"ydj_supplier;fw_print",
"ydj_supplier;fw_settopreset",
"ydj_supplier;fw_sharelayout",
"ydj_supplier;fw_submit",
"ydj_supplier;fw_submitchange",
"ydj_supplier;fw_tasktmpl",
"ydj_supplier;fw_unaudit",
"ydj_supplier;fw_unchange",
"ydj_supplier;fw_undistribute",
"ydj_supplier;fw_unforbid",
"ydj_supplier;fw_unsubmit",
"ydj_supplier;fw_view",
"ydj_supplier;fw_viewrecord",
"ydj_target;fw_view",
"ydj_target;mobile_view",
"ydj_target;mycompany",
"ydj_target;myself",
"ydj_target;mysubordinates",
"ydj_transferorderapply;fw_audit",
"ydj_transferorderapply;fw_cancel",
"ydj_transferorderapply;fw_change",
"ydj_transferorderapply;fw_close",
"ydj_transferorderapply;fw_delete",
"ydj_transferorderapply;fw_export",
"ydj_transferorderapply;fw_import",
"ydj_transferorderapply;fw_listattach",
"ydj_transferorderapply;fw_modify",
"ydj_transferorderapply;fw_new",
"ydj_transferorderapply;fw_operatelog",
"ydj_transferorderapply;fw_presetfilteredit",
"ydj_transferorderapply;fw_print",
"ydj_transferorderapply;fw_reserveinventory",
"ydj_transferorderapply;fw_sharelayout",
"ydj_transferorderapply;fw_submit",
"ydj_transferorderapply;fw_submitchange",
"ydj_transferorderapply;fw_syncchange",
"ydj_transferorderapply;fw_tasktmpl",
"ydj_transferorderapply;fw_unaudit",
"ydj_transferorderapply;fw_uncancel",
"ydj_transferorderapply;fw_unchange",
"ydj_transferorderapply;fw_unclose",
"ydj_transferorderapply;fw_unsubmit",
"ydj_transferorderapply;fw_view",
"ydj_transferorderapply;fw_viewrecord",
"ydj_trend;fw_view",
"ydj_trend;mycompany",
"ydj_trend;mydepartment",
"ydj_trend;myself",
"ydj_trend;mysubordinates",
"ydj_unit;fw_view",
"ydj_vist;fw_view",
"ms_markingassistant;fw_view",
"ydj_historybill;fw_audit",
"ydj_historybill;fw_change",
"ydj_historybill;fw_delete",
"ydj_historybill;fw_distribute",
"ydj_historybill;fw_export",
"ydj_historybill;fw_forbid",
"ydj_historybill;fw_import",
"ydj_historybill;fw_listattach",
"ydj_historybill;fw_modify",
"ydj_historybill;fw_new",
"ydj_historybill;fw_operatelog",
"ydj_historybill;fw_presetfilteredit",
"ydj_historybill;fw_print",
"ydj_historybill;fw_settopreset",
"ydj_historybill;fw_sharelayout",
"ydj_historybill;fw_submit",
"ydj_historybill;fw_submitchange",
"ydj_historybill;fw_tasktmpl",
"ydj_historybill;fw_unaudit",
"ydj_historybill;fw_unchange",
"ydj_historybill;fw_undistribute",
"ydj_historybill;fw_unforbid",
"ydj_historybill;fw_unsubmit",
"ydj_historybill;fw_view",
"ydj_historybill;fw_viewrecord",
"ydj_renewtype;fw_view",
            };

            var result = GetPermItems(permItems);

            return result;
        }

        public static List<MPTabbarModel> GetAgentRoleMPMenu_BigCustomerAdmin()
        {
            /*
             select '"' + ftabbar + ';' + fgroup + ';' + fmenuid + ';' + fmenuname  +  '",'  
            from t_mp_rolemenu t0  with(nolock) 
            where t0.froleId=@roleId and fisallow=1
             */

            var menuItems = new List<string>()
            {
                "工作台;销售管理;720326618328993798;商品图册",
                "工作台;销售管理;720561097932935181;小区楼盘",
                "工作台;销售管理;720561606232248333;购物车",
                "工作台;销售管理;720565024824889423;我的商机",
                "工作台;销售管理;720565024824889424;我的客户",
                "工作台;订单管理;720565024824889427;合同单",
                "工作台;订单管理;720565024824889428;收款单",
                "工作台;订单管理;720565024824889429;退款单",
                "工作台;企业管理;720565024824889431;审批",
                "工作台;企业管理;720565024824889432;渠道伙伴",
                "首页;快捷入口;720590220789157913;录商机",
                "首页;快捷入口;720590220793352192;录合同",
                "工作台;企业管理;732316900327034886;员工管理",
                "工作台;销售管理;758711323021414406;库存明细查询",
                "工作台;销售管理;801768148641648646;经典案例",
                "工作台;销售管理;807915410342154246;客户图库",
                "工作台;服务管理;807915975830802438;服务单",
                "工作台;服务管理;807915975830802439;售后反馈单",
                "工作台;企业管理;807916152440360972;统计分析",
                "首页;快捷入口;813016334223937542;扫一扫",
                "首页;快捷入口;918587513702187020;录客户",
                "首页;快捷入口;918587603690979334;录服务单",
                "首页;快捷入口;918587639514529798;录售后单",
                "统计;客户;997916152440360100;客户概览",
                "统计;客户;997916152440360101;客户来源占比分析",
                "统计;客户;997916152440360102;部门客户TOP5",
                "统计;客户;997916152440360103;客户年龄占比分析",
                "统计;销售;997916152440360104;销售概览",
                "统计;销售;997916152440360105;销售趋势分析",
                "统计;销售;997916152440360106;员工业绩排名",
                "统计;销售;997916152440360107;系列销售占比",
                "统计;销售;997916152440360108;品牌销售占比",
                "统计;销售;997916152440360109;品类销售金额TOP10",
                "统计;销售;997916152440360110;品类销售数量TOP10",
                "统计;销售;997916152440360111;型号销售TOP10",
                "统计;采购;997916152440360112;采购概览",
                "统计;采购;997916152440360113;采购型号占比",
                "统计;采购;997916152440360114;采购趋势分析",
                "统计;库存;997916152440360115;库存概览",
                "统计;库存;997916152440360116;库存品类数量TOP10",
                "统计;库存;997916152440360117;库存品类金额占比",
                "统计;库存;997916152440360118;库存系列金额占比",
                "工作台;销售管理;994614394863353856;促销活动",
            };


            var mpMenu = GetMPTabbars(menuItems);

            return mpMenu;
        }

    }
}
