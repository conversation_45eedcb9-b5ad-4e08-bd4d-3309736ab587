using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.ChargeDialog
{
    /// <summary>
    /// 收支记录：通过员工获取主岗位对应部门
    /// </summary>
    [InjectService]
    [FormId("coo_chargedialog")]
    [OperationNo("getdeptbyfid")]
    public class GetDeptByStaff : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            //40936 客户充值默认携带业务员，允许业务员修改-后端开发
            base.BeginOperationTransaction(e);

            string fid = this.GetQueryOrSimpleParam("fid", "");
            if (!fid.IsNullOrEmptyOrWhiteSpace())
            {
                var sql = $@" select t_bd_staffentry.fdeptid as fdeptid from T_BD_STAFF inner join t_bd_staffentry on t_bd_staffentry.fid = T_BD_STAFF.fid
                                where T_BD_STAFF.fid = '{fid}' and t_bd_staffentry.fismain = 1 ";
                this.Result.SrvData = this.DBService.ExecuteDynamicObject(this.Context, sql).FirstOrDefault();
                this.Result.IsSuccess = this.DBService.ExecuteDynamicObject(this.Context, sql).Count > 0;
            }
            else
            {
                this.Result.IsSuccess = false;
            }
        }
    }
}
