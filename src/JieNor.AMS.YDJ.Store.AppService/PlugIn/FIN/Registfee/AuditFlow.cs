using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Flow;
using JieNor.Framework.DataTransferObject.BPM;
using JieNor.Framework.MetaCore.BizState;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.Registfee
{
    /// <summary>
    /// 费用应付单：审核流
    /// </summary>
    [InjectService]
    [FormId("ste_registfee")]
    [OperationNo("auditflow")]
    public class AuditFlow : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            var fmainorgids = e.DataEntitys.Select(x => Convert.ToString(x["fmainorgid"])).ToList();
            var agentInfo = GetAgentInfo(this.Context, fmainorgids);

            // 创建流程节点信息服务
            var flowNodeInfoService = new FlowNodeInfoService(this.Context);

            foreach (var dataEntity in e.DataEntitys)
            {
                // 获取当前审批流节点信息
                var flowNodeInfo = flowNodeInfoService.GetCurrentFlowNodeInfo(dataEntity);
                
                // 获取节点位置信息
                var positionInfo = flowNodeInfoService.GetNodePositionInfo(dataEntity);

                //创建单据的经销商的经销类型-直营
                var fmainorgid = Convert.ToString(dataEntity["fmainorgid"]);
                var isfmainorgid = agentInfo.GetValue(fmainorgid);
                
                //完成最后一个业务节点审批,系统需要触发外部接口调用逻辑，传数据包到中台。
                //无论同步成功或失败，都拦截当前审核单据的逻辑
                if (positionInfo.IsSecondToLastNode && isfmainorgid)
                {
                    string syncMessage = "";
                    try
                    {
                        var syncResult = this.Gateway.InvokeBillOperation(this.Context, "ste_registfee", e.DataEntitys, "syncregistfeesubmithq",
                            new Dictionary<string, object>
                            {
                                { "IgnoreCheckPermssion", true }, { "IgnoreValidateDataEntities", true }
                            });

                        if (syncResult != null && syncResult.IsSuccess)
                        {
                            syncMessage = "数据已提交总部";
                        }
                        else
                        {
                            syncMessage = $"数据提交总部失败：{syncResult?.SimpleMessage ?? "未知错误"}";
                        }
                    }
                    catch (Exception ex)
                    {
                        syncMessage = $"数据提交总部异常：{ex.Message}";
                    }

                    // 无论同步成功还是失败，都抛出异常拦截当前审核单据的逻辑
                    throw new BusinessException($"{syncMessage}");
                }
            }
        }

        /// <summary>
        /// 获取当前经销商相关字段信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private Dictionary<string, bool> GetAgentInfo(UserContext userCtx, List<string> fmainorgids)
        {
            var dic = new Dictionary<string, bool>();
            if (fmainorgids.Count == 0) return dic;

            var agentInfo = userCtx.LoadBizBillHeadDataById("bas_agent", fmainorgids, "fid,fmainorgid,fname,fnumber,actualownernumber,fmanagemodel").ToList();

            foreach (var item in agentInfo)
            {
                if (!dic.ContainsKey(Convert.ToString(item["fid"])))
                {
                    var fmanagemodel = Convert.ToString(item["fmanagemodel"]) == "1" ? true : false;
                    dic.Add(Convert.ToString(item["fid"]), fmanagemodel);
                }
            }

            return dic;
        }
    }
}
