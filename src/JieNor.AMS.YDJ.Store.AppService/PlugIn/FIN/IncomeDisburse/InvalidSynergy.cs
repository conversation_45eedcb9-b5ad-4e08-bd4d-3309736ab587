//using System;
//using System.Linq;
//using JieNor.Framework;
//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using JieNor.Framework.SuperOrm.DataEntity;
//using JieNor.Framework.SuperOrm.DataManager;
//using JieNor.Framework.SuperOrm;
//using JieNor.Framework.CustomException;
//using JieNor.Framework.MetaCore.FormMeta;
//using JieNor.Framework.MetaCore.Validator;
//using JieNor.AMS.YDJ.DataTransferObject.Enums;
//using JieNor.AMS.YDJ.Core.Interface;
//using JieNor.Framework.SuperOrm.Metadata.DataEntity;
//using JieNor.AMS.YDJ.Store.AppService.Helper;
//using System.Collections.Generic;

//namespace JieNor.AMS.YDJ.Store.AppService.Plugin.IncomeDisburse
//{
//    /// <summary>
//    /// 收支记录：协同红冲，接收协同方提交过来的红冲请求
//    /// </summary>
//    [InjectService]
//    [FormId("coo_incomedisburse")]
//    [OperationNo("InvalidSynergy")]
//    public class InvalidSynergy : AbstractOperationServicePlugIn
//    {
//        /// <summary>
//        /// 预处理校验规则
//        /// </summary>
//        /// <param name="e"></param>
//        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
//        {
//            base.PrepareValidationRules(e);

//            /*
//                定义表头校验规则
//            */
//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            {
//                return !newData["fsourceformid"].IsNullOrEmptyOrWhiteSpace();
//            }).WithMessage("源单FormId为空，请检查！"));

//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            {
//                if (newData["fcoocompanyid"].IsNullOrEmptyOrWhiteSpace())
//                {
//                    newData["fcoocompanyid"] = Convert.ToString(this.Context?.CallerContext?.Company);
//                }
//                return !newData["fcoocompanyid"].IsNullOrEmptyOrWhiteSpace();
//            }).WithMessage("来源方企业ID为空，请检查！"));

//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            {
//                if (newData["fcooproductid"].IsNullOrEmptyOrWhiteSpace())
//                {
//                    newData["fcooproductid"] = Convert.ToString(this.Context?.CallerContext?.Product);
//                }
//                return !newData["fcooproductid"].IsNullOrEmptyOrWhiteSpace();
//            }).WithMessage("来源方产品ID为空，请检查！"));
//        }

//        /// <summary>
//        /// 调用操作事物前触发的事件
//        /// </summary>
//        /// <param name="e"></param>
//        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
//        {
//            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
//            var dataEntity = e.DataEntitys[0];

//            string sourceFormId = Convert.ToString(e.DataEntitys[0]["fsourceformid"]).Trim().ToLower();

//            //与之关联的源单信息
//            var sourceForm = this.MetaModelService?.LoadFormModel(this.Context, sourceFormId);
//            var numberField = sourceForm.GetNumberField();

//            //原收支记录数据包
//            var oldTranId = dataEntity["foldtranid"] as string;
//            var sourceIncomeDisburse = this.LoadSourceIncomeDisburse(oldTranId);

//            //客户或供应商ID
//            var customerOrSupplierId = "";

//            DynamicObject sourceOrder = null;
//            switch (sourceFormId)
//            {
//                case "ydj_purchaseorder":
//                case "ydj_saleintention":
//                    if (Convert.ToString(dataEntity["foperationmode"])
//                        .EqualsIgnoreCase(((int)Enu_OperateMode.HQDirect).ToString()) == false)
//                    {
//                        //源单交易流水号
//                        string tranId = Convert.ToString(e.DataEntitys[0]["fsourcetranid"]);
//                        if (tranId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException($"源单交易流水号为空，请检查！");
//                        sourceOrder = this.GetSourceOrderByTranId(tranId, sourceForm);
//                    }
//                    break;
//                case "ydj_order":
//                    if (Convert.ToString(dataEntity["foperationmode"])
//                        .EqualsIgnoreCase(((int)Enu_OperateMode.HQDirect).ToString()))
//                    {
//                        //源单交易流水号
//                        string tranId = Convert.ToString(e.DataEntitys[0]["fsourcetranid"]);
//                        if (tranId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException($"源单交易流水号为空，请检查！");
//                        sourceOrder = this.GetSourceOrderByTranId(tranId, sourceForm);
//                    }
//                    break;
//                case "ydj_supplier":
//                case "ydj_customer":
//                    //协同企业ID
//                    var coocompanyId = Convert.ToString(e.DataEntitys[0]["fcoocompanyid"]);
//                    if (coocompanyId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException($"协同企业ID为空，请检查！");
//                    customerOrSupplierId = this.GetCustomerOrSupplierId(coocompanyId);
//                    break;
//                default:
//                    break;
//            }

//            switch (sourceFormId)
//            {
//                case "ydj_purchaseorder":
//                case "ydj_saleintention":
//                    if (sourceOrder == null) throw new BusinessException("找不到源头单据！");

//                    if (Convert.ToString(dataEntity["foperationmode"])
//                        .EqualsIgnoreCase(((int)Enu_OperateMode.HQDirect).ToString()) == false)
//                    {
//                        dataEntity["fsourceid"] = sourceOrder["id"];
//                        dataEntity["fsourcenumber"] = numberField?.DynamicProperty?.GetValue(sourceOrder);
//                        dataEntity["forderno"] = numberField?.DynamicProperty?.GetValue(sourceOrder);
//                        switch (sourceFormId)
//                        {
//                            case "ydj_purchaseorder":
//                                dataEntity["fsupplierid"] = sourceOrder["fsupplierid"];
//                                break;
//                            case "ydj_saleintention":
//                                dataEntity["fcustomerid"] = sourceOrder["fcustomerid"];
//                                break;
//                            default:
//                                break;
//                        }
//                    }
//                    break;
//                case "ydj_order":
//                    if (sourceOrder == null) throw new BusinessException("找不到源头单据！");

//                    if (Convert.ToString(dataEntity["foperationmode"])
//                        .EqualsIgnoreCase(((int)Enu_OperateMode.HQDirect).ToString()))
//                    {
//                        dataEntity["fsourceid"] = sourceOrder["id"];
//                        dataEntity["fsourcenumber"] = numberField?.DynamicProperty?.GetValue(sourceOrder);
//                        dataEntity["forderno"] = numberField?.DynamicProperty?.GetValue(sourceOrder);
//                        dataEntity["fcustomerid"] = sourceOrder["fcustomerid"];

//                        //部门等于取原收支记录的部门
//                        if (sourceIncomeDisburse != null)
//                        {
//                            dataEntity["fdeptid"] = sourceIncomeDisburse["fdeptid"];
//                        }
//                    }
//                    break;
//                case "ydj_supplier":
//                    dataEntity["fsupplierid"] = customerOrSupplierId;
//                    break;
//                case "ydj_customer":
//                    dataEntity["fcustomerid"] = customerOrSupplierId;
//                    break;
//                default:
//                    break;
//            }

//            var dm = this.Container.GetService<IDataManager>();
//            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));

//            //保存前预处理
//            var prepareService = this.Container.GetService<IPrepareSaveDataService>();
//            prepareService?.PrepareDataEntity(this.Context, this.HtmlForm, e.DataEntitys, OperateOption.Create());

//            //反写本地源收支记录数据
//            this.UpdateSourceIncomeDisburse(dataEntity);

//            var incomeDisburseService = this.Container.GetService<IIncomeDisburseService>();
//            //初始化收支记录
//            incomeDisburseService.InitIncomeDisburse(this.Context, e.DataEntitys);

//            //保存
//            dataEntity["fissyn"] = true;
//            dm.Save(e.DataEntitys);

//            //switch (sourceFormId)
//            //{
//            //    case "ydj_order":
//            //        RewriteOrder(sourceOrder, dataEntity);
//            //        break;
//            //}

//            //将调用传递过来的参数原路返回
//            this.Result.SimpleData.Merge(this.SimpleData);

//            this.Result.SimpleMessage = "红冲成功，已生成对应的红冲记录！";
//            this.Result.IsSuccess = true;
//        }

//        /// <summary>
//        /// 设置部门字段值
//        /// </summary>
//        /// <param name="dataEntity"></param>
//        private void SetDeptFieldValue(DynamicObject dataEntity)
//        {
//            //当前实体数据包中是否存在 freceiveorgid 属性，该收款组织ID是从博领K3系统协同过来的
//            var receiveOrgIdProperty = dataEntity
//                ?.DynamicObjectType?.Properties?.OfType<DynamicProperty>()
//                ?.FirstOrDefault(p => p.Name.EqualsIgnoreCase("freceiveorgid"));

//            var receiveOrgId = receiveOrgIdProperty?.GetValue<string>(dataEntity)?.Trim() ?? "";
//            if (receiveOrgId.IsNullOrEmptyOrWhiteSpace()) return;

//            //根据K3的收款组织ID匹配麦浩的部门
//            var orgIds = new List<string> { receiveOrgId };

//            //加载部门对应的K3组织ID
//            var deptOrgMapKv = DirectSynergyHelper.GetDeptIdsByK3OrgIds(this.Context, orgIds);

//            var orgData = new Dictionary<string, string>();
//            deptOrgMapKv.TryGetValue(receiveOrgId, out orgData);
//            var deptId = "";
//            orgData?.TryGetValue("deptId", out deptId);

//            if (deptId.IsNullOrEmptyOrWhiteSpace())
//            {
//                throw new BusinessException($"收款组织【{receiveOrgId}】未匹配到部门，请检查易到家【资料映射值】中是否有维护部门与组织的对应关系。");
//            }

//            dataEntity["fdeptid"] = deptId;
//        }

//        /// <summary>
//        /// 反写本地源收支记录数据
//        /// </summary>
//        /// <param name="sourceIncomeDisburse"></param>
//        private void UpdateSourceIncomeDisburse(DynamicObject sourceIncomeDisburse)
//        {
//            if (sourceIncomeDisburse != null)
//            {
//                //更新源收支记录状态为“已红冲”
//                sourceIncomeDisburse["fbizstatus"] = "bizstatus_03";

//                var dm = this.Container.GetService<IDataManager>();
//                dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
//                dm.Save(sourceIncomeDisburse);
//            }
//        }

//        /// <summary>
//        /// 加载原收支记录数据包
//        /// </summary>
//        /// <param name="oldTranId"></param>
//        /// <returns></returns>
//        private DynamicObject LoadSourceIncomeDisburse(string oldTranId)
//        {
//            if (oldTranId.IsNullOrEmptyOrWhiteSpace()) return null;

//            var dm = this.Container.GetService<IDataManager>();
//            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
//            string where = $"fmainorgid=@fmainorgid and ftranid=@ftranid";
//            var sqlParam = new SqlParam[]
//            {
//                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
//                new SqlParam("ftranid", System.Data.DbType.String, oldTranId)
//            };
//            var dataReader = this.Context.GetPkIdDataReader(this.HtmlForm, where, sqlParam);
//            var dataEntity = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();

//            return dataEntity;
//        }

//        /// <summary>
//        /// 根据交易流水号获取源单信息
//        /// </summary>
//        /// <param name="tranId"></param>
//        /// <param name="sourceForm"></param>
//        /// <returns></returns>
//        private DynamicObject GetSourceOrderByTranId(string tranId, HtmlForm sourceForm)
//        {
//            var dm = this.Container.GetService<IDataManager>();
//            dm.InitDbContext(this.Context, sourceForm.GetDynamicObjectType(this.Context));
//            string where = $"fmainorgid=@fmainorgid and ftranid=@ftranid";
//            var sqlParam = new SqlParam[]
//            {
//                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
//                new SqlParam("ftranid", System.Data.DbType.String, tranId)
//            };
//            var dataReader = this.Context.GetPkIdDataReader(sourceForm, where, sqlParam);
//            var dataEntity = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
//            return dataEntity;
//        }

//        /// <summary>
//        /// 获取客户或供应商ID
//        /// </summary>
//        /// <param name="companyId"></param>
//        /// <returns></returns>
//        private string GetCustomerOrSupplierId(string companyId)
//        {
//            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "coo_company");
//            var dm = this.Container.GetService<IDataManager>();
//            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
//            string where = $"fmainorgid=@fmainorgid and fmycompanyId=@fmycompanyId and fcompanyid=@fcompanyid and fcoostatus='已协同'";
//            var sqlParam = new SqlParam[]
//            {
//                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
//                new SqlParam("fmycompanyId", System.Data.DbType.String, this.Context.Company),
//                new SqlParam("fcompanyid", System.Data.DbType.String, companyId)
//            };
//            var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
//            var dataEntity = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
//            if (dataEntity != null)
//            {
//                return dataEntity["fcustomerorsupplierId"] as string;
//            }
//            return "";
//        }

//        ///// <summary>
//        ///// 反写销售合同
//        ///// </summary>
//        ///// <param name="sourceOrder">源单</param>
//        ///// <param name="incomeDisburse">收支记录</param>
//        //private void RewriteOrder(DynamicObject sourceOrder, DynamicObject incomeDisburse)
//        //{
//        //    if (sourceOrder == null || incomeDisburse == null) return;

//        //    var freducedbrokerage = Convert.ToDecimal(incomeDisburse["freducedbrokerage"]);
//        //    if (freducedbrokerage == 0) return;

//        //    // 红冲【账户方向】=减 且【业务状态】=已确认的收支记录后，《收支记录》的【已扣佣金】立即反写（累减）《销售合同.财务信息》的【已扣拥金】；
//        //    sourceOrder["freducedbrokerage"] = Convert.ToDecimal(sourceOrder["freducedbrokerage"]) - Convert.ToDecimal(incomeDisburse["freducedbrokerage"]);

//        //    var dm = this.Container.GetService<IDataManager>();
//        //    dm.InitDbContext(this.Context, this.MetaModelService.LoadFormModel(this.Context, "ydj_order").GetDynamicObjectType(this.Context));

//        //    dm.Save(sourceOrder);
//        //}
//    }
//}