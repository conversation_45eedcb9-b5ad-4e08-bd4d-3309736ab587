using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.AMS.YDJ.Store.AppService.Helper;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.OtherStockOut
{
    /// <summary>
    /// 其他出库单：作废
    /// </summary>
    [InjectService]
    [FormId("stk_otherstockout")]
    [OperationNo("Cancel")]
    public class Cancel : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            DirectHelper.Cancel(this.Context, e.DataEntitys);
        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            var svc = this.Container.GetService<IReserveUpdateService>();
            var opResult = svc.DeleteReserve(this.Context, this.HtmlForm, e.DataEntitys, this.Option);
            this.Result.MergeResult(opResult);

            this.Result.IsSuccess = true;
        }
    }
}
