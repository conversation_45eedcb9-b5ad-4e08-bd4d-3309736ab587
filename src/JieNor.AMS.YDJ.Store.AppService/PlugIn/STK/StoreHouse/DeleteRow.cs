using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.StoreHouse
{
    /// <summary>
    /// 仓库：删除仓位明细行
    /// </summary>
    [InjectService]
    [FormId("ydj_storehouse")]
    [OperationNo("deleterow")]
    public class DeleteRow : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            var entityKey = "fentity";
            var htmlEntity = this.OperationContext.HtmlForm.GetEntryEntity(entityKey);
            if (e.DataEntitys.Length > 1)
            {
                throw new BusinessException($"不支持批量删除{this.HtmlForm.Caption}的{htmlEntity.Caption}行！");
            }
            var dataEntities = e.DataEntitys[0];
            if (dataEntities == null)
            {
                throw new BusinessException($"没有选中{this.HtmlForm.Caption}的{htmlEntity.Caption}行！");
            }
            var entrys = htmlEntity.DynamicProperty.GetValue<DynamicObjectCollection>(dataEntities);
            if (entrys == null || entrys.Count <= 0) return;

            var checkEntryIds = new List<string>();
            this.OperationContext.SelectedRows
                ?.Where(o => o.EntityKey.EqualsIgnoreCase(entityKey))
                ?.Select(o => o.EntryPkValue)
                ?.ToList()
                ?.ForEach(o =>
                {
                    var exists = entrys.FirstOrDefault(k => Convert.ToString(k["id"]).EqualsIgnoreCase(o));
                    if (exists != null)
                    {
                        checkEntryIds.Add(o);
                    }
                });

            if (checkEntryIds.Count <= 0) return;

            //查找所有引用了仓库的业务表单
            var refForms = new List<Dictionary<string, string>>();
            var sqlText = $"select distinct frefformid,freffieldkey from t_sys_lookupboject where fbdformid='{this.HtmlForm.Id}'";
            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
            {
                while (reader.Read())
                {
                    refForms.Add(new Dictionary<string, string>
                    {
                        { "frefformid", reader.GetString("frefformid") },
                        { "freffieldkey", reader.GetString("freffieldkey") }
                    });
                }
            }
            if (refForms.Count <= 0) return;

            //生成检查语句
            var checkSqlList = new List<string>();
            var groups = refForms.GroupBy(o => o["frefformid"]);
            foreach (var group in groups)
            {
                var formId = group.Key;
                HtmlForm bizForm = null;
                try
                {
                    bizForm = this.MetaModelService.LoadFormModel(this.Context, formId);
                }
                catch { }
                if (bizForm == null 
                    || bizForm.BillHeadTableName.IsNullOrEmptyOrWhiteSpace()
                    || (bizForm.ElementType != HtmlElementType.HtmlForm_BaseForm
                        && bizForm.ElementType != HtmlElementType.HtmlForm_BillForm)) continue;

                foreach (var item in group)
                {
                    var field = bizForm.GetField(item["freffieldkey"]) as HtmlBaseDataField;
                    if (field == null 
                        || !field.RefFormId.EqualsIgnoreCase(this.HtmlForm.Id) 
                        || field.Entity.TableName.IsNullOrEmptyOrWhiteSpace()) continue;

                    //与仓库字段关联的仓位字段
                    var ctlFields = bizForm.GetFieldList().Where(o =>
                        o is HtmlBaseDataEntryField
                            && (o as HtmlBaseDataEntryField).ControlFieldKey.EqualsIgnoreCase(field.Id));
                    foreach (var ctlField in ctlFields)
                    {
                        if (ctlField.FieldName.IsNullOrEmptyOrWhiteSpace()
                            || ctlField.Entity.TableName.IsNullOrEmptyOrWhiteSpace()) continue;

                        var checkSql = $@"select top 1 {ctlField.FieldName} flocid,'{bizForm.Caption}' frefformname,'{ctlField.Caption}' freffieldname from {ctlField.Entity.TableName} where {ctlField.FieldName}=@pkid";
                        checkSqlList.Add(checkSql);
                    }
                }
            }
            if (checkSqlList.Count <= 0) return;

            //开始检查
            var errorMsgs = new List<string>();
            var paramList = new List<SqlParam> { new SqlParam("@pkid", System.Data.DbType.String, checkEntryIds.First()) };
            var checkSqlText = string.Join(" union all " + Environment.NewLine, checkSqlList);
            using (var reader = this.DBService.ExecuteReader(this.Context, checkSqlText, paramList))
            {
                while (reader.Read())
                {
                    var locId = reader.GetString("flocid");
                    var locEntry = entrys.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(locId));

                    errorMsgs.Add(string.Format("编码为【{0}】的仓位，已经被 \"{1}\"(字段：{2}) 所引用，不允许删除！",
                                locEntry["flocnumber"],
                                reader.GetString("frefformname"),
                                reader.GetString("freffieldname")));
                }
            }
            errorMsgs = errorMsgs.Distinct().ToList();
            if (errorMsgs.Count > 0)
            {
                this.Result.ComplexMessage.ErrorMessages.AddRange(errorMsgs);
                throw new BusinessException($"仓位删除失败！");
            }
        }
    }
}