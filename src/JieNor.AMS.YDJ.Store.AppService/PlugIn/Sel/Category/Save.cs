using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework.Interface.Log;
using System.Threading;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sel.Category
{
    /// <summary>
    /// 选配类别：保存插件
    /// </summary>
    [InjectService]
    [FormId("sel_category")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 日志服务
        /// </summary>
        private ILogServiceEx LogServiceEx { get; set; }

        /// <summary>
        /// 是否是通过 Excel 导入调用的保存
        /// </summary>
        private bool IsExcelImportSave
        {
            get
            {
                var topOrperationNo = this.Option.GetVariableValue("TopOrperationNo", string.Empty);
                return topOrperationNo.EqualsIgnoreCase("ExcelImport");
            }
        }

        /// <summary>
        /// 本次保存操作需要关联更新辅助属性组合值的选配类别ID
        /// 属性生成顺序修改后，需要关联更新辅助属性组合值（名称、编码、生成顺序）字段
        /// </summary>
        private string NeedRelationUpdateSelCategoryId { get; set; }

        /// <summary>
        /// 辅助属性组合值模型
        /// </summary>
        private HtmlForm AuxPropValSetForm { get; set; }

        /// <summary>
        /// 辅助属性组合值ORM实例
        /// </summary>
        private IDataManager AuxPropValSetDm { get; set; }

        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            //检查明细
            var errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return this.CheckEntry(newData, out errorMessage);
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            //添加选配类别属性值校验器
            var propValueRule = this.Container.GetValidRuleService(YDJHtmlElementType.HtmlValidator_SelCategoryPropValueValidation);
            if (propValueRule != null)
            {
                propValueRule.Initialize(this.Context, "");
                e.Rules.Add(propValueRule);
            }
        }

        /// <summary>
        /// 执行操作事务前事件，通知插件对要处理的数据进行排序等预处理
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            //加载引用数据
            var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refMgr.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), e.DataEntitys, false);

            foreach (var dataEntity in e.DataEntitys)
            {
                this.ProcAttributeFieldValue(dataEntity);
            }

            //初始化本次保存操作需要关联更新辅助属性组合值的选配类别ID
            this.InitNeedRelationUpdateSelCategoryId(e.DataEntitys);
        }

        /// <summary>
        /// 执行操作事务后事件，通知插件对象执行其它事务无关的业务逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            //异步关联更新辅助属性组合值
            //Task.Run(() =>
            //{
            //    this.RelationUpdateAuxPropValueSet(e.DataEntitys);
            //});

            //另起一个线程 处理辅助属性保存逻辑，确保保存数据能保存进去
            Task task = new Task(() =>
            {
                this.RelationUpdateAuxPropValueSet(e.DataEntitys);
            });
            ThreadWorker.QuequeTask(task, result => { });

            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 初始化本次保存操作需要关联更新辅助属性组合值的选配类别ID
        /// </summary>
        private void InitNeedRelationUpdateSelCategoryId(DynamicObject[] dataEntitys)
        {
            if (this.IsExcelImportSave) return;

            //先只处理手工保存一个的情况
            //手工修改保存时，如果修改了属性生成顺序，则自动更新辅助属性组合值（名称、编码、生成顺序）字段
            var dataEntity = dataEntitys.FirstOrDefault();
            if (!dataEntity.DataEntityState.FromDatabase) return;

            var selCategoryId = Convert.ToString(dataEntity["id"]);
            var entrys = dataEntity["fentity"] as DynamicObjectCollection;
            if (!entrys.Any()) return;

            //如果没有修改属性生成顺序，则不用处理
            var dbDataEntity = this.Context.LoadBizDataById(this.HtmlForm.Id, selCategoryId);
            var dbEntrys = dbDataEntity?["fentity"] as DynamicObjectCollection;
            if (dbEntrys == null || !dbEntrys.Any()) return;

            foreach (var entry in entrys)
            {
                var propId = Convert.ToString(entry["fpropid"]);
                var displaySeq = Convert.ToInt32(entry["fdisplayseq"]);
                foreach (var dbEntry in dbEntrys)
                {
                    if (Convert.ToString(dbEntry["fpropid"]).EqualsIgnoreCase(propId)
                        && Convert.ToInt32(dbEntry["fdisplayseq"]) != displaySeq)
                    {
                        //当前的属性生成顺序有被修改
                        this.NeedRelationUpdateSelCategoryId = selCategoryId;
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 关联更新辅助属性组合值
        /// </summary>
        private void RelationUpdateAuxPropValueSet(DynamicObject[] dataEntitys)
        {
            var needRelationUpdateSelCategoryId = this.NeedRelationUpdateSelCategoryId;
            if (needRelationUpdateSelCategoryId.IsNullOrEmptyOrWhiteSpace()) return;

            this.NeedRelationUpdateSelCategoryId = "";

            //批量加载辅助属性组合值ID（数据量有可能比较大，需要分批处理，否则会占用大量的系统内存）
            var auxPropValIds = this.LoadAuxPropValueSetIdsBySelCategoryId(needRelationUpdateSelCategoryId);
            if (auxPropValIds == null || !auxPropValIds.Any()) return;

            this.LogServiceEx = this.Container.GetService<ILogServiceEx>();

            //加载辅助属性组合值模型
            this.AuxPropValSetForm = this.MetaModelService.LoadFormModel(this.Context, "bd_auxpropvalueset");
            this.AuxPropValSetDm = this.GetDataManager();
            this.AuxPropValSetDm.InitDbContext(this.Context, this.AuxPropValSetForm.GetDynamicObjectType(this.Context));

            //当前选配类别的属性明细
            var dataEntity = dataEntitys.FirstOrDefault();
            var selCategoryEntrys = dataEntity["fentity"] as DynamicObjectCollection;
            //批次数
            var batchSize = 5000;

            //分批处理
            var bacheCount = decimal.ToInt32(Math.Ceiling(auxPropValIds.Count * 1.0m / batchSize));
            var batchIndex = 1;
            while (batchIndex <= bacheCount)
            {
                var batchIds = auxPropValIds.Skip((batchIndex - 1) * batchSize).Take(batchSize).ToList();
                if (batchIds.Count < 1) break;

                var auxPropValSets = this.Context.LoadBizDataById("bd_auxpropvalueset", batchIds);

                this.BatchRelationUpdateAuxPropValueSet(auxPropValSets, selCategoryEntrys);

                batchIndex++;
            }
        } 

        /// <summary>
        /// 分批关联更新辅助属性组合值
        /// </summary>
        private void BatchRelationUpdateAuxPropValueSet(List<DynamicObject> auxPropValSets, DynamicObjectCollection selCategoryEntrys)
        {
            if (auxPropValSets == null || !auxPropValSets.Any()) return;
            var saveData_exts = new List<DynamicObject>();

            DynamicObject[] lstAllAuxPropObjs = this.LoadAllAuxProperties(this.Context);

            var LstAttr_e = auxPropValSets.Select(o => Convert.ToString(o["fattrinfo_e"])).Distinct().ToList();
            var AttrExtObjs = this.Context.LoadBizDataById("bd_auxpropvalue_ext", LstAttr_e);

            //批量加载属性和属性值数据包
            var propIds = new List<string>();
            var propValueIds = new List<string>();
            foreach (var setObj in auxPropValSets)
            {
                var setEntrys = setObj?["fentity"] as DynamicObjectCollection;
                if (setEntrys == null || !setEntrys.Any()) continue;
                foreach (var setEntry in setEntrys)
                {
                    propIds.Add(Convert.ToString(setEntry["fauxpropid"]));
                    propValueIds.Add(Convert.ToString(setEntry["fvalueid"]));
                }
            }
            propIds = propIds.Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            propValueIds = propValueIds.Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            DynamicObjectCollection propDatas = null;
            DynamicObjectCollection propValueDatas = null;
            if (propIds.Any()) propDatas = this.Context.LoadBizBillHeadDataById("sel_prop", propIds);
            if (propValueIds.Any()) propValueDatas = this.Context.LoadBizBillHeadDataById("sel_propvalue", propValueIds, "fname,fnumber");
            if (propDatas == null || !propDatas.Any()) return;
            if (propValueDatas == null || !propValueDatas.Any()) return;

            var saveDatas = new List<DynamicObject>();

            //重新组装辅助属性组合值（名称、生成顺序）字段值
            foreach (var setObj in auxPropValSets)
            {
                var setEntrys = setObj?["fentity"] as DynamicObjectCollection;
                if (setEntrys == null || !setEntrys.Any()) continue;

                //自动根据表体组装的编码及名称回填表头
                var lstAuxSortObjs = setEntrys.Join(lstAllAuxPropObjs,
                      ok => ok["fauxpropid"] as string,
                      ik => ik["id"] as string,
                      (ok, ik) =>
                          new
                          {
                              Item1 = ok,
                              Item2 = ik
                          },
                      StringComparer.OrdinalIgnoreCase);

                //新字段目的是按属性排序固定辅助属性名称的顺序
                var strHeadName_e = string.Join(",", lstAuxSortObjs
                  .OrderBy(o => o.Item1["fauxpropid"])
                  .Select(o =>
                  {
                      return string.Format("{0}:{1}", Convert.ToString(o.Item2["fname"]).Trim(), Convert.ToString(o.Item1["fvaluename"]).Trim());
                  }));

                //找到对应的选配类别属性明细，然后取选配类别属性明细的生成顺序字段值
                var canFind = false;
                foreach (var item in setEntrys)
                {
                    var selEntry = selCategoryEntrys.FirstOrDefault(o =>
                        Convert.ToString(o["fpropid"]).EqualsIgnoreCase(Convert.ToString(item["fauxpropid"])));
                    if (selEntry == null) continue;
                    item["fdisplayseq"] = selEntry["fdisplayseq"];
                    canFind = true;
                }
                if (!canFind) continue;

                var setNames = new List<string>(); 
                //按生成顺序字段升序排列
                var _setEntrys = setEntrys.OrderBy(o => Convert.ToInt32(o["fdisplayseq"])).ToList();
                foreach (var _setEntry in _setEntrys)
                {
                    var propId = Convert.ToString(_setEntry["fauxpropid"]);
                    var propValueId = Convert.ToString(_setEntry["fvalueid"]);
                    if (propId.IsNullOrEmptyOrWhiteSpace() || propValueId.IsNullOrEmptyOrWhiteSpace()) continue;

                    var propData = propDatas.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(propId));
                    var propName = Convert.ToString(propData?["fname"]).Trim(); 
                    if (propName.IsNullOrEmptyOrWhiteSpace()) continue;

                    var propValueData = propValueDatas.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(propValueId));
                    var propValueName = Convert.ToString(propValueData?["fname"]).Trim();
                    var propValueNumber = Convert.ToString(propValueData?["fnumber"]).Trim();
                    if (propValueName.IsNullOrEmptyOrWhiteSpace())
                    {
                        //找不到属性值基础资料名称时，直接取已有的属性值名称，可能是辅助资料或者文本类型属性值名称
                        propValueName = Convert.ToString(_setEntry["fvaluename"]).Trim();
                    }
                    _setEntry["fvaluename"] = propValueName;

                    if (!propValueNumber.IsNullOrEmptyOrWhiteSpace())
                    {
                        _setEntry["fvaluenumber"] = propValueNumber;
                    }

                    setNames.Add($"{propName}:{propValueName}"); 
                }
                if (setNames.Any())
                {
                    setObj["fname"] = string.Join(",", setNames);
                    setObj["fname_e"] = strHeadName_e;
                    saveDatas.Add(setObj);

                    if (AttrExtObjs.Any()) 
                    {
                        var setObj_e = AttrExtObjs.Where(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(setObj["fattrinfo_e"]))).ToList();
                        if (setObj_e.Any())
                        {
                            setObj_e.ForEach(o =>
                            {
                                o["fnumber"] = string.Join(",", setNames);
                                o["fname"] = string.Join(",", setNames);
                                o["fname_e"] = strHeadName_e;
                            });
                            saveData_exts.AddRange(setObj_e);
                        }
                    }
                }
            }

            if (!saveDatas.Any()) return;

            //批次数
            var batchSize = 1000;

            //分批保存
            var bacheCount = decimal.ToInt32(Math.Ceiling(saveDatas.Count * 1.0m / batchSize));
            var batchIndex = 1;
            while (batchIndex <= bacheCount)
            {
                var batchObjs = saveDatas.Skip((batchIndex - 1) * batchSize).Take(batchSize).ToList();
                if (batchObjs.Count < 1) break;

                try
                {
                    this.AuxPropValSetDm.Save(batchObjs);
                }
                catch (Exception ex)
                {
                    var message = $"{this.HtmlForm.Caption}【fdisplayseq - 生成顺序】字段值手工修改保存时，分批关联更新辅助属性组合值时出现错误。";
                    this.LogServiceEx.Error(message, ex);
                }

                batchIndex++;

                //降低cpu占用，防止cpu过高
                Thread.Sleep(20);
            }
             
            if (saveData_exts.Any())
            {
                var flexFormInfo_ext = this.Context.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "bd_auxpropvalue_ext");
                var dm = this.Context.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, flexFormInfo_ext.GetDynamicObjectType(this.Context));

                var bacheCount_ext = decimal.ToInt32(Math.Ceiling(saveData_exts.Count * 1.0m / batchSize));
                var batchIndex_ext = 1;
                while (batchIndex_ext <= bacheCount_ext)
                {
                    var batchObjs = saveData_exts.Skip((batchIndex_ext - 1) * batchSize).Take(batchSize).ToList();
                    if (batchObjs.Count < 1) break;

                    try
                    { 
                        dm.Save(saveData_exts);
                    }
                    catch (Exception ex)
                    {
                        var message = $"{this.HtmlForm.Caption}【fdisplayseq - 生成顺序】辅助属性扩展更新错误。";
                        this.LogServiceEx.Error(message, ex);
                    }

                    batchIndex_ext++;
                } 
            }
        }

        /// <summary>
        /// 加载所有属性
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public DynamicObject[] LoadAllAuxProperties(UserContext userCtx)
        {
            var auxPropMeta = this.MetaModelService.LoadFormModel(userCtx, "sel_prop");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, auxPropMeta.GetDynamicObjectType(userCtx));
            userCtx.UpdateMdlSchema(auxPropMeta.Id);

            var aclFilter = DataRowACLHelper.GetDataRowACLFilter(userCtx, "", "");

            var allAuxPropPkIdSql = $"select {auxPropMeta.HeadEntity.PkFieldName} from {auxPropMeta.HeadEntity.TableName} with(nolock) where {aclFilter} and {auxPropMeta.ForbidStatusFldKey}='0'";
            var allPkIds = this.DBService.ExecuteDynamicObject(userCtx, allAuxPropPkIdSql, null)
                .Select(o => o[0])
                .ToArray();

            return dm.Select(allPkIds).OfType<DynamicObject>().ToArray();
        }

        /// <summary>
        /// 根据选配类别ID加载辅助属性组合值ID
        /// </summary>
        private List<string> LoadAuxPropValueSetIdsBySelCategoryId(string selCategoryId)
        {
            var sqlText = $@"
            select distinct apv.fid from t_bd_auxpropvalue apv with(nolock)
            inner join t_bd_material m with(nolock) on m.fid=apv.fmaterialid 
            where m.fselcategoryid='{selCategoryId}'";

            var auxPropValIds = this.DBService.ExecuteDynamicObject(this.Context, sqlText)
                ?.Select(o => Convert.ToString(o["fid"]))
                ?.ToList();

            return auxPropValIds;
        }

        /// <summary>
        /// 处理辅助属性字段值
        /// </summary>
        /// <param name="dataEntity"></param>
        private void ProcAttributeFieldValue(DynamicObject dataEntity)
        {
            var entrys = (dataEntity["fentity"] as DynamicObjectCollection)
                ?.OrderBy(o => Convert.ToInt32(o["fdisplayseq"]));

            var auxPropTxts = new List<string>();
            foreach (var entry in entrys)
            {
                var propName = Convert.ToString((entry["fpropid_ref"] as DynamicObject)?["fname"]);
                var propValueName = Convert.ToString((entry["fdefaultpropvalueid_ref"] as DynamicObject)?["fname"]);

                if (propName.IsNullOrEmptyOrWhiteSpace()
                    || propValueName.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                auxPropTxts.Add($"{propName.Trim()}:{propValueName.Trim()}");
            }

            var attribute = "";
            if (auxPropTxts.Any())
            {
                attribute = $"示例：{string.Join(",", auxPropTxts)}";
            }

            dataEntity["fattribute"] = attribute;
        }

        /// <summary>
        /// 检查明细
        /// </summary>
        /// <param name="newData"></param>
        /// <param name="errorMessage"></param>
        /// <returns></returns>
        private bool CheckEntry(DynamicObject newData, out string errorMessage)
        {
            errorMessage = "";

            var entrys = newData["fentity"] as DynamicObjectCollection;

            var whetherInforce = entrys.FirstOrDefault(o => Convert.ToBoolean(o["fiswhetherinforce"]));
            if (whetherInforce == null)
            {
                errorMessage = $"{this.HtmlForm.Caption}【{newData["fnumber"]}】的【明细信息】必须勾选一行【是否生效】！";
                return false;
            }

            ////是否存在断号
            //var isBroken = false;
            //var seqs = entrys
            //    .Where(o => !Convert.ToString(o["fpropid"]).IsNullOrEmptyOrWhiteSpace())
            //    .Select(o => Convert.ToInt32(o["fdisplayseq"]))
            //    .ToList();
            //for (int i = 1; i <= seqs.Count; i++)
            //{
            //    if (!seqs.Contains(i))
            //    {
            //        isBroken = true;
            //        break;
            //    }
            //}

            ////是否存在小于1的顺序号
            //if (isBroken || seqs.Any(seq => seq < 1))
            //{
            //    errorMessage = $"{this.HtmlForm.Caption}【{newData["fnumber"]}】的【生成顺序】必须大于0且不允许断号！";
            //    return false;
            //}

            return true;
        }
    }
}