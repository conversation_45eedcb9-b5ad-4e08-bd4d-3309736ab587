using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchasePrice
{
    /// <summary>
    /// 采购价目：禁用
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseprice|ydj_selfpurchaseprice")]
    [OperationNo("Forbid")]
    public class Forbid : BaseValidationPlugin
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            var priceService = Container.GetService<IPriceService>();
            priceService.ClearPriceCache(this.Context, e.DataEntitys, "fproductid_e");
        }
    }
}
