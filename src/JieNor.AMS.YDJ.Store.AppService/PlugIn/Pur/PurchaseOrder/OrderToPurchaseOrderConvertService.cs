using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core;
using JieNor.Framework.DataEntity.BillType;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PurchaseOrder
{
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("ydj_order2ydj_purchaseorder")]
    public class OrderToPurchaseOrderConvertService : AbstractConvertServicePlugIn
    {
        const string KEYFORMAT = "productId={0}&attrInfo={1}&orderDate={2}";

        /// <summary>
        /// 采购计划合同转采购建议采购量
        /// </summary>
        private Dictionary<string, object> OutspotEntries;

        /// <summary>
        /// 是否焕新订单一键采购
        /// </summary>
        private bool IsRenewalPur;

        protected override void OnInitialized(InitializeServiceEventArgs e)
        {
            base.OnInitialized(e);

            if (this.Option != null)
            {
                this.Option.TryGetVariableValue("outspotEntries", out OutspotEntries);
                this.Option.TryGetVariableValue("IsRenewalPur", out IsRenewalPur);
            }
            if (OutspotEntries == null)
            {
                OutspotEntries = new Dictionary<string, object>();
            }
        }

        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            var dataEntities = e.TargetDataEntities?.ToList();
            if (dataEntities == null || dataEntities.Count <= 0)
            {
                return;
            }

            setParameter(dataEntities);
            setPurUnit(dataEntities);

            var datas = dataEntities.SelectMany(x =>
            {
                var fentry = x["fentity"] as DynamicObjectCollection;
                var orderDate = Convert.ToDateTime(x["fdate"]).ToString("yyyy-MM-dd");//业务日期
                var supplierId = Convert.ToString(x["fsupplierid"]);

                return fentry.Select(y =>
                {
                    var productId = Convert.ToString(y["fmaterialid"]);//商品id
                    var attrInfo = Convert.ToString(y["fattrinfo"]);//辅助属性
                    return new
                    {
                        clientId = string.Format(KEYFORMAT, productId, attrInfo, orderDate),
                        entry = y,
                        productId = productId,
                        attrInfo = attrInfo,
                        orderDate = orderDate,
                        supplierId = supplierId
                    };
                });
            }).Where(x => string.IsNullOrWhiteSpace(x.productId) == false).ToList();

            if (datas.Count <= 0)
            {
                return;
            }

            var requestDatas = datas.Distinct(x => x.clientId).Select(x => new
            {
                clientId = x.clientId,
                productId = x.productId,
                bizDate = x.orderDate,
                supplierId = x.supplierId,
                length = 0,
                width = 0,
                thick = 0,
                attrInfo = new
                {
                    id = x.attrInfo
                }
            }).ToList();

            var gateway = this.UserContext.Container.GetService<IHttpServiceInvoker>();

            // 获取价格
            var result = gateway.InvokeBillOperation(this.UserContext,
                            "ydj_price",
                             null,
                            "getprices",
                            new Dictionary<string, object>
                            {
                               { "productInfos",JsonConvert.SerializeObject(requestDatas)},
                                { "priceFlag",4}
                            }
                         );

            if (result == null || !result.IsSuccess || result.SrvData == null)
            {
                compute(dataEntities);
                return;
            }

            JArray array = JArray.FromObject(result.SrvData);

            if (array == null || array.Count <= 0)
            {
                compute(dataEntities);
                return;
            }

            foreach (var data in datas)
            {
                var jObj = array.FirstOrDefault(x => (string)x["clientId"] == data.clientId) as JObject;
                if (jObj != null && ((bool)jObj["success"]))
                {
                    data.entry["fprice"] = Convert.ToDecimal(jObj["purPrice"]);
                }
            }

            compute(dataEntities);
            //补充单据类型，如果是经销商账号登录，会存在获取不到单据类型，所以需要做补充
            SupplyBillType(this.UserContext, dataEntities);
            //更新单据类型
            ModifyBillType(dataEntities);
            //更新送达方
            ModifyFdeliverid(dataEntities);

            //获取期初单据类型id
            var qcBillType = this.UserContext.GetBillTypeByBizObject(this.TargetHtmlForm.Id, "ydj_purchaseorder_qc");
            var qcBillTypeId = Convert.ToString(qcBillType?.fid);

            List<string> orderId = new List<string>();

            //2、增加《销售合同》转采购时, 如果《销售合同》单据类型 = "期初销售合同" 时, 下推生成的《采购订单》单据类型 = "期初采购订单", 并且锁定该单据类型字段
            foreach (var item in dataEntities)
            {
                if (Convert.ToString(item["fsourcetype"]) == "ydj_order_qc")
                {
                    item["fbilltypeid"] = qcBillTypeId;
                }
                foreach (DynamicObject entity in item["fentity"] as DynamicObjectCollection)
                {
                    orderId.Add(Convert.ToString(entity["fsoorderinterid"]));
                }
            }
            var orderObjs = this.UserContext.LoadBizDataById("ydj_order", orderId.Distinct());
            var RefObjMgr = this.UserContext.Container.GetService<LoadReferenceObjectManager>();
            var orderForm = this.UserContext.Container.GetService<IMetaModelService>()
                .LoadFormModel(this.UserContext, "ydj_order");

            //加载引用数据
            RefObjMgr?.Load(this.UserContext, orderObjs.ToArray(), false, orderForm, new List<string>() { "fbilltype", "fcustomercontactid" });

            foreach (var dataEntity in dataEntities)
            {
                string OrderId = "";
                foreach (DynamicObject entity in dataEntity["fentity"] as DynamicObjectCollection)
                {
                    if (!string.IsNullOrWhiteSpace(entity["fchannel"].ToString()) && string.IsNullOrWhiteSpace(Convert.ToString(entity["fgoodschanneltype"])))
                    {
                        DynamicObject channelItem = this.UserContext.LoadBizDataById("ste_channel", entity["fchannel"].ToString());
                        if (channelItem != null)
                        {
                            entity["fgoodschanneltype"] = channelItem["ftype"];
                        }
                    }
                    if (!string.IsNullOrWhiteSpace(Convert.ToString(entity["fsoorderinterid"])))
                    {
                        OrderId = Convert.ToString(entity["fsoorderinterid"]);
                    }
                }
                if (Convert.ToBoolean(dataEntity["fpiecesendtag"]))
                {
                    var orderObj = orderObjs.Where(a => a["id"].Equals(OrderId)).FirstOrDefault();
                    if (orderObj != null)
                    {
                        if (Convert.ToString(((orderObj["fbilltype_ref"] as DynamicObject)?["fname"])).Equals("销售转单") || Convert.ToBoolean(orderObj["fisresellorder"]))
                        {
                            dataEntity["fcustomerid"] = orderObj["fterminalcustomer"];
                            dataEntity["fconsignee"] = orderObj["fcontacts_c"];
                            dataEntity["fphone"] = orderObj["fcoophone"];
                            dataEntity["fprovince"] = orderObj["fprovince_c"];
                            dataEntity["fcity"] = orderObj["fcity_c"];
                            dataEntity["fregion"] = orderObj["fregion_c"];
                            dataEntity["faddress"] = orderObj["fcooaddress"];

                        }
                        else
                        if (!Convert.ToString(((orderObj["fbilltype_ref"] as DynamicObject)?["fname"])).Equals("销售转单") && !Convert.ToBoolean(orderObj["fisresellorder"]))
                        {
                            dataEntity["fcustomerid"] = orderObj["fcustomerid"];
                            dataEntity["fconsignee"] = (orderObj["fcustomercontactid_ref"] as DynamicObject)?["fcontacter"];
                            dataEntity["fphone"] = orderObj["fphone"];
                            dataEntity["fprovince"] = orderObj["fprovince"];
                            dataEntity["fcity"] = orderObj["fcity"];
                            dataEntity["fregion"] = orderObj["fregion"];
                            dataEntity["faddress"] = orderObj["faddress"];
                        }
                    }
                }

                {
                    var orderObj = orderObjs.Where(a => a["id"].Equals(OrderId)).FirstOrDefault();
                    if (orderObj != null)
                    {
                        if (Convert.ToString(orderObj["fmanagemodel"]).Equals("1"))
                        {
                            dataEntity["fstoreid"] = orderObj["fstore"];
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 更新单据类型
        /// </summary>
        /// <param name="dataEntities"></param>
        private void ModifyBillType(List<DynamicObject> dataEntities)
        {
            var svc = this.UserContext.Container.GetService<IBillTypeService>();
            var purchaseOrderBillTypeInfos = svc.GetBillTypeInfors(this.UserContext, "ydj_purchaseorder");
            var orderBillTypeInfos = svc.GetBillTypeInfors(this.UserContext, "ydj_order");

            foreach (var item in dataEntities)
            {
                var billDataObj = orderBillTypeInfos.FirstOrDefault(x => x.fid.EqualsIgnoreCase(Convert.ToString(item["fbilltypeid"])));
                if (billDataObj != null)
                {
                    string type = billDataObj.fname;

                    if (type != null && type.EqualsIgnoreCase("大客户销售合同"))
                    {
                        item["fbilltypeid"] = GetBilltypeId("大客户采购订单", purchaseOrderBillTypeInfos); ;//大客户采购订单
                    }
                    else if (type != null && type.EqualsIgnoreCase("期初销售合同"))
                    {
                        item["fbilltypeid"] = GetBilltypeId("期初采购订单", purchaseOrderBillTypeInfos);//期初采购订单
                    }
                    else
                     if (billDataObj.fprimitiveid.EqualsIgnoreCase("ydj_order_mdsy") || (billDataObj.fmainorgid == this.UserContext.TopCompanyId && billDataObj.fid.EqualsIgnoreCase("ydj_order_mdsy")))
                    {
                        var paramSetObj = billDataObj.BillTypeParaSet.fparamset;
                        //var paramSetObj = billDataObj.fparamset;
                        if (paramSetObj != null)
                        {
                            bool fautoalterstatus = false;
                            bool.TryParse(Convert.ToString(paramSetObj["fautoalterstatus"]), out fautoalterstatus);
                            if (fautoalterstatus)
                            {
                                item["fbilltypeid"] = GetBilltypeId("摆场订单", purchaseOrderBillTypeInfos);//摆场订单
                            }
                            else
                            {
                                item["fbilltypeid"] = GetBilltypeId("标准采购", purchaseOrderBillTypeInfos);//"po_type_01";//系统预制的标准采购
                            }
                        }
                        else
                        {
                            item["fbilltypeid"] = GetBilltypeId("标准采购", purchaseOrderBillTypeInfos);//"po_type_01";//系统预制的标准采购
                        }
                    }
                    else
                    {
                        item["fbilltypeid"] = GetBilltypeId("标准采购", purchaseOrderBillTypeInfos);//"po_type_01";//系统预制的标准采购
                    }
                }
                else
                {
                    item["fbilltypeid"] = GetBilltypeId("标准采购", purchaseOrderBillTypeInfos);//"po_type_01";//系统预制的标准采购
                }
            }
        }

        private string GetBilltypeId(string type, List<BillTypeInfo> billData)
        {
            var result = string.Empty;

            var matchBillTypes = billData.Where(s => s.fname.EqualsIgnoreCase(type));
            if (matchBillTypes.Any())
            {
                // 先取本地的
                var local = matchBillTypes.FirstOrDefault(s => s.fmainorgid.EqualsIgnoreCase(this.UserContext.Company));
                if (local != null)
                {
                    return local.fid;
                }

                // 再取总部的
                var topOrg =
                    matchBillTypes.FirstOrDefault(s => s.fmainorgid.EqualsIgnoreCase(this.UserContext.TopCompanyId));
                if (topOrg != null)
                {
                    return topOrg.fid;
                }

                // 任意取一个
                return matchBillTypes.First().fid;
            }

            switch (type)
            {
                case "大客户采购订单":
                    return "ydj_purchaseorder_type";//系统预制的标准采购
                case "期初采购订单":
                    return "ydj_purchaseorder_qc";//期初采购订单
                case "摆场订单":
                    return "ydj_purchaseorder_bc";//摆场订单
                case "标准采购":
                    return "po_type_01";//系统预制的标准采购
            }

            return result;
        }

        /// <summary>
        /// 更新送达方
        /// </summary>
        /// <param name="dataEntities"></param>
        private void ModifyFdeliverid(List<DynamicObject> dataEntities)
        {
            IPurchaseOrderService purchaseOrderService = this.UserContext.Container.GetService<IPurchaseOrderService>();

            foreach (var item in dataEntities)
            {
                //得到城市
                var cityid = GetStoreCity(item["fdeptid"].ToString());

                //明细里找出一个业绩品牌
                var fentries = item["fentity"] as DynamicObjectCollection;

                //先从明细行找，如果有送达方直接做明细行的
                var fsaleorgid = fentries.FirstOrDefault()["fshipperdeliver"].ToString().Trim();
                if (fsaleorgid.IsNullOrEmptyOrWhiteSpace())
                {
                    //业绩品牌做了分组下推多个都是一组的，默认取一条
                    var fresultbrandid = fentries.FirstOrDefault()["fresultbrandid"].ToString();
                    var dm = GetDeliverByBrandidAndCity(fresultbrandid, cityid);
                    if (dm != null)
                    {
                        //送达方赋值
                        item["fdeliverid"] = dm["id"].ToString();
                        fsaleorgid = dm["id"].ToString();
                    }
                    else if (this.UserContext.IsSecondOrg)
                    {
                        var _data = GetDefaultDeliver();
                        item["fdeliverid"] = _data?["id"].ToString();
                        fsaleorgid = _data?["id"].ToString();
                    }
                }
                else
                {
                    item["fdeliverid"] = fsaleorgid;
                }
                var fsupplierid = Convert.ToString(item["fsupplierid"]);
                if (!fsupplierid.IsNullOrEmptyOrWhiteSpace())
                {
                    item["fsupplierid"] = fsupplierid;
                }
                else
                {
                    if (!fsaleorgid.IsNullOrEmptyOrWhiteSpace())
                    {
                        item["fsupplierid"] = purchaseOrderService.GetSupplierIdByDeliverId(this.UserContext, fsaleorgid);
                    }
                }

            }
        }

        /// <summary>
        /// 得到门店城市
        /// </summary>
        /// <param name="deptid"></param>
        /// <returns></returns>
        private string GetStoreCity(string deptid)
        {
            string strSql = @"select top 1 t2.fmycity from t_bd_department t1 with(nolock)
                            join t_bas_store t2 with(nolock) on t1.fstore = t2.fid where t1.fid =@fid";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fid", System.Data.DbType.String, deptid)
            };
            string fmycity = "";
            using (var dr = this.UserContext.ExecuteReader(strSql, sqlParam))
            {
                if (dr.Read())
                {
                    fmycity = Convert.ToString(dr["fmycity"]);
                }
            }

            return fmycity;
        }

        /// <summary>
        /// 根据城市和品牌找到送达方
        /// </summary>
        /// <param name="deptid"></param>
        /// <returns></returns>
        private DynamicObject GetDeliverByBrandidAndCity(string fresultbrandid, string fcity)
        {
            if (fresultbrandid.IsNullOrEmptyOrWhiteSpace() || fcity.IsNullOrEmptyOrWhiteSpace())
                return null;
            var metaModelService = this.UserContext.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(UserContext, "bas_deliver");
            var dm = this.UserContext.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserContext, htmlForm.GetDynamicObjectType(this.UserContext));
            var Agents = new List<string>() { UserContext.Company };
            //获取当前用户登录经销商的《主经销商配置表》
            var topCtx = UserContext.CreateTopOrgDBContext();
            var mainAgentConfigs = topCtx.LoadBizDataByACLFilter("bas_mainagentconfig", $" fmainagentid = '{UserContext.Company}'  AND fforbidstatus='0' ").FirstOrDefault();
            if (mainAgentConfigs != null)
            {
                //存在配置，则需要将所有子经销商也包含进来
                var subAgentEntrys = mainAgentConfigs["fsubagent"] as DynamicObjectCollection;
                if (subAgentEntrys != null && subAgentEntrys.Any())
                {
                    Agents.AddRange(subAgentEntrys.Select(t => Convert.ToString(t["fsubagentid"])));
                }
            }

            var where = @"fforbidstatus = 0 and fagentid in ('{0}') and fcity='{1}'".Fmt(string.Join("','", Agents), fcity);

            var sqlParam = new List<SqlParam>
            {
            };
            var reader = UserContext.GetPkIdDataReader(htmlForm, where, sqlParam);
            var data = dm.SelectBy(reader).OfType<DynamicObject>();
            if (data != null && data.Count() > 0)
            {
                //35321 【慕思现场-正式区问题 526】调整 销售转采购 送达方生成逻辑, 如果匹配到多个送达方时, 系统不进行赋值让用户手动选择
                var count = data.Where(c => (c["fentry"] as DynamicObjectCollection).Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]))).Count();
                if ((!IsRenewalPur && count == 1) || (IsRenewalPur && count >= 1))
                {
                    foreach (var item in data)
                    {
                        var fentry = item["fentry"] as DynamicObjectCollection;
                        var isexists = fentry.Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]));
                        if (isexists)
                            return item;
                    }
                }
                //如果城市 +系列没匹配到 还是要走上级城市逻辑
                if (count == 0)
                {
                    var parentcity = this.UserContext.Container.GetService<IOrderService>().GetParentCity(this.UserContext, fcity);
                    if (parentcity.IsNullOrEmptyOrWhiteSpace()) return null;

                    where = @"fforbidstatus = 0 and fagentid in ('{0}') and fcity='{1}' ".Fmt(string.Join("','", Agents), parentcity);
                    var deliver = this.UserContext.LoadBizDataByFilter("bas_deliver", where);
                    if (!deliver.IsNullOrEmptyOrWhiteSpace())
                    {
                        var count_temp = deliver.Where(c => (c["fentry"] as DynamicObjectCollection).Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]))).Count();
                        if ((!IsRenewalPur && count_temp == 1) || (IsRenewalPur && count_temp >= 1))
                        {
                            foreach (var item in deliver)
                            {
                                var fentry = item["fentry"] as DynamicObjectCollection;
                                var isexists = fentry.Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]));
                                if (isexists)
                                    return item;
                            }
                        }
                    }
                }
            }
            //通过深圳市宝安区匹配不到，尝试通过深圳市匹配
            else
            {
                var parentcity = this.UserContext.Container.GetService<IOrderService>().GetParentCity(this.UserContext, fcity);

                if (parentcity.IsNullOrEmptyOrWhiteSpace()) return null;

                where = @"fforbidstatus = 0 and fagentid in ('{0}') and fcity='{1}' ".Fmt(string.Join("','", Agents), parentcity);
                var deliver = this.UserContext.LoadBizDataByFilter("bas_deliver", where);
                if (!deliver.IsNullOrEmptyOrWhiteSpace())
                {
                    var count = deliver.Where(c => (c["fentry"] as DynamicObjectCollection).Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]))).Count();
                    if ((!IsRenewalPur && count == 1) || (IsRenewalPur && count >= 1))
                    {
                        foreach (var item in deliver)
                        {
                            var fentry = item["fentry"] as DynamicObjectCollection;
                            var isexists = fentry.Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]));
                            if (isexists)
                                return item;
                        }
                    }
                }
            }
            return null;
        }

        private DynamicObject GetDefaultDeliver()
        {
            string orgid = this.UserContext.IsTopOrg ? this.UserContext.TopCompanyId : UserContext.Company;
            return this.UserContext.LoadBizDataByFilter("bas_deliver", $" fforbidstatus = 0 and fagentid = '{orgid}' ").FirstOrDefault();
        }

        /// <summary>
        /// 补充单据类型
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="dataEntities"></param>
        private void SupplyBillType(UserContext userContext, List<DynamicObject> dataEntities)
        {
            var svc = userContext.Container.GetService<IBillTypeService>();
            var billTypeInfos = svc.GetBillTypeInfors(userContext, "ydj_purchaseorder");
            var billTypeInfo = billTypeInfos.FirstOrDefault(f => f.fname == "标准采购" || (!f.fprimitivename.IsNullOrEmptyOrWhiteSpace() && f.fprimitivename == "标准采购"));
            if (billTypeInfo == null)
            {
                return;
            }

            foreach (var item in dataEntities)
            {
                if (Convert.ToString(item["fbilltypeid"]).IsNullOrEmptyOrWhiteSpace())
                    item["fbilltypeid"] = billTypeInfo.fid;
            }
        }
        private void setPurUnit(List<DynamicObject> dataEntities)
        {
            if (dataEntities == null || dataEntities.Count <= 0)
            {
                return;
            }

            var entries = dataEntities.SelectMany(x => x["fentity"] as DynamicObjectCollection).ToList();
            if (entries == null || entries.Count <= 0)
            {
                return;
            }

            var productIds = entries.Select(x => Convert.ToString(x["fmaterialid"])).Where(x => false == string.IsNullOrWhiteSpace(x)).Distinct().ToList();
            if (productIds == null || productIds.Count <= 0)
            {
                return;
            }

            var metaModelService = this.UserContext.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(UserContext, "ydj_product");
            var dm = this.UserContext.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserContext, htmlForm.GetDynamicObjectType(this.UserContext));
            var productEntities = dm.Select(productIds).OfType<DynamicObject>().ToList();

            if (productEntities == null || productEntities.Count <= 0)
            {
                return;
            }

            foreach (var entry in entries)
            {
                var productId = Convert.ToString(entry["fmaterialid"]);
                var productEntity = productEntities.FirstOrDefault(x => Convert.ToString(x["id"]) == productId);
                if (productEntity == null)
                {
                    continue;
                }
                entry["fbizunitid"] = productEntity["fpurunitid"];
            }

            var unitConvertService = this.UserContext.Container.GetService<IUnitConvertService>();
            unitConvertService.ConvertByBasQty(this.UserContext, this.TargetHtmlForm, dataEntities, OperateOption.Create());
        }

        private void compute(List<DynamicObject> dataEntities)
        {

            var sql = $@"/*dialect*/ 
SELECT
	age.fcustomchannel 
FROM
	t_bas_agent age
	JOIN t_bas_organization org ON org.fid = '{this.UserContext.BizOrgId}'
WHERE
	age.fnumber = org.fnumber";

            var agents = this.UserContext.ExecuteDynamicObject(sql, new List<SqlParam>() { });

            var isBigCustomer = false;

            if (agents.Count > 0 && Convert.ToString(agents.First()?["fcustomchannel"]) == "1")
            {
                isBigCustomer = true;
            }

            foreach (var dataEntity in dataEntities)
            {
                var fentries = dataEntity["fentity"] as DynamicObjectCollection;
                decimal sum = 0;

                foreach (var fentry in fentries)
                {
                    var productsql = $@"
SELECT
	fnumber 
FROM
	T_BD_MATERIAL 
WHERE
	fid = '{Convert.ToString(fentry["fmaterialid"])}' 
	AND fmainorgid = '821347239912935425'";

                    var products = this.UserContext.ExecuteDynamicObject(productsql, new List<SqlParam>() { });


                    if (isBigCustomer && products.Count > 0)
                    {
                        fentry["fprice"] = 0;
                    }


                    //采购计划合同转采购，采购数量为建议采购量
                    if (OutspotEntries.Count > 0)
                    {
                        decimal qty = Convert.ToDecimal(OutspotEntries[fentry["fsoorderentryid"].ToString()]);
                        if (qty > 0)
                        {
                            fentry["fqty"] = qty;
                        }
                    }
                    var amount = Convert.ToDecimal(fentry["fprice"]) * Convert.ToDecimal(fentry["fqty"]); //计算金额
                    fentry["famount"] = amount;
                    fentry["fdealprice"] = fentry["fprice"];
                    fentry["fdealamount"] = amount;
                    fentry["fdistrate"] = 10;
                    fentry["fdistamount"] = 0;
                    fentry["fretailamount"] = Convert.ToDecimal(fentry["fqty"]) * Convert.ToDecimal(fentry["fretailprice"]);
                    fentry["fretaildealamount"] = Convert.ToDecimal(fentry["fqty"]) * Convert.ToDecimal(fentry["fretaildealprice"]);
                    sum += amount;



                }
                dataEntity["ffbillamount"] = sum;
                dataEntity["fdealamount"] = sum;
                dataEntity["fpayamount"] = sum;
            }
        }

        private void setParameter(List<DynamicObject> dataEntities)
        {
            if (dataEntities == null || dataEntities.Count <= 0)
            {
                return;
            }

            var billTypeGroups = dataEntities.Where(x => false == x.DataEntityState.FromDatabase && false == string.IsNullOrWhiteSpace(Convert.ToString(x["fbilltypeid"])))
                                             .GroupBy(x => Convert.ToString(x["fbilltypeid"])).ToList();

            if (billTypeGroups == null || billTypeGroups.Count <= 0)
            {
                return;
            }

            var billTypeService = this.UserContext.Container.GetService<IBillTypeService>();

            foreach (var billTypeGroup in billTypeGroups)
            {
                var paramSetObj = billTypeService.GetBillTypeParamSet(this.UserContext, this.TargetHtmlForm, billTypeGroup.Key);
                if (paramSetObj == null)
                {
                    continue;
                }
                foreach (var dataEntity in billTypeGroup)
                {
                    dataEntity["fenablenotice"] = paramSetObj["fenablenotice"];

                    if (string.IsNullOrWhiteSpace(Convert.ToString(dataEntity["fsupplierid"])))
                    {
                        dataEntity["fsupplierid"] = paramSetObj.GetValue("fsupplierid", "");
                    }
                }
            }
        }
        /// <summary>
        /// 根据某个字段去分组
        /// </summary>
        /// <param name="e"></param>
        public override void OnSplitBillData(OnSplitBillDataEventArgs e)
        {
            base.OnSplitBillData(e);
            // 如果是直销模式，可以添加额外的分组条件
            if (this.UserContext.IsDirectSale)
            {
                if (e.SourceFormId.EqualsIgnoreCase("ydj_order"))
                {
                    // 根据源单主表的主键ID进行分单
                    // 这样每个源单都会生成一个独立的目标单
                    e.GroupInfo.Add("fbillhead_id");
                }
            }
        }
    }
}
