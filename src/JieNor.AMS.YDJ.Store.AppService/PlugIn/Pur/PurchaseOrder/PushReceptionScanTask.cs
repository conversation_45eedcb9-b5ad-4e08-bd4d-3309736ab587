using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.CustomEventData;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单下推收货扫描任务
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("push2recscantask")]
    public class PushReceptionScanTask: AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);

            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.BeforeGetConvertRule:
                    {
                        var eventData = e.EventData as BeforeGetConvertRuleData;
                        this.PrepareConvertRuleData(e.DataEntities, eventData);
                    }
                    break;
            }
        }

        /// <summary>
        /// 准备下推操作参数
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="eventData"></param>
        private void PrepareConvertRuleData(DynamicObject[] dataEntities, BeforeGetConvertRuleData eventData)
        {
            if (eventData == null || !eventData.RuleId.EqualsIgnoreCase("ydj_purchaseorder2bcm_receptionscantask")) return;

            eventData.RuleId = "ydj_purchaseorder2bcm_receptionscantask";

            if (dataEntities != null && dataEntities.Any())
            {
                foreach (var dataEntity in dataEntities)
                {
                    var poId = dataEntity["id"] as string;
                    var billNo = dataEntity["fbillno"] as string;
                    if (poId.IsEmptyPrimaryKey()) continue;

                    var entries = dataEntity["fentity"] as DynamicObjectCollection;
                    //自动关闭和手动关闭不下推
                    var selectedRows = entries.Where(x => !(Convert.ToString(x["fclosestatus_e"]) == "3"
                                                            || Convert.ToString(x["fclosestatus_e"]) == "4"))
                        .Select(x => new SelectedRow
                        {
                            PkValue = poId,
                            BillNo = billNo,
                            EntityKey = "fentity",
                            EntryPkValue = Convert.ToString(x["id"])
                        }).ToList();
                    eventData.SelectedRows = selectedRows;
                }
            }

            if (eventData.SelectedRows.Count() == 0)
            {
                throw new BusinessException("无满足条件的明细行，无法执行入库操作！");
            }
        }
    }
}
