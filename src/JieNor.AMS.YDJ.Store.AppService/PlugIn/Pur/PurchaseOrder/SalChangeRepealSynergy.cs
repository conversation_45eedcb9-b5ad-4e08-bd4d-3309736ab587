using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PurchaseOrder
{
    /// <summary>
    /// 采购订单：订购意向书变更单作废协同
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("SalChangeRepealSynergy")]
    public class SalChangeRepealSynergy : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var data = this.GetQueryOrSimpleParam<string>("data");

            var jArray = JArray.Parse(data);
            if (jArray.Count == 0)
            {
                throw new BusinessException("data参数格式不正确!");
            }

            foreach (var jToken in jArray)
            {
                var jObj = (JObject)jToken;
                string tranId = jObj["tranId"].Value<string>();
                string reason = jObj["reason"].Value<string>();

                if (tranId.IsNullOrEmptyOrWhiteSpace() || reason.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                //采购订单
                var dm = this.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
                string where = $"fmainorgid=@fmainorgid and ftranid=@ftranid";
                var sqlParam = new SqlParam[]
                {
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("ftranid", System.Data.DbType.String, tranId)
                };
                var dataReader = this.Context.GetPkIdDataReader(this.HtmlForm, where, sqlParam);
                var dataEntity = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();

                if (dataEntity == null)
                {
                    throw new BusinessException($"交易流水号【{tranId}】对应的采购订单不存在！");
                }

                if (!Convert.ToString(dataEntity["fbizstatus"]).EqualsIgnoreCase("business_status_09"))
                {
                    throw new BusinessException($"采购订单【{dataEntity["fbillno"]}】业务状态不是【协同完成】，不允许作废发送！");
                }

                // 协同状态：不允许变更
                dataEntity["fbizstatus"] = "business_status_11";
                //保存
                dm.Save(dataEntity);

                this.Logger.WriteLog(this.Context,
                    new LogEntry
                    {
                        BillIds = dataEntity["id"] as string,
                        BillNos = dataEntity["fbillno"] as string,
                        BillFormId = this.HtmlForm.Id,
                        OpName = "订购意向书变更单作废协同操作",
                        OpCode = this.OperationNo,
                        Content = "客服【不允许变更】, {0}".Fmt(reason),
                        DebugData = "客服【不允许变更】, {0}".Fmt(reason),
                        Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                        Level = Enu_LogLevel.Info.ToString(),
                        LogType = Enu_LogType.RecordType_03
                    }
                );
            }

            //标记成功
            this.Result.IsSuccess = true;
        }
    }
}