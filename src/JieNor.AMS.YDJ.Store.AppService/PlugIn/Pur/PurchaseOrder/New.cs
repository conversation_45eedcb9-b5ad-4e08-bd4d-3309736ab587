using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PurchaseOrder
{
    /// <summary>
    /// 采购订单：新增
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("New")]
    public class New : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName.ToLower())
            {
                case "aftercreateuidata":
                    this.ProcUiData(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 处理打包后的前端数据包
        /// </summary>
        /// <param name="e"></param>
        private void ProcUiData(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as JObject;
            if (eventData != null)
            {
                //根据单据类型参数设置相关字段的默认值
                var billTypeId = Convert.ToString(eventData?["fbilltypeid"]?["id"]);
                if (!billTypeId.IsNullOrEmptyOrWhiteSpace())
                {
                    var billTypeService = this.Container.GetService<IBillTypeService>();
                    var paramSetObj = billTypeService.GetBillTypeParamSet(this.Context, this.HtmlForm, billTypeId);
                    if (paramSetObj != null)
                    {
                        //默认供应商
                        var supplierId = Convert.ToString(paramSetObj["fsupplierid"]);
                        if (!supplierId.IsNullOrEmptyOrWhiteSpace())
                        {
                            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_supplier");
                            var dm = this.GetDataManager();
                            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                            var supplier = dm.Select(supplierId) as DynamicObject;
                            if (supplier != null)
                            {
                                JObject joSupplier = new JObject();
                                joSupplier["id"] = supplier["id"] as string;
                                joSupplier["fnumber"] = supplier["fnumber"] as string;
                                joSupplier["fname"] = supplier["fname"] as string;
                                eventData["fsupplierid"] = joSupplier;
                            }
                        }
                    }
                }
            }
        }
    }
}