using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.SaleIntention;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单：联查
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("LinkFormSearch")]
    public class LinkFormSearch : LinkFormSearchBase
    {
        protected override void DealLinkForm(UserContext userContext, DynamicObject[] dataEntities, List<Dictionary<string, object>> linkFormDatas)
        {
            var pkids = dataEntities.Select(o => o["id"]).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            if (pkids.Count <= 0) return;

            //过滤条件
            var filterStr = "";
            if (pkids.Count == 1)
            {
                filterStr = $"fpoorderinterid='{pkids[0]}'";
            }
            else
            {
                filterStr = $"fpoorderinterid in('{string.Join("','", pkids)}')";
            }

            //采购退货通知单
            var returnNoticeForm = this.MetaModelService.LoadFormModel(this.Context, "pur_returnnotice");
            linkFormDatas.Add(new Dictionary<string, object>
            {
                { "formId", returnNoticeForm.Id },
                { "formCaption", returnNoticeForm.Caption },
                { "flag", "nextForm" },
                { "filterString", filterStr }
            });

            //采购退货单
            var stockReturnForm = this.MetaModelService.LoadFormModel(this.Context, "stk_postockreturn");
            linkFormDatas.Add(new Dictionary<string, object>
            {
                { "formId", stockReturnForm.Id },
                { "formCaption", stockReturnForm.Caption },
                { "flag", "nextForm" },
                { "filterString", filterStr }
            });

            //联查销售合同
            LinkOrder(dataEntities, this.MetaModelService, linkFormDatas);

            //联查发货扫描任务
            LinkDelivery(dataEntities, this.MetaModelService, linkFormDatas);

            //联查收支记录
            LinkIncomeDisburse(dataEntities, this.MetaModelService, linkFormDatas);
        }

        /// <summary>
        /// 联查发货扫描任务
        /// </summary>
        private void LinkDelivery(DynamicObject[] dataEntities, IMetaModelService metaModelService, List<Dictionary<string, object>> linkFormDatas)
        {
            var fbillnos = dataEntities.Select(o => o["fbillno"]?.ToString()).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            if (fbillnos.Count <= 0) return;

            //过滤条件
            var filterStr = "";
            if (fbillnos.Count == 1)
            {
                filterStr = $"fsourceformid='ydj_purchaseorder' and fsourcebillno='{fbillnos[0]}'";
            }
            else
            {
                filterStr = $"fsourceformid='ydj_purchaseorder' and fsourcebillno in('{string.Join("','", fbillnos)}')";
            }
            //联查发货扫描任务
            var deliveryForm = metaModelService.LoadFormModel(this.Context, "bcm_deliveryscantask");
            linkFormDatas.Add(new Dictionary<string, object>
            {
                { "formId", deliveryForm.Id },
                { "formCaption", deliveryForm.Caption },
                { "flag", "nextForm" },
                { "filterString", filterStr }
            });
        }


        /// <summary>
        /// 联查销售合同
        /// </summary>
        private void LinkOrder(DynamicObject[] dataEntities, IMetaModelService metaModelService, List<Dictionary<string, object>> linkFormDatas)
        {
            var id = dataEntities.Select(o => o["id"]).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            var strSql = $@"select distinct fsourceinterid from t_ydj_poorderentry where fid ='{id[0]}'";
            List<string> fids = new List<string>();
            using (var dr = this.Context.ExecuteReader(strSql, new List<SqlParam>() { }))
            {
                while (dr.Read())
                {
                    fids.Add(dr["fsourceinterid"].ToString());
                }
            }
            if (fids.Count == 0) return;

            //找到合同下标用于修改
            var purFlag = -1;
            for (int i = 0; i < linkFormDatas.Count; i++)
            {
                foreach (var val in linkFormDatas[i])
                {
                    if (val.Value.ToString() == "ydj_order")
                    {
                        purFlag = i;
                        goto ModifyData;
                    }
                }
            }
            ModifyData:
            var purchaseorder = metaModelService.LoadFormModel(this.Context, "ydj_order");
            string filterStr = "fid in ('{0}')".Fmt(string.Join("','", fids));
            if (purFlag < 0)
            {
                linkFormDatas.Add(new Dictionary<string, object>
                    {
                        { "formId", purchaseorder.Id },
                        { "formCaption", purchaseorder.Caption },
                        { "flag", "preForm" },
                        { "filterString", filterStr },
                        { "visible",1}
                    });
            }
            else
            {
                linkFormDatas[purFlag] = (new Dictionary<string, object>
                {
                    { "formId", purchaseorder.Id },
                    { "formCaption", purchaseorder.Caption },
                    { "flag", "preForm" },
                    { "filterString", filterStr },
                    { "visible",1}
                });
            }
        }

        /// <summary>
        /// 联查收支记录
        /// </summary>
        private void LinkIncomeDisburse(DynamicObject[] dataEntities, IMetaModelService metaModelService, List<Dictionary<string, object>> linkFormDatas)
        {
            var pkids = dataEntities.Select(o => o["id"]).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            if (pkids.Count <= 0) return;

            //过滤条件
            var filterStr = "";
            if (pkids.Count == 1)
            {
                filterStr = $"fsourceformid='ydj_purchaseorder' and fsourceid='{pkids[0]}'";
            }
            else
            {
                filterStr = $"fsourceformid='ydj_purchaseorder' and fsourceid in('{string.Join("','", pkids)}')";
            }
            //联查收支记录
            var incomedisburseForm = metaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            linkFormDatas.Add(new Dictionary<string, object>
            {
                { "formId", incomedisburseForm.Id },
                { "formCaption", incomedisburseForm.Caption },
                { "flag", "nextForm" },
                { "filterString", filterStr }
            });
        }
    }
}