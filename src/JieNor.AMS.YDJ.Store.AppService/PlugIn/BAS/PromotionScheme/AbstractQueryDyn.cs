using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.PromotionScheme
{
    public abstract class AbstractQueryDyn : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case "onAfterParseFilterString":
                    this.OnAfterParseFilterString(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 处理基础资料字段过滤条件解析后事件逻辑
        /// </summary>
        /// <param name="e"></param>
        private void OnAfterParseFilterString(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Tuple<string, string>;
            var fieldKey = eventData.Item1?.ToLowerInvariant(); //基础资料字段标识
            var fieldFilter = eventData.Item2; //基础资料字段过滤条件
            var formid = this.HtmlForm.Id;
            switch (fieldKey)
            {
                case "fpromotionid":
                    {
                        var fdeliverid = this.GetQueryOrSimpleParam<string>("fdeliverid", "");
                        // 仅能搜索总部商品
                        if (!this.Context.IsTopOrg)
                        {
                            ////如果有子经销商，那么子经销商的数据也要看到
                            ////更新促销相关信息
                            //var commonCombine = new Model.PurchaseOrder.CommonCombine(this.Context, this.Context.Container.GetService<IDBService>());
                            //string where = commonCombine.GetPromotionSQL(this.Context, fdeliverid);
                            //where = where.Replace("between fbegindate and DATEADD(day,1,fenddate)", "between t0.fbegindate and DATEADD(day,1,t0.fenddate)");

                            string where = !string.IsNullOrWhiteSpace(fdeliverid) ? $" AND ( fdeliverid='{fdeliverid}' or a.fsaleorgid=(select T_BAS_DELIVER.fsaleorgid from T_BAS_DELIVER where fid='{fdeliverid}'))" : "";
                            if (!string.IsNullOrWhiteSpace(fdeliverid))
                            {
                                e.Result = ($@" fid in ( select distinct fpromotionid from t_ydj_fcombinerangeentry a with(nolock) join 
																			t_bas_promotioncombine b with(nolock) on a.fid=b.fid where fagentid='{this.Context.Company}' and fisall=0 {where}
                                                                            union
                                                                            select distinct b.fpromotionid from t_ydj_fcombinerangeentry a with(nolock) 
                                                                            join  t_bas_promotioncombine b with(nolock) on a.fid=b.fid
                                                                            join t_bas_macentry c with(nolock) on a.fagentid=c.fsubagentid
                                                                            join t_bas_mac d with(nolock) on c.fid=d.fid
                                                                            join T_BAS_AGENT e  with(nolock) on d.fnumber=e.fnumber
                                                                            where e.fid='{this.Context.Company}' and fisall=0 {where}
                                                                            union 
                                                                            select distinct a.fpromotionid from t_bas_promotioncombine a with(nolock) 
                                                                           	join t_ydj_fcombinerangeentry b with(nolock) on a.fid=b.fid 
                                                                           	where fisall=1 and b.fsaleorgid in ( select  a.fsaleorgid from T_BAS_DELIVER a with(nolock)
                                                                                              join t_bas_organization b with(nolock) on a.fsaleorgid=b.fid
                                                                                              join T_BAS_AGENT c with(nolock) on a.fagentid=c.fid
                                                                                              where  c.fid='{this.Context.Company}' and a.fforbidstatus=0   {(!string.IsNullOrWhiteSpace(fdeliverid) ? $" AND a.fid='{fdeliverid}'" : "")}
                                                                                              union
                                                                                              select a.fsaleorgid  from
	                                                                                          T_BAS_DELIVER a with(nolock)
                                                                                              join t_bas_organization b with(nolock) on a.fsaleorgid=b.fid
                                                                                              join T_BAS_AGENT c with(nolock) on a.fagentid=c.fid
	                                                                                          join t_bas_macentry e with(nolock) on c.fid=e.fsubagentid
	                                                                                          join t_bas_mac d with(nolock) on e.fid=d.fid
	                                                                                          join T_BAS_AGENT f with(nolock) on d.fnumber=f.fnumber
                                                                                              where  f.fid='{this.Context.Company}' and a.fforbidstatus=0   {(!string.IsNullOrWhiteSpace(fdeliverid) ? $" AND a.fid='{fdeliverid} '" : "")}
                                                                            )
                                                                          ) ");
                            }
                            e.Cancel = true;
                        }
                    }
                    break;
            }
        }
    }
}
