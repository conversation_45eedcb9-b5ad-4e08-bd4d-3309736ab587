using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.BankNum
{
    [InjectService]
    [FormId("ydj_banknum")] //银行账号添加 保存
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            // 验证开户行必录
            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((n, o) =>
            {
                var faddress = Convert.ToString(n["fbankid"]);
                if (faddress.IsNullOrEmptyOrWhiteSpace()) return false;
                return true;
            }).WithMessage("开户行不能为空"));

            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((n, o) =>
            {
                if (this.Context.IsDirectSale)
                {
                    var isrefundbanknum = Convert.ToBoolean(n["fisrefundbanknum"]);
                    var fopbankid = Convert.ToString(n["fopbankid"]);
                    if (isrefundbanknum)
                    {
                        if (string.IsNullOrWhiteSpace(fopbankid))
                        {
                            return false;
                        }
                    }
                }
                return true;
            }).WithMessage("请填写【开户网点】信息"));
        }
    }
}
