using JieNor.AMS.YDJ.Core;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.PromotionCombine
{
    /// <summary>
    /// 促销活动：编辑
    /// </summary>
    [InjectService]
    [FormId("bas_promotioncombine")]
    [OperationNo("refresh")]
    public class Refresh : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                return;
            }
            if (this.Context.BizOrgId == this.Context.TopCompanyId)
            {
                return;
            }

            List<string> materials = new List<string>();
            //存在明细行字段，拼接明细行条件
            var agents = new ProductDataIsolateHelper().GetCurrentUserAgentInfos(this.Context);
            var para = new DataQueryRuleParaInfo() { SrcFormId = this.HtmlForm.Id };
            var pros = this.Context.LoadBizDataByFilter("bas_deliver", string.Format(" fforbidstatus = 0 and fagentid='{0}'", this.Context.BizOrgId));
            foreach (var item in pros)
            {
                para.SrcPara["deliverid"] = Convert.ToString(item["id"]);
                var productIds = new ProductDataIsolateHelper().GetAuthProductDataPKID(Context, para, agents);
                materials.AddRange(productIds);
            }
            List<DynamicObject> removeitem = new List<DynamicObject>();
            List<DynamicObject> removeitem1 = new List<DynamicObject>();
            var entrys = e.DataEntitys[0]["fcombineentry"] as DynamicObjectCollection;
            foreach (var item in entrys)
            {
                if (materials.IndexOf(Convert.ToString(item["fproductid"])) < 0)
                {//不存在，移除行
                    removeitem.Add(item);
                }
            }
            var rangeentry = e.DataEntitys[0]["frangeentry"] as DynamicObjectCollection;

            foreach (var item in rangeentry)
            {
                if (string.IsNullOrWhiteSpace(Convert.ToString(item["fdeliverid"]))) continue;
                if (!pros.Any(a=>a["id"].Equals(item["fdeliverid"])))
                {//不存在，移除行
                    removeitem1.Add(item);
                }
            }
            removeitem.ForEach(a => entrys.Remove(a));
            removeitem1.ForEach(a => rangeentry.Remove(a));


            //var bill = e.DataEntitys[0];
            //var fmainorgid = bill["fmainorgid"]?.ToString();
            //var fnumber = bill["fnumber"]?.ToString();
            //if (bill.DataEntityState.FromDatabase && !fmainorgid.EqualsIgnoreCase(this.Context.Company))
            //{
            //    throw new Exception($@"所选{this.HtmlForm.Caption}【{fnumber}】是总部授权的{this.HtmlForm.Caption}，只能列表查看，不能进行编辑！");
            //}
        }


    }



}

