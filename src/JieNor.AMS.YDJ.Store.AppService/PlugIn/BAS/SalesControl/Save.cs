using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.SalesControl
{
    /// <summary>
    /// 销售人员可销控制：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_salescontrol")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var msg = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var resutl = true;
                var formIds = newData["fbizobject"]?.ToString(); 
                if (formIds.IsNullOrEmptyOrWhiteSpace ()) 
                { 
                    return resutl;
                }

                var lst = formIds.SplitKey();
                foreach (var formId in lst)
                {
                    HtmlForm formInfo = this.MetaModelService.LoadFormModel(this.Context, formId);
                    if(formInfo ==null )
                    {
                        continue;
                    }

                    if(formInfo.ElementType != Framework.DataTransferObject.HtmlElementType.HtmlForm_BillForm)
                    {
                        msg += "选择的业务对象【{0}】业务单据，不允许选择，请重新设置控制的业务对象！".Fmt(formInfo.Caption) + Environment.NewLine;
                        resutl = false;
                    }

                    var stockFlds = formInfo.GetFieldList().ToList().Where(f => f is HtmlBaseDataField && "ydj_storehouse".EqualsIgnoreCase((f as HtmlBaseDataField)?.RefFormId))?.ToList();
                    if (stockFlds == null || stockFlds.Count == 0)
                    {
                        msg += "选择的业务对象【{0}】中不包含仓库字段，不允许选择，请重新设置控制的业务对象！".Fmt (formInfo.Caption) + Environment.NewLine;
                        resutl= false;
                    }

                }                 
                return resutl;
            }).WithMessage("{0}", (billObj, propObj) => msg));


        }

        /// <summary>
        /// 执行操作事务前事件，通知插件对要处理的数据进行排序等预处理
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            //移除空行
            for (int i = 0; i < e.DataEntitys.Length; i++)
            {
                //要移除的明细列表
                List<DynamicObject> toRemoveList = new List<DynamicObject>();
                var fentity = e.DataEntitys[i]["fentity"] as DynamicObjectCollection;
                if (fentity!=null&&fentity.Any())
                {
                    for (int j = 0; j < fentity.Count; j++)
                    {
                        var house = Convert.ToString(fentity[j]["fhouseid"]);
                        if (house.IsNullOrEmptyOrWhiteSpace())//空行移除
                        {
                            toRemoveList.Add(fentity[j]);
                        }                        
                    }
                    foreach (var item in toRemoveList)
                    {
                        fentity.Remove(item);
                    }
                }
            }

        }



        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            //清除缓存 
            var pubSubService = this.Container.GetService<IPubSubService>();
            pubSubService.PublishMessage<string >(ConstPubSubChannel.SakesControl, " ");
        }

    }
}
