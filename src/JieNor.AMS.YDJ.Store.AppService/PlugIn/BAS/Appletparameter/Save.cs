using System;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using System.Data;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Appletparameter
{
    /// <summary>
    /// 会员版小程序参数：保存
    /// </summary>
    [InjectService]
    [FormId("bas_appletparameter")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            //验证 [小程序名称]字段不为空
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //根据当前的按钮选择来选择是否验证
                var isTrue = Convert.ToBoolean(newData["fopenapplet"]);
                if (isTrue)
                {
                    string fappletname = Convert.ToString(newData["fappletname"]);
                    if (fappletname.IsNullOrEmptyOrWhiteSpace()) return false;
                }
                return true;
            }).WithMessage("小程序名称 不能为空,请检查数据！"));

            //验证[AppID] 字段不为空
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //根据当前的按钮选择来选择是否验证
                var isTrue = Convert.ToBoolean(newData["fopenapplet"]);
                if (isTrue)
                {
                    string fappletid = Convert.ToString(newData["fappletid"]);
                    if (fappletid.IsNullOrEmptyOrWhiteSpace()) return false;
                }
                return true;
            }).WithMessage("AppID 不能为空,请检查数据！"));

            //验证[AppSecret] 字段不为空
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //根据当前的按钮选择来选择是否验证
                var isTrue = Convert.ToBoolean(newData["fopenapplet"]);
                if (isTrue)
                {
                    string fappsecret = Convert.ToString(newData["fappsecret"]);
                    if (fappsecret.IsNullOrEmptyOrWhiteSpace()) return false;
                }
                return true;
            }).WithMessage("AppSecret 不能为空,请检查数据！"));

            //验证[会员权益] 字段不为空
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //根据当前的按钮选择来选择是否验证
                var isTrue = Convert.ToBoolean(newData["fopenapplet"]);
                if (isTrue)
                {
                    string fcontent = Convert.ToString(newData["fcontent"]);
                    if (fcontent.IsNullOrEmptyOrWhiteSpace()) return false;
                }
                return true;
            }).WithMessage("会员权益 不能为空,请检查数据！"));
        }

        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null) return;
            foreach (var dataEntity in e.DataEntitys)
            {
                //如果没打开这个开关  则清空字段值
                if (!Convert.ToBoolean(dataEntity["fopenapplet"]))
                {
                    dataEntity["fappletname"] = string.Empty;
                    dataEntity["fappletid"] = string.Empty;
                    dataEntity["fappsecret"] = string.Empty;
                    dataEntity["fcontent"] = null;
                }
            }
        }
    }
}
