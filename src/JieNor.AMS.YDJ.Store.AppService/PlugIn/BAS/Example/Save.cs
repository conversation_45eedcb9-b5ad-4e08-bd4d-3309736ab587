using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.MetaCore.FormOp;

namespace JieNor.AMS.YDJ.Store.AppService.PlugIn.BAS.Example
{
    /// <summary>
    /// 示例模型：保存
    /// </summary>
    [InjectService]
    [FormId("bas_example")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        #region 后端插件常用事件

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义校验规则
            */

            //方式一：简单校验
            e.Rules.Add(this.RuleFor("fbillhead", data => data["ftext"]).NotEmpty().WithMessage("文本字段不能为空！"));

            //方式二：处理复杂的校验逻辑
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //进行相关业务逻辑的校验，返回 false 表示验证不通过......

                return true;

            }).WithMessage("文本字段不能为空！"));

            //方式三：在错误消息中显示某些字段值
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //进行相关业务逻辑的校验，返回 false 表示验证不通过......

                return true;

            }).WithMessage("编号为【{0}】文本字段不能为空！", (billObj, propObj) => billObj["fbillno"]));

            //方式四：根据不同的校验逻辑提示不同的错误信息
            var errorMsg = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //进行相关业务逻辑的校验，返回 false 表示验证不通过......

                //此处可以根据不同的校验逻辑提示不同的错误信息
                errorMsg = "";

                return true;

            }).WithMessage("{0}", (billObj, propObj) => errorMsg));
        }

        /// <summary>
        /// 执行操作事务前事件，通知插件对要处理的数据进行排序等预处理
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

        }

        /// <summary>
        /// 执行操作事务后事件，通知插件对象执行其它事务无关的业务逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

        }

        #endregion

        #region 后端插件不常用事件

        /// <summary>
        /// 初始化操作数据上下文
        /// </summary>
        /// <param name="e"></param>
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {

        }

        /// <summary>
        /// 初始化服务插件事件
        /// </summary>
        /// <param name="operCtx"></param>
        protected override void InitializeServicePlugIn(OperationContext operCtx)
        {

        }

        /// <summary>
        /// 权限检测
        /// </summary>
        /// <param name="e"></param>
        public override void OnCheckPermssion(OnCheckPermssionArgs e)
        {

        }

        /// <summary>
        /// 准备操作选项
        /// </summary>
        /// <param name="e"></param>
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {

        }

        /// <summary>
        /// 准备操作关联服务
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareBusinessServices(PrepareBusinessServiceEventArgs e)
        {

        }

        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {

        }

        #endregion
    }
}