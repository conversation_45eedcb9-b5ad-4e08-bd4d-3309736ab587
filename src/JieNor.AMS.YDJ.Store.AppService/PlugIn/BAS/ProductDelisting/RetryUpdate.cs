using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.AMS.YDJ.MS.API.DTO.ProductDelisting;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.ProductDelisting
{
    /// <summary>
    /// 初始数据更新重试
    /// </summary>
    [InjectService]
    [FormId("ydj_productdelisting")]
    [OperationNo("RetryUpdate")]
    public class RetryUpdate : AbstractOperationServicePlugIn
    {
        public async override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;
            var delistDatas = new List<DynamicObject>();
            var productDelistings = new List<DynamicObject>();
            var topCtx = this.Context.CreateTopOrgDBContext();
            var refObjMgr = topCtx.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(topCtx, e.DataEntitys, true, this.HtmlForm, new List<string> { "fmaterialid", "fpropvalueid", "fseltypeid" });
            foreach (var delistObj in e.DataEntitys)
            {
                //Task task = new Task(() =>
                //{
                Dictionary<string, object> dic = new Dictionary<string, object>();
                ProductDelistingPushDTO dTO = new ProductDelistingPushDTO();
                var datas = new List<MS.API.DTO.ProductDelisting.ProductDelistingPush>();
                var _entry = (delistObj["fentity"] as DynamicObjectCollection)
                .Where(b => Convert.ToBoolean(b["fenable"])).FirstOrDefault();
                var _propentry = (delistObj["fpropentry"] as DynamicObjectCollection).FirstOrDefault();
                MS.API.DTO.ProductDelisting.ProductDelistingPush dtoItem = new MS.API.DTO.ProductDelisting.ProductDelistingPush();
                dtoItem.Id = Convert.ToString(delistObj["fhqid"]);
                dtoItem.EstimateQty = Convert.ToInt32(_entry["festimateqty"]);
                dtoItem.WarnQty = Convert.ToInt32(_entry["fwarnqty"]);
                dtoItem.HqPushTime = Convert.ToString(_entry["fhqpushtime"]);
                dtoItem.specmaterial = Convert.ToString(delistObj["fspecmaterial"]);
                dtoItem.ProductNo = Convert.ToString((delistObj["fmaterialid_ref"] as DynamicObject)?["fnumber"]);
                dtoItem.ProductName = Convert.ToString((delistObj["fmaterialid_ref"] as DynamicObject)?["fname"]);
                dtoItem.SelType = Convert.ToString((delistObj["fseltypeid_ref"] as DynamicObject)?["fnumber"]);
                dtoItem.Color = Convert.ToString((_propentry?["fpropvalueid_ref"] as DynamicObject)?["fnumber"]);
                datas.Add(dtoItem);
                dTO.Data = datas;
                dic.Add("dto", JsonConvert.SerializeObject(dTO));
                dic.Add("source", "retry");
                this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new[] { delistObj }, "ProductDelistingPush", dic);
                //Task.Delay(5000);
                //ProductDelistingHelper.UpdateDelistingByPush(this.Context, delistObj, ref productDelistings);
                //delistDatas.Add(delistObj);

                ////保存数据
                //if (delistDatas.Any())
                //{
                //topCtx.SaveBizData(this.HtmlForm.Id, delistObj);
                //topCtx.SaveBizData(this.HtmlForm.Id, productDelistings);
                //this.Context.SaveBizData(FormId, delistDatas);
                //var saveResult = this.Gateway.InvokeBillOperation(this.Context, "ydj_productdelisting", delistDatas, "save", null);
                //var saveResult1 = this.Gateway.InvokeBillOperation(this.Context, "ydj_productdelisting", productDelistings, "save", null);
                //saveResult.ThrowIfHasError(true, "退市产品初始化更新失败！");
                //saveResult1.ThrowIfHasError(true, "退市产品关联初始化更新失败！");
                //标记商品
                var allProductIds = delistDatas.Where(d => !Convert.ToString(d["fmaterialid"]).IsNullOrEmptyOrWhiteSpace()).Select(t => Convert.ToString(t["fmaterialid"])).Distinct().ToList();
                var allSelTypeIds = delistDatas.Where(d => !Convert.ToString(d["fseltypeid"]).IsNullOrEmptyOrWhiteSpace()).Select(t => Convert.ToString(t["fseltypeid"])).Distinct().ToList();
                ProductDelistingHelper.MarkProductDelisting(this.Context, allProductIds, allSelTypeIds);
                //}
                //});
                //ThreadWorker.QuequeTask(task, x =>
                //{
                //    if (x?.Exception != null)
                //    {
                //        throw new BusinessException("执行更新[初始数据更新重试]失败！");
                //    }
                //});
            }

            //foreach (var delistObj in e.DataEntitys)
            //{
            //    Dictionary<string, object> dic = new Dictionary<string, object>();
            //    ProductDelistingPushDTO dTO = new ProductDelistingPushDTO();
            //    var datas = new List<MS.API.DTO.ProductDelisting.ProductDelistingPush>();
            //    var _entry = (delistObj["fentity"] as DynamicObjectCollection)
            //    .Where(b => Convert.ToBoolean(b["fenable"])).FirstOrDefault();
            //    var _propentry = (delistObj["fpropentry"] as DynamicObjectCollection).FirstOrDefault();
            //    MS.API.DTO.ProductDelisting.ProductDelistingPush dtoItem = new MS.API.DTO.ProductDelisting.ProductDelistingPush();
            //    dtoItem.Id = Convert.ToString(delistObj["fhqid"]);
            //    dtoItem.EstimateQty = Convert.ToInt32(_entry["festimateqty"]);
            //    dtoItem.WarnQty = Convert.ToInt32(_entry["fwarnqty"]);
            //    dtoItem.HqPushTime = Convert.ToString(_entry["fhqpushtime"]);
            //    dtoItem.specmaterial = Convert.ToString(delistObj["fspecmaterial"]);
            //    dtoItem.ProductNo = Convert.ToString((delistObj["fmaterialid_ref"] as DynamicObject)?["fnumber"]);
            //    dtoItem.ProductName = Convert.ToString((delistObj["fmaterialid_ref"] as DynamicObject)?["fname"]);
            //    dtoItem.SelType = Convert.ToString((delistObj["fseltypeid_ref"] as DynamicObject)?["fnumber"]);
            //    dtoItem.Color = Convert.ToString((_propentry?["fpropvalueid_ref"] as DynamicObject)?["fnumber"]);
            //    datas.Add(dtoItem);
            //    dTO.Data = datas;
            //    dic.Add("dto", JsonConvert.SerializeObject(dTO));
            //    dic.Add("source", "retry");
            //    this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new[] { delistObj }, "ProductDelistingPush", dic);
            //    //标记商品
            //    var allProductIds = delistDatas.Where(d => !Convert.ToString(d["fmaterialid"]).IsNullOrEmptyOrWhiteSpace()).Select(t => Convert.ToString(t["fmaterialid"])).Distinct().ToList();
            //    var allSelTypeIds = delistDatas.Where(d => !Convert.ToString(d["fseltypeid"]).IsNullOrEmptyOrWhiteSpace()).Select(t => Convert.ToString(t["fseltypeid"])).Distinct().ToList();
            //    ProductDelistingHelper.MarkProductDelisting(this.Context, allProductIds, allSelTypeIds);
                
            //}


        }
    }
}
