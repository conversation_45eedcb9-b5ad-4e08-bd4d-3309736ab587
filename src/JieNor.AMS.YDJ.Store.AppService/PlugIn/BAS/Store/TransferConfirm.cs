using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Store;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Store
{
    /// <summary>
    /// 门店：转让确认
    /// </summary>
    [InjectService]
    [FormId("bas_store")]
    [OperationNo("TransferConfirm")]
    public class TransferConfirm : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 初始化服务插件上下文时触发的事件
        /// </summary>
        /// <param name="operCtx"></param>
        /// <param name="serviceList"></param>
        public new void InitializeOperationContext(OperationContext operCtx, params object[] serviceList)
        {
            // 启用幂等性检查
            var serCtrlOpt = serviceList.FirstOrDefault(o => o is ServiceControlOption) as ServiceControlOption;
            serCtrlOpt.SupportIdemotency = true;

            base.InitializeOperationContext(operCtx, serviceList);
        }

        /// <summary>
        /// 初始化服务插件时触发的事件
        /// </summary>
        /// <param name="operCtx"></param>
        protected override void InitializeServicePlugIn(OperationContext operCtx)
        {
            base.InitializeServicePlugIn(operCtx);

            // 此处忽略操作日志（无意义），在转让确认成功时再手工记录操作日志
            operCtx.Option.SetIgnoreOpLogFlag();
        }

        /// <summary>
        /// 准备操作选项时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);

            e.OpCtlParam.IgnoreOpMessage = true;
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            var storeId = this.GetQueryOrSimpleParam<string>("storeId");
            var agentId = this.GetQueryOrSimpleParam<string>("agentid");
            var isTransferAdmin = this.GetQueryOrSimpleParam<string>("istransferadmin")=="1";

            if (storeId.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.ComplexMessage.WarningMessages.Add($"请选择门店！");
                return;
            }
            if (agentId.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.ComplexMessage.WarningMessages.Add($"请选择二级分销商！");
                return;
            }

            // 校验二级分销商
            var agentForm = this.MetaModelService.LoadFormModel(this.Context, "bas_agent");
            var agentObj = this.Context.LoadBizBillHeadDataById(agentForm.Id, agentId, "fnumber,fname,fisreseller,fstatus");
            if (agentObj == null)
            {
                this.Result.ComplexMessage.WarningMessages.Add(
                    $"{agentForm.Caption}ID【{agentId}】不存在或已被删除，无法转让！");
                return;
            }
            if (Convert.ToString(agentObj["fisreseller"]) != "1")
            {
                this.Result.ComplexMessage.WarningMessages.Add(
                    $"{agentForm.Caption}【{agentObj["fnumber"]}/{agentObj["fname"]}】不是分销商，无法转让！");
                return;
            }
            if (!Convert.ToString(agentObj["fstatus"]).EqualsIgnoreCase("E"))
            {
                this.Result.ComplexMessage.WarningMessages.Add(
                    $"{agentForm.Caption}【{agentObj["fnumber"]}/{agentObj["fname"]}】未审核，无法转让！");
                return;
            }

            // 校验门店
            var storeObj = this.Context.LoadBizDataById(this.HtmlForm.Id, storeId);
            if (storeObj == null)
            {
                this.Result.ComplexMessage.WarningMessages.Add(
                    $"{this.HtmlForm.Caption}ID【{storeId}】不存在或已被删除，无法转让！");
                return;
            }
            if (!Convert.ToString(storeObj["fstatus"]).EqualsIgnoreCase("E"))
            {
                this.Result.ComplexMessage.WarningMessages.Add(
                    $"{this.HtmlForm.Caption}【{storeObj["fnumber"]}/{storeObj["fname"]}】未审核，不允许转让！");
                return;
            }
            if (Convert.ToBoolean(storeObj["fistransfer"]))
            {
                this.Result.ComplexMessage.WarningMessages.Add(
                    $"{this.HtmlForm.Caption}【{storeObj["fnumber"]}/{storeObj["fname"]}】已经被转让，不允许重复转让！");
                return;
            }
            if (!isTransferAdmin&&!storeObj["fsrcstoreid"].IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.ComplexMessage.WarningMessages.Add(
                    $"{this.HtmlForm.Caption}【{storeObj["fnumber"]}/{storeObj["fname"]}】是由一级经销商转让的，不允许重复转让！");
                return;
            }
            if (!isTransferAdmin) 
            { 
                var targetStoreObj = this.LoadStoreBySrcStoreIds(storeId);
                if (targetStoreObj != null)
                {
                    this.Result.ComplexMessage.WarningMessages.Add(
                        $"{this.HtmlForm.Caption}【{storeObj["fnumber"]}/{storeObj["fname"]}】，" +
                        $"已经转让给二级分销商【{targetStoreObj["fagentnumber"]}/{targetStoreObj["fagentname"]}】，" +
                        $"二级分销商的门店为【{targetStoreObj["fnumber"]}/{targetStoreObj["fname"]}】，不允许重复转让！");
                    return;
                }
            }

            // 新增一个二级分销商企业下的门店（操作不可逆）
            var newStoreObj = this.CreateTargetStore(storeObj, agentId);
            var newStoreId = Convert.ToString(newStoreObj["id"]);
            var newAgentNumber = Convert.ToString(agentObj["fnumber"]);
            var newAgentName = Convert.ToString(agentObj["fname"]);
            var newStoreNumber = Convert.ToString(newStoreObj["fnumber"]);
            //上次最后一次门店转让的经销商
            var LastAgentId = Convert.ToString(storeObj["fagentid"]);
            var LastAgentObj = this.Context.LoadBizBillHeadDataById(agentForm.Id, LastAgentId, "fnumber,fname,fisreseller,fstatus");
            var LastAgentNumber = Convert.ToString(LastAgentObj["fnumber"]);
            var LastAgentName = Convert.ToString(LastAgentObj["fname"]);

            // 将当前门店标记为“已转让”
            storeObj["fistransfer"] = true;
            storeObj["ftargetstoreid"] = newStoreId;
            storeObj["ftargetagentid"] = agentId;

            // 将当前门店禁用
            // 直接改禁用状态，因为标准禁用操作有各种校验逻辑
            //storeObj["fforbidid"] = this.Context.UserId;
            //storeObj["fforbidstatus"] = true;
            //storeObj["fforbiddate"] = DateTime.Now;

            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(storeObj);

            //一级经销商 门店id。
            var topstoreid = this.GetQueryOrSimpleParam<string>("topstoreid");
            var topstoreObj = this.Context.LoadBizDataById(this.HtmlForm.Id, topstoreid);
            topstoreObj["ftargetstoreid"] = newStoreId;
            topstoreObj["ftargetagentid"] = agentId;
            //更新当前门店的目标经销商、目标门店
            dm.Save(topstoreObj);
            //一级经销商门店超级转让需要更新 最新转让的目标经销商到 一级经销商的目标经销商字段上。
            var Msgstr = isTransferAdmin ? "超级转让":"转让"; 

            var logServiceEx = this.Container.GetService<ILogServiceEx>();
            logServiceEx.SyncBatchWriteLog(this.Context, new LogEntry[]
            {
                new LogEntry()
                {
                    BillFormId = this.HtmlForm.Id,
                    OpCode = "TransferConfirm",
                    OpName = "转让确认",
                    BillIds = Convert.ToString(topstoreObj["id"]),
                    BillNos = Convert.ToString(topstoreObj["fnumber"]),
                    Level = Enu_LogLevel.Info.ToString(),
                    LogType = Enu_LogType.RecordType_03,
                    Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                    Content = $"执行了【转让确认】操作！{Msgstr}到分销商名称：{newAgentName}，编码：{newAgentNumber}",
                    Detail = ""
                }
            });

            // 转让门店的授权清单给新门店
            this.TransferStoreProductAuth(storeId, newStoreId);

            // 禁用门店关联的部门
            //this.ForbidDepartment(storeId);

            // 禁用门店关联的组织
            this.ForbidOrg(storeId);

            // 记录操作日志
            this.WriteLog(storeObj);

            this.Result.ComplexMessage.SuccessMessages.Add($"{this.HtmlForm.Caption}【{storeObj["fnumber"]}】转让成功！");
            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 新增一个二级分销商企业下的门店（操作不可逆）
        /// </summary>
        /// <param name="storeObj"></param>
        /// <param name="agentId">二级分销商ID</param>
        /// <returns></returns>
        private DynamicObject CreateTargetStore(DynamicObject storeObj, string agentId)
        {
            var newStoreObj = new DynamicObject(this.HtmlForm.GetDynamicObjectType(this.Context));

            // 将一级经销商的门店数据复制到二级经销商新门店中
            storeObj.CopyTo(newStoreObj, null, true, false, false,
                (newObj, oldObj) =>
                {

                },
                (dtName, oldObj, newObjs) =>
                {
                    return true;
                });
            newStoreObj.GetDataEntityType().SetDirty(newStoreObj, true);

            // 重置不需要复制的字段值
            var allFlds = this.HtmlForm.GetFieldList().ToList();
            var flds = this.HtmlForm.HeadEntity.GetFieldList(allFlds).Where(f => f.CanCopy == 0).ToList();
            foreach (var fld in flds)
            {
                fld?.DynamicProperty?.ResetValue(newStoreObj);
            }

            // 将新门店企业ID设置为二级经销商对应的企业ID，经销商ID其实就是企业ID
            newStoreObj["fnumber"] = storeObj["fnumber"];
            newStoreObj["fmainorgid"] = agentId;
            newStoreObj["fsrcstoreid"] = storeObj["id"];
            newStoreObj["fsrcagentid"] = this.Context.Company;
            newStoreObj["forgid"] = agentId; //所属上级组织为“当前要转让的二级经销商”
            newStoreObj["fagentid"] = agentId; //经销商为“当前要转让的二级经销商”
            newStoreObj["ftranid"] = "";
            newStoreObj["fparenttranid"] = "";
            newStoreObj["ftoptranid"] = "";

            var newStoreArray = new DynamicObject[] { newStoreObj };

            // 以二级经销商企业身份创建门店
            var agentCtx = this.Context.CreateAgentDBContext(agentId);

            // 填充字段默认值
            var defCalulator = this.Container.GetService<IDefaultValueCalculator>();
            defCalulator.Execute(agentCtx, this.HtmlForm, newStoreArray);

            // 保存前预处理
            var preService = this.Container.GetService<IPrepareSaveDataService>();
            preService.PrepareDataEntity(agentCtx, this.HtmlForm, newStoreArray, OperateOption.Create());

            // 调用标准的保存操作
            var saveResult = this.Gateway.InvokeBillOperation(
                agentCtx,
                this.HtmlForm.Id,
                newStoreArray,
                "save",
                new Dictionary<string, object>());
            saveResult.ThrowIfHasError(true, $"调用{this.HtmlForm.Caption}保存失败！");

            // 调用标准的提交操作
            var submitResult = this.Gateway.InvokeBillOperation(
                agentCtx,
                this.HtmlForm.Id,
                newStoreArray,
                "submit",
                new Dictionary<string, object>());
            submitResult.ThrowIfHasError(true, $"调用{this.HtmlForm.Caption}提交失败！");

            // 调用标准的审核操作
            var auditResult = this.Gateway.InvokeBillOperation(
                agentCtx,
                this.HtmlForm.Id,
                newStoreArray,
                "audit",
                new Dictionary<string, object> 
                {
                    { "FromStoreTransfer", true }
                });
            auditResult.ThrowIfHasError(true, $"调用{this.HtmlForm.Caption}审核失败！");

            if (auditResult.IsSuccess)
            {
                //门店转让生成二级分销门店及部门时，对应生成一级部门。
                var _result = StoreHelper.CreateDeptBySubStore(this.Context, newStoreObj, this.Option);
                this.Result.MergeResult(_result);
            }

            return newStoreObj;
        }

        /// <summary>
        /// 禁用门店关联的部门
        /// </summary>
        /// <param name="storeId">门店ID</param>
        private void ForbidDepartment(string storeId)
        {
            var sqlText = $@"
            select fid from t_bd_department with(nolock) 
            where fmainorgid=@fmainorgid and fstore=@fstore";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("@fstore", System.Data.DbType.String, storeId)
            };

            var deptIds = this.DBService
                .ExecuteDynamicObject(this.Context, sqlText, sqlParam)
                .Select(o => o["fid"]);

            if (!deptIds.Any()) return;

            var deptForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_dept");
            var deptDm = this.GetDataManager();
            deptDm.InitDbContext(this.Context, deptForm.GetDynamicObjectType(this.Context));

            var deptObjs = deptDm.Select(deptIds).OfType<DynamicObject>().ToArray();
            if (deptObjs == null && !deptObjs.Any()) return;

            // 调用标准的禁用操作
            //var forbidResult = this.Gateway.InvokeBillOperation(
            //    this.Context,
            //    deptForm.Id,
            //    deptObjs,
            //    "forbid",
            //    new Dictionary<string, object>());
            //forbidResult.ThrowIfHasError(true, $"调用{deptForm.Caption}禁用失败！");

            // 直接改禁用状态，因为标准禁用操作有各种校验逻辑
            foreach (var deptObj in deptObjs)
            {
                deptObj["fforbidid"] = this.Context.UserId;
                deptObj["fforbidstatus"] = true;
                deptObj["fforbiddate"] = DateTime.Now;
            }

            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, deptForm.GetDynamicObjectType(this.Context));
            dm.Save(deptObjs);
        }

        /// <summary>
        /// 禁用门店关联的组织
        /// </summary>
        /// <param name="storeId">门店ID</param>
        private void ForbidOrg(string storeId)
        {
            var orgForm = this.MetaModelService.LoadFormModel(this.Context, "bas_organization");

            var orgObj = this.Context.LoadBizDataById(orgForm.Id, storeId);
            if (orgObj == null) return;

            // 直接改禁用状态，因为标准禁用操作有各种校验逻辑
            orgObj["fforbidid"] = this.Context.UserId;
            orgObj["fforbidstatus"] = true;
            orgObj["fforbiddate"] = DateTime.Now;

            var orgDm = this.GetDataManager();
            orgDm.InitDbContext(this.Context, orgForm.GetDynamicObjectType(this.Context));
            orgDm.Save(orgObj);
        }

        /// <summary>
        /// 转让门店的授权清单给新门店
        /// </summary>
        /// <param name="storeId">旧门店ID</param>
        /// <param name="newStoreId">新门店ID</param>
        private void TransferStoreProductAuth(string storeId, string newStoreId)
        {
            var sqlText = $@"select fid from t_ydj_productauth with(nolock) where forgid=@forgid";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@forgid", System.Data.DbType.String, storeId)
            };

            var productAuthIds = this.DBService
                .ExecuteDynamicObject(this.Context, sqlText, sqlParam)
                .Select(o => o["fid"]);

            if (!productAuthIds.Any()) return;

            var productAuthForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_productauth");
            var productAuthDm = this.GetDataManager();
            productAuthDm.InitDbContext(this.Context, productAuthForm.GetDynamicObjectType(this.Context));

            var productAuthObjs = productAuthDm.Select(productAuthIds).OfType<DynamicObject>().ToArray();
            if (productAuthObjs == null && !productAuthObjs.Any()) return;

            var newProductAuthObjs = new List<DynamicObject>();

            foreach (var productAuthObj in productAuthObjs)
            {
                var newProductAuthObj = new DynamicObject(productAuthForm.GetDynamicObjectType(this.Context));
                newProductAuthObjs.Add(newProductAuthObj);

                // 复制数据包
                productAuthObj.CopyTo(newProductAuthObj, null, true, false, false,
                    (newObj, oldObj) =>
                    {

                    },
                    (dtName, oldObj, newObjs) =>
                    {
                        return true;
                    });
                newProductAuthObj.GetDataEntityType().SetDirty(newProductAuthObj, true);

                // 重置不需要复制的字段值
                var allFlds = productAuthForm.GetFieldList().ToList();
                var flds = productAuthForm.HeadEntity.GetFieldList(allFlds).Where(f => f.CanCopy == 0).ToList();
                foreach (var fld in flds)
                {
                    fld?.DynamicProperty?.ResetValue(newProductAuthObj);
                }

                // 将新门店企业ID设置为二级经销商对应的企业ID，经销商ID其实就是企业ID
                newProductAuthObj["fnumber"] = productAuthObj["fnumber"];
                newProductAuthObj["fname"] = productAuthObj["fname"];
                newProductAuthObj["fmainorgid"] = this.Context.Company;
                newProductAuthObj["forgid"] = newStoreId;
            }

            var newArray = newProductAuthObjs.ToArray();

            // 填充字段默认值
            var defCalulator = this.Container.GetService<IDefaultValueCalculator>();
            defCalulator.Execute(this.Context, productAuthForm, newArray);

            // 保存前预处理
            var preService = this.Container.GetService<IPrepareSaveDataService>();
            preService.PrepareDataEntity(this.Context, productAuthForm, newArray, OperateOption.Create());

            // 调用标准的保存操作
            var saveResult = this.Gateway.InvokeBillOperation(
                this.Context,
                productAuthForm.Id,
                newArray,
                "save",
                new Dictionary<string, object>());
            saveResult.ThrowIfHasError(true, $"调用{productAuthForm.Caption}保存失败！");
        }

        /// <summary>
        /// 根据门店ID加载已转让过的门店数据包
        /// </summary>
        private DynamicObject LoadStoreBySrcStoreIds(string srcStoreId)
        {
            var sqlText = $@"
            select top 1 t.fnumber,t.fname,t2.fnumber fagentnumber,t2.fname fagentname from t_bas_store t with(nolock) 
            left join t_bas_agent t2 with(nolock) on t2.fid=t.fmainorgid
            where t.fsrcstoreid='{srcStoreId}'";

            var storeObj = this.DBService
                .ExecuteDynamicObject(this.Context, sqlText)
                .FirstOrDefault();

            return storeObj;
        }

        /// <summary>
        /// 记录操作日志
        /// </summary>
        private void WriteLog(DynamicObject storeObj)
        {
            var logServiceEx = this.Container.GetService<ILogServiceEx>();
            logServiceEx.SyncBatchWriteLog(this.Context, new LogEntry[]
            {
                new LogEntry()
                {
                    BillFormId = this.HtmlForm.Id,
                    OpCode = "TransferConfirm",
                    OpName = "转让确认",
                    BillIds = Convert.ToString(storeObj["id"]),
                    BillNos = Convert.ToString(storeObj["fnumber"]),
                    Level = Enu_LogLevel.Info.ToString(),
                    LogType = Enu_LogType.RecordType_03,
                    Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                    Content = $"执行了【转让确认】操作！",
                    Detail = ""
                }
            });
        }
    }
}
