using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{

     
        /// <summary>
        /// 商品操作的校验：经销商、门店用户不能删除、提交、审核、反审核总部创建的商品信息
        /// </summary>
    public    class ValidationOrgOperation : AbstractBaseValidation
    {

        public ValidationOrgOperation(string operationDesc)
        {
            OperationDesc = operationDesc;
        }

        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        public virtual string OperationDesc { get; private set; }
         
        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }


        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities==null || dataEntities.Length ==0)
            {
                return result;
            }

            foreach (var item in dataEntities)
            {
                var fbizorgid = item["fbizorgid"]?.ToString ();
                var fnumber = item["fnumber"]?.ToString();
                var fname = item["fname"]?.ToString();
                if (item.DataEntityState.FromDatabase && !fbizorgid.EqualsIgnoreCase(userCtx.Company))
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = @"商品【{0} {1}】是总部授权商品，只能查看，不能进行{2} ！".Fmt (fnumber,fname, OperationDesc),
                        DataEntity = item,                        
                    });
                }
            }
            return result;
        }
    }


}
