using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Leads
{
    ///// <summary>
    /// 销售线索：分配
    /// </summary>
    [InjectService]
    [FormId("ydj_leads")]
    [OperationNo("leadsclaim")]
    public class LeadsClaim: AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            //e.Rules.Add(this.RuleFor("fbillhead", (dataObj) => dataObj["fleadsstatus"] as string)
            //    .IsTrue((dataObj, leadStatus) => leadStatus.EqualsIgnoreCase("leads_status_01") || leadStatus.EqualsIgnoreCase("leads_status_02") && dataObj["fdutyid"].IsEmptyPrimaryKey())
            //    .WithMessage("线索分配失败：{0}，业务状态必须为【未分配】或【已分配】且负责人为空时才可分配！", (dataObj, leadStatus) => dataObj["fbillno"]));
            e.Rules.Add(this.RuleFor("fbillhead", (dataObj) => dataObj["fleadsstatus"] as string).IsTrue((dataObj, leadStatus) => leadStatus.EqualsIgnoreCase("leads_status_01")
             && dataObj["fdutyid"].IsEmptyPrimaryKey())
             .WithMessage("机会分配失败：{0}，业务状态必须为【未分配】且负责人为空时才可分配！",
             (dataObj, leadStatus) => dataObj["fbillno"]));

        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null)
            {
                throw new BusinessException("请至少选择一条有效线索进行此操作！");
            }

            if (e.DataEntitys.Length == 0) return;

            var responsorUserId = this.GetQueryOrSimpleParam<string>("responsorUserId");
            var responsorDeptId = this.GetQueryOrSimpleParam<string>("responsorDeptId");
            //负责人为空
            if(responsorUserId.IsEmptyPrimaryKey())
            {
                throw new BusinessException("分配失败：请指定分配对象负责人！");
            }
            var dm = this.GetDataManager();
            //如果负责人不为空
            if (!responsorUserId.IsEmptyPrimaryKey())
            {
                var staffMeta = this.MetaModelService.LoadFormModel(this.Context, "ydj_staff");                
                dm.InitDbContext(this.Context, staffMeta.GetDynamicObjectType(this.Context));
                var staffUserObj = dm.Select(responsorUserId) as DynamicObject;
                if (staffUserObj == null  || (bool)staffUserObj["fforbidstatus"])
                {
                    throw new BusinessException("分配失败：指定的负责人已被删除或禁用！");
                }

                var linkStaffDeptId = staffUserObj["fdeptid"] as string;
                //如果部门不为空
                if (!linkStaffDeptId.IsEmptyPrimaryKey())
                {
                    responsorDeptId = linkStaffDeptId;
                }
            }
           
            foreach (var dataEntity in e.DataEntitys)
            {                
                dataEntity["fdeptid"] = responsorDeptId;
                dataEntity["fdutyid"] = responsorUserId;
                dataEntity["fleadsstatus"] = "leads_status_02";
            }

            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(e.DataEntitys);
            this.AddRefreshPageAction();
            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "分配成功!";

        }
    }
}
