using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using System.Data;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Leads
{
    /// <summary>
    /// 移动端获取列表通用接口
    /// </summary>
    [InjectService("getReferenceFilters")]
    public class GetReferenceFilters: AbstractOperationService
    {
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            int pageIndex = this.GetQueryOrSimpleParam<int>("pageIndex", 0);
            int pageSize = this.GetQueryOrSimpleParam<int>("pageSize", 0);

            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, this.UserCtx.Company)
            };
            string countSql = "select count(1) from ({0}) as t";
            string pageDataSql = @"
select top {0} * from (
select row_number() over(order by {1}) as fpageindentity,t.* from (
{2}
) as t
) as tt where tt.fpageindentity>{3}
";

            string tabelSql = @"select fid as [id],{0} as [name],{1} as [number],{3} as [createdate] from {2} where fmainorgid=@fmainorgid";
            var htmlForm = this.OperationContext.HtmlForm;
            tabelSql = string.Format(tabelSql, htmlForm.NameFldKey, htmlForm.NumberFldKey, htmlForm.BillHeadTableName,htmlForm.CreateDateFldKey);

            bool isPage = false;
            int currentMaxPages = 0;
            int currentRecords = 0;

            if (pageIndex > 0 && pageSize > 0)
            {
                isPage = true;
                //获取当前表中实际的记录数
                countSql = string.Format(countSql, tabelSql);
                using (var countReader = this.DBService.ExecuteReader(this.UserCtx, countSql, sqlParams))
                {
                    if (countReader.Read())
                    {
                        currentRecords = Convert.ToInt32(countReader[0]);
                    }
                }
                //求出当前最大的页面数
                currentMaxPages = currentRecords % pageSize == 0 ? currentRecords / pageSize : currentRecords / pageSize + 1;
                //校正当前请求的页数
                if (pageIndex > currentMaxPages)
                {
                    pageIndex = currentMaxPages > 0 ? currentMaxPages : 1;
                }
            }


            //获取当前请求页面记录
            List<Dictionary<string, string>> datas = new List<Dictionary<string, string>>();
            if (currentMaxPages > 0 || isPage == false)
            {
                if (isPage)
                {
                    pageDataSql = string.Format(pageDataSql, pageSize, "createdate desc", tabelSql, (pageIndex - 1) * pageSize);
                }
                else
                {
                    pageDataSql = tabelSql;
                }
                using (var dataReader = this.DBService.ExecuteReader(this.UserCtx, pageDataSql, sqlParams))
                {
                    while (dataReader.Read())
                    {
                        Dictionary<string, string> data = new Dictionary<string, string>();
                        data["id"] = Convert.ToString(dataReader["id"]);
                        data["name"] = Convert.ToString(dataReader["name"]);
                        data["number"] = Convert.ToString(dataReader["number"]);
                        data["value"] = data["id"];
                        datas.Add(data);
                    }
                }
            }

            if (isPage == false)
            {
                currentMaxPages = 1;
                currentRecords = datas.Count;
                pageIndex = 1;
                pageSize = currentRecords;
            }

            this.OperationContext.Result.SrvData = new
            {
                pageInfo = new
                {
                    pageIndex = pageIndex,
                    pageSize = pageSize,
                    pageCount = currentMaxPages,
                    pageRows = currentRecords
                },
                filters = datas
            };
            this.OperationContext.Result.IsSuccess = true;
            this.OperationContext.Result.SimpleMessage = "查询成功!";
        }
    }
}
