using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Customer
{
    /// <summary>
    /// 分配负责人(列表)
    /// </summary>
    [InjectService]
    [FormId("ydj_customer")]
    [OperationNo("distributeduty")]
    public class DistributeDuty : AbstractOperationServicePlugIn
    {
        private string allocateRedisStr = "temp_allocate";
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            //前端传过来的客户id
            var customerIdArrayStr = this.GetQueryOrSimpleParam<string>("customerid", "");
            //部门id
            var deptId = this.GetQueryOrSimpleParam<string>("deptid","");
            //员工id
            var staffId = this.GetQueryOrSimpleParam<string>("dutyid", "");

            if (string.IsNullOrWhiteSpace(customerIdArrayStr))
            {
                this.Result.IsSuccess = false;
                this.Result.IsShowMessage = true;
                this.Result.ComplexMessage.ErrorMessages.Add("数据有误，请检查");
                return;
            }

            var customerIdJArray = JArray.Parse(customerIdArrayStr);

            var customerIdHashSet = new HashSet<string>();

            foreach (var customerId in customerIdJArray)
            {
                var customerIdStr = Convert.ToString(customerId);

                customerIdHashSet.Add(customerIdStr);
            }

            var redisCache = this.Context.Container.GetService<IRedisCache>();

            //存放被别人正在分配的客户id,别人操作正在分配的，不能同时操作
            var redisSelectIdInfoList = GetRedisCacheInfo(redisCache, customerIdHashSet);

            var notRedisSelectIdInfoList = customerIdHashSet.Where(x=> !redisSelectIdInfoList.Any(y=>Convert.ToString(y).Equals(x))).ToList();

            if (notRedisSelectIdInfoList != null && notRedisSelectIdInfoList.Any())
            {
                SetRedisCacheInfo(redisCache, notRedisSelectIdInfoList);
            }


            //客户数据包
            var customerDys = this.Context.LoadBizDataById("ydj_customer",customerIdHashSet,false);

            //需要保存客户的数据包
            var saveCustomerDys = new List<DynamicObject>();

            if (redisSelectIdInfoList != null && customerIdHashSet.Any())
            {
                //这里是别人正在分配的客户数据包，这里需要提示，客户正在分配中，请等待别人分配完再来分配
                var redisSelectCustomerDys = customerDys.Where(x=> redisSelectIdInfoList.Any(y=>Convert.ToString(x["id"]).Equals(y))).ToList();

                if (redisSelectCustomerDys != null && redisSelectCustomerDys.Any())
                {
                    var warnMessageList = new List<string>();

                    foreach (var redisSelectCustomerDy in redisSelectCustomerDys)
                    {
                        var number = Convert.ToString(redisSelectCustomerDy["fnumber"]);

                        var warnMessageStr = $"客户【{number}】正在被别人分配，请等待稍后再试!";

                        warnMessageList.Add(warnMessageStr);

                    }

                    if (warnMessageList != null && warnMessageList.Count > 0)
                    {
                        this.Result.ComplexMessage.WarningMessages.AddRange(warnMessageList);

                        this.Result.IsShowMessage = true;
                    }

                }
                //没有别人正在分配的客户数据包，可以进行分配
                var notRedisSelectCustomerDys = customerDys.Where(x => !redisSelectIdInfoList.Any(y => Convert.ToString(x["id"]).Equals(y))).ToList();

                if (notRedisSelectCustomerDys != null && notRedisSelectCustomerDys.Any())
                {
                    FillDutyInfo(notRedisSelectCustomerDys.ToArray(), staffId,deptId,ref saveCustomerDys);
                }
            }
            else
            {
                if (customerDys != null && customerDys.Any())
                {
                    FillDutyInfo(customerDys.ToArray(), staffId, deptId, ref saveCustomerDys);
                }
            }

            if (saveCustomerDys != null && saveCustomerDys.Any())
            {
                var customerSaveResult = Gateway.InvokeBillOperation(this.Context,"ydj_customer",saveCustomerDys,"save",new Dictionary<string, object>());

                customerSaveResult?.ThrowIfHasError(true, $"保存客户档案失败！");

                foreach (var saveCustomerDy in saveCustomerDys)
                {
                    var customerNumber = Convert.ToString(saveCustomerDy["fnumber"]);
                    this.Result.ComplexMessage.SuccessMessages.Add($"客户【{customerNumber}】分配负责人成功");
                    this.Result.IsShowMessage = true;
                }
            }

            DeleteRedisCacheInfo(redisCache,notRedisSelectIdInfoList);

            //刷新页面
            this.AddRefreshPageAction();


        }

        /// <summary>
        /// 获取Redis缓存信息
        /// </summary>
        /// <param name="redisCache"></param>
        /// <param name="selectIds"></param>
        /// <returns></returns>
        private IEnumerable<string> GetRedisCacheInfo(IRedisCache redisCache, IEnumerable<string> selectIds)
        {
            var hashSet = new HashSet<string>();

            foreach (var selectId in selectIds)
            {
                var keyStr = $"{allocateRedisStr}_{selectId}_{this.Context.Company}";
                hashSet.Add(keyStr);
            }

            var redisDic = redisCache.GetAll<string>(hashSet);

            var valueList = redisDic.Where(p => !p.Value.IsNullOrEmptyOrWhiteSpace()).Select(x => x.Value).ToList();

            if (valueList == null)
            {
                return new List<string>();
            }
            else
            {
                return valueList;
            }
        }

        /// <summary>
        /// 设置Redis缓存消息
        /// </summary>
        /// <param name="redisCache"></param>
        /// <param name="selectIds"></param>
        private void SetRedisCacheInfo(IRedisCache redisCache, IEnumerable<string> selectIds)
        {
            var redisDic = new Dictionary<string, string>();
            /*foreach (var selectId in selectIds)
            {
                var keyStr = $"{allocateRedisStr}_{selectId}_{this.Context.Company}";
                if (!redisDic.ContainsKey(keyStr))
                {
                    redisDic.Add(keyStr,selectId);
                }

            }

            if (redisDic != null && redisDic.Count > 0)
            {
                redisCache.SetAll<string>(this.Context,redisDic);
                // redisCache.SetTimeToLive();
                //设置过期时间
                redisDic.Keys.ForEach(x => redisCache.SetTimeToLive(x, new TimeSpan(0, 0, 2, 0)));

            }*/

            foreach (var selectId in selectIds)
            {
                var keyStr = $"{allocateRedisStr}_{selectId}_{this.Context.Company}";
                if (!redisDic.ContainsKey(keyStr))
                {
                    redisDic.Add(keyStr, selectId);
                    redisCache.Set(this.Context, keyStr, selectId, new TimeSpan(0, 0, 2, 0));
                }

            }
        }

        /// <summary>
        /// 配置完成后，删除Redis缓存信息
        /// </summary>
        /// <param name="redisCache"></param>
        /// <param name="selectIds"></param>
        private void DeleteRedisCacheInfo(IRedisCache redisCache, IEnumerable<string> selectIds)
        {
            var hashSet = new HashSet<string>();

            foreach (var selectId in selectIds)
            {
                var keyStr = $"{allocateRedisStr}_{selectId}_{this.Context.Company}";
                hashSet.Add(keyStr);
            }

            if (hashSet.Any())
            {
                redisCache.RemoveAll(hashSet);
            }

        }

        /// <summary>
        /// 填充负责人信息
        /// </summary>
        /// <param name="customerDys"></param>
        /// <param name="dutyId"></param>
        /// <param name="deptId"></param>
        private void FillDutyInfo(DynamicObject [] customerDys,string dutyId,string deptId,ref List<DynamicObject> saveCustomerDys)
        {
            var dateNowTime = DateTime.Now;
            foreach (var customerDy in customerDys)
            {
                var dutyEntrys = customerDy["fdutyentry"] as DynamicObjectCollection;
                if (dutyEntrys == null || !dutyEntrys.Any())
                {
                    var newDutyEntry = dutyEntrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;

                    newDutyEntry["fdutyid"] = dutyId;

                    newDutyEntry["fdeptid"] = deptId;

                    newDutyEntry["fjointime"] = dateNowTime;

                    dutyEntrys.Add(newDutyEntry);

                    saveCustomerDys.Add(customerDy);
                }
                else
                {
                    var findDutyEntry = dutyEntrys.FirstOrDefault(x=>Convert.ToString(x["fdutyid"]).Equals(dutyId));

                    if (findDutyEntry != null)
                    {
                        //说明在客户联系人中已经存在该负责人，那么就不需要再次添加了，要给个提示说已经分配过了
                        var customerNumber = Convert.ToString(customerDy["fnumber"]);
                        
                        this.Result.ComplexMessage.WarningMessages.Add($"客户【{customerNumber}】已经分配过该负责人，无需再次分配。");

                        this.Result.IsShowMessage = true;

                        continue;
                    }
                    else
                    {
                        var newDutyEntry = dutyEntrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;

                        newDutyEntry["fdutyid"] = dutyId;

                        newDutyEntry["fdeptid"] = deptId;

                        newDutyEntry["fjointime"] = dateNowTime;

                        dutyEntrys.Add(newDutyEntry);

                        saveCustomerDys.Add(customerDy);
                    }

                }
            }
        }
    }
}