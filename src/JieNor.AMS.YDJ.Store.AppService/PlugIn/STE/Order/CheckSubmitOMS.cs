using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Store.AppService.MuSi;
using JieNor.AMS.YDJ.Store.AppService.MuSi.DTO;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同:重新提交oms
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("checksubmitoms")]
    public class CheckSubmitOMS : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);
            if (selectRowIds == null || selectRowIds.Count == 0)
            {
                this.Result.SimpleMessage = "请先选择商品行！";
                this.Result.IsSuccess = false;
                return;
            }

            string errorMsg = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (selectRowIds == null || selectRowIds.Count == 0)
                {
                    errorMsg = "请先选择商品行！";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);
            string errorMsg = "";
            List<string> errorRowId = new List<string>();
            foreach (var item in e.DataEntitys)
            {
                var entrys = item["fentry"] as DynamicObjectCollection;
                var attrEntrys = this.GetAttachmentList(item);
                foreach (var selectRowIdItem in selectRowIds)
                {
                    var entryItem = entrys.Where(a => a["id"].Equals(selectRowIdItem.Id)).FirstOrDefault();
                    if (entryItem != null)
                    {
                        if (Convert.ToBoolean(entryItem["fstonetable"]))
                        {
                            if ((attrEntrys != null && attrEntrys.Count > 0) && attrEntrys.Any(a => a["ffilegrouping"].Equals(entryItem["ffactorybillno"]) && Convert.ToBoolean(a["ftabledrawing"])))
                            {
                                continue;
                            }
                            else
                            {
                                errorRowId.Add(selectRowIdItem.Id);
                                errorMsg += $"对不起，第" + (entrys.IndexOf(entryItem) + 1) + "行工厂订单号为" + entryItem["ffactorybillno"] + "的附件含石材台面，请上传台面图纸！<br />";
                            }
                        }
                        else if (Convert.ToBoolean(entryItem["funstdtypefactory"]))
                        {
                            if ((attrEntrys != null && attrEntrys.Count > 0) && attrEntrys.Any(a => a["ffilegrouping"].Equals(entryItem["ffactorybillno"]) && Convert.ToBoolean(a["funstdtypedoc"])))
                            {
                                continue;
                            }
                            else
                            {
                                errorRowId.Add(selectRowIdItem.Id);
                                errorMsg += $"对不起，第" + (entrys.IndexOf(entryItem) + 1) + "行工厂订单号为" + entryItem["ffactorybillno"] + "附件为工厂单非标，请上传非标凭证！<br />";
                            }
                        }
                    }
                }
                foreach (var selectRowIdItem in selectRowIds)
                {
                    var entryItem = entrys.Where(a => a["id"].Equals(selectRowIdItem.Id)).FirstOrDefault();
                    if (entryItem != null)
                    {
                        if (Convert.ToString(entryItem["fomsprogress"]).Equals("-2") || Convert.ToString(entryItem["fomsprogress"]).Equals("5"))
                        {
                            continue;
                        }
                        else
                        {
                            errorRowId.Add(selectRowIdItem.Id);
                            errorMsg += $"对不起，当前第" + (entrys.IndexOf(entryItem) + 1) + "行商品定制订单进度不为“待提交“或“已退回”，不允许提交！<br />";
                        }
                    }
                }
            }
            //List<string> successRow = new List<string>();
            //successRow = selectRowIds.Where(a => !errorRowId.Contains(a.Id)).Select(a => a.Id).ToList();
            this.Result.SrvData = new
            {
                successRowId = selectRowIds.Where(a => !errorRowId.Contains(a.Id)).Select(a => a.Id).ToList(),
                errorRowId = errorRowId.Distinct(),
                errorMsg = errorMsg
            };
            //this.Result.SimpleMessage = errorMsg;
            this.Result.IsSuccess = true;
        }

        private DynamicObjectCollection GetAttachmentList(DynamicObject orderObj)
        {
            var attachFormMeta = this.MetaModelService.LoadFormModel(this.Context, "bd_attachlist");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, attachFormMeta.GetDynamicObjectType(this.Context));
            var pkIdReader = this.Context.GetPkIdDataReader(attachFormMeta, "flinkformid=@linkFormId and flinkbillinterid=@linkBillId", new SqlParam[]
            {
                new SqlParam("linkFormId", System.Data.DbType.String, this.HtmlForm.Id),
                new SqlParam("linkBillId", System.Data.DbType.String, orderObj["id"]),
            });

            var linkAttachBillObj = dm.SelectBy(pkIdReader)
                .OfType<DynamicObject>()
                .FirstOrDefault();
            if (linkAttachBillObj == null) return null;

            var entrys = (DynamicObjectCollection)linkAttachBillObj["fdrawentity"];

            return entrys;
        }

    }


}
