using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 是否下推采购
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("ispurchaseinvoice")]
    public class IsPurchaseinvoice : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var fbillnos = this.GetQueryOrSimpleParam<string>("fbillno", null);
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);
            if (!fbillnos.IsNullOrEmptyOrWhiteSpace())
            {
                List<string> validataMsgs = new List<string>();

                validataMsgs = ValidationPurOrder(fbillnos.TrimEnd(','), selectRowIds);
                this.Result.SrvData = string.Join("。</br>", validataMsgs);
                this.Result.IsSuccess = !(validataMsgs.Count > 0);
            }
        }

        /// <summary>
        /// 检查是否已经下推过采购订单
        /// </summary>
        /// <param name="dataEntity"></param>
        private string CheckIsPushPurOrder(string fbillno, List<Row> rows)
        {
            List<string> result = new List<string>();
            string strSql = "";
            ///如果先了明细按明细来查下游戏
            if (rows.Count > 0)
            {
                strSql = @"/*dialect*/select fsourcenumber, count(1) count from t_ydj_purchaseorder t1
                inner join t_ydj_poorderentry t2 on t1.fid=t2.fid
                where t1.fmainorgid=@fmainorgid and  t2.fsourceentryid in ('{0}') group by fsourcenumber".Fmt(string.Join("','", rows.Select(x => x.Id).ToList()));
            }
            else
            {
                strSql = @"/*dialect*/select fsourcenumber, count(1) count from t_ydj_purchaseorder t1
                inner join t_ydj_poorderentry t2 on t1.fid=t2.fid
                where t1.fmainorgid=@fmainorgid and  t2.fsourcebillno in ('{0}') group by fsourcenumber ".Fmt(string.Join("','", fbillno.Split(',').ToList()));
            }
            var sqlParam = new List<SqlParam>
                    {
                        new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company)
                    };
            using (var reader = this.Context.ExecuteReader(strSql, sqlParam))
            {
                if (reader.Read())
                    result.Add($"【{Convert.ToString(reader["fsourcenumber"])}】");
            }
            return string.Join(",", result);
        }

        public List<string> ValidationPurOrder(string fbillno, List<Row> rows)
        {

            List<string> result = new List<string>();
            string whereStr = "t2.fsourcebillno in ('{0}') ".Fmt(string.Join("','", fbillno.Split(',').ToList()));
            if (rows.Count > 0)
            {
                whereStr = "t2.fsoorderentryid in ('{0}') ".Fmt(string.Join("','", rows.Select(x => x.Id).ToList()));
            }
            string selectsql = $@" /*dialect*/select t1.fsourcenumber from t_ydj_purchaseorder t1 
                                   inner join t_ydj_poorderentry t2 on t1.fid=t2.fid  
                                  where    t1.fmainorgid=@fmainorgid and {whereStr} ";
            var sqlParam = new List<SqlParam>
                    {
                        new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company)
                    };
            var list = this.Context.ExecuteDynamicObject(selectsql, sqlParam);
            if (list.Count() <= 0)
            {
                return result;
            }
            var group = list.GroupBy(x => Convert.ToString(x["fsourcenumber"])).ToList();
            foreach (var item in group)
            {
                result.Add($"当前合同{item.Key}已存在下游采购单据");
            }

            string selectchangesql = $@" /*dialect*/select t2.fsourcebillno, oe.fseq,bd.fnumber from t_ydj_purchaseorder_chg t1 
                                   inner join t_ydj_poorderentry_chg t2 on t1.fid=t2.fid  
                                   inner join T_YDJ_ORDERENTRY oe on oe.fentryid=t2.fsoorderentryid
                                   inner join T_BD_MATERIAL bd on oe.fproductid=bd.fid
                                   where  t1.fmainorgid=@fmainorgid and t1.fstatus not in ('E') and t1.fcancelstatus='0' and t2.fentrychange<>'' and {whereStr}  ";
           var chagngeList= this.Context.ExecuteDynamicObject(selectchangesql, sqlParam);
           var changeGroup = chagngeList.GroupBy(x => Convert.ToString(x["fsourcebillno"]));
            foreach (var item in changeGroup)
            {
                foreach (var _change in item)
                {
                    result.Add($"销售合同【{item.Key}】第{Convert.ToInt32(_change["fseq"])}行商品【{Convert.ToString(_change["fnumber"])}】,下游存在变更中的采购订单，建议核查后再采购");
                }
            }
            return result;
        }
    }
}
