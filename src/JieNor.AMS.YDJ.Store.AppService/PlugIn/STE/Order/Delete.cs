using System;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using System.Text;
using System.Collections.Generic;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.Utils;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.Framework.Interface.Log;
using System.Threading.Tasks;
using static JieNor.AMS.YDJ.Core.Helpers.ProductDelistingHelper;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：删除
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */

            var errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((newData, oldData) =>
            {
                if (Convert.ToBoolean(newData["fisresellorder"])
                    && Convert.ToString(newData["fsourcetype"]).EqualsIgnoreCase("ydj_purchaseorder")
                    && !newData["fsourcenumber"].IsNullOrEmptyOrWhiteSpace()
                    && !newData["fsourceid"].IsNullOrEmptyOrWhiteSpace())
                {
                    errorMessage = $"{this.HtmlForm.Caption}【{newData[this.HtmlForm.GetNumberField().PropertyName]}】来源于二级分销商，不允许删除！";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((newData, oldData) =>
            {
                if (!CheckIsWhereCreate(newData))
                {
                    return false;
                }
                return true;
            }).WithMessage("订货单【{0}】已生成收支记录，不允许删除！",
           (billObj, propObj) => propObj["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((newData, oldData) =>
            {
                if (!CheckIsWhereCreate(newData))
                {
                    var receivable = Convert.ToString(newData["freceivable"]);
                    if (!receivable.IsNullOrEmptyOrWhiteSpace())
                    {
                        decimal receivableNumber = 0;
                        var isOk = decimal.TryParse(receivable, out receivableNumber);
                        if (isOk && receivableNumber - Convert.ToDecimal(newData["fcollectedamount"]) > 0)
                        {
                            return false;
                        }
                        return true;
                    }
                }
                return true;
            }).WithMessage("订货单【{0}】已收款【{1}】，不允许删除！",
            (billObj, propObj) => propObj["fbillno"],
            (billObj, propObj) => (Convert.ToDecimal(propObj["freceivable"]) - Convert.ToDecimal(propObj["fcollectedamount"])).ToString("f2")));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var changeStatus = Convert.ToString(newData["fchangestatus"]);
                var status = Convert.ToString(newData["fstatus"]).ToLower();
                return !((status == "b" || status == "d" || status == "e") && (changeStatus == "1" || changeStatus == "2" || changeStatus == "3"));
            }).WithMessage("单据状态=创建/已提交/已审核 且 变更状态=变更中/变更已提交/已变更的单据，不允许删除！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!newData["fbilltype"].IsNullOrEmpty() && newData["fbilltype"].Equals("ydj_order_vsix"))
                {
                    var fentrys = newData["fdrawentity"] as DynamicObjectCollection;
                    var errStatus = new string[] { "draw_status_02", "draw_status_03", "draw_status_05", "draw_status_06" };
                    if (!fentrys.IsNullOrEmpty() && fentrys.Any(x => errStatus.Contains(x["fdrawstatus"])))
                    {
                        return false;
                    }
                }
                return true;
            }).WithMessage("对不起，当前订单已提交过总部审核，禁止删除！"));

            /*
             * 1.销售合同表头操作<删除>时，需校验商品行是否包含【非标审批状态】=“待审批”，如果存在则需提示：“当前订单包含非标商品待审批中，不允许删除！”。 关联任务：32491
             */
            var errorMsg = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fentrys = newData["fentry"] as DynamicObjectCollection;
                if (fentrys.Any(t => Convert.ToString(t["funstdtypestatus"]) == "02"))
                {
                    errorMsg = "当前订单包含非标商品待审批中，不允许删除！";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

            //OMS定制 当【单据类型】=“v6定制柜合同”时，则执行<删除>时，需弹出Toast警告提示：“对不起，当前订单为v6定制柜合同，仅允许作废，不允许删除！”
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                // 加载引用数据
                var refMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
                var formDt = this.HtmlForm.GetDynamicObjectType(this.Context);
                refMgr.Load(this.Context, formDt, newData, false);
                var fbilltype = newData["fbilltype_ref"] as DynamicObject;
                var v6swjBillTypeName = Convert.ToString(fbilltype["fname"]);

                if (v6swjBillTypeName == "v6定制柜合同")
                {
                    return false;
                }
                return true;
            }).WithMessage("对不起，当前订单为v6定制柜合同，仅允许作废，不允许删除！"));
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            var orderSyncCheckOperationService = this.Container.GetService<IOrderSyncCheckOperationService>();
            e.DataEntitys = orderSyncCheckOperationService.SyncCheckOperation(Context, OperationNo, e.DataEntitys, Result.ComplexMessage.WarningMessages);

            //检查是否已有收支记录
            e.DataEntitys = checkIncomeDisburse(e.DataEntitys);

            //检查是否存在关联的设计方案与量尺记录,如果存在提示用户先去删除量尺记录和设计方案
            this.deleteOrder(e.DataEntitys);
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            //删除关联的预留单，先删除预留单，再反写上游的意向单以免影响意向单重新生成预留单
            this.DeleteReserveBill(e.DataEntitys);

            //反写上游单据
            this.WriteBackSourceBill(e.DataEntitys);

            //反写销售机会
            writeBackCustomerRecord(e.DataEntitys);

            // 反写合同关联的二级经销商采购订单【一级合同状态】为“订单已驳回”
            //ResellerHelper.WriteBackPurchaseOrders(
            //    this.OperationContext, 
            //    e.DataEntitys, 
            //    OneLvOrderSratusConst.Reject);


            ProductDelistingHelper.DealDelistingDataByCommon(this.Context, this.HtmlForm, this.OperationNo, e.DataEntitys.ToList());
        }

        private void deleteOrder(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }
            //设计方案                  
            var fdesignscheme = Convert.ToString(dataEntities[0]["fdesignscheme"]);
            //量尺记录            
            var fscalerecord = Convert.ToString(dataEntities[0]["fscalerecord"]);

            if (!fdesignscheme.IsEmptyPrimaryKey() || !fscalerecord.IsEmptyPrimaryKey())
            {
                var sqldesignText = "select * from t_ydj_designscheme where fnumber=@fdesignscheme and fmainorgid=@fmainorgid";
                var sqlscaleText = "select * from t_ydj_scalerecord where fbillno=@fscalerecord  and fmainorgid=@fmainorgid";

                var sqlParams = new List<SqlParam> {
                    new SqlParam ( "@fdesignscheme",System.Data.DbType.String,fdesignscheme),
                    new SqlParam ( "@fscalerecord",System.Data.DbType.String,fscalerecord),
                    new SqlParam ( "@fmainorgid",System.Data.DbType.String,Context.Company)
                };
                string[] dedignArray = new string[] { };
                string[] scaleArray = new string[] { };
                using (var reader = this.DBService.ExecuteReader(this.Context, sqldesignText, sqlParams))
                {
                    while (reader.Read())
                    {
                        var design = reader.GetValue<string>("fid");
                        dedignArray = design?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    }
                }
                using (var reader = this.DBService.ExecuteReader(this.Context, sqlscaleText, sqlParams))
                {
                    while (reader.Read())
                    {
                        var scale = reader.GetValue<string>("fid");
                        scaleArray = scale?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    }
                }
                if (dedignArray.Length > 0 || scaleArray.Length > 0)
                {
                    throw new BusinessException("删除失败：请将关联设计方案或量尺记录删除，否则不允许删除合同单！");
                }
            }
        }

        /// <summary>
        /// 反写销售机会
        /// </summary>
        /// <param name="dataEntities"></param>
        private void writeBackCustomerRecord(DynamicObject[] dataEntities)
        {
            var billNos = dataEntities.Select(x => Convert.ToString(x[this.HtmlForm.NumberFldKey])).Where(x => false == string.IsNullOrWhiteSpace(x)).Distinct().ToList();

            if (billNos == null || billNos.Count <= 0)
            {
                return;
            }

            var metaModelService = this.Container.GetService<IMetaModelService>();
            var recordForm = metaModelService.LoadFormModel(this.Context, "ydj_customerrecord");
            var recordDm = this.Container.GetService<IDataManager>();
            recordDm.InitDbContext(this.Context, recordForm.GetDynamicObjectType(this.Context));

            var where = new StringBuilder(" fmainorgid = @fmainorgid and forderno ");
            var sqlParamers = new List<SqlParam>
            {
                new SqlParam("@fmainorgid",System.Data.DbType.String,this.Context.Company)
            };

            if (billNos.Count == 1)
            {
                where.Append(" = @forderno");
                sqlParamers.Add(new SqlParam("@forderno", System.Data.DbType.String, billNos[0]));
            }
            else
            {
                where.Append(" in (");
                where.Append(string.Join(",", billNos.Select((x, i) => $"@forderno{i}")));
                where.Append(")");
                sqlParamers.AddRange(billNos.Select((x, i) => new SqlParam($"@forderno{i}", System.Data.DbType.String, x)));
            }

            var dataReader = this.Context.GetPkIdDataReader(recordForm, where.ToString(), sqlParamers);
            var recordEntities = recordDm.SelectBy(dataReader).OfType<DynamicObject>().ToList();

            if (recordEntities != null && recordEntities.Count > 0)
            {
                foreach (var recordEntity in recordEntities)
                {
                    recordEntity["forderno"] = string.Empty;
                }
                recordDm.Save(recordEntities);
            }
        }

        /// <summary>
        /// 反写上游单据
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void WriteBackSourceBill(DynamicObject[] dataEntitys)
        {
            var formId = "ydj_saleintention";
            //var saleNumbers = dataEntitys.Where(x => Convert.ToString(x["fsourcetype"]).EqualsIgnoreCase(formId))
            //                             .Select(x => Convert.ToString(x["fsourcenumber"]))
            //                             .Where(x => false == string.IsNullOrWhiteSpace(x))
            //                             .ToList();

            //if (saleNumbers == null || saleNumbers.Count <= 0)
            //{
            //    return;
            //}

            if (dataEntitys == null || dataEntitys.Length <= 0) return;
            foreach (var dataEntity in dataEntitys)
            {
                var orderentrys = dataEntity["fentry"] as DynamicObjectCollection;
                var sourceNumberList = orderentrys.Select(o => Convert.ToString(o["fsourcenumber_e"])).Distinct().ToList();
                var sourceTypeList = orderentrys.Select(o => Convert.ToString(o["fsourcetype_e"])).Distinct().ToList();
                if (sourceNumberList.Count > 0 && sourceTypeList.Contains("ydj_saleintention"))
                {
                    var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, sourceTypeList[0].ToString());
                    var dm = this.Container.GetService<IDataManager>();
                    dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                    string where = $"fmainorgid=@fmainorgid and {htmlForm.NumberFldKey} in ('{string.Join("','", sourceNumberList)}')";
                    var sqlParam = new SqlParam[]
                    {
                            new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company)
                    };
                    var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
                    var saleEntities = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();

                    if (saleEntities == null || saleEntities.Count <= 0)
                    {
                        return;
                    }
                    foreach (var saleEntity in saleEntities)
                    {
                        saleEntity["fispushorder"] = false;
                        saleEntity["forderid"] = "";
                        var sourceentrys = saleEntity["fentity"] as DynamicObjectCollection;
                        var listorderentrys = orderentrys.Select(o => Convert.ToString(o["fsourceentryid_e"])).ToList();
                        sourceentrys.ForEach(a =>
                        {
                            //合同删除，对应的明细反写为0
                            if (listorderentrys.Contains(a["Id"]))
                            {
                                a["fdoorderstatus"] = "0";
                            }
                            //else
                            //{
                            //    a["fdoorderstatus"] = "1";
                            //}
                        });
                    }
                    orderentrys.Select(o => Convert.ToString(o["fsourceentryid_e"]));

                    //保存意向单的save接口，重新生成意向单的预留单
                    var result = this.Gateway.InvokeBillOperation(this.Context, formId, saleEntities, "save", new Dictionary<string, object>());
                    result.ThrowIfHasError(true, $"保存{htmlForm.Caption}失败!");
                }
            }
        }

        /// <summary>
        /// 删除关联的预留单
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void DeleteReserveBill(DynamicObject[] dataEntitys)
        {
            var svc = this.Container.GetService<IReserveUpdateService>();
            var opResult = svc.DeleteReserve(this.Context, this.HtmlForm, dataEntitys, this.Option);

            this.Result.MergeResult(opResult);
        }

        /// <summary>
        /// 自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
            switch (e.EventName)
            {
                //让插件返回源单数据
                case "getSourceDatas":
                    getSourceDatas(e);
                    break;
                default:
                    return;
            }
        }

        private void getSourceDatas(OnCustomServiceEventArgs e)
        {
            //设计方案数据包
            var dataEntities = e.DataEntities;
            var eventData = e.EventData as Dictionary<string, object>;
            var sourceForm = eventData["sourceForm"] as HtmlForm;
            //销售机会数据包
            var sourceEntities = new List<DynamicObject>();
            //处理逻辑
            switch (sourceForm.Id)
            {
                //来源单据为销售机会
                case "ydj_customerrecord":
                    var fsourcetype = Convert.ToString(dataEntities[0]["fsourcetype"]);
                    var fsourcenumber = Convert.ToString(dataEntities[0]["fsourcenumber"]);
                    if (fsourcetype == "ydj_saleintention" && !fsourcenumber.IsNullOrEmptyOrWhiteSpace())
                    {
                        var scaleModelService = this.Container.GetService<IMetaModelService>();
                        var scaleHtmForm = scaleModelService.LoadFormModel(this.Context, fsourcetype);
                        var scaleDm = this.Container.GetService<IDataManager>();
                        scaleDm.InitDbContext(this.Context, scaleHtmForm.GetDynamicObjectType(this.Context));
                        var scalrReader = this.Context.GetPkIdDataReader(scaleHtmForm, " fbillno=@fsourcenumber",
                         new SqlParam[] {
                             new SqlParam("@fsourcenumber",System.Data.DbType.String,fsourcenumber)
                         }
                         );
                        var scalObj = scaleDm.SelectBy(scalrReader).OfType<DynamicObject>().FirstOrDefault();
                        var saleType = Convert.ToString(scalObj["fsourcetype"]);
                        var salenumber = Convert.ToString(scalObj["fsourcenumber"]);
                        if (saleType == "ydj_customerrecord" && !salenumber.IsNullOrEmptyOrWhiteSpace())
                        {
                            var saleModelService = this.Container.GetService<IMetaModelService>();
                            var saleHtmForm = saleModelService.LoadFormModel(this.Context, saleType);
                            var saleDm = this.Container.GetService<IDataManager>();
                            saleDm.InitDbContext(this.Context, saleHtmForm.GetDynamicObjectType(this.Context));
                            var saleReader = this.Context.GetPkIdDataReader(saleHtmForm, " fbillno=@fsourcenumber",
                             new SqlParam[] {
                                 new SqlParam("@fsourcenumber",System.Data.DbType.String,salenumber)
                             }
                             );
                            var saleObj = saleDm.SelectBy(saleReader).OfType<DynamicObject>().FirstOrDefault();
                            sourceEntities.Add(saleObj);
                        }

                        var result = new Dictionary<string, object>();
                        result["sourceEntities"] = sourceEntities;
                        //一个销售机会只有一个销售合同，所以同源数据包只有它自己了
                        result["isogenyEntities"] = dataEntities.ToList();
                        e.Result = result;
                        e.Cancel = true;
                    }
                    break;
            }
        }

        /// <summary>
        /// 检查收支记录
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private DynamicObject[] checkIncomeDisburse(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return dataEntities;
            }

            var ids = dataEntities.Select(x => Convert.ToString(x["id"])).Distinct().ToList();
            var metaModelService = this.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            var multiValueQueryService = this.Container.GetService<IMultiValueQueryService>();
            var where = " fmainorgid=@fmainorgid and fsourceformid=@fsourceformid ";
            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid",System.Data.DbType.String,this.Context.Company),
                new SqlParam("@fsourceformid",System.Data.DbType.String,this.HtmlForm.Id)
            };
            var cidEntities = multiValueQueryService.Select(this.Context, where, sqlParams, htmlForm, "fsourceid", ids);

            if (cidEntities == null || cidEntities.Count <= 0)
            {
                return dataEntities;
            }

            var fsourceids = cidEntities.Select(x => Convert.ToString(x["fsourceid"])).ToList();

            return dataEntities.Where(x =>
            {
                var id = Convert.ToString(x["id"]);
                var billNo = Convert.ToString(x["fbillno"]);

                if (false == fsourceids.Contains(id))
                {
                    return true;
                }

                this.Result.ComplexMessage.ErrorMessages.Add($"{this.HtmlForm.Caption}[{billNo}]已有{htmlForm.Caption}，不允许删除!");
                return false;
            }).ToArray();
        }

        /// <summary>
        /// 检查是否生成收支记录
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private bool CheckIsWhereCreate(DynamicObject dataEntitie)
        {
            List<object> ids = new List<object>();
            ids.Add(Convert.ToString(dataEntitie["ID"]));
            var metaModelService = this.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            var multiValueQueryService = this.Container.GetService<IMultiValueQueryService>();
            var where = " fmainorgid=@fmainorgid and fsourceformid=@fsourceformid ";
            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid",System.Data.DbType.String,this.Context.Company),
                new SqlParam("@fsourceformid",System.Data.DbType.String,this.HtmlForm.Id)
            };
            var cidEntities = multiValueQueryService.Select(this.Context, where, sqlParams, htmlForm, "fsourceid", ids);

            if (cidEntities == null || cidEntities.Count <= 0)
            {
                return true;
            }
            return false;
        }
        //public override void PrepareBusinessServices(PrepareBusinessServiceEventArgs e)
        //{
        //    base.PrepareBusinessServices(e);

        //    var opt = this.OperationContext.Option;
        //    opt.SetVariableValue("SyncMode", MuSi.Enums.Enu_MuSiSyncMode.MQ);
        //}
    }
}