using JieNor.AMS.YDJ.Store.AppService.MuSi;
using JieNor.AMS.YDJ.Store.AppService.MuSi.DTO;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.E3;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs.MerChant;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：通过销售合同查询并更新物流进度
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("getorderlogisticsprogress")]
    public class GetOrderLogisticsProgress : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            string orderid = this.GetQueryOrSimpleParam<string>("orderid", "");
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);
            if (string.IsNullOrWhiteSpace(orderid) || selectRowIds == null || selectRowIds.Count == 0)
            {
                throw new BusinessException("请选择要查询的销售合同明细!");
            }
            var datas = GetPendingPurchaseOrders(orderid, selectRowIds);
            //构建出来入参
            if (datas == null || datas.Count == 0)
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "未查询到符合条件的数据";
                return;
            }
            List<DynamicObject> objs = new List<DynamicObject>();
            var e3Obj = MuSiApi.GetE3ExtApp(this.Context);
            if (e3Obj == null)
            {
                throw new BusinessException("获取E3应用信息失败");
            }

            string appKey = Convert.ToString(e3Obj["fappkey"]);
            string e3htmlId = "ydj_e3logisticsprogress";
            var e3Html = this.MetaModelService.LoadFormModel(this.Context, e3htmlId);
            if (appKey.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("外部应用匹配不正确！");
            }
            foreach (var dataItem in datas)
            {
                MSE3LogisticsRequestDTO e3Request = new MSE3LogisticsRequestDTO();
                e3Request.khck = Convert.ToString(dataItem["pobillno"]);
                if (this.Context.IsDirectSale)
                {
                    e3Request.khck = Convert.ToString(dataItem["fbillno"]);
                }
                if (Convert.ToInt32(dataItem["fseq_e"]) >= 10)
                {
                    e3Request.dealCodeLineSn = "000" + Convert.ToInt32(dataItem["fseq_e"]);
                }
                else
                {
                    e3Request.dealCodeLineSn = "0000" + Convert.ToInt32(dataItem["fseq_e"]) * 10;
                }

                JObject configObj = JObject.Parse(appKey);
                string key = configObj.GetJsonValue("key", "");
                string requestTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                string secret = configObj.GetJsonValue("secret", "");
                string version = configObj.GetJsonValue("version", "");
                string serviceType = configObj.GetJsonValue("serviceType", "");
                string data = e3Request.ToJson();
                //data = "{\"express_sn\":null,\"dealCode\":null,\"khck\":\"CGD1000611000000202\",\"jhCode\":null,\"waveNo\":null,\"dealCodeLineSn\":\"\"}";

                var sign = SecurityUtil.HashString($@"key={key}&requestTime={requestTime}&secret={secret}&version={version}&serviceType={serviceType}&data={data}");
                MSE3LogisticsDTO dto = new MSE3LogisticsDTO();
                dto.sign = sign;
                dto.key = key;
                dto.requestTime = requestTime;
                dto.version = version;
                dto.serviceType = serviceType;
                dto.data = data;
                //待确定请求参数，返回结果
                var result = MuSiApi.GetE3LogisticsProgress(this.Context, this.HtmlForm, dto);
                //step1:将获取的数据存到中间表
                if (result != null)
                {
                    //JObject _obj = result.data;
                    //if (_obj == null)
                    //{

                    //}
                    //JArray array = _obj["data"] as JArray;
                    //JArray array = result.data;
                    JArray array = result.data as JArray;
                    foreach (var item in array)
                    {
                        // 获取物流单号
                        string expressSn = Convert.ToString(item["express_sn"]);

                        // 获取送货日期（取 track_list 中的第一个 shipping_time）
                        string deliveryDate = "";
                        string signDate = "";
                        var trackList = item["track_list"] as JArray;
                        if (trackList != null)
                        {
                            var signNode = trackList.FirstOrDefault(t => Convert.ToString(t["shipping_status"]) == "3");
                            if (signNode != null)
                            {
                                signDate = Convert.ToString(signNode["shipping_time"]);
                            }
                            var deliveryNode = trackList.FirstOrDefault(t => Convert.ToString(t["shipping_status"]) == "2");
                            if (deliveryNode != null)
                            {
                                deliveryDate = Convert.ToString(deliveryNode["shipping_time"]);
                            }
                        }

                        // 遍历 items_list 匹配 dataItem
                        foreach (var subItem in item["items_list"])
                        {
                            string itemCode = Convert.ToString(subItem["itemCode"]);
                            string dealCodeLineSn = Convert.ToString(subItem["dealCodeLineSn"]);
                            string itemQuantity = Convert.ToString(subItem["itemQuantity"]);

                            // 匹配 dataItem 的商品和行编码
                            if (Convert.ToString(dataItem["fnumber"]) == itemCode &&
                                Convert.ToInt32(dataItem["fseq_e"]) * 10 == Convert.ToInt32(dealCodeLineSn))
                            {
                                // 设置 obj 的值
                                DynamicObject obj = new DynamicObject(e3Html.GetDynamicObjectType(this.Context));
                                obj["id"] = Guid.NewGuid().ToString();
                                obj["fjson"] = result.ToJson();
                                obj["forderid"] = orderid; // 合同ID
                                obj["fsourcetype"] = "ydj_order"; // 合同
                                obj["fsourcenumber"] = dataItem["fbillno"]; // 合同编号
                                obj["fentryid"] = dataItem["fentryid"]; // 合同明细ID
                                obj["fmaterialid"] = dataItem["fproductid"]; // 商品ID
                                obj["fattrinfo"] = dataItem["fattrinfo"]; // 辅助属性
                                obj["fattrinfo_e"] = dataItem["fattrinfo_e"]; // 辅助属性
                                obj["fcustomdes_e"] = dataItem["fcustomdes_e"]; // 定制说明
                                obj["fpoid"] = dataItem["poId"]; // 采购订单ID
                                obj["fpobillno"] = dataItem["pobillno"]; // 采购订单号
                                obj["fhqderno"] = dataItem["fhqderno"]; // 采购订单号
                                obj["fposeq"] = dataItem["fseq_e"]; // 采购订单行号
                                obj["flogisticsno"] = expressSn; // 物流送货单号
                                if (!string.IsNullOrWhiteSpace(deliveryDate))
                                {
                                    obj["fdeliverydate"] = deliveryDate; // 送货日期
                                }
                                obj["fbizqty"] = 0; // 签收数量
                                // 判断行签收状态
                                if (Convert.ToDecimal(dataItem["pobizqty"]) == Convert.ToDecimal(itemQuantity))
                                {
                                    obj["fentrysignstatus"] = "entrysign_type_01"; // 已签收
                                }
                                else if (Convert.ToDecimal(dataItem["pobizqty"]) > Convert.ToDecimal(itemQuantity) && Convert.ToDecimal(itemQuantity) > 0)
                                {
                                    obj["fentrysignstatus"] = "entrysign_type_02"; // 部分签收
                                }
                                else if (Convert.ToDecimal(itemQuantity) == 0)
                                {
                                    obj["fentrysignstatus"] = "entrysign_type_03"; // 未签收
                                }
                                if (!string.IsNullOrWhiteSpace(signDate))
                                {
                                    obj["fbizqty"] = itemQuantity; // 签收数量
                                    obj["fsigndate"] = signDate; // 签收日期
                                }
                                obj["fmodifydate"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"); // 数据更新时间
                                objs.Add(obj);
                            }
                        }
                    }

                }
            }
            // 1. 按fentryid分组，汇总签收日期不为空的签收数量
            var entrySignSummary = objs
                .GroupBy(o => Convert.ToString(o["fentryid"]))
                .Select(g => new
                {
                    fentryid = g.Key,
                    TotalSignQty = g.Where(a => !string.IsNullOrWhiteSpace(Convert.ToString(a["fsigndate"]))).Sum(x => Convert.ToDecimal(x["fbizqty"]))
                })
                .ToDictionary(x => x.fentryid, x => x.TotalSignQty);


            foreach (var dataItem in entrySignSummary)
            {
                var objItem = objs.Where(o => Convert.ToString(o["fentryid"]) == dataItem.Key).FirstOrDefault();
                var orderItem = datas.Where(o => Convert.ToString(o["fentryid"]) == dataItem.Key).FirstOrDefault();
                if (objItem != null && orderItem != null)
                {
                    decimal fbizqty = Convert.ToDecimal(orderItem["fbizqty"]);
                    decimal TotalSignQty = Convert.ToDecimal(dataItem.Value);
                    if (TotalSignQty == 0)
                    {
                        objItem["fentrysignstatus"] = "entrysign_type_03"; // 未签收
                    }
                    else if (TotalSignQty >= fbizqty)
                    {
                        objItem["fentrysignstatus"] = "entrysign_type_01"; // 已签收
                    }
                    else
                    {
                        objItem["fentrysignstatus"] = "entrysign_type_02"; // 部分签收
                    }
                }
                else if (objItem == null && orderItem != null)
                {
                    objItem["fentrysignstatus"] = "";
                }
            }
            if (objs.Any())
            {
                var topCtx = this.Context.CreateTopOrgDBContext();
                var dbServiceEx = this.Context.Container.GetService<IDBServiceEx>();
                string delSql = $"delete t_ydj_e3logisticsprogress where forderid in ('{orderid}') AND fentryid in ('{string.Join("','", selectRowIds.Select(a => a.Id))}')";
                dbServiceEx.Execute(this.Context, delSql);
                // 直接保存数据库
                var dm = this.GetDataManager();
                dm.InitDbContext(topCtx, e3Html.GetDynamicObjectType(topCtx));
                dm.Save(objs);
                //var saveResult = this.Context.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(this.Context, e3htmlId, objs, "save", null);
                //saveResult.ThrowIfHasError(true, "E3物流进度查询中间表保存失败！");
            }
            //step2:更新销售合同明细

            var groupedData = datas
               .GroupBy(d => new { forderid = d["fid"], fentryid = d["fentryid"] })
               .Select(g => new
               {
                   forderid = g.Key.forderid,
                   fentryid = g.Key.fentryid,
                   TotalPobizqty = g.Sum(x => Convert.ToDecimal(x["pobizqty"])),//采购订单数量
                   TotalFbizqty = objs.Where(a => Convert.ToString(a["forderid"]).Equals(Convert.ToString(g.Key.forderid)) && Convert.ToString(a["fentryid"]).Equals(Convert.ToString(g.Key.fentryid))).Sum(x => Convert.ToDecimal(x["fbizqty"]))//签收数量
               })
               .ToList();
            List<DynamicObject> needSaveObjs = new List<DynamicObject>();
            foreach (var item in groupedData)
            {
                var orderItem = e.DataEntitys.Where(a => Convert.ToString(a["id"]).Equals(item.forderid)).FirstOrDefault();
                if (orderItem == null) continue;
                var orderEntry = orderItem["fentry"] as DynamicObjectCollection;
                var orderEntryItem = orderEntry.Where(a => Convert.ToString(a["id"]).Equals(item.fentryid)).FirstOrDefault();
                if (orderEntryItem == null) continue;
                var objItem = objs.Where(a => Convert.ToString(a["fentryid"]).Equals(item.fentryid)).FirstOrDefault();
                if (objItem == null) continue;
                // 判断行签收状态
                if (item.TotalPobizqty == item.TotalFbizqty)
                {
                    orderEntryItem["fentrysignstatus"] = "entrysign_type_01"; // 已签收
                }
                else if (item.TotalPobizqty > item.TotalFbizqty && item.TotalFbizqty > 0)
                {
                    orderEntryItem["fentrysignstatus"] = "entrysign_type_02"; // 部分签收
                }
                else if (item.TotalFbizqty == 0)
                {
                    orderEntryItem["fentrysignstatus"] = "entrysign_type_03"; // 未签收
                }
                else
                {
                    orderEntryItem["fentrysignstatus"] = ""; // 没有物流轨迹，就为空
                }
                if (needSaveObjs.Where(a => Convert.ToString(a["id"]).Equals(orderItem["id"])).FirstOrDefault() != null)
                {
                    needSaveObjs.Remove(needSaveObjs.Where(a => Convert.ToString(a["id"]).Equals(orderItem["id"])).FirstOrDefault());
                }
                needSaveObjs.Add(orderItem);
            }

            foreach (var order in needSaveObjs)
            {
                var orderEentry = order["fentry"] as DynamicObjectCollection;
                if (orderEentry == null || orderEentry.Count == 0) continue;
                var orderEntries = orderEentry.Where(a => Convert.ToString(a["fdeliverytype"]).Equals("delivery_type_01")).ToList();
                if (orderEentry == null || orderEentry.Count == 0) continue;


                // 处理套件头签收状态
                var suit = orderEntries.Where(a => !string.IsNullOrWhiteSpace(Convert.ToString(a["fsuitcombnumber"]))).ToList();
                foreach (var suitItem in suit)
                {
                    var suitNo = Convert.ToString(suitItem["fsuitcombnumber"]);
                    // 找到同套件号的子件，且fsubqty != 0
                    var subItems = orderEntries.Where(a => Convert.ToString(a["fsuitcombnumber"]) == suitNo && Convert.ToDecimal(a["fsubqty"]) != 0).ToList();
                    if (subItems.Count == 0) continue;

                    // 统计签收状态
                    bool _allNotSigned = subItems.All(a => Convert.ToString(a["fentrysignstatus"]) == "entrysign_type_03");
                    bool _allSigned = subItems.All(a => Convert.ToString(a["fentrysignstatus"]) == "entrysign_type_01");

                    var head = orderEntries.Where(a => Convert.ToString(a["fsuitcombnumber"]) == suitNo && Convert.ToDecimal(a["fsubqty"]) == 0).FirstOrDefault();
                    if (_allNotSigned)
                    {
                        head["fentrysignstatus"] = "entrysign_type_03"; // 未签收
                    }
                    else if (_allSigned)
                    {
                        head["fentrysignstatus"] = "entrysign_type_01"; // 已签收
                    }
                    else
                    {
                        head["fentrysignstatus"] = "entrysign_type_02"; // 部分签收
                    }
                }

                // 判断明细签收状态
                bool allSigned = orderEntries.All(a => Convert.ToString(a["fentrysignstatus"]) == "entrysign_type_01");
                bool hasPartialSign = orderEntries.Any(a => Convert.ToString(a["fentrysignstatus"]) == "entrysign_type_02" || Convert.ToString(a["fentrysignstatus"]) == "entrysign_type_01");
                bool allNotSigned = orderEntries.All(a => Convert.ToString(a["fentrysignstatus"]) == "entrysign_type_03");

                // 更新表头签收状态
                if (allSigned)
                {
                    order["fordersignstatus"] = "ordersign_type_01"; // 已签收
                }
                else if (hasPartialSign)
                {
                    order["fordersignstatus"] = "ordersign_type_02"; // 部分签收
                }
                else if (allNotSigned)
                {
                    order["fordersignstatus"] = "ordersign_type_03"; // 未签收
                }
                else
                {
                    order["fordersignstatus"] = "ordersign_type_03"; // 未签收
                }
            }
            var orderHtml = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            var grpOrgDatas = needSaveObjs.GroupBy(f => f["fmainorgid"].ToString()).ToList();
            foreach (var item in grpOrgDatas)
            {
                var ctx = this.Context.CreateAgentDBContext(item.Key);
                var _objs = needSaveObjs.Where(a => Convert.ToString(a["fmainorgid"]).Equals(item.Key)).ToList();
                // 直接保存数据库
                var dm = this.GetDataManager();
                dm.InitDbContext(ctx, orderHtml.GetDynamicObjectType(ctx));
                dm.Save(_objs);
                var billnos = _objs.Select(o => Convert.ToString(o["fbillno"])).ToList();
            }

            var logService = this.Container.GetService<ILogService>();

            logService?.WriteLog(this.Context, new LogEntry()
            {
                BillFormId = this.HtmlForm.Id,
                BillIds = Convert.ToString(orderid),
                BillNos = Convert.ToString(datas.FirstOrDefault()?["fbillno"]),
                LogType = Enu_LogType.RecordType_03,
                OpName = "物流进度查询",
                Content = $"物流进度查询,明细行：{string.Join(",", datas.Select(a => Convert.ToString(a["fentryid"])))}",
                Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                Level = Enu_LogLevel.Info.ToString(),
                OpDate = DateTime.Now
            });
            this.AddRefreshPageAction();




        }

        /// <summary>
        /// 获取符合条件的订单
        /// </summary>
        /// <returns></returns>
        private DynamicObjectCollection GetPendingPurchaseOrders(string orderid, List<Row> rowIds)
        {
            string sql = "";
            if (this.Context.IsDirectSale)
            {
                sql = $@"select a.fid,a.fbillno,a.fmainorgid,b.fentryid,b.fproductid,b.fbizqty,mat.fnumber,mat.fname,b.fattrinfo,b.fattrinfo_e,b.fcustomdes_e,b.fseq_e fseq_e,'' poentryid,'' as poId,b.fbizqty pobizqty,'0'  fhqderno ,'' pobillno
                        from t_ydj_order a with(nolock)
                        inner join t_ydj_orderentry b with(nolock) on a.fid=b.fid
                        inner join T_STK_POSTOCKINEntry poStockIn with(nolock) on a.fid=poStockIn.fsoorderinterid AND b.fentryid=poStockIn.fsoorderentryid
                        inner join t_bd_material mat with(nolock) on b.fproductid=mat.fid
                        where a.fpiecesendtag=1 and fordersignstatus<>'entrysign_type_01'  and b.fdeliverytype='delivery_type_01' 
                        AND a.fid='{orderid}' and b.fentryid in ('{string.Join("','", rowIds.Select(a => a.Id))}')    ";
            }
            else
            {
                sql = $@"select a.fid,a.fbillno,a.fmainorgid,b.fentryid,b.fproductid,b.fbizqty,mat.fnumber,mat.fname,b.fattrinfo,b.fattrinfo_e,b.fcustomdes_e,poentry.fseq_e,poentry.fentryid poentryid,poentry.fid as poId,poentry.fbizqty pobizqty,po.fhqderno ,po.fbillno  pobillno
                        from t_ydj_order a with(nolock)
                        inner join t_ydj_orderentry b with(nolock) on a.fid=b.fid
                        inner join t_ydj_poorderentry poentry with(nolock) on b.fentryid=poentry.fsoorderentryid
                        inner join t_ydj_purchaseorder po with(nolock) on poentry.fid=po.fid
                        inner join t_bd_material mat with(nolock) on b.fproductid=mat.fid
                        where a.fpiecesendtag=1 and fordersignstatus<>'entrysign_type_01' and b.fbizpurqty>0 and b.fdeliverytype='delivery_type_01' 
                        AND a.fid='{orderid}' and b.fentryid in ('{string.Join("','", rowIds.Select(a => a.Id))}')    ";
            }
            return this.DBService.ExecuteDynamicObject(this.Context, sql);
        }

    }
}
