using JieNor.Framework;
using JieNor.Framework.DataTransferObject.FileServer;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 响应文件服务器组件上传后保存文件的事件
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("fsw_uploadfile")]
    public class SyncFSWidgetUploadData : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 执行操作事务
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            var tranId = this.GetQueryOrSimpleParam<string>("CooTranId");

            var fsData = this.GetQueryOrSimpleParam<string>("fsData")?
                .FromJson<IEnumerable<FSWidgetFileInfo>>() ?? new FSWidgetFileInfo[] { };

            if (tranId.IsNullOrEmptyOrWhiteSpace()
                || fsData.Any() == false) return;

            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            var pkIdReader = this.Context.GetPkIdDataReader(this.HtmlForm, "ftranid=@tranid",
                new SqlParam[]
                {
                    new SqlParam("tranid", System.Data.DbType.String, tranId)
                });
            var orderObj = dm.SelectBy(pkIdReader).OfType<DynamicObject>()
                .FirstOrDefault();
            if (orderObj.IsNullOrEmpty()) return;

            var fileAttachEntity = this.HtmlForm.GetEntryEntity("FDrawEntity");
            var fileEntryObjs = fileAttachEntity.DynamicProperty.GetValue<DynamicObjectCollection>(orderObj);
            var originalFileEntryObjs = fileEntryObjs.ToList();
            fileEntryObjs.Clear();
            foreach (var fileInfo in fsData)
            {
                DynamicObject existFileObj = null;
                for (int i = originalFileEntryObjs.Count - 1; i >= 0; i--)
                {
                    if (StringUtils.EqualsIgnoreCase(originalFileEntryObjs[i]["ffileid"] as string, fileInfo.FileId)
                      || StringUtils.EqualsIgnoreCase(originalFileEntryObjs[i]["id"] as string, fileInfo.CooSrcId))
                    {
                        existFileObj = originalFileEntryObjs[i];
                        originalFileEntryObjs.RemoveAt(i);
                        break;
                    }
                }
                if (existFileObj == null)
                {
                    existFileObj = fileEntryObjs.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                }

                existFileObj["ffileid"] = fileInfo.FileId;
                existFileObj["ffilename"] = fileInfo.FileName;
                existFileObj["ffileformat"] = fileInfo.FileFormat;
                existFileObj["ffilesize"] = fileInfo.FileSize;
                existFileObj["fuploader"] = fileInfo.Uploader;
                existFileObj["fuploaderid"] = fileInfo.UploaderId;
                existFileObj["fuptime"] = fileInfo.UploadTime;
                existFileObj["fnote"] = fileInfo.Description;
                fileEntryObjs.Add(existFileObj);

            }
            var pkService = this.Container.GetService<IDataEntityPkService>();
            pkService.AutoSetPrimaryKey(this.Context, orderObj, dm.DataEntityType);
            dm.Save(orderObj);
        }
    }
}
