using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：图纸提交审核
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("submitthe")]
    public class SubmitThe : SubmitBase
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            //选中图纸行Id
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<string>() : JsonConvert.DeserializeObject<List<string>>(rowIds);
            var fentrymsg = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!newData["fbilltype"].IsNullOrEmpty() && newData["fbilltype"].Equals("ydj_order_vsix"))
                {
                    var drawFentry = newData["fdrawentity"] as DynamicObjectCollection;
                    var auditStatus = new string[] { "draw_status_02", "draw_status_03", "draw_status_05", "draw_status_06" };
                    var auditFileFormat = new string[] { "ord", "dwg" };
                    if (selectRowIds.IsNullOrEmpty() || !drawFentry.Any(x => selectRowIds.Contains(x["id"])))
                    {
                        fentrymsg = "对不起，请先选择需要提交审批的图纸行！";
                        return false;
                    }
                    else if (drawFentry.Any(x => !x["fdrawstatus"].Equals("draw_status_01") && !x["fdrawstatus"].Equals("draw_status_04")))
                    {
                        fentrymsg = "对不起，仅允许勾选“新建”或“已驳回”状态图纸！";
                        return false;
                    }
                    else if (!drawFentry.Any(x => auditStatus.Contains(x["fdrawstatus"]) && auditFileFormat.Contains(x["ffileformat"].ToString().ToLower()) && !selectRowIds.Contains(x["id"]))
                    && !drawFentry.Any(x => auditFileFormat.Contains(x["ffileformat"].ToString().ToLower()) && selectRowIds.Contains(x["id"])))
                    {
                        fentrymsg = "对不起，提交总部审核必须包含ord或dwg格式图纸！";
                        return false;
                    }
                    //已在保存处判断
                    //else if (drawFentry.IsNullOrEmpty()|| 
                    //drawFentry.Any(x=>x["ffileid"].IsNullOrEmptyOrWhiteSpace()|| x["ffilename"].IsNullOrEmptyOrWhiteSpace()||
                    //x["fdrawstatus"].IsNullOrEmptyOrWhiteSpace() || x["ffileformat"].IsNullOrEmptyOrWhiteSpace() ||
                    //x["ffilesize"].IsNullOrEmptyOrWhiteSpace() || x["fuploader"].IsNullOrEmptyOrWhiteSpace()||
                    //x["fuploaderid"].IsNullOrEmptyOrWhiteSpace() || x["fuptime"].IsNullOrEmptyOrWhiteSpace()))
                    //{
                    //    fentrymsg = "对不起，当前订单图纸不存在，或者图纸信息补充不完整，请检查！";
                    //    return false;
                    //}
                    return true;
                }
                else
                {
                    fentrymsg = "该订单单据类型非v6定制柜合同，操作失败！";
                    return false;
                }
            }).WithMessage("{0}", (billObj, propObj) => fentrymsg));
        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<string>() : JsonConvert.DeserializeObject<List<string>>(rowIds);
            foreach (var item in e.DataEntitys)
            {
                var drawFentry = item["fdrawentity"] as DynamicObjectCollection;
                var drawData = drawFentry.Where(x => selectRowIds.Contains(x["id"]));
                foreach (var draw in drawData)
                {
                    draw["fdrawstatus"] = "draw_status_02";
                }
            }
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(Context));
            dm.Save(e.DataEntitys);
        }
    }
}
