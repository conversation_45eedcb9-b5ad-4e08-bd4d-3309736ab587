using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：下推转单申请
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("TransferOrderApply")]
    public class PushTransferOrderApply : BasePushTransferOrderApply
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var cancelstatus = Convert.ToBoolean(newData["fcancelstatus"]);
                return !cancelstatus;
            }).WithMessage("销售合同已作废"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var status = Convert.ToString(newData["fstatus"]);
                if (!status.EqualsIgnoreCase("E"))
                {
                    return false;
                }
                return true;
            }).WithMessage("销售合同未审核"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var needtransferorder = Convert.ToBoolean(newData["fneedtransferorder"]);
                return needtransferorder;
            }).WithMessage("需转单未勾选"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var freceivable = Convert.ToDecimal(newData["freceivable"]);
                return freceivable > 0;
            }).WithMessage("未确认收款"));

            //1. 在 销售合同 列表 点击 <转单申请> 时, 如果勾选的商品 不为总部的商品 , 提示错误"勾选行商品存在非总部商品, 不允许提交转单申请!" 
            var mainorgid = this.Context.IsTopOrg ? this.Context.Company : this.Context.TopCompanyId;
            var errorMessage = string.Empty;
            var refMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, this.HtmlForm.Id);
            var sourceFormDt = htmlForm.GetDynamicObjectType(this.Context);
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";
                //加载引用数据
                refMgr.Load(this.Context, sourceFormDt, newData, false);
                var entrys = newData["fentry"] as DynamicObjectCollection;

                var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
                var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);
                foreach (var entry in entrys)
                {
                    var product = entry["fproductid_ref"] as DynamicObject;
                    string id = Convert.ToString(entry["Id"]);
                    if (selectRowIds.Exists(x => x.Id == id))
                    {
                        if (Convert.ToString(product["fmainorgid"]) != mainorgid)
                        {
                            errorMessage += $"勾选行商品存在非总部商品, 不允许提交转单申请";
                            return false;
                        }
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            //商品明细已关闭的不允许再转单, 提示错误"商品明细已关闭, 不允许提交转单申请!" 
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";
                //选中商品行Id
                var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
                var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);
                //< !--'0':'正常','1':'整单关闭','2':'部分关闭','3':'自动关闭','4':'手动关闭'-- >    
                var status = new List<string>() { "0", "2" };
                var entrys = newData["fentry"] as DynamicObjectCollection;
                if (entrys.Any(t => !status.Contains(t["fclosestatus_e"].ToString()) && (selectRowIds.Count == 0 || selectRowIds.Any(s => s.Id.Contains(t["id"].ToString())))))
                {
                    errorMessage += $"勾选行商品明细已关闭, 不允许提交转单申请";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            var order = e.DataEntitys[0];
            List<string> msg = new List<string>();
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var rows = JsonConvert.DeserializeObject<List<Row>>(rowIds);
            var entries = order["fentry"] as DynamicObjectCollection;

            List<DynamicObject> selectedEntrys = new List<DynamicObject>();

            foreach (var entry in entries)
            {
                var entryid = Convert.ToString(entry["id"]);
                var seq = Convert.ToString(entry["fseq"]);
                var status = Convert.ToString(entry["ftransferorderstatus"]);
                var closeStatus = Convert.ToString(entry["fclosestatus_e"]);

                if (!rows.Any(s => s.Id.EqualsIgnoreCase(entryid)))
                {
                    continue;//如果满足if里面的条件,则不执行循环语句里面的剩余内容,跳出循环,执行下一次循环
                }

                if (!status.IsNullOrEmptyOrWhiteSpace() && Convert.ToInt32(status) != (int)TransferOrderStatus.Reject)
                {
                    msg.Add($"【商品信息】明细行{seq}已转单，不允许重复转单");
                }
                if (closeStatus != ((int)CloseStatus.Default).ToString())
                {
                    msg.Add($"【商品信息】明细行{seq}已关闭，不允许转单");
                }

                selectedEntrys.Add(entry);
            }

            if (msg.Count > 0)
            {
                throw new BusinessException(string.Join("</br>", msg));
            }

            this.CheckDeliver(order, selectedEntrys);
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }
            var loger = this.Context.Container.GetService<ILogService>();
            var order = e.DataEntitys[0];
            var orderBillNo = order["fbillno"].ToString();
            //var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            //var rows = JsonConvert.DeserializeObject<List<Row>>(rowIds);
            List<Row> _rows = new List<Row>();
            var entry = order["fentry"] as DynamicObjectCollection;
            foreach (var item in entry)
            {
                _rows.Add(new Row()
                {
                    Id = Convert.ToString(item["id"])
                });
            }
            _rows.AddRange(GetSameComboList(order, _rows));
            var applyBillNoList = new List<string>();
            this.Option.SetVariableValue("fbatchno", Guid.NewGuid().ToString("N"));

            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【接单方发起转单申请】，内容:{4}".Fmt(this.Context.UserName,
            this.Context.UserPhone, this.Context.Company,
                 DateTime.Now.ToString("HH:mm:ss"), "rows：" + JsonConvert.SerializeObject(_rows)),
                "PushTransferOrderApply");
            var argetDataObjects = new List<DynamicObject>();
            foreach (var row in _rows)
            {
                var dynamicObjects = new List<DynamicObject>();
                var applyBillNos = new List<string>();
                Push(new SelectedRow()
                {
                    PkValue = order["id"] as string,
                    BillNo = orderBillNo,
                    EntryPkValue = row.Id,
                    EntityKey = "fentry"
                }, out applyBillNos, out dynamicObjects);
                applyBillNoList.AddRange(applyBillNos);
                argetDataObjects.AddRange(dynamicObjects);
            }
            if (_rows.Count!=applyBillNoList.Count)
            {
                this.Result.ComplexMessage.ErrorMessages.Add($"销售合同【{orderBillNo}】发起转单申请单失败，有明细行未生成转单申请单");
                throw new BusinessException($"销售合同【{orderBillNo}】发起转单申请单失败，有明细行未生成转单申请单");
            }
            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【接单方发起转单申请】，内容:{4}".Fmt(this.Context.UserName,
            this.Context.UserPhone, this.Context.Company,
                 DateTime.Now.ToString("HH:mm:ss"), "applyBillNoList：" + JsonConvert.SerializeObject(applyBillNoList)),
                "PushTransferOrderApply");
            this.Option.SetVariableValue("TargetDataObjects_ToMusi", argetDataObjects);
            this.Result.IsSuccess = true;
            this.Result.ComplexMessage.SuccessMessages.Add($"销售合同【{orderBillNo}】转单申请成功：{string.Join(",", applyBillNoList)}");
        }

        /// <summary>
        /// 下推
        /// </summary>
        /// <param name="selectRow"></param>
        /// <param name="list"></param>
        /// <returns></returns>
        private bool Push(SelectedRow selectRow, out List<string> list, out List<DynamicObject> dynamicObjects)
        {
            list = new List<string>();
            dynamicObjects = new List<DynamicObject>();
            List<SelectedRow> selectRows = new List<SelectedRow>();
            if (selectRow != null)
            {
                selectRows.Add(selectRow);
            }

            var billCvtCtx = new BillConvertContext()
            {
                RuleId = "ydj_order2ydj_transferorderapply",
                SourceFormId = "ydj_order",
                TargetFormId = OrderCommon.TransferOrderApplyFormId,
                SelectedRows = selectRows.ToConvertSelectedRows(),
                Option = this.Option
            };

            var convertService = this.Container.GetService<IConvertService>();
            var result = convertService.Push(this.Context, billCvtCtx);
            result?.ThrowIfHasError(true, $"销售合同下推转单申请单操作时出错！");
            var convertResult = result.SrvData as ConvertResult;

            var invokeResult = this.Gateway.InvokeBillOperation(this.Context,
                billCvtCtx.TargetFormId,
                convertResult.TargetDataObjects,
                "submit",
                new Dictionary<string, object>());
            if (invokeResult.IsSuccess)
            {
                foreach (var targetData in convertResult.TargetDataObjects)
                {
                    list.Add(targetData["fbillno"].ToString());
                }
                dynamicObjects.AddRange(convertResult.TargetDataObjects);
            }
            else
                invokeResult?.ThrowIfHasError(true, $"转单申请单提交操作时出错！");
            return invokeResult.IsSuccess;
        }

        /// <summary>
        /// 获取“未选中且未转单的商品明细”中与“已勾选明细”存在相同【套件组合号】或【配件组合号】或【沙发组合号】的行
        /// </summary>
        /// <returns></returns>
        private List<Row> GetSameComboList(DynamicObject order, List<Row> rows)
        {
            List<Row> sameRow = new List<Row>();
            List<string> entryIds = rows.Select(x => x.Id).ToList();
            var entries = order["fentry"] as DynamicObjectCollection;
            if (entries != null && entries.Count() > 0)
            {
                var selectedEntries = entries.Where(x => entryIds.Contains(x["id"].ToString())).ToList(); //勾选中的明细 
                var otherEntries = entries.Where(x => !entryIds.Contains(x["id"].ToString()) && x["ftransferorderstatus"].IsNullOrEmptyOrWhiteSpace()).ToList(); //其他未被选中且未发起转单申请的明细
                if (otherEntries != null && otherEntries.Count() > 0)
                {
                    foreach (var other in otherEntries)
                    {
                        foreach (var selected in selectedEntries)
                        {
                            if ((!string.IsNullOrWhiteSpace(selected["fsuitcombnumber"].ToString()) && selected["fsuitcombnumber"].ToString() == other["fsuitcombnumber"].ToString())
                                || (!string.IsNullOrWhiteSpace(selected["fpartscombnumber"].ToString()) && selected["fpartscombnumber"].ToString() == other["fpartscombnumber"].ToString())
                                || (!string.IsNullOrWhiteSpace(selected["fsofacombnumber"].ToString()) && selected["fsofacombnumber"].ToString() == other["fsofacombnumber"].ToString()))
                            {
                                var id = other["id"].ToString();
                                if (!sameRow.Exists(x => x.Id == id))
                                {
                                    sameRow.Add(new Row()
                                    {
                                        Id = id
                                    });
                                }
                            }
                        }
                    }
                }
            }
            return sameRow.Distinct().ToList();
        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            //慕思中台业务对象设置手工同步
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_transferorderapply");
            var fieldMapObjs = this.Container.GetService<IMuSiBizObjMapService>()
            .GetBizObjMaps(this.Context, htmlForm, Enu_MuSiSyncDir.CurrentToMuSi, Enu_MuSiSyncTimePoint.SyncManual);
            if (!fieldMapObjs.IsNullOrEmpty())
            {
                this.Option.TryGetVariableValue("TargetDataObjects_ToMusi", out List<DynamicObject> targetDataObjects);
                if (targetDataObjects != null && targetDataObjects.Count > 0)
                {
                    IMuSiService muSiService = this.Container.GetService<IMuSiService>();
                    muSiService.SyncAsync(this.Context, htmlForm, targetDataObjects);
                }
            }
        }
    }

    public class BasePushTransferOrderApply : AbstractOperationServicePlugIn
    {
        [InjectProperty]
        protected IOrderService OrderService { get; set; }

        /// <summary>
        /// 检查送达方
        /// </summary>
        /// <param name="syncingEntrys"></param>
        protected void CheckDeliver(DynamicObject order, List<DynamicObject> syncingEntrys)
        {
            var entryMatchDelivers = this.OrderService.GetEntryMatchDelivers(this.Context, order, syncingEntrys);

            foreach (var entry in syncingEntrys)
            {
                var entryId = Convert.ToString(entry["id"]);
                if (entryMatchDelivers.TryGetValue(entryId, out var matchDelivers))
                {
                    if (matchDelivers != null && matchDelivers.Count > 0)
                    {
                        continue;
                    }
                }
                throw new BusinessException("当前提交匹配不到送达方, 无法执行该操作, 请联系总部人员协助处理 !");
            }

            /*
             * Feat#4620 【慕思项目-正式环境】 非标审批/转单审批 接口调整, 增加送达方字段对接
             * 3.   在《销售合同》商品明细点击 <转单申请>时, 只针对一级经销商增加校验, 检查商品行给到中台时是否会存在多个【送达方】的情况, 如果存在则需要用户先手工选择在提交
             *  1)   首先如果当前企业对应《经销商》没有勾选上【是否分销商】 且 对应商品行的【送达方】为空的商品行, 就会进行该校验
             *  2)   获取当前企业对应《经销商》匹配 未禁用的《主经销商配置表》表头的【主经销商】, 获取该《主经销商配置表》表头的【主经销商】与 表体的【子经销商】
             *  3)   根据当前企业对应《经销商》的【实控人ID】 + 刚刚《主经销商配置表》表头的【主经销商】与 表体的【子经销商】, 找到所有相同【实控人ID】且 相同【经销商】(只要匹配到其中一个即可) 且 未禁用 的《送达方》
             *  4)   再来需要获取城市的信息作为匹配送达方的条件之一,  根据该《销售合同》表头的【门店名称】(销售部门)的 对应《部门》的【关联门店】, 找到对应《门店》基础资料的【城市】, 匹配对应《送达方》的【城市】, 进行过滤
             *  5)   同时根据该《销售合同》的商品明细的【业绩品牌】, 匹配 《送达方》 品牌信息的【系列】
             *  6)   用4与5的条件匹配上送达方时, 如果匹配到多个时提示用户 "当前明细第X行,   商品 XXXX 存在多个送达方, 请手工填入送达方后再次提交 ! "
             *  7)   此时《销售合同》的商品明细的【送达方】, 仅针对报错的那行解锁 允许编辑【送达方】, 并且可以选择的【送达方】按照 1~4 的逻辑过滤出可选的《送达方》
             *  8)   用户填写后该行【送达方】有值, 就可以不用再次校验该商品行的送达方是否存在多个了
             */
            Dictionary<string, List<string>> entryDeliverIds = new Dictionary<string, List<string>>();
            var agent = this.Context.LoadBizDataById("bas_agent", this.Context.Company);
            if (agent != null)
            {
                var fisreseller = Convert.ToBoolean(agent["fisreseller"]);
                if (!fisreseller)
                {
                    var productIds = syncingEntrys.Select(s => Convert.ToString(s["fproductid"])).ToList();
                    var products = this.Context.LoadBizBillHeadDataById("ydj_product", productIds, "fname");

                    foreach (var entry in syncingEntrys)
                    {
                        var entryId = Convert.ToString(entry["id"]);

                        entryMatchDelivers.TryGetValue(entryId, out var matchDelivers);
                        if (matchDelivers != null && matchDelivers.Count > 1)
                        {
                            var seq = Convert.ToInt32(entry["Fseq"]);
                            var productId = Convert.ToString(entry["fproductid"]);
                            var product = products.FirstOrDefault(s =>
                                Convert.ToString(s["id"]).EqualsIgnoreCase(productId));
                            this.Result.ComplexMessage.WarningMessages.Add($"当前明细第{seq}行,商品 {product?["fname"]} 存在多个送达方, 请手工填入送达方后再次提交 ! ");

                            entryDeliverIds[entryId] = matchDelivers.Select(s => Convert.ToString(s["id"])).ToList();
                        }
                    }
                }
            }

            if (this.Result.ComplexMessage.WarningMessages.Any())
            {
                this.Result.SrvData = entryDeliverIds;
                throw new BusinessException("");
            }

            this.Option.SetVariableValue("__EntryMatchDelivers__", entryMatchDelivers);
        }
    }
}