using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.LinkProgress
{
    /// <summary>
    /// 关联流程：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_linkprogress")]
    [OperationNo("Save")]
    public class Save: AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var errorMessage = new StringBuilder();
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var result = true;
                //当前关联流程名称
                var name = Convert.ToString(newData["fname"]);
                //主体的表单id
                var bizObjectId = Convert.ToString(newData["fbizobject"]);
                //主体的关联流程节点名称的fieldid
                var linkNodeFieldId = Convert.ToString(newData["flinknode"]);
                //主体的关联流程节点权重的fieldid
                var linkWeightFieldId = Convert.ToString(newData["flinkweight"]);
                //当前关联流程节点明细
                var progressEntries = newData["fentry"] as DynamicObjectCollection;
                //主体的表单
                var bizForm = this.MetaModelService.LoadFormModel(this.Context, bizObjectId);
                if (string.IsNullOrWhiteSpace(bizObjectId))
                {
                    result = false;
                    errorMessage.AppendLine($"{name}的流程主体业务表单必录!");
                    //表单不存在时不必再分析其字段
                    return result;
                }
                if (false == string.IsNullOrWhiteSpace(bizObjectId) && bizForm == null)
                {
                    result = false;
                    errorMessage.AppendLine($"{name}配置的流程主体业务表单{bizObjectId}并不存在!");
                    //表单不存在时不必再分析其字段
                    return result;
                }
                //主体的关联流程节点名称的字段
                if (string.IsNullOrWhiteSpace(linkNodeFieldId))
                {
                    result = false;
                    errorMessage.AppendLine($"{name}的关联节点字段必录!");
                }
                var linkNodeField = bizForm.GetField(linkNodeFieldId);
                if (false == string.IsNullOrWhiteSpace(linkNodeFieldId) && linkNodeField == null)
                {
                    result = false;
                    errorMessage.AppendLine($"{name}关联节点字段配置错误，流程主体{bizForm.Caption}并不存在字段{linkNodeFieldId}!");
                }
                //主体的关联流程权重的字段
                if (string.IsNullOrWhiteSpace(linkWeightFieldId))
                {
                    result = false;
                    errorMessage.AppendLine($"{name}的关联权重字段必录!");
                }
                var linkWeightField = bizForm.GetField(linkWeightFieldId);
                if (false == string.IsNullOrWhiteSpace(linkWeightFieldId) && linkWeightField == null)
                {
                    result = false;
                    errorMessage.AppendLine($"{name}关联权重字段配置错误，流程主体{bizForm.Caption}并不存在字段{linkWeightFieldId}!");
                }
                //检测主体表单的节点权重字段和节点名称字段是不是同一个明细层级
                if (linkWeightField != null && linkNodeField != null && false == linkWeightField.EntityKey.EqualsIgnoreCase(linkNodeField.EntityKey))
                {
                    result = false;
                    errorMessage.AppendLine($"{name}配置错误，流程主体{bizForm.Caption}字段{linkNodeField.Caption}与字段{linkWeightField.Caption}不是同一层级!");
                }

                //按分支名称分组关联明细
                var groupEntries = progressEntries.GroupBy(x => Convert.ToString(x["fbranchname"]));

                foreach(var groupEntry in groupEntries)
                {
                    var branchName = groupEntry.Key;
                    var nodeNames = new List<string>();
                    var formIds = new List<string>();

                    if (string.IsNullOrWhiteSpace(branchName))
                    {
                        result = false;
                        errorMessage.AppendLine($"{name}节点信息配置错误，分支名称必录!");
                        continue;
                    }

                    //遍历所有关联流程节点
                    foreach (var prgEntry in progressEntries)
                    {
                        //关联流程节点名称
                        var nodeName = Convert.ToString(prgEntry["fnodename"]);

                        if (string.IsNullOrWhiteSpace(nodeName))
                        {
                            result = false;
                            errorMessage.AppendLine($"{name}节点信息配置错误,结点名称必录!");
                            continue;
                        }

                        if (nodeNames.Contains(nodeName))
                        {
                            result = false;
                            errorMessage.AppendLine($"{name}节点信息配置错误，同一个{branchName}分支，不可以重复配置相同的结点名称!");
                        }
                        else
                        {
                            nodeNames.Add(nodeName);
                        }

                        var linkBizObjectId = Convert.ToString(prgEntry["flinkbizobject"]);
                        if (string.IsNullOrWhiteSpace(linkBizObjectId))
                        {
                            result = false;
                            errorMessage.AppendLine($"{name}节点信息配置错误，{branchName}分支的{nodeName}配置的关联对象必录存在!");
                            //表单不存在时不必再分析其字段，所以continue掉下面的程序
                            continue;
                        }

                        var linkBizForm = this.MetaModelService.LoadFormModel(this.Context, linkBizObjectId);
                        if (linkBizForm == null)
                        {
                            result = false;
                            errorMessage.AppendLine($"{name}节点信息配置错误，{branchName}分支的{nodeName}配置的关联对象{linkBizObjectId}并不存在!");
                            //表单不存在时不必再分析其字段，所以continue掉下面的程序
                            continue;
                        }

                        if (formIds.Contains(linkBizObjectId))
                        {
                            result = false;
                            errorMessage.AppendLine($"{name}节点信息配置错误，同一个{branchName}分支，不可以重复配置相同的关联对象{linkBizForm.Caption}!");
                        }
                        else
                        {
                            formIds.Add(linkBizObjectId);
                        }

                        //关联流程节点的业务表单与主体表单的编号关联的字段id,即linkBizForm[linkBillNoFieldId]=bizForm[bizForm.NumberFldKey]
                        var linkBillNoFieldId = Convert.ToString(prgEntry["flinkbillno"]);
                        //关联流程节点的业务表单与主体表单的明细id关联的字段id,即linkBizForm[linkEntryFieldId]=bizForm.Entry["id"]
                        var linkEntryFieldId = Convert.ToString(prgEntry["flinkentryid"]);
                        //关联流程节点的业务表单的来源单id关联的字段id,即linkBizForm[linkSourceFormId]=linkBizForm.SourceForm.id
                        var linkSourceFormId = Convert.ToString(prgEntry["flinksourceformid"]);

                        if (string.IsNullOrWhiteSpace(linkBillNoFieldId))
                        {
                            result = false;
                            errorMessage.AppendLine($"{name}关联主体编号字段配置错误，{branchName}分支的{nodeName}的关联主体编号字段必录!");
                        }

                        if (string.IsNullOrWhiteSpace(linkSourceFormId))
                        {
                            result = false;
                            errorMessage.AppendLine($"{name}关联对象源单字段配置错误，{branchName}分支的{nodeName}的关联对象源单字段必录!");
                        }

                        //关联流程节点的业务表单与主体表单的编号关联的字段
                        var linkBillNoField = linkBizForm.GetField(linkBillNoFieldId);
                        //关联流程节点的业务表单与主体表单的明细id关联的字段
                        var linkEntryField = linkBizForm.GetField(linkEntryFieldId);
                        //关联流程节点的业务表单的来源单id关联的字段
                        var linkSourceFormField = linkBizForm.GetField(linkSourceFormId);

                        if (false == string.IsNullOrWhiteSpace(linkBillNoFieldId) && linkBillNoField == null)
                        {
                            result = false;
                            errorMessage.AppendLine($"{name}关联主体编号字段配置错误，{branchName}分支的{nodeName}配置的关联对象{linkBizForm.Caption}并不存在{linkBillNoFieldId}字段!");
                        }

                        if (false == string.IsNullOrWhiteSpace(linkSourceFormId) && linkSourceFormField == null)
                        {
                            result = false;
                            errorMessage.AppendLine($"{name}关联对象源单字段配置错误，{branchName}分支的{nodeName}配置的关联对象{linkBizForm.Caption}并不存在{linkSourceFormId}字段!");
                        }

                        if (linkBillNoField != null && 
                            linkSourceFormField != null && 
                            false == linkBillNoField.EntityKey.EqualsIgnoreCase(linkSourceFormField.EntityKey))
                        {
                            result = false;
                            errorMessage.AppendLine($"{name}节点信息配置错误，{branchName}分支的{nodeName}配置的关联主体编号字段[{linkBillNoField.Caption}]与关联对象源单字段[{linkSourceFormField.Caption}]在{linkBizForm.Caption}中不是同一明细层级的字段");
                        }

                        if (linkEntryField == null && linkBillNoField != null && false == linkBillNoField.IsBillHeadField)
                        {
                            result = false;
                            errorMessage.AppendLine($"{name}节点信息配置错误，{branchName}分支的{nodeName}配置的关联主体明细字段为空时,关联主体编号字段[{linkBillNoField.Caption}]必须是{linkBizForm.Caption}的表头字段");
                        }

                        if (linkEntryField != null && linkBillNoField != null && false == linkEntryField.EntityKey.EqualsIgnoreCase(linkBillNoField.EntityKey))
                        {
                            result = false;
                            errorMessage.AppendLine($"{name}节点信息配置错误，{branchName}分支的{nodeName}配置的关联主体编号字段[{linkBillNoField.Caption}]与关联主体明细字段[{linkEntryField.Caption}]在{linkBizForm.Caption}中不是同一明细层级的字段");
                        }

                        if (linkWeightField != null && 
                            linkBillNoField != null && 
                            linkWeightField.IsBillHeadField && 
                            false == linkBillNoField.IsBillHeadField)
                        {
                            result = false;
                            errorMessage.AppendLine($"{name}节点信息配置错误，{branchName}分支的{nodeName}配置的关联主体编号字段[{linkBillNoField.Caption}]是{linkBizForm.Caption}表体字段，而{linkWeightField.Caption}是{bizForm.Caption}表头字段!");
                        }

                        if (linkWeightField != null &&
                            linkBillNoField != null &&
                            false == linkWeightField.IsBillHeadField &&
                            linkBillNoField.IsBillHeadField)
                        {
                            result = false;
                            errorMessage.AppendLine($"{name}节点信息配置错误，{branchName}分支的{nodeName}配置的关联主体编号字段[{linkBillNoField.Caption}]是{linkBizForm.Caption}表头字段，而{linkWeightField.Caption}是{bizForm.Caption}表体字段!");
                        }
                    }
                }
                
                return result;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage.ToString()));
        }

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            e.DataEntitys = checkUnique(e.DataEntitys);

            calculateSumfWeight(e.DataEntitys);
        }

        /// <summary>
        /// 计算明细总权限重
        /// </summary>
        /// <param name="dataEntities"></param>
        private void calculateSumfWeight(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            foreach(var dataEntity in dataEntities)
            {
                var fentries = dataEntity["fentry"] as DynamicObjectCollection;
                if (fentries == null || fentries.Count <= 0)
                {
                    continue;
                }
                foreach(var fentry in fentries)
                {
                    var branchWeight = Convert.ToInt32(fentry["fbranchweight"]);
                    var nodeWeight = Convert.ToInt32(fentry["fnodeweight"]);
                    fentry["fsumweight"] = branchWeight * 10000 + nodeWeight;
                }
            }
        }

        /// <summary>
        /// 检查唯一性
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private DynamicObject[] checkUnique(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return dataEntities;
            }
            var groups = dataEntities.GroupBy(x => Convert.ToString(x["fbizobject"])).ToList();
            var repeats = groups.Where(x => x.Count() > 1).ToList();
            var singles = groups.Where(x => x.Count() == 1).ToList();

            if (repeats != null && repeats.Count > 0)
            {
                foreach(var repeat in repeats)
                {
                    var formId = repeat.Key;
                    var htmlForm = this.MetaModelService.LoadFormModel(this.Context, formId);
                    var numbers = repeat.Select(x => Convert.ToString(x[this.HtmlForm.NumberFldKey])).ToList();
                    this.Result.ComplexMessage.ErrorMessages.Add($"编号[{string.Join(",", numbers)}]{this.HtmlForm.Caption}重复定义了{htmlForm.Caption}的关联流程!");
                }
            }

            if (singles == null || singles.Count <= 0)
            {
                return Array.Empty<DynamicObject>();
            }

            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));

            var bizObjectIds = singles.SelectMany(x => x.Select(y => Convert.ToString(y["fbizobject"]))).ToList();
            var where = $"fmainorgid='{this.Context.Company}'";
            var dataReader = this.Context.GetPkIdDataReaderWithHeadField(this.HtmlForm, "fbizobject", bizObjectIds, where);
            var dbEntities = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();

            if (dbEntities == null || dbEntities.Count <= 0)
            {
                return singles.SelectMany(x => x).ToArray();
            }

            var results = new List<DynamicObject>();
            foreach(var dataEntity in singles.SelectMany(x => x))
            {
                var bizObjectId = Convert.ToString(dataEntity["fbizobject"]);
                var id = Convert.ToString(dataEntity["id"]);
                var existedItem = dbEntities.FirstOrDefault(x => Convert.ToString(x["fbizobject"]).EqualsIgnoreCase(bizObjectId) && 
                                                                 false == Convert.ToString(x["id"]).EqualsIgnoreCase(id));
                if (existedItem == null)
                {
                    results.Add(dataEntity);
                    continue;
                }
                var htmlForm = this.MetaModelService.LoadFormModel(this.Context, bizObjectId);
                this.Result.ComplexMessage.ErrorMessages.Add($"已存在流程主体为{htmlForm.Caption}的{this.HtmlForm.Caption}");
            }
            return results.ToArray();
        }
    }
}
