using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System.Data;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Plugin.STE.AfterFeedback
{
    /// <summary>
    /// 获取售后反馈单售后信息
    /// </summary>
    [InjectService]
    [FormId("ste_afterfeedback")]
    [OperationNo("getfeedbackinfo")]
    public class GetFeedBackInfo : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys.IsNullOrEmpty() || !e.DataEntitys.Any()) return;
            //售后反馈单主键ID
            var feedbackno = this.GetQueryOrSimpleParam<string>("feedbackno");
            if (feedbackno.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.SrvData = null;
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage="请补齐参数信息！";
                return;
            }
            this.Result.IsSuccess = true;
            this.Result.SrvData = this.Context.LoadBizDataByNo("ste_afterfeedback","fbillno", new List<string> { feedbackno }, false).FirstOrDefault();
        }
    }
}