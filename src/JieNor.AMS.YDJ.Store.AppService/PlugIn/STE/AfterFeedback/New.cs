using JieNor.AMS.YDJ.Store.AppService.Service;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.AfterFeedback
{
    /// <summary>
	/// 新增时逻辑处理
	/// </summary>
	[InjectService]
    [FormId("ste_afterfeedback")]
    [OperationNo("New")]
    public class New : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
        }
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }
            //查找符合条件的慕思经销商数据
            var crmAgentService = this.Container.GetService<CrmDistributorService>();
            var idList = crmAgentService.GetBizCrmAgentIdDic(this.Context);
            //当前登录用户关联的员工【业务类别】包含“售后或通用”，则售后人员需自动默认取当前员工
            var staffObj = this.Context.LoadBizDataByACLFilter("ydj_staff", $"flinkuserid='{this.Context.UserId}'");
            Framework.SuperOrm.DataEntity.DynamicObject staffInfo = null;
            if (!staffObj.IsNullOrEmpty())
            {
                staffInfo = staffObj.Where(x => (x["fentity"] as DynamicObjectCollection).Any(y => y["fbiztype"] != null && (y["fbiztype"].ToString() == "7" || y["fbiztype"].ToString() == "0")))?.FirstOrDefault();
            }
            foreach (var data in e.DataEntitys)
            {
                if (!staffInfo.IsNullOrEmpty())
                {
                    data["fstaffid"] = staffInfo["id"];
                    data["fphone"] = staffInfo["fphone"];
                    data["fdeptid"] = staffInfo["fdeptid"];
                }
                if (idList != null && idList.Count == 1)
                {
                    data["fagentid"] = idList.First().Key;
                    data["fauthcity"] = idList.First().Value;
                }
            }
        }
    }
}
