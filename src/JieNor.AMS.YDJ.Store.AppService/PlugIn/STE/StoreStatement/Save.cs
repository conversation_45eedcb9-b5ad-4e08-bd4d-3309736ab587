using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.StoreStatement
{
    /// <summary>
    /// 店面结算对账单：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_storestatement")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            //检查合同是否已加入店面对账单
            e.DataEntitys = checkExistedOrder(e.DataEntitys);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            //计算经销商结算金额
            calculateCostAmount(e.DataEntitys);
            //更新收支记录
            updateIncomedisburse(e.DataEntitys);

        }

        /// <summary>
        /// 计算经销商结算金额
        /// </summary>
        /// <param name="dataEntities"></param>
        private void calculateCostAmount(DynamicObject[] dataEntities)
        {
            foreach(var dataEntity in dataEntities)
            {
                dataEntity["fsumsettleamount"] = 0;
            }

            var costIds = dataEntities.SelectMany(x => x["fentry"] as DynamicObjectCollection)
                                      .Select(x => Convert.ToString(x["fcostid"]))
                                      .Where(x => false == string.IsNullOrWhiteSpace(x))
                                      .Distinct()
                                      .ToList();
            if (costIds == null || costIds.Count <= 0)
            {
                return;
            }

            var costForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_costaccounting");
            var costDm = this.Container.GetService<IDataManager>();
            costDm.InitDbContext(this.Context, costForm.GetDynamicObjectType(this.Context));
            var costEntities = costDm.Select(costIds).OfType<DynamicObject>().ToArray();

            foreach(var dataEntity in dataEntities)
            {
                var costEntries = dataEntity["fentry"] as DynamicObjectCollection;
                var sumAmount = 0m;
                foreach (var costEntry in costEntities)
                {
                    var costId = Convert.ToString(costEntry["id"]);
                    var costEntity = costEntities.FirstOrDefault(x => Convert.ToString(x["id"]).EqualsIgnoreCase(costId));
                    if (costEntity == null)
                    {
                        continue;
                    }
                    var settleAmount = Convert.ToDecimal(costEntity["fsumsettleamount"]);
                    sumAmount += settleAmount;
                }
                dataEntity["fsumsettleamount"] = sumAmount;
            }
        }

        /// <summary>
        /// 计算收款核销总金额
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="incomeEntities"></param>
        private void calculateIncomeAmount(DynamicObject[] dataEntities, DynamicObject[] incomeEntities)
        {
            if (incomeEntities == null || incomeEntities.Length <= 0)
            {
                foreach (var dataEntity in dataEntities)
                {
                    dataEntity["fsumverifyamount"] = 0;
                }
                return;
            }

            foreach(var dataEntity in dataEntities)
            {
                dataEntity["fsumverifyamount"] = 0;
                var incomeEntries = dataEntity["fincomeentry"] as DynamicObjectCollection;
                if (incomeEntries == null || incomeEntries.Count <= 0)
                {
                    continue;
                }

                var sumAmount = 0m;
                foreach(var incomeEntry in incomeEntries)
                {
                    var incomeId = Convert.ToString(incomeEntry["fincomeid"]);
                    var incomeEntity = incomeEntities.FirstOrDefault(x => Convert.ToString(x["id"]).EqualsIgnoreCase(incomeId));
                    if (incomeEntity == null)
                    {
                        continue;
                    }
                    //账户收支方向
                    var direction = Convert.ToString(incomeEntity["fdirection"]).Trim().ToLower();
                    //用途
                    var purpose = Convert.ToString(incomeEntity["fpurpose"]).Trim().ToLower();
                    //金额
                    var amount = Convert.ToDecimal(incomeEntity["famount"]);

                    switch (purpose)
                    {
                        //红冲
                        case "bizpurpose_04":
                            switch (direction)
                            {
                                case "direction_01":
                                    amount = -amount;
                                    break;
                            }
                            break;
                        //退款
                        case "bizpurpose_06":
                            amount = -amount;
                            break;
                    }

                    sumAmount += amount;
                }
                dataEntity["fsumverifyamount"] = sumAmount;
            }
        }

        /// <summary>
        /// 更新收支记录
        /// </summary>
        /// <param name="dataEntities"></param>
        private void updateIncomedisburse(DynamicObject[] dataEntities)
        {
            var incomeIds = dataEntities.SelectMany(x => x["fincomeentry"] as DynamicObjectCollection)
                                        .Select(x => Convert.ToString(x["fincomeid"]))
                                        .Where(x => false == string.IsNullOrWhiteSpace(x))
                                        .Distinct()
                                        .ToList();

            if (incomeIds == null || incomeIds.Count <= 0)
            {
                throw new BusinessException($"已核销收支记录至少要有一行收支记录！");
            }

            var incomeForm = this.MetaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            var incomeDm = this.Container.GetService<IDataManager>();
            incomeDm.InitDbContext(this.Context, incomeForm.GetDynamicObjectType(this.Context));
            var incomeEntities = incomeDm.Select(incomeIds).OfType<DynamicObject>().ToArray();

            calculateIncomeAmount(dataEntities, incomeEntities);

            if (incomeEntities == null || incomeEntities.Length <= 0)
            {
                return;
            }

            foreach (var incomeEntity in incomeEntities)
            {
                var dealerStatus = Convert.ToString(incomeEntity["fdealerstatus"]);
                if (string.IsNullOrWhiteSpace(dealerStatus) || dealerStatus == "1")
                {
                    incomeEntity["fdealerstatus"] = "2";
                }
            }

            var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
            prepareSaveDataService.PrepareDataEntity(this.Context, incomeForm, incomeEntities, OperateOption.Create());
            incomeDm.Save(incomeEntities);

            var existedIncomeIds = getExistedIncomeDisburseId(dataEntities);
            var deleteIncomeIds = existedIncomeIds.Where(x => false == incomeIds.Contains(x)).ToList();
            if (deleteIncomeIds == null || deleteIncomeIds.Count <= 0)
            {
                return;
            }

            var deleteIncomes = incomeDm.Select(deleteIncomeIds).OfType<DynamicObject>().ToArray();
            if (deleteIncomes == null || deleteIncomes.Length <= 0)
            {
                return;
            }

            foreach (var deleteIncome in deleteIncomes)
            {
                var dealerStatus = Convert.ToString(deleteIncome["fdealerstatus"]);
                if (dealerStatus == "3")
                {
                    throw new BusinessException("经销商已对账的收支记录不可以删除!");
                }
                if (dealerStatus == "2")
                {
                    deleteIncome["fdealerstatus"] = "1";
                }
            }

            prepareSaveDataService.PrepareDataEntity(this.Context, incomeForm, deleteIncomes, OperateOption.Create());
            incomeDm.Save(deleteIncomes);
        }

        /// <summary>
        /// 获取已存在的收支记录
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private List<string> getExistedIncomeDisburseId(DynamicObject[] dataEntities)
        {
            var results = new List<string>();
            var ids = dataEntities.Where(x => x.DataEntityState.FromDatabase).Select(x => Convert.ToString(x["id"])).ToList();
            if (ids == null || ids.Count <= 0)
            {
                return results;
            }
            var dbService = this.Container.GetService<IDBService>();
            if (ids.Count <= 50)
            {
                var sql = new StringBuilder("select fincomeid from t_ydj_storeincomeentry where fid ");
                var sqlParams = new List<SqlParam>();
                if (ids.Count == 1)
                {
                    sql.Append("=@fid");
                    sqlParams.Add(new SqlParam("@fid", System.Data.DbType.String, ids[0]));
                }
                else
                {
                    sql.Append(" in (");
                    sql.Append(string.Join(",", ids.Select((x, i) => $"@fid{i}")));
                    sqlParams.AddRange(ids.Select((x, i) => new SqlParam($"@fid{i}", System.Data.DbType.String, x)));
                    sql.Append(")");
                }
                using (var dataReader = dbService.ExecuteReader(this.Context, sql.ToString(), sqlParams))
                {
                    while (dataReader.Read())
                    {
                        results.Add(Convert.ToString(dataReader.GetValue(0)));
                    }
                }
                return results;
            }

            using (var tran = this.Context.CreateTransaction())
            {
                var tableName = dbService.CreateTempTableWithDataList(this.Context, ids,false);
                var sql = $"select fincomeid from t_ydj_storeincomeentry e inner join {tableName} t on t.fid=e.fid";
                using (var dataReader = dbService.ExecuteReader(this.Context, sql))
                {
                    while (dataReader.Read())
                    {
                        results.Add(Convert.ToString(dataReader.GetValue(0)));
                    }
                }
                tran.Complete();

                dbService.DeleteTempTableByName(Context, tableName, true);
            }
            return results;
        }

        /// <summary>
        /// 检查合同是否已加入店面对账单
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private DynamicObject[] checkExistedOrder(DynamicObject[] dataEntities)
        {
            var orderIds = dataEntities.SelectMany(x => x["fentry"] as DynamicObjectCollection)
                                       .Select(x => Convert.ToString(x["forderid"]))
                                       .Where(x => false == string.IsNullOrWhiteSpace(x))
                                       .Distinct()
                                       .ToList();

            if (orderIds == null || orderIds.Count <= 0)
            {
                throw new BusinessException("至少要有一行结算合同明细！");
            }

            var multiValueQueryService = this.Container.GetService<IMultiValueQueryService>();
            var where = "fmainorgid=@fmainorgid";
            var sqlParams = new List<SqlParam> { new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company) };
            var existedEntities = multiValueQueryService.Select(this.Context, where, sqlParams, this.HtmlForm, "forderid", orderIds).ToArray();

            //检查合同是否已加入店面对账单
            return checkExistedOrder(dataEntities, existedEntities);
        }

        /// <summary>
        /// 检查合同是否已加入店面对账单
        /// </summary>
        /// <param name="currentEntities"></param>
        /// <param name="existedEntities"></param>
        /// <returns></returns>
        private DynamicObject[] checkExistedOrder(DynamicObject[] currentEntities, DynamicObject[] existedEntities)
        {
            if (existedEntities == null || existedEntities.Length <= 0)
            {
                return currentEntities;
            }

            var existedEntries = existedEntities.SelectMany(x => x["fentry"] as DynamicObjectCollection).ToList();
            var results = new List<DynamicObject>();
            foreach(var currentEntity in currentEntities)
            {
                var currentEntries = currentEntity["fentry"] as DynamicObjectCollection;
                var isValid = true;
                foreach(var currentEntry in currentEntries)
                {
                    var orderId = Convert.ToString(currentEntry["forderid"]);
                    var existedEntry = existedEntries.FirstOrDefault(x => Convert.ToString(x["forderid"]).EqualsIgnoreCase(orderId));
                    if (existedEntry == null)
                    {
                        continue;
                    }
                    var currentId = Convert.ToString(currentEntry["id"]);
                    var existedId = Convert.ToString(existedEntry["id"]);
                    if (false == currentId.EqualsIgnoreCase(existedId))
                    {
                        isValid = false;
                        var existedEntity = existedEntry.Parent as DynamicObject;
                        this.Result.ComplexMessage.ErrorMessages.Add($"编号[{existedEntity["fbillno"]}]的{this.HtmlForm.Caption}已有和当前单据相同的合同的结算明细！");
                        break;
                    }
                }
                if (isValid)
                {
                    results.Add(currentEntity);
                }
            }
            return results.ToArray();
        }
    }
}
