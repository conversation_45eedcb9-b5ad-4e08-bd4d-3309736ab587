using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MP
{
    public class RoleMPMenuPermitInfo
    {
        public RoleMPMenuPermitInfo()
        {
            this.RoleUsers = new Dictionary<string, string>((IEqualityComparer<string>)StringComparer.OrdinalIgnoreCase);
            this.UserInfo = new List<Dictionary<string, object>>();
            this.MPMenuPermission = new List<MPTabbarModel>();
        }

        ///// <summary>产品id</summary>
        //public string ProductId { get; set; }

        ///// <summary>组织id</summary>
        //public string CompanyId { get; set; }

        ///// <summary>组织名称</summary>
        //public string CompanyName { get; set; }

        public string RoleId { get; set; }

        //public string RoleName { get; set; }

        /// <summary>忽略角色用户的保存与处理（常用于系统预置权限时使用）</summary>
        //public bool IgnoreRoleUser { get; set; }

        /// <summary>角色包含用户信息</summary>
        public Dictionary<string, string> RoleUsers { get; set; }

        /// <summary>
        /// 用户信息：永远传递用户的其他信息到前端，比如：用户编码，用户所属企业 等等...
        /// 用于前期定义的 RoleUsers 只是一个包含了 用户内码 和 用户姓名键值对字典，导致前端想显示用户其他信息时没法显示，所以加此属性用于传递用户的其他信息
        /// </summary>
        public List<Dictionary<string, object>> UserInfo { get; set; }

        /// <summary>小程序菜单权限信息</summary>
        public List<MPTabbarModel> MPMenuPermission { get; set; }

        //public override string ToString()
        //{
        //    return $"{this.CompanyName}:{this.RoleName}";
        //}
    }
}
