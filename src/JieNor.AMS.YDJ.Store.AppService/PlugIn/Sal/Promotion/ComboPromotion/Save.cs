using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using JieNor.AMS.YDJ.Core;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sal.Promotion.ComboPromotion
{
    /// <summary>
    /// 套餐组合：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_combopromotion")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (e.DataEntitys.IsNullOrEmpty())
            {
                return;
            }

            // 是否导入
            var isImportNumber = this.GetQueryOrSimpleParam<string>("ImportNumber").EqualsIgnoreCase("true");
            HashSet<string> authProductIds = new HashSet<string>();
            if (isImportNumber)
            {
                var sql = this.Context.GetAuthProductDataPKID(new DataQueryRuleParaInfo
                {
                    FormId = "ydj_product",
                    SrcFormId = this.HtmlForm.Id
                });

                using (var reader = this.Context.ExecuteReader(sql, new List<SqlParam>()))
                {
                    while (reader.Read())
                    {
                        var productId = Convert.ToString(reader[0]);
                        authProductIds.Add(productId);
                    }
                }
            }

            // 判断是否存在【促销门店】=全部门店的
            var hasAllDept = e.DataEntitys.Any(s => Convert.ToString(s["fdeptscope"]).EqualsIgnoreCase("deptscope_01"));
            var allDeptIds = new List<string>();
            if (hasAllDept && !isImportNumber)
            {
                var deptFormMeta = this.MetaModelService.LoadFormModel(this.Context, "ydj_dept");
                string sql =
                    $"select {deptFormMeta.BillPKFldName} from {deptFormMeta.BillHeadTableName} with(nolock) where fmainorgid=@fmainorgid and fforbidstatus='0'";

                allDeptIds =
                    this.Context
                        .ExecuteDynamicObject(sql, new SqlParam[] { new SqlParam("@fmainorgid", DbType.String, this.Context.Company) })
                        .Select(s => Convert.ToString(s["fid"]))
                        .ToList();
            }

            foreach (var dataEntity in e.DataEntitys)
            {
                var fdeptscope = Convert.ToString(dataEntity["fdeptscope"]);
                var fdeptentry = dataEntity["fdeptentry"] as DynamicObjectCollection;

                // 全部门店
                if (fdeptscope.EqualsIgnoreCase("deptscope_01") && allDeptIds.Any())
                {
                    foreach (var deptId in allDeptIds)
                    {
                        var deptEntry = fdeptentry.FirstOrDefault(s => Convert.ToString(s["fdeptid"]).EqualsIgnoreCase(deptId));
                        if (deptEntry != null)
                        {
                            continue;
                        }

                        deptEntry = fdeptentry.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                        deptEntry["fdeptid"] = deptId;

                        fdeptentry.Add(deptEntry);
                    }
                }

                // 导入
                if (isImportNumber)
                {
                    List<DynamicObject> beRemove = new List<DynamicObject>();

                    #region 移除《促销商品》的未授权商品
                    var fproductentry = dataEntity["fproductentry"] as DynamicObjectCollection;
                    foreach (var productEntry in fproductentry)
                    {
                        // 新建行
                        if (!productEntry.DataEntityState.FromDatabase)
                        {
                            var fmaterialid = Convert.ToString(productEntry["fmaterialid"]);
                            if (!authProductIds.Contains(fmaterialid))
                            {
                                beRemove.Add(productEntry);
                            }
                        }
                    }

                    if (beRemove.Any())
                    {
                        foreach (var item in beRemove)
                        {
                            fproductentry.Remove(item);
                        }
                    }
                    #endregion
                }
            }
        }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(new SaveValidation());

            /*
                定义表头校验规则
            */

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fstartdate = Convert.ToDateTime(newData["fstartdate"]);
                var fenddate = Convert.ToDateTime(newData["fenddate"]);

                return fenddate > fstartdate;
            }).WithMessage("【促销结束时间】必须大于【促销开始时间】。"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fdeptscope = Convert.ToString(newData["fdeptscope"]);
                var fdeptentry = newData["fdeptentry"] as DynamicObjectCollection;

                // 指定门店
                if (fdeptscope.EqualsIgnoreCase("deptscope_02") && fdeptentry.IsNullOrEmpty())
                {
                    return false;
                }
                return true;
            }).WithMessage("请填写促销门店信息。"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fproductentry = newData["fproductentry"] as DynamicObjectCollection;

                return fproductentry.All(s => Convert.ToDecimal(s["fqty"]) > 0);
            }).WithMessage("促销商品数量要大于0。"));
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;


        }
    }
}