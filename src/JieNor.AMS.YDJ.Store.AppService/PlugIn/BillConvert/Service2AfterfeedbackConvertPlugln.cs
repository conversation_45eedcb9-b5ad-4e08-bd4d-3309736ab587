using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 服务单下推售后反馈单
    /// </summary>
    [InjectService]
    [FormId("ste_afterfeedback")]
    [OperationNo("ydj_service2ste_afterfeedback")]
    public class Service2AfterfeedbackConvertPlugln : AbstractConvertServicePlugIn
    {
        /// <summary>
        /// 字段计算逻辑处理
        /// </summary>
        /// <param name="e"></param>
        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);
            if (!e.TargetDataEntities.IsNullOrEmpty() && e.TargetDataEntities.Any())
            {
                //当前登录用户关联的员工【业务类别】包含“售后或通用”，则售后人员需自动默认取当前员工
                var staffObj = this.UserContext.LoadBizDataByACLFilter("ydj_staff", $"flinkuserid='{this.UserContext.UserId}'");
                DynamicObject staffInfo = null;
                if (!staffObj.IsNullOrEmpty())
                {
                    staffInfo = staffObj.Where(x => (x["fentity"] as DynamicObjectCollection)
                  .Any(y => y["fbiztype"] != null && (y["fbiztype"].ToString() == "7" || y["fbiztype"].ToString() == "0")))?.FirstOrDefault();
                }
                foreach (var data in e.TargetDataEntities)
                {
                    if (!staffInfo.IsNullOrEmpty())
                    {
                        data["fstaffid"] = staffInfo["id"];
                        data["fphone"] = staffInfo["fphone"];
                        data["fdeptid"] = staffInfo["fdeptid"];
                    }
                }
                if (this.UserContext.IsDirectSale)
                {
                    var svc = this.UserContext.Container.GetService<IBillTypeService>();
                    var billTypeInfos = svc.GetBillTypeInfors(this.UserContext, this.TargetHtmlForm.Id);
                    var billTypeInfo = billTypeInfos.FirstOrDefault(f => f.fname == "客诉售后反馈" || (!f.fprimitivename.IsNullOrEmptyOrWhiteSpace() && f.fprimitivename == "客诉售后反馈"));
                    if (billTypeInfo == null)
                    {
                        return;
                    }

                    foreach (var item in e.TargetDataEntities)
                    {
                        if (Convert.ToString(item["fbilltypeid"]).IsNullOrEmptyOrWhiteSpace())
                            item["fbilltypeid"] = billTypeInfo.fid;
                    }
                }
            }
        }
    }
}
