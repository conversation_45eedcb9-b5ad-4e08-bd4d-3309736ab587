using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 销售出库单->发货排单申请单的单据转换插件
    /// </summary>
    [InjectService]
    [FormId("stk_scheduleapply")]
    [OperationNo("stk_sostockout2stk_scheduleapply")]
    public class SoStockOut2ScheduleApplyConvertPlugIn: AbstractConvertServicePlugIn
    {
        protected IEnumerable<Dictionary<string, object>> InteractReturnData { get; set; }

        protected override void OnInitialized(InitializeServiceEventArgs e)
        {
            base.OnInitialized(e);

            var returnGoodData = new List<Dictionary<string, object>>();
            this.Option.TryGetVariableValue("returnData", out returnGoodData);
            this.InteractReturnData = returnGoodData;
        }
        /// <summary>
        /// 获得来源单数据后事件
        /// </summary>
        /// <param name="e"></param>
        public override void AfterGetSourceBillData(AfterGetSourceBillDataEventArgs e)
        {
            base.AfterGetSourceBillData(e);
        }

        /// <summary>
        /// 字段值映射前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeMapFieldValue(BeforeMapFieldValueEventArgs e)
        {
            if (this.InteractReturnData == null
                || this.InteractReturnData.Any() == false) return;
            if (e.SourceDataEntities.Any() == false) return;

            var targetFldKey = e.FieldMapObject?.Id;
            var targetField = this.TargetHtmlForm?.GetField(targetFldKey);
            if (targetField == null) return;

            bool isCancel = false;
            object targetValue = null;

            var srcEntryId = e.SourceDataEntities.First().GetString("fentity_id");
            var existReturnObj = this.InteractReturnData.FirstOrDefault(o => o.GetString("entryPkValue").EqualsIgnoreCase(srcEntryId));
            if (existReturnObj == null) return;

            object returnQty = 0m;

            switch (targetField.Id.ToLower())
            {
                case "fqty":
                case "fstockqty":
                    if (existReturnObj.TryGetValue("returnQty", out returnQty))
                    {
                        isCancel = true;
                        targetValue = returnQty;
                    }
                    break;
                case "famount":
                    var dPrice = Convert.ToDecimal(e.SourceDataEntities.First().GetValue("fprice", 0m));
                    if (existReturnObj.TryGetValue("returnQty", out returnQty))
                    {
                        isCancel = true;
                        targetValue = dPrice * Convert.ToDecimal(returnQty);
                    }
                    break;
            }

            if (isCancel)
            {
                e.Cancel = true;
                targetField.DynamicProperty.SetValue(e.TargetEntryDataEntity, targetValue);
            }
        }

        /// <summary>
        /// 退货单字段计算逻辑处理
        /// </summary>
        /// <param name="e"></param>
        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities?.Any() != true)
            {
                throw new BusinessException("退货失败：未能成功生成发货排单申请单！");
            }

            var returnType = "";
            this.Option.TryGetVariableValue("returnType", out returnType);

            var targetReturnBillObjs = e.TargetDataEntities;
            foreach (var applyObj in targetReturnBillObjs)
            {
                switch (returnType)
                {
                    case "sostockreturn_scenetype_01":
                        applyObj["fapplytype"] = "0";
                        break;
                    case "sostockreturn_scenetype_02":
                        applyObj["fapplytype"] = "1";
                        break;
                }
            }
        }
    }
}
