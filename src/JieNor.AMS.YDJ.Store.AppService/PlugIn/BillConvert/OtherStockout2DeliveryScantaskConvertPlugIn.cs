using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 其他出库单下推发货扫描任务
    /// </summary>
    [InjectService]
    [FormId("bcm_deliveryscantask")]
    [OperationNo("stk_otherstockout2bcm_deliveryscantask")]
    public class OtherStockOut2DeliveryScantaskConvertPlugIn : AbstractConvertServicePlugIn
    {
        /*已经有对应的《发货扫描任务》时, 则不允许重复生成*/
        public override void AfterGetSourceBillData(AfterGetSourceBillDataEventArgs e)
        {
            base.AfterGetSourceBillData(e);

            if (e.SourceDataEntities == null || e.SourceDataEntities.Count() <= 0) return;

            var fbillnos = e.SourceDataEntities.ToArray().Select(x => (string)x["fbillno"]).Distinct().ToList();
            if (fbillnos == null || fbillnos.Count() == 0) return;

            //判断发货扫描任务是否有单据
            var sql = @"SELECT a.fentryid FROM t_bcm_descantaskentity a left join t_bcm_deliveryscantask b on a.fid = b.fid
WHERE b.fmainorgid = '{0}' AND a.flinkformid='stk_otherstockout' and a.flinkbillno in ({1}) ".Fmt(this.UserContext.Company, fbillnos.JoinEx(",", true));

            var dbService = this.UserContext.Container.GetService<IDBService>();
            var exsit1 = dbService.ExecuteDynamicObject(this.UserContext, sql);
            if (exsit1.Count > 0)
            {
                throw new BusinessException("当前订单已经存在对应的发货任务, 不允许重复生成!");
            }
        }

        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities == null || e.TargetDataEntities.Count() <= 0) return;

            var dataEntities = e.TargetDataEntities.ToArray();

            var datas = dataEntities.SelectMany(x =>
            {
                var fentry = x["ftaskentity"] as DynamicObjectCollection;
                return fentry.Where(z => !Convert.ToString(z["fmaterialid"]).IsNullOrEmptyOrWhiteSpace()).Select(y =>
                {
                    return new
                    {
                        fmaterialid = (string)y["fmaterialid"],//商品id
                        flinkentryid = (string)y["flinkrowinterid"]//关联单行内码其他出库单明细）
                    };
                });
            });

            SetInfoByParm(datas.Select(x => x.fmaterialid).Distinct().ToList(),
                datas.Select(x => x.flinkentryid).Distinct().ToList(), dataEntities);
        }

        /// <summary>
        /// 根据商品打包类型设置打包类型,包件数和待扫描包数
        /// 根据关联单据查询关联单据行号
        /// 根据来源单据查询来源单据行号
        /// </summary>
        private void SetInfoByParm(List<string> productIds, List<string> flinkentryids, DynamicObject[] dataEntities)
        {
            var dbService = this.UserContext.Container.GetService<IDBService>();

            var productForm = this.MetaModelService.LoadFormModel(this.UserContext, "ydj_product");
            var dm = this.UserContext.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserContext, productForm.GetDynamicObjectType(this.UserContext));
            var products = dm.Select(productIds).OfType<DynamicObject>().ToList();

            //查询上游《其他出库单》单据体-商品明细 对应商品 的行号
            var sql1 = @"select fentryid,FSeq from t_stk_otherstockoutentry where fentryid in ({0})".Fmt(flinkentryids.JoinEx(",", true));
            var linkentries = dbService.ExecuteDynamicObject(this.UserContext, sql1);

            foreach (var dataEntity in dataEntities)
            {
                var fentries = dataEntity["ftaskentity"] as DynamicObjectCollection;
                foreach (var fentry in fentries)
                {
                    if (products != null && products.Count > 0)
                    {
                        var productId = (string)fentry["fmaterialid"];
                        var product = products.FirstOrDefault(x => (string)x["id"] == productId);
                        if (product != null)
                        {
                            fentry["fpackagingtype"] = product["fpackagtype"];
                            var fwaitworkqty = fentry["fwaitworkqty"] ?? 0;

                            switch (product["fpackagtype"])
                            {
                                case "1":
                                    fentry["fpackagingqty"] = "1";
                                    fentry["fwaitscanqty"] = fwaitworkqty;
                                    break;
                                case "2":
                                    fentry["fpackagingqty"] = product["fbag"];
                                    fentry["fwaitscanqty"] = Convert.ToInt32(fwaitworkqty) * Convert.ToInt32(product["fbag"]);
                                    break;
                                case "3":
                                    fentry["fpackagingqty"] = product["fpiece"];
                                    fentry["fwaitscanqty"] = Convert.ToInt32(fwaitworkqty) / Convert.ToInt32(product["fpiece"]);
                                    break;
                                default:
                                    break;
                            }
                        }
                    }

                    if (linkentries != null && linkentries.Count > 0)
                    {
                        var linkentryid = (string)fentry["flinkrowinterid"];
                        var linkentry = linkentries.FirstOrDefault(x => (string)x["fentryid"] == linkentryid);
                        if (linkentry != null)
                        {
                            fentry["flinkrownumber"] = linkentry["FSeq"];
                            fentry["fsourceinterid"] = linkentry["FSeq"];
                        }
                    }
                }
            }
        }
    }
}
