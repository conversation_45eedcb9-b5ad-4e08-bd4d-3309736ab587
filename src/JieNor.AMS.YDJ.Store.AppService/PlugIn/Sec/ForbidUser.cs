using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.DataTransferObject;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Auth;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sec
{
    /// <summary>
    /// 禁用用户账号：如果用户属于系统运维角色，则禁用所有经销商企业里面的对应用户
    /// </summary>
    [InjectService]
    [FormId("sec_user")]
    [OperationNo("Forbid")]
    public class ForbidUser : AbstractOperationServicePlugIn
    {


        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null) return;

            if (!this.Context.IsTopOrg)
            {
                var ex = e.DataEntitys.Any(f => Convert.ToString(f["froleids_txt"]).IndexOf("系统运维") > -1 || Convert.ToString(f["froleids_txt"]).IndexOf("经销商管理员") > -1);
                if(ex )
                {
                    throw new Exception("所操作的用户属于总部的运维用户或经销商管理员，不允许禁用");
                }
            }

            var datas = (from p in e.DataEntitys
                         where Convert.ToString(p["froleids_txt"]).IndexOf("系统运维") > -1
                         select new BaseDataSummary()
                         {
                             Id = Convert.ToString(p["Id"]),
                             Name = Convert.ToString(p["fname"]),
                             Number = Convert.ToString(p["fnumber"]),
                         }
                         ).ToList();
            if (datas != null)
            {
                var result = DataAuthHelp.DevOpsUserForbid(this.Context, datas, true);
                this.Result.MergeResult(result);
            }
        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            if(e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            SetStaffMuSiStatusAndSyncDate(e.DataEntitys);
        }

        /// <summary>
        /// 设置相同手机号的员工的慕思同步状态为待同步以及同步时间为null
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void SetStaffMuSiStatusAndSyncDate(DynamicObject [] dataEntitys)
        {
            var phoneList = dataEntitys.Select(x => Convert.ToString(x["fphone"])).ToList();
            if (phoneList != null && phoneList.Any())
            {
                var sqlStr = $" fphone in ({String.Join(",", phoneList.Select(x => $"'{x}'"))}) and fmainorgid='{this.Context.Company}' ";

                var staffDys = this.Context.LoadBizDataByACLFilter("ydj_staff", sqlStr, false);

                if (staffDys != null && staffDys.Any())
                {
                    foreach (var staffDy in staffDys)
                    {
                        //协同时间设置为null
                        staffDy["fmusisyncdate"] = null;

                        //'01':'待协同','02':'协同成功','03':'协同失败'
                        staffDy["fmusisyncstatus"] = "01";

                    }

                    this.Context.SaveBizData("ydj_staff", staffDys);
                }

            }
        }
    }


    /// <summary>
    /// 反禁用用户账号：如果用户属于系统运维角色，则禁用所有经销商企业里面的对应用户
    /// </summary>
    [InjectService]
    [FormId("sec_user")]
    [OperationNo("UnForbid")]
    public class UnForbidUser : AbstractOperationServicePlugIn
    {


        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null) return;

            var datas = (from p in e.DataEntitys
                         where Convert.ToString(p["froleids_txt"]).IndexOf("系统运维") > -1
                         select new BaseDataSummary()
                         {
                             Id = Convert.ToString(p["Id"]),
                             Name = Convert.ToString(p["fname"]),
                             Number = Convert.ToString(p["fnumber"]),
                         } 
                         ).ToList();
            if(datas!=null )
            {
                var result = DataAuthHelp.DevOpsUserForbid(this.Context, datas, false);
                this.Result.MergeResult(result);
            }
             
        }
    }



}
