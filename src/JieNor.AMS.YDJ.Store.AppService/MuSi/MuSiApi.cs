using JieNor.AMS.YDJ.Store.AppService.MuSi.DTO;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.SystemIntegration;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Utils;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using Newtonsoft.Json.Linq;
using JieNor.Framework.SuperOrm;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs;
using JieNor.Framework.DataTransferObject;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Api;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Response;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi
{
    /// <summary>
    /// 慕思Api
    /// </summary>
    public class MuSiApi
    {
        /// <summary>
        /// 非标审批
        /// </summary>
        private static readonly string UnstdAuditUrl = "/DRC-INTERFACE/derucci/unstdApv/push";

        /// <summary>
        /// 终端订单进度(OMS)
        /// </summary>
        private static readonly string OmsFactoryOrderUrl = "/GENGEN-BASE-V2/omsFactoryOrder/rotation";

        /// <summary>
        /// 对账单：对账相符
        /// </summary>
        private static readonly string HSConfirmBillUrl = "/DRC-INTERFACE/derucci/statement/push";

        /// <summary>
        /// 对账单：对账不符
        /// </summary>
        private static readonly string HSUnconfirmBillUrl = "/DRC-INTERFACE/derucci/statement/push";

        /// <summary>
        /// 对账单：对账撤销
        /// </summary>
        private static readonly string HSUndoBillUrl = "/DRC-INTERFACE/derucci/statement/push";

        /// <summary>
        /// 期初盘点：扫码信息
        /// </summary>
        private static readonly string ScanBarcodeInfoUrl = "/DRC-INTERFACE/derucci/barcode/query";

        /// <summary>
        /// 售达方余额(经销商)查询
        /// </summary>
        private static readonly string AgentBalanceUrl = "/DRC-INTERFACE/derucci/balance/getCusttomerBalance";

        /// <summary>
        /// 获取费用池数据 
        /// </summary>
        private static readonly string CostPoolUrl = "/DRC-INTERFACE/derucci/balance/getCustCostPoolBala";

        /// <summary>
        /// 认证中心-修改密码后，通知慕思
        /// </summary>
        private static readonly string ModifyPwdSyncUrl = "/user/accountpwd/ydjPasswordSyn";

        /// <summary>
        /// 认证中心接口
        /// </summary>
        private static readonly List<string> MusiAuthUrl = new List<string>() { "/dealermanage/dealeruser/employeeInfo",
                                                                        "/dealermanage/dealeruser/deptInfo",
                                                                        "/dealermanage/dealeruser/positionInfo",
                                                                        "/user/accountpwd/ydjPasswordSyn ",
                                                                        };


        /// <summary>
        /// 物流单据轨迹查询接口
        /// </summary>
        private static readonly string shipping_trade_get_wlgj = "?app_mode=func&app_act=a_gz/api_gz_wlxx/ec";

        /// <summary>
        /// 直营：销售出库单审核同步中台
        /// </summary>
        private static readonly string zy_stk_sostockoutUrl = "/DRC-INTERFACE/derucci/salesDelivery/push";

        /// <summary>
        /// 直营：库存调拨单审核同步中台
        /// </summary>
        private static readonly string zy_stk_invtransferUrl = "/DRC-INTERFACE/derucci/zyTransfer/push ";

        /// <summary>
        /// 直营：其它出库单审核同步中台
        /// </summary>
        private static readonly string zy_stk_otherstockoutUrl = "/DRC-INTERFACE/derucci/zyOtherOutbound/push";

        /// <summary>
        /// 直营：获取开户银行
        /// </summary>
        private static readonly string zy_getBanks = "/DRC-INTERFACE/derucci/bank/getBanks";

        /// <summary>
        /// 直营：盘点单审核同步中台
        /// </summary>
        private static readonly string zy_stk_inventoryverify = "/DRC-INTERFACE/derucci/zyStocktaking/push";


        /// <summary>
        /// 直营：销售退货单审核同步中台；接口1
        /// </summary>
        private static readonly string zy_stk_returnbysalorder = "/DRC-INTERFACE/derucci/salesContrac/push3";


        /// <summary>
        /// 直营：销售退货单审核同步中台；接口2
        /// </summary>
        private static readonly string zy_stk_returnbyoutstock = "/DRC-INTERFACE/derucci/salesDelivery/push";


        /// <summary>
        /// 获取费用池数据
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="agent"></param>
        /// <param name="companyNum"></param>
        /// <returns></returns>
        public static MuSiResponse<JArray> GetCostPoolByIncoke(UserContext userCtx, HtmlForm htmlForm, Dictionary<string, object> param)
        {
            var data = param;

            return Invoke<JArray>(userCtx, htmlForm, CostPoolUrl, "总部费用池查询", data);
        }

        /// <summary>
        /// 售达方余额(经销商)查询
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="deliver"></param>
        /// <returns></returns>
        public static MuSiResponse<JObject> GetAgentBalance(UserContext userCtx, HtmlForm htmlForm, string agent, string companyNum)
        {
            var data = new Dictionary<string, object>
            {
                { "data",new Dictionary<string,object>
                    {
                      { "soldToPartyNum", agent },
                      { "salesCompNum",companyNum}
                    }
                }
            };

            return Invoke<JObject>(userCtx, htmlForm, AgentBalanceUrl, "售达方余额查询", data);
        }

        /// <summary>
        /// 发送非标审批
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param> 
        public static MuSiResponse<object> SendUnstdAudit(UserContext userCtx, HtmlForm htmlForm, UnstdAuditDTO dto)
        {
            var data = new Dictionary<string, object>
            {
                { "data", dto.Entrys }
            };

            return Invoke<object>(userCtx, htmlForm, UnstdAuditUrl, "非标审批", data, dto.GetNumbers());
        }

        /// <summary>
        /// 发送非标审批
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param> 
        public static Task<MuSiResponse<object>> SendUnstdAuditAsync(UserContext userCtx, HtmlForm htmlForm, UnstdAuditDTO dto)
        {
            return Task.Run(() => SendUnstdAudit(userCtx, htmlForm, dto));
        }

        /// <summary>
        /// 发送终端订单进度(OMS)
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param> 
        public static MuSiResponse<object> SendOmsFactoryOrder(UserContext userCtx, HtmlForm htmlForm, OmsOrderPogressDTO dto)
        {
            var data = new Dictionary<string, object>
            {
                { "data", dto.Entrys }
            };

            return InvokeOMS<object>(userCtx, htmlForm, OmsFactoryOrderUrl, "终端订单进度(OMS)", data, dto.GetNumbers());
        }

        /// <summary>
        /// 发送终端订单进度(OMS)
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param> 
        public static Task<MuSiResponse<object>> SendOmsFactoryOrderAsync(UserContext userCtx, HtmlForm htmlForm, OmsOrderPogressDTO dto)
        {
            return Task.Run(() => SendOmsFactoryOrder(userCtx, htmlForm, dto));
        }

        /// <summary>
        /// 总部对账单：对账相符
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param> 
        public static MuSiResponse<object> SendHSConfirmBill(UserContext userCtx, HtmlForm htmlForm, HSConfirmBillDTO dto)
        {
            var data = new Dictionary<string, object>
            {
                { "data", dto.Entrys }
            };

            return Invoke<object>(userCtx, htmlForm, HSConfirmBillUrl, "对账相符", data, dto.GetNumbers());
        }

        /// <summary>
        /// 总部对账单：对账不符
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param> 
        public static MuSiResponse<object> SendHSUnconfirmBill(UserContext userCtx, HtmlForm htmlForm, HSUnconfirmBillDTO dto)
        {
            var data = new Dictionary<string, object>
            {
                { "data", dto.Entrys }
            };

            return Invoke<object>(userCtx, htmlForm, HSUnconfirmBillUrl, "对账不符", data, dto.GetNumbers());
        }

        /// <summary>
        /// 总部对账单：对账撤销
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param> 
        public static MuSiResponse<object> SendHSUndoBill(UserContext userCtx, HtmlForm htmlForm, HSUndoBillDTO dto)
        {
            var data = new Dictionary<string, object>
            {
                { "data", dto.Entrys }
            };

            return Invoke<object>(userCtx, htmlForm, HSUndoBillUrl, "对账撤销", data, dto.GetNumbers());
        }

        /// <summary>
        /// 期初盘点：扫码信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param> 
        public static MuSiResponse<JArray> SendScanBarcodeInfo(UserContext userCtx, HtmlForm htmlForm, string barcodes)
        {
            var data = new Dictionary<string, object>
            {
                { "barcode", barcodes }
            };

            return Invoke<JArray>(userCtx, htmlForm, ScanBarcodeInfoUrl, "扫码信息", data);
        }


        /// <summary>
        /// 发送修改密码信息给慕思
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param> 
        public static Task<MuSiResponse<JArray>> SendModifyPwdInfo(UserContext userCtx, HtmlForm htmlForm, MsAuthModifyPwdDto dto)
        {
            var data = new Dictionary<string, object>
            {
                { "account", dto.account },
                { "phone", dto.phone },
                { "password", dto.password },
                { "optUserName", dto.optUserName },
                { "systemCode", dto.systemCode }
            };

            return Task.Run(() => InvokeMusiAuth<JArray>(userCtx, htmlForm, ModifyPwdSyncUrl, "修改密码", data, new List<string>() { dto.account }));
        }


        /// <summary>
        /// 物流单据轨迹查询接口	
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param> 
        public static Task<MuSiResponse<JArray>> GetE3LogisticsProgressSync(UserContext userCtx, HtmlForm htmlForm, MSE3LogisticsDTO dto)
        {
            var data = new Dictionary<string, object>
            {
                { "sign", dto.sign },
                { "key", dto.key },
                { "requestTime",dto.requestTime },
                { "version", dto.version },
                { "serviceType", dto.serviceType },
                { "data", dto.data }
            };
            string wlgj = "".GetAppConfig("ms.e3.url") + shipping_trade_get_wlgj;

            return Task.Run(() => InvokeE3<JArray>(userCtx, htmlForm, wlgj, "物流单据轨迹查询接口", data, new List<string>() { }));
        }



        /// <summary>
        /// 物流单据轨迹查询接口	
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param> 
        public static MuSiResponse<JArray> GetE3LogisticsProgress(UserContext userCtx, HtmlForm htmlForm, MSE3LogisticsDTO dto)
        {
            var data = new Dictionary<string, object>
            {
                { "sign", dto.sign },
                { "key", dto.key },
                { "requestTime",dto.requestTime },
                { "version", dto.version },
                { "serviceType", dto.serviceType },
                { "data", dto.data }
            };
            string wlgj = "".GetAppConfig("ms.e3.url") + shipping_trade_get_wlgj;

            return InvokeE3<JArray>(userCtx, htmlForm, wlgj, "物流单据轨迹查询接口", data, new List<string>() { });
        }




        /// <summary>
        /// 【直营】发送销售出库单	
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param> 
        public static async Task<MuSiResponse<JArray>> SendStkSoStockOut(UserContext userCtx, HtmlForm htmlForm, Dictionary<string, object> data)
        {
            List<Dictionary<string, object>> lst = new List<Dictionary<string, object>>();
            lst.Add(data);
            var obj = new Dictionary<string, List<Dictionary<string, object>>>
            {
                { "data", lst }
            };
            return InvokeZY<JArray>(userCtx, htmlForm, zy_stk_sostockoutUrl, "发送销售出库单", obj, new List<string>() { Convert.ToString(data["zyInvNum"]) });
        }


        /// <summary>
        /// 【直营】发送库存调拨单	
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param> 
        public static async Task<MuSiResponse<JArray>> SendInvTransfer(UserContext userCtx, HtmlForm htmlForm, Dictionary<string, object> data)
        {
            List<Dictionary<string, object>> lst = new List<Dictionary<string, object>>();
            lst.Add(data);
            var obj = new Dictionary<string, List<Dictionary<string, object>>>
            {
                { "data", lst }
            };
            return InvokeZY<JArray>(userCtx, htmlForm, zy_stk_invtransferUrl, "发送库存调拨单", obj, new List<string>() { Convert.ToString(data["zyTranferNo"]) });
        }


        /// <summary>
        /// 【直营】发送其它出库单	
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param> 
        public static async Task<MuSiResponse<JArray>> SendStkOtherStockOut(UserContext userCtx, HtmlForm htmlForm, Dictionary<string, object> data)
        {
            List<Dictionary<string, object>> lst = new List<Dictionary<string, object>>();
            lst.Add(data);
            var obj = new Dictionary<string, List<Dictionary<string, object>>>
            {
                { "data", lst }
            };
            return InvokeZY<JArray>(userCtx, htmlForm, zy_stk_otherstockoutUrl, "发送其它出库单", obj, new List<string>() { Convert.ToString(data["zyOutboundNum"]) });
        }



        /// <summary>
        /// 【直营】获取开户银行
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="date">获取日期：********</param>
        /// <returns></returns>
        public static ZYMuSiResponse<JArray> ZY_getBanks(UserContext userCtx, HtmlForm htmlForm, Dictionary<string, object> data)
        {
            //var obj = new Dictionary<string, Dictionary<string, object>>
            //{
            //    { "data", data }
            //};
            return InvokeZY<JArray>(userCtx, htmlForm, zy_getBanks, "获取开户银行", data, new List<string>() { });
        }

        /// <summary>
        /// 【直营】盘点单
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="date">获取日期：********</param>
        /// <returns></returns>
        public static MuSiResponse<JArray> ZY_PushInventoryVerify(UserContext userCtx, HtmlForm htmlForm, Dictionary<string, object> data)
        {
            List<Dictionary<string, object>> lst = new List<Dictionary<string, object>>();
            lst.Add(data);
            var obj = new Dictionary<string, List<Dictionary<string, object>>>
            {
                { "data", lst }
            };
            return InvokeZY<JArray>(userCtx, htmlForm, zy_stk_inventoryverify, "发送盘点单", obj, new List<string>() { Convert.ToString(data["zyInvNum"]) });
        }


        /// <summary>
        /// 【直营】销售退货单审核同步中台；接口1	
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param> 
        public static MuSiResponse<JArray> SendSTKReturnBySalOrder(UserContext userCtx, HtmlForm htmlForm, Dictionary<string, object> data)
        {
            List<Dictionary<string, object>> lst = new List<Dictionary<string, object>>();
            lst.Add(data);
            var obj = new Dictionary<string, List<Dictionary<string, object>>>
            {
                { "data", lst }
            };
            return InvokeZY<JArray>(userCtx, htmlForm, zy_stk_returnbysalorder, "发送销售退货单关联销售合同", obj, new List<string>() { });
        }


        /// <summary>
        /// 【直营】销售退货单审核同步中台；接口2
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param> 
        public static MuSiResponse<JArray> SendSTKReturnByOutStock(UserContext userCtx, HtmlForm htmlForm, Dictionary<string, object> data)
        {
            List<Dictionary<string, object>> lst = new List<Dictionary<string, object>>();
            lst.Add(data);
            var obj = new Dictionary<string, List<Dictionary<string, object>>>
            {
                { "data", lst }
            };
            return InvokeZY<JArray>(userCtx, htmlForm, zy_stk_returnbyoutstock, "发送销售退货单关联销售合同", obj, new List<string>() { });
        }

        /// <summary>
        /// 执行
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="url"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public static MuSiResponse<T> Invoke<T>(UserContext userCtx, HtmlForm bizForm, string url, string opName, Dictionary<string, object> data, List<string> numbers = null)
        {
            userCtx.CheckNoTestCompany();

            numbers = numbers == null ? new List<string>() { "" } : numbers;
            var extApp = GetExtApp(userCtx);
            if (MusiAuthUrl.Contains(url))
            {
                extApp = MusiAuthService.GetExtApp(userCtx);
            }
            if (extApp == null) return MuSiResponse<T>.SUCCESS;

            var systemIntegrationService = userCtx.Container.GetService<ISystemIntegrationService>();

            //操作日志对象
            var opLogForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "si_operationlog");
            var opLogObj = systemIntegrationService.CreateOperationLog(userCtx, opLogForm, url, opName, bizForm.Id);

            //opLogObj["fdescription"] = "当前系统调用慕思中台接口";
            opLogObj["fopstatus"] = "2";

            int loopTimes = 2, times = 0;

        retry:
            try
            {
                times++;

                var client = new MuSiSaleHttpClient(userCtx, extApp);

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"请求参数：{data.ToJson()}");

                var resp = client.Send<Dictionary<string, object>, MuSiResponse<T>>(url, data);
                if (resp.type?.ToLower() == "s" || resp.type?.ToLower() == "e")
                {
                    resp.State = true;
                    resp.Code = 200;
                }
                string fopstatus = resp.State ? "2" : "3";
                opLogObj["fopstatus"] = fopstatus;

                if (fopstatus.EqualsIgnoreCase("3"))
                {
                    opLogObj["ferrorsource"] = 2;
                    opLogObj["fdescription"] = resp.Msg;
                    opLogObj["frequestdata"] = data.ToJson();
                    opLogObj["fcanretry"] = true;
                    opLogObj["ffailnumbers"] = string.Join(",", numbers);
                    //WriteRetryLog(userCtx, bizForm, CostPoolUrl, "总部费用池查询", data, opLogObj, resp);
                }
                else
                {
                    opLogObj["fsuccessnumbers"] = string.Join(",", numbers);
                }

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"响应参数：{resp.ToJson()}");
                systemIntegrationService.SaveOperationLogAsync(userCtx, opLogForm, new[] { opLogObj });

                if (!resp.State && times < loopTimes)
                {
                    systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"重试第{times}次：");
                    Thread.Sleep(30 * 1000);
                    goto retry;
                }

                return resp;
            }
            catch (Exception ex)
            {
                var exceptionInfo = ex.ProcessExceptionMessage();

                opLogObj["fdescription"] = exceptionInfo.FullMessage;
                opLogObj["frequestdata"] = data.ToJson();
                opLogObj["fcanretry"] = true;
                opLogObj["ffailnumbers"] = string.Join(",", numbers);

                //日志保存至文件
                userCtx.Container.GetService<ILogServiceEx>().Error(ex);

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, exceptionInfo.FullStackTrace, true);
                systemIntegrationService.SaveOperationLogAsync(userCtx, opLogForm, new[] { opLogObj });

                if (times < loopTimes)
                {
                    systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"重试第{times}次：");
                    Thread.Sleep(30 * 1000);
                    goto retry;
                }

                throw;
            }
        }



        /// <summary>
        /// 执行
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="url"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public static MuSiResponse<T> InvokeMusiAuth<T>(UserContext userCtx, HtmlForm bizForm, string url, string opName, Dictionary<string, object> data, List<string> numbers = null)
        {
            userCtx.CheckNoTestCompany();

            numbers = numbers == null ? new List<string>() { "" } : numbers;
            var extApp = MusiAuthService.GetExtApp(userCtx);
            if (extApp == null) return MuSiResponse<T>.SUCCESS;

            var systemIntegrationService = userCtx.Container.GetService<ISystemIntegrationService>();

            //操作日志对象
            var opLogForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "si_operationlog");
            var opLogObj = systemIntegrationService.CreateOperationLog(userCtx, opLogForm, url, opName, bizForm.Id);

            //opLogObj["fdescription"] = "当前系统调用慕思中台接口";
            opLogObj["fopstatus"] = "2";

            int loopTimes = 2, times = 0;

        retry:
            try
            {
                times++;

                var client = new MuSiSaleHttpClient(userCtx, extApp);

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"请求参数：{data.ToJson()}");

                var resp = client.Send<Dictionary<string, object>, MuSiResponse<T>>(url, data, Enu_HttpMethod.Post, Enu_ContentType.Json);
                if (resp.Code == 0)
                {
                    resp.State = true;
                    resp.Code = 200;
                }
                string fopstatus = resp.State ? "2" : "3";
                opLogObj["fopstatus"] = fopstatus;

                if (fopstatus.EqualsIgnoreCase("3"))
                {
                    opLogObj["ferrorsource"] = 2;
                    opLogObj["fdescription"] = resp.Msg;
                    opLogObj["frequestdata"] = data.ToJson();
                    opLogObj["fcanretry"] = true;
                    opLogObj["ffailnumbers"] = string.Join(",", numbers);
                    //WriteRetryLog(userCtx, bizForm, CostPoolUrl, "总部费用池查询", data, opLogObj, resp);
                }
                else
                {
                    opLogObj["fsuccessnumbers"] = string.Join(",", numbers);
                }

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"响应参数：{resp.ToJson()}");
                systemIntegrationService.SaveOperationLogAsync(userCtx, opLogForm, new[] { opLogObj });

                if (!resp.State && times < loopTimes)
                {
                    systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"重试第{times}次：");
                    Thread.Sleep(30 * 1000);
                    goto retry;
                }

                return resp;
            }
            catch (Exception ex)
            {
                var exceptionInfo = ex.ProcessExceptionMessage();

                opLogObj["fdescription"] = exceptionInfo.FullMessage;
                opLogObj["frequestdata"] = data.ToJson();
                opLogObj["fcanretry"] = true;
                opLogObj["ffailnumbers"] = string.Join(",", numbers);

                //日志保存至文件
                userCtx.Container.GetService<ILogServiceEx>().Error(ex);

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, exceptionInfo.FullStackTrace, true);
                systemIntegrationService.SaveOperationLogAsync(userCtx, opLogForm, new[] { opLogObj });

                if (times < loopTimes)
                {
                    systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"重试第{times}次：");
                    Thread.Sleep(30 * 1000);
                    goto retry;
                }

                throw;
            }
        }


        /// <summary>
        /// 执行
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="url"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public static MuSiResponse<T> InvokeOMS<T>(UserContext userCtx, HtmlForm bizForm, string url, string opName, Dictionary<string, object> data, List<string> numbers = null)
        {
            userCtx.CheckNoTestCompany();

            numbers = numbers == null ? new List<string>() { "" } : numbers;
            var extApp = GetOMSExtApp(userCtx);
            if (extApp == null) return MuSiResponse<T>.SUCCESS;

            var systemIntegrationService = userCtx.Container.GetService<ISystemIntegrationService>();

            //操作日志对象
            var opLogForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "si_operationlog");
            var opLogObj = systemIntegrationService.CreateOperationLog(userCtx, opLogForm, url, opName, bizForm.Id);

            //opLogObj["fdescription"] = "当前系统调用慕思中台接口";
            opLogObj["fopstatus"] = "2";

            int loopTimes = 2, times = 0;

        retry:
            try
            {
                times++;

                var client = new MuSiSaleHttpClient(userCtx, extApp, true);

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"请求参数：{data.ToJson()}");

                var resp = client.Send<Dictionary<string, object>, MuSiResponse<T>>(url, data, true);
                if (resp.type?.ToLower() == "s" || resp.type?.ToLower() == "e")
                {
                    resp.State = true;
                    resp.Code = 200;
                }
                string fopstatus = resp.State ? "2" : "3";
                opLogObj["fopstatus"] = fopstatus;

                if (fopstatus.EqualsIgnoreCase("3"))
                {
                    opLogObj["ferrorsource"] = 2;
                    opLogObj["fdescription"] = resp.Msg;
                    opLogObj["frequestdata"] = data.ToJson();
                    opLogObj["fcanretry"] = true;
                    opLogObj["ffailnumbers"] = string.Join(",", numbers);
                    //WriteRetryLog(userCtx, bizForm, CostPoolUrl, "总部费用池查询", data, opLogObj, resp);
                }
                else
                {
                    opLogObj["fsuccessnumbers"] = string.Join(",", numbers);
                }

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"响应参数：{resp.ToJson()}");
                systemIntegrationService.SaveOperationLogAsync(userCtx, opLogForm, new[] { opLogObj });

                if (!resp.State && times < loopTimes)
                {
                    systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"重试第{times}次：");
                    Thread.Sleep(30 * 1000);
                    goto retry;
                }

                return resp;
            }
            catch (Exception ex)
            {
                var exceptionInfo = ex.ProcessExceptionMessage();

                opLogObj["fdescription"] = exceptionInfo.FullMessage;
                opLogObj["frequestdata"] = data.ToJson();
                opLogObj["fcanretry"] = true;
                opLogObj["ffailnumbers"] = string.Join(",", numbers);

                //日志保存至文件
                userCtx.Container.GetService<ILogServiceEx>().Error(ex);

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, exceptionInfo.FullStackTrace, true);
                systemIntegrationService.SaveOperationLogAsync(userCtx, opLogForm, new[] { opLogObj });

                if (times < loopTimes)
                {
                    systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"重试第{times}次：");
                    Thread.Sleep(30 * 1000);
                    goto retry;
                }

                throw;
            }
        }


        /// <summary>
        /// 执行
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="url"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public static MuSiResponse<T> InvokeE3<T>(UserContext userCtx, HtmlForm bizForm, string url, string opName, Dictionary<string, object> data, List<string> numbers = null)
        {
            userCtx.CheckNoTestCompany();

            numbers = numbers == null ? new List<string>() { "" } : numbers;
            var extApp = GetE3ExtApp(userCtx);
            if (extApp == null) return MuSiResponse<T>.SUCCESS;

            var systemIntegrationService = userCtx.Container.GetService<ISystemIntegrationService>();

            //操作日志对象
            var opLogForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "si_operationlog");
            var opLogObj = systemIntegrationService.CreateOperationLog(userCtx, opLogForm, url, opName, bizForm.Id);

            //opLogObj["fdescription"] = "当前系统调用慕思中台接口";
            opLogObj["fopstatus"] = "2";

            int loopTimes = 2, times = 0;

        retry:
            try
            {
                times++;

                var client = new MuSiE3Client(userCtx, extApp);

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"请求参数：{data.ToJson()}");


                var resp = client.Send<MuSiResponse<T>>(url, data, Enu_HttpMethod.Post, Enu_ContentType.FormUrlEncoded);
                //if (resp.type?.ToLower() == "s" || resp.type?.ToLower() == "e")
                //{
                //    resp.State = true;
                //    resp.Code = 200;
                //}
                //string fopstatus = resp.State ? "2" : "3";
                //opLogObj["fopstatus"] = fopstatus;

                //if (fopstatus.EqualsIgnoreCase("3"))
                //{
                //    opLogObj["ferrorsource"] = 2;
                //    opLogObj["fdescription"] = resp.Msg;
                //    opLogObj["frequestdata"] = data.ToJson();
                //    opLogObj["fcanretry"] = true;
                //    opLogObj["ffailnumbers"] = string.Join(",", numbers);
                //    //WriteRetryLog(userCtx, bizForm, CostPoolUrl, "总部费用池查询", data, opLogObj, resp);
                //}
                //else
                //{
                //    opLogObj["fsuccessnumbers"] = string.Join(",", numbers);
                //}

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"响应参数：{resp.ToJson()}");
                systemIntegrationService.SaveOperationLogAsync(userCtx, opLogForm, new[] { opLogObj });

                if (!resp.message.Equals("success") && resp.status != -1 && times < loopTimes)
                {
                    systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"重试第{times}次：");
                    Thread.Sleep(30 * 1000);
                    goto retry;
                }

                return resp;
            }
            catch (Exception ex)
            {
                var exceptionInfo = ex.ProcessExceptionMessage();

                opLogObj["fdescription"] = exceptionInfo.FullMessage;
                opLogObj["frequestdata"] = data.ToJson();
                opLogObj["fcanretry"] = true;
                opLogObj["ffailnumbers"] = string.Join(",", numbers);

                //日志保存至文件
                userCtx.Container.GetService<ILogServiceEx>().Error(ex);

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, exceptionInfo.FullStackTrace, true);
                systemIntegrationService.SaveOperationLogAsync(userCtx, opLogForm, new[] { opLogObj });

                if (times < loopTimes)
                {
                    systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"重试第{times}次：");
                    Thread.Sleep(30 * 1000);
                    goto retry;
                }

                throw;
            }
        }



        /// <summary>
        /// 【直营】执行
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="url"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public static ZYMuSiResponse<T> InvokeZY<T>(UserContext userCtx, HtmlForm bizForm, string url, string opName, Dictionary<string, object> data, List<string> numbers = null)
        {
            //userCtx.CheckNoTestCompany();

            numbers = numbers == null ? new List<string>() { "" } : numbers;
            var extApp = GetExtApp(userCtx);
            if (MusiAuthUrl.Contains(url))
            {
                extApp = MusiAuthService.GetExtApp(userCtx);
            }
            if (extApp == null) return ZYMuSiResponse<T>.SUCCESS;

            var systemIntegrationService = userCtx.Container.GetService<ISystemIntegrationService>();

            //操作日志对象
            var opLogForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "si_operationlog");
            var opLogObj = systemIntegrationService.CreateOperationLog(userCtx, opLogForm, url, opName, bizForm.Id);

            //opLogObj["fdescription"] = "当前系统调用慕思中台接口";
            opLogObj["fopstatus"] = "2";

            int loopTimes = 2, times = 0;

        retry:
            try
            {
                times++;

                var client = new MuSiSaleHttpClient(userCtx, extApp);

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"请求参数：{data.ToJson()}");

                var resp = client.Send<Dictionary<string, object>, ZYMuSiResponse<T>>(url, data);
                if (resp.STATUS.ToLower().Equals("s") || resp.STATUS.ToLower().Equals("e"))
                {
                    resp.State = true;
                    resp.Code = 200;
                }
                string fopstatus = resp.STATUS.ToLower().Equals("s") ? "2" : "3";
                opLogObj["fopstatus"] = fopstatus;

                if (fopstatus.EqualsIgnoreCase("3"))
                {
                    opLogObj["ferrorsource"] = 2;
                    opLogObj["fdescription"] = resp.MESSAGE;
                    opLogObj["frequestdata"] = data.ToJson();
                    opLogObj["fcanretry"] = true;
                    opLogObj["ffailnumbers"] = string.Join(",", numbers);
                }
                else
                {
                    opLogObj["fsuccessnumbers"] = string.Join(",", numbers);
                }

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"响应参数：{resp.ToJson()}");
                systemIntegrationService.SaveOperationLogAsync(userCtx, opLogForm, new[] { opLogObj });

                if (!resp.MESSAGE.ToLower().Contains("success") && times < loopTimes)
                {
                    systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"重试第{times}次：");
                    Thread.Sleep(30 * 1000);
                    goto retry;
                }

                return resp;
            }
            catch (Exception ex)
            {
                var exceptionInfo = ex.ProcessExceptionMessage();

                opLogObj["fdescription"] = exceptionInfo.FullMessage;
                opLogObj["frequestdata"] = data.ToJson();
                opLogObj["fcanretry"] = true;
                opLogObj["ffailnumbers"] = string.Join(",", numbers);

                //日志保存至文件
                userCtx.Container.GetService<ILogServiceEx>().Error(ex);

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, exceptionInfo.FullStackTrace, true);
                systemIntegrationService.SaveOperationLogAsync(userCtx, opLogForm, new[] { opLogObj });

                if (times < loopTimes)
                {
                    systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"重试第{times}次：");
                    Thread.Sleep(30 * 1000);
                    goto retry;
                }

                throw;
            }
        }

        /// <summary>
        /// 【直营】执行
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="url"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public static MuSiResponse<T> InvokeZY<T>(UserContext userCtx, HtmlForm bizForm, string url, string opName, Dictionary<string, List<Dictionary<string, object>>> data, List<string> numbers = null)
        {
            //userCtx.CheckNoTestCompany();

            numbers = numbers == null ? new List<string>() { "" } : numbers;
            var extApp = GetExtApp(userCtx);
            if (MusiAuthUrl.Contains(url))
            {
                extApp = MusiAuthService.GetExtApp(userCtx);
            }
            if (extApp == null) return MuSiResponse<T>.SUCCESS;

            var systemIntegrationService = userCtx.Container.GetService<ISystemIntegrationService>();

            //操作日志对象
            var opLogForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "si_operationlog");
            var opLogObj = systemIntegrationService.CreateOperationLog(userCtx, opLogForm, url, opName, bizForm.Id);

            //opLogObj["fdescription"] = "当前系统调用慕思中台接口";
            opLogObj["fopstatus"] = "2";

            int loopTimes = 2, times = 0;

        retry:
            try
            {
                times++;

                var client = new MuSiSaleHttpClient(userCtx, extApp);

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"请求参数：{data.ToJson()}");

                var resp = client.Send<Dictionary<string, List<Dictionary<string, object>>>, MuSiResponse<T>>(url, data);
                if (resp.type?.ToLower() == "s" || resp.type?.ToLower() == "e")
                {
                    resp.State = true;
                    resp.Code = 200;
                }
                string fopstatus = resp.State ? "2" : "3";
                opLogObj["fopstatus"] = fopstatus;

                if (fopstatus.EqualsIgnoreCase("3"))
                {
                    opLogObj["ferrorsource"] = 2;
                    opLogObj["fdescription"] = resp.Msg;
                    opLogObj["frequestdata"] = data.ToJson();
                    opLogObj["fcanretry"] = true;
                    opLogObj["ffailnumbers"] = string.Join(",", numbers);
                    //WriteRetryLog(userCtx, bizForm, CostPoolUrl, "总部费用池查询", data, opLogObj, resp);
                }
                else
                {
                    opLogObj["fsuccessnumbers"] = string.Join(",", numbers);
                }

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"响应参数：{resp.ToJson()}");
                systemIntegrationService.SaveOperationLogAsync(userCtx, opLogForm, new[] { opLogObj });

                if (!resp.State && times < loopTimes)
                {
                    systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"重试第{times}次：");
                    Thread.Sleep(30 * 1000);
                    goto retry;
                }

                return resp;
            }
            catch (Exception ex)
            {
                var exceptionInfo = ex.ProcessExceptionMessage();

                opLogObj["fdescription"] = exceptionInfo.FullMessage;
                opLogObj["frequestdata"] = data.ToJson();
                opLogObj["fcanretry"] = true;
                opLogObj["ffailnumbers"] = string.Join(",", numbers);

                //日志保存至文件
                userCtx.Container.GetService<ILogServiceEx>().Error(ex);

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, exceptionInfo.FullStackTrace, true);
                systemIntegrationService.SaveOperationLogAsync(userCtx, opLogForm, new[] { opLogObj });

                if (times < loopTimes)
                {
                    systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"重试第{times}次：");
                    Thread.Sleep(30 * 1000);
                    goto retry;
                }

                throw;
            }
        }

        private static void WriteRetryLog<T>(UserContext userCtx, HtmlForm bizForm, string url, string opName, Dictionary<string, object> data, DynamicObject opLogObj, MuSiResponse<T> resp)
        {
            var systemIntegrationService = userCtx.Container.GetService<ISystemIntegrationService>();

            var opLogRetryForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "si_operationlogretry");
            var opLogRetryObj = systemIntegrationService.CreateOperationLog(userCtx, opLogRetryForm, url, opName, bizForm.Id);
            systemIntegrationService.WriteOperationLog(userCtx, opLogRetryObj, $"请求参数：{data.ToJson()}");

            opLogRetryObj = (DynamicObject)opLogObj.Clone();
            opLogRetryObj["id"] = string.Empty;

            systemIntegrationService.WriteOperationLog(userCtx, opLogRetryObj, $"响应参数：{resp.ToJson()}");
            systemIntegrationService.SaveOperationLogAsync(userCtx, opLogRetryForm, new[] { opLogRetryObj });
        }

        /// <summary>
        /// 执行
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="url"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public static MuSiResponse<T> InvokeWithoutLog<T>(UserContext userCtx, string url, object data)
        {
            var extApp = GetExtApp(userCtx);
            if (extApp == null) return MuSiResponse<T>.SUCCESS;

            var client = new MuSiSaleHttpClient(userCtx, extApp);

            var resp = client.Send<object, MuSiResponse<T>>(url, data);

            return resp;
        }

        /// <summary>
        /// 执行
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="url"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public static MuSiResponse<T> OMSInvokeWithoutLog<T>(UserContext userCtx, string url, object data)
        {
            var extApp = GetOMSExtApp(userCtx);
            if (extApp == null) return MuSiResponse<T>.SUCCESS;

            var client = new MuSiSaleHttpClient(userCtx, extApp);

            var resp = client.Send<object, MuSiResponse<T>>(url, data);

            return resp;
        }

        /// <summary>
        /// 获取外部应用
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private static DynamicObject GetExtApp(UserContext userCtx)
        {
            var sysProfileService = userCtx.Container.GetService<ISystemProfile>();

            var topCtx = userCtx.CreateTopOrgDBContext();

            var extAppId = sysProfileService.GetSystemParameter<string>(topCtx, "si_datasyncparam", "fmusiextappid", "");

            if (extAppId.IsNullOrEmptyOrWhiteSpace())
            {
                var formMeta = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "si_datasyncparam");

                throw new BusinessException($"未设置【{formMeta.Caption}】的【{formMeta.GetField("fmusiextappid")?.Caption}】");
            }

            var extApp = topCtx.LoadBizDataById("sys_externalapp", extAppId);
            return extApp;
        }

        /// <summary>
        /// 获取外部应用
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private static DynamicObject GetOMSExtApp(UserContext userCtx)
        {
            var sysProfileService = userCtx.Container.GetService<ISystemProfile>();

            var topCtx = userCtx.CreateTopOrgDBContext();

            var extAppId = sysProfileService.GetSystemParameter<string>(topCtx, "si_datasyncparam", "fmusiomsextappid", "");

            if (extAppId.IsNullOrEmptyOrWhiteSpace())
            {
                var formMeta = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "si_datasyncparam");

                throw new BusinessException($"未设置【{formMeta.Caption}】的【{formMeta.GetField("fmusiomsextappid")?.Caption}】");
            }

            var extApp = topCtx.LoadBizDataById("sys_externalapp", extAppId);
            return extApp;
        }

        /// <summary>
        /// 获取E3外部应用
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static DynamicObject GetE3ExtApp(UserContext userCtx)
        {
            var sysProfileService = userCtx.Container.GetService<ISystemProfile>();

            var topCtx = userCtx.CreateTopOrgDBContext();

            var extAppId = sysProfileService.GetSystemParameter<string>(topCtx, "si_datasyncparam", "fmusie3appid", "");

            if (extAppId.IsNullOrEmptyOrWhiteSpace())
            {
                var formMeta = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "si_datasyncparam");

                throw new BusinessException($"未设置【{formMeta.Caption}】的【{formMeta.GetField("fmusie3appid")?.Caption}】");
            }

            var extApp = topCtx.LoadBizDataById("sys_externalapp", extAppId);
            return extApp;
        }
    }
}
