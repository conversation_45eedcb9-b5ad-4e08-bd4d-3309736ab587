using System.Collections.Generic;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration
{
    public interface IMuSiFormServiceLoader
    {
        /// <summary>
        /// 创建集成服务实例
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="form"></param>
        /// <param name="extAppId"></param>
        /// <param name="syncDir"></param>
        /// <param name="syncTimePoint"></param>
        /// <returns></returns>
        IEnumerable<FormServiceDesc> CreateSyncService(UserContext userCtx, HtmlForm form, string extAppId, Enu_MuSiSyncDir syncDir, Enu_MuSiSyncTimePoint syncTimePoint);
    }
}