using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.FormService;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.FormService
{
    /// <summary>
    /// 慕思服务实例构造器
    /// </summary>
    [InjectService("<PERSON>S<PERSON>")]
    public class MuSiFormServiceLoader : IFormServiceLoader, IMuSiFormServiceLoader
    {
        /// <summary>
        /// 数据库访问服务
        /// </summary>
        [InjectProperty]
        protected IDBService DBService { get; set; }

        /// <summary>
        /// 日志服务
        /// </summary>
        [InjectProperty]
        protected ILogServiceEx Logger { get; set; }

        [InjectProperty]
        protected IMuSiBizObjMapService MapService { get; set; }

        /// <summary>
        /// 创建集成服务实例
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="form"></param>
        /// <param name="syncTimePoint"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        public IEnumerable<FormServiceDesc> CreateSyncService(UserContext userCtx, HtmlForm form, Enu_SyncTimePoint syncTimePoint, OperateOption option)
        {
            List<FormServiceDesc> lstServiceDesc = new List<FormServiceDesc>();

            Enu_MuSiSyncTimePoint musiSyncTimePoint;

            switch (syncTimePoint)
            {
                case Enu_SyncTimePoint.SyncWhenSave:
                    musiSyncTimePoint = Enu_MuSiSyncTimePoint.SyncAfterSave;
                    break;
                case Enu_SyncTimePoint.SyncWhenDelete:
                    musiSyncTimePoint = Enu_MuSiSyncTimePoint.SyncAfterDelete;
                    break;
                case Enu_SyncTimePoint.SyncWhenAudit:
                    musiSyncTimePoint = Enu_MuSiSyncTimePoint.SyncAfterAudit;
                    break;
                case Enu_SyncTimePoint.SyncWhenUnAudit:
                    musiSyncTimePoint = Enu_MuSiSyncTimePoint.SyncAfterUnaudit;
                    break;
                case Enu_SyncTimePoint.SyncWhenSubmit:
                    musiSyncTimePoint = Enu_MuSiSyncTimePoint.SyncAfterSubmit;
                    break;
                case Enu_SyncTimePoint.SyncWhenForbid:
                    musiSyncTimePoint = Enu_MuSiSyncTimePoint.SyncAfterForbid;
                    break;
                case Enu_SyncTimePoint.SyncWhenUnForbid:
                    musiSyncTimePoint = Enu_MuSiSyncTimePoint.SyncAfterUnForbid;
                    break;
                default:
                    throw new BusinessException($"{syncTimePoint}未定义！");
            }

            var topCtx = userCtx.CreateTopOrgDBContext();
            var configs = this.MapService.GetBizObjMaps(topCtx, form,
                (Enu_MuSiSyncDir.CurrentToMuSi | Enu_MuSiSyncDir.MuSiToCurrent), musiSyncTimePoint);

            if (configs == null || configs.Count == 0) return lstServiceDesc;

            foreach (var config in configs)
            {
                lstServiceDesc.Add(new FormServiceDesc()
                {
                    ServiceId = YDJHtmlElementType.HtmlBizService_MuSiSysSync,
                    ServiceAlias = "慕思系统数据集成服务",
                    ParamString = $"{{'extAppId':'{config["fextappid"]}','billMapId':'{config["id"]}'}}",
                    Condition = config["ffilterstring"] as string
                });
            }

            return lstServiceDesc;
        }

        /// <summary>
        /// 创建集成服务实例
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="form"></param>
        /// <param name="extAppId"></param>
        /// <param name="syncDir"></param>
        /// <param name="syncTimePoint"></param>
        /// <returns></returns>
        public IEnumerable<FormServiceDesc> CreateSyncService(UserContext userCtx, HtmlForm form, string extAppId, Enu_MuSiSyncDir syncDir, Enu_MuSiSyncTimePoint syncTimePoint)
        {
            List<FormServiceDesc> lstServiceDesc = new List<FormServiceDesc>();

            if (extAppId.IsNullOrEmptyOrWhiteSpace()) return lstServiceDesc;

            var configs = this.MapService.GetBizObjMaps(userCtx, form, extAppId, syncDir, syncTimePoint);

            if (configs == null || configs.Count == 0) return lstServiceDesc;

            foreach (var config in configs)
            {
                lstServiceDesc.Add(new FormServiceDesc()
                {
                    ServiceId = YDJHtmlElementType.HtmlBizService_MuSiSysSync,
                    ServiceAlias = "慕思系统数据集成服务",
                    ParamString = $"{{'extAppId':'{extAppId}','billMapId':'{config["id"]}'}}",
                    Condition = config["ffilterstring"] as string
                });
            }

            return lstServiceDesc;
        }

        ///// <summary>
        ///// 加载配置
        ///// </summary>
        ///// <param name="userCtx"></param>
        ///// <param name="form"></param>
        ///// <param name="extAppId"></param>
        ///// <param name="filter"></param>
        ///// <returns></returns>
        //private List<Dictionary<string, string>> LoadConfigs(UserContext userCtx, HtmlForm form, string extAppId, string filter)
        //{
        //    var configs = new List<Dictionary<string, string>>();

        //    // 如果总部为空，根据当前上下文找到对应的组织，取其总部
        //    if (userCtx.TopCompanyId.IsNullOrEmptyOrWhiteSpace())
        //    {
        //        string sql = $"select ftopcompanyid from t_bas_organization where fid = '{userCtx.Company}'";
        //        using (var reader = this.DBService.ExecuteReader(userCtx, sql))
        //        {
        //            if (reader.Read())
        //            {
        //                userCtx.UserSession.TopCompanyId = reader.GetString(0);
        //            }
        //        }
        //    }

        //    var strSql = $@"
        //    select fid,fextappid,fapino,ffilterstring,fmainorgid,fsyncaftersave,fsyncafterdelete,
        //    fsyncafteraudit,fsyncafterunaudit,fmyobjectid,fextobjectid from t_si_musibizobjmap 
        //    where fmyobjectid=@formId and fforbidstatus='0' and (fmainorgid='0' or fmainorgid=@fmainorgid or fmainorgid=@ftopcompanyid) and fextappid=@extAppId {filter} order by fsyncorder asc";

        //    var sqlParam = new List<SqlParam>
        //    {
        //        new SqlParam("formId", DbType.String, form.Id),
        //        new SqlParam("fmainorgid", DbType.String, userCtx.Company),
        //        new SqlParam("ftopcompanyid", DbType.String, userCtx.TopCompanyId),
        //        new SqlParam("extAppId", DbType.String, extAppId)
        //    };

        //    var datas = new List<Dictionary<string, string>>();
        //    using (var reader = this.DBService.ExecuteReader(userCtx, strSql, sqlParam))
        //    {
        //        while (reader.Read())
        //        {
        //            var data = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        //            {
        //                { "fsyncaftersave",Convert.ToString(reader["fsyncaftersave"])},
        //                { "fsyncafterdelete",Convert.ToString(reader["fsyncafterdelete"])},
        //                { "fsyncafteraudit",Convert.ToString(reader["fsyncafteraudit"]) },
        //                { "fsyncafterunaudit", Convert.ToString(reader["fsyncafterunaudit"]) },
        //                { "fextappid",Convert.ToString(reader["fextappid"])},
        //                { "fmyobjectid",Convert.ToString(reader["fmyobjectid"])},
        //                { "fextobjectid",Convert.ToString(reader["fextobjectid"])},
        //                { "fid",Convert.ToString(reader["fid"])},
        //                { "fapino",Convert.ToString(reader["fapino"])},
        //                { "fmainorgid",Convert.ToString(reader["fmainorgid"])},
        //                { "ffilterstring", Convert.ToString(reader["ffilterstring"])}
        //            };
        //            datas.Add(data);
        //        }
        //    }

        //    if (datas.Count <= 0) return configs;

        //    //1.加载自已公司的配置
        //    configs = datas.Where(x => x["fmainorgid"].EqualsIgnoreCase(userCtx.Company)).ToList();

        //    //2.加载总部的配置
        //    foreach (var data in datas.Where(x => x["fmainorgid"].EqualsIgnoreCase(userCtx.TopCompanyId)))
        //    {
        //        var fapino = data["fapino"];
        //        var fextappid = data["fextappid"];
        //        var fmyobjectid = data["fmyobjectid"];

        //        //如果已存在相同的配置则不需要再加载
        //        var config = configs.FirstOrDefault(x => x["fapino"].EqualsIgnoreCase(fapino) &&
        //                                                 x["fextappid"].EqualsIgnoreCase(fextappid) &&
        //                                                 x["fmyobjectid"].EqualsIgnoreCase(fmyobjectid));

        //        if (config == null)
        //        {
        //            configs.Add(data);
        //        }
        //    }

        //    //3.加载系统预置的配置
        //    foreach (var data in datas.Where(x => x["fmainorgid"].EqualsIgnoreCase("0")))
        //    {
        //        var fapino = data["fapino"];
        //        var fextappid = data["fextappid"];
        //        var fmyobjectid = data["fmyobjectid"];

        //        //如果已存在相同的配置则不需要再加载
        //        var config = configs.FirstOrDefault(x => x["fapino"].EqualsIgnoreCase(fapino) &&
        //                                                 x["fextappid"].EqualsIgnoreCase(fextappid) &&
        //                                                 x["fmyobjectid"].EqualsIgnoreCase(fmyobjectid));

        //        if (config == null)
        //        {
        //            configs.Add(data);
        //        }
        //    }

        //    return configs;
        //}
    }
}
