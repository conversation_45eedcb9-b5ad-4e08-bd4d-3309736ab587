using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.BAS.Product
{
    /// <summary>
    /// 商品：慕思通用保存同步操作
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("MSSaveSync")]
    public class MSSaveSync : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case "BeforeConvertBaseData":
                    this.BeforeConvertBaseData(e);
                    break;
                case "AfterPackSourceBill":
                    this.AfterPackSourceBill(e);
                    break;
                case "BeforeSaveSourceBill":
                    this.BeforeSaveSourceBill(e);
                    break;
                case "SourceBillFieldMapping":
                    this.SourceBillFieldMapping(e);
                    break;
                case "BeforeCreateBaseData":
                    this.BeforeCreateBaseData(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// //基础资料字段值转换前事件：可指定当前需要转换的基础资料字段
        /// </summary>
        /// <param name="e"></param>
        private void BeforeConvertBaseData(OnCustomServiceEventArgs e)
        {
            //e.Cancel = true;
            //e.Result = new List<string>
            //{

            //};
        }

        /// <summary>
        /// 来源单据打包后事件：可对打包后的数据包进行处理或者直接覆盖整个数据包都是可以的
        /// </summary>
        /// <param name="e"></param>
        private void AfterPackSourceBill(OnCustomServiceEventArgs e)
        {
            //var dataEntitys = (e.EventData as DynamicObject[])?.ToList();
            //if (dataEntitys == null || dataEntitys.Count < 1) return;

        }

        /// <summary>
        /// 创建基础资料前事件：可指定当前不需要创建的基础资料表单
        /// </summary>
        /// <param name="e"></param>
        private void BeforeCreateBaseData(OnCustomServiceEventArgs e)
        {
            //e.Cancel = true;
            //e.Result = new List<string>
            //{
            //    "ydj_series"
            //};
        }

        /// <summary>
        /// 来源单据保存前事件：可对当前要保存的数据包做处理
        /// </summary>
        /// <param name="e"></param>
        private void BeforeSaveSourceBill(OnCustomServiceEventArgs e)
        {
            var dataEntitys = (e.EventData as DynamicObject[])?.ToList();
            if (dataEntitys == null || dataEntitys.Count < 1) return;

            var seriesIds = dataEntitys.Where(s => !s["fseriesid"].IsNullOrEmptyOrWhiteSpace())
                .Select(s => Convert.ToString(s["fseriesid"])).Distinct();

            var serieses = this.Context.LoadBizDataById("ydj_series", seriesIds);

            //查找型号
            var seltypeids = dataEntitys.Where(s => !s["fseltypeid"].IsNullOrEmptyOrWhiteSpace())
                .Select(s => Convert.ToString(s["fseltypeid"])).Distinct();
            var seltypes = this.Context.LoadBizDataById("sel_type", seltypeids);

            var productCategory = this.Context
                .LoadBizDataByNo("ydj_productcategory", "fnumber", new[] { "CHLB_SYS_02" })
                // 只取当前组织的
                .FirstOrDefault(s => Convert.ToString(s["fmainorgid"]).EqualsIgnoreCase(this.Context.Company));

            foreach (var dataEntity in dataEntitys)
            {
                var fdescription = Convert.ToString(dataEntity["fdescription"]);
                //总部手工单自动创建的商品，重新下发时停购标记取消，但是如果是手动创建的停购商品，重新下发不走此逻辑
                if (fdescription.EqualsIgnoreCase("总部手工单下发自动创建") || fdescription.Contains("导入")) 
                {
                    dataEntity["fendpurchase"] = false;
                }
                //总部下发，慕思商品默认为是
                dataEntity["fismusiproduct"] = true;
                // 根据【系列】（业绩品牌）获取品牌反写
                var seriesId = Convert.ToString(dataEntity["fseriesid"]);
                var series = serieses.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(seriesId));
                dataEntity["fbrandid"] = Convert.ToString(series?["fbrandid"]);
                // 默认存货类别
                dataEntity["fcostcategoryid"] = Convert.ToString(productCategory?["id"]);

                // 根据【型号】获取商品主图反写
                var seltypeid = Convert.ToString(dataEntity["fseltypeid"]);
                var seltype = seltypes.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(seltypeid));
                dataEntity["fimage"] = Convert.ToString(seltype?["fimage"]);
                dataEntity["fimage_txt"] = Convert.ToString(seltype?["fimage_txt"]);

                /*
                 * 【慕思现场】商品接口增加"核价状态"的判断, 是否可下单来判断销售组织是否禁用
                 * ①当中台给金蝶传输的产品销售组织时, 如果对应的"禁用状态"="已启用" 且 "核价状态"为“可下单”时，则金蝶的销售组织对应的"禁用状态"="已启用"。
                 * ②当中台给金蝶传输的产品销售组织时, 如果对应的"禁用状态"="已启用" 且 "核价状态"不等于“可下单”时，则金蝶的销售组织对应的"禁用状态"="已禁用"。
                 * ③当中台给金蝶传输的产品销售组织时, 如果对应的"禁用状态"="已禁用" ，则金蝶的销售组织对应的"禁用状态"="已禁用"。
                 */
                var saleOrgEntrys = (DynamicObjectCollection)dataEntity["fsaleorgentry"];
                foreach (var saleOrgEntry in saleOrgEntrys)
                {
                    // 禁用状态
                    var fdisablestatus = Convert.ToString(saleOrgEntry["fdisablestatus"]);
                    // 核价状态
                    var fverifypricestatus = Convert.ToString(saleOrgEntry["fverifypricestatus"]);

                    saleOrgEntry["fdisablestatus"] = fdisablestatus.EqualsIgnoreCase("1") && fverifypricestatus.EqualsIgnoreCase("可下单") ? "1" : "2";
                }
            }
        }

        /// <summary>
        /// 来源单据字段映射事件：可对已存在的数据包做映射覆盖
        /// </summary>
        /// <param name="e"></param>
        private void SourceBillFieldMapping(OnCustomServiceEventArgs e)
        {
            //var eventData = e.EventData as Tuple<DynamicObject[], IEnumerable<DynamicObject>>;
            //if (eventData == null
            //    || eventData.Item1 == null
            //    || eventData.Item1.Length < 1) return;
        }
    }
}
