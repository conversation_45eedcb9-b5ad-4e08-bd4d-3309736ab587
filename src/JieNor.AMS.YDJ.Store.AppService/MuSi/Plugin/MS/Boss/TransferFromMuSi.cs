using System;
using System.Linq;
using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.MS.Boss
{
    /// <summary>
    /// 实控人：慕思同步
    /// </summary>
    [InjectService]
    [FormId("ms_boss")]
    [OperationNo("syncfrommusi")]
    [ThirdSystemId("musi")]
    public class TransferToMuSi : AbstractSyncDataFromMuSiPlugIn
    {
        //to do 实控人拉取联系人的时候需要再触发本地经销商的失败重试，即 重置本地经销商的拉取次数。 
        public override void AfterExecute(AfterExecuteEventArgs e)
        {
            base.AfterExecute(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;
            var DBServiceEx = this.Context.Container.GetService<IDBServiceEx>();
            var bossids = e.DataEntitys.Select(o => Convert.ToString(o["id"])).ToList(); 
            // 针对经销商本地重试五次仍旧失败 但是关联经销商的实控人、联系方式依旧为空的场景，需要重置中间表状态
            string sql = $@"/*dialect*/
                            update t1 set t1.fretrytimes = 0
                            from t_ms_pulldata t1
                            inner
                            join t_ms_deliver_series as delser on delser.fagentnumber = t1.fbizobjno
                            left
                            join t_bas_agent as ag on ag.fnumber = t1.fbizobjno
                            where delser.fbossid in  ({ bossids.JoinEx(",", true)}) and ag.fnumber is null and t1.fretrytimes = 5; 
                            ";



            DBServiceEx.Execute(this.Context, sql); 
        }
        
    }
}
