namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Enums
{
    /// <summary>
    /// 同步时机枚举定义
    /// </summary>
    public enum Enu_MuSiSyncTimePoint
    {
        /// <summary>
        /// 定时同步
        /// </summary>
        SyncTimer,
        /// <summary>
        /// 手工同步
        /// </summary>
        SyncManual,
        /// <summary>
        /// 保存时同步
        /// </summary>
        SyncAfterSave,
        /// <summary>
        /// 删除时同步
        /// </summary>
        SyncAfterDelete,
        /// <summary>
        /// 审核时同步
        /// </summary>
        SyncAfterAudit,
        /// <summary>
        /// 反审核时同步
        /// </summary>
        SyncAfterUnaudit,
        /// <summary>
        /// 禁用时同步
        /// </summary>
        SyncAfterForbid,
        /// <summary>
        /// 反禁用时同步
        /// </summary>
        SyncAfterUnForbid,
        /// <summary>
        /// 提交时同步
        /// </summary>
        SyncAfterSubmit
    }
}