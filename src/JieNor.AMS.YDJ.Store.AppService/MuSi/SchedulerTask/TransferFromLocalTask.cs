using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.SchedulerTask
{
    /// <summary>
    /// 处理本地失败的经销商主数据
    /// </summary>
    [InjectService]
    [TaskSvrId("transferfromlocal")]
    [Caption("处理本地失败的经销商主数据")]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class TransferFromLocalTask : AbstractScheduleWorker
    {
        /// <summary>
        /// 计划任务配置时的参数设置界面
        /// </summary>
        public override string ParamFormId => "si_transferfromlocaltaskparam";

        /// <summary>
        /// 计划同步任务参数
        /// </summary>
        protected Dictionary<string, object> JobParameter { get; set; }

        protected ILogServiceEx LogService { get; set; }

        protected IDBServiceEx DBServiceEx { get; set; }

        /// <summary>
        /// 执行数据打包同步分发逻辑
        /// </summary>
        protected override async Task DoExecute()
        {
            this.LogService = this.UserContext.Container.GetService<ILogServiceEx>();
            this.DBServiceEx = this.UserContext.Container.GetService<IDBServiceEx>();

            this.JobParameter = Convert.ToString(this.TaskObject.Parameter).FromJson<Dictionary<string, object>>() ?? new Dictionary<string, object>();

            var allFieldMapBillObjs = this.LoadAllLinkFieldMapBills();

            var fmaxsize = this.JobParameter.GetInt("fmaxsize");
            if (fmaxsize <= 0 || fmaxsize > 100)
            {
                fmaxsize = 100;
            }

            DateTime date = DateTime.Now;

            foreach (var item in allFieldMapBillObjs)
            {
                if (item.Item2.IsNullOrEmpty())
                {
                    continue;
                }

                var formMeta = item.Item1;
                string tmpTableName = this.DBService.CreateTemporaryTableName(this.UserContext);
                try
                {
                    List<string> successTranIds = new List<string>();
                    bool invoked = false;
                    // 初始化数据，没有数据则不需要处理,重复数据不需要处理
                    var unhandledObjs = InitData(tmpTableName, formMeta, date);
                    if (unhandledObjs.Any())
                    {
                        JArray data = new JArray();
                        foreach (var obj in unhandledObjs)
                        {
                            var ftranid = Convert.ToString(obj["ftranid"]);
                            if (Convert.ToString(obj["fopstatus"]).EqualsIgnoreCase("1"))
                            {
                                successTranIds.Add(ftranid);
                                continue;
                            }
                            var json = Convert.ToString(obj["fjson"]);
                            if (json.IsNullOrEmptyOrWhiteSpace())
                            {
                                successTranIds.Add(ftranid);
                                continue;
                            }

                            data.Add(JObject.Parse(json));

                            if (data.Count == fmaxsize)
                            {
                                InvokeAll(item, formMeta, data, successTranIds);
                                invoked = true;

                                data.Clear();
                            }
                        }

                        if (data.Any())
                        {
                            InvokeAll(item, formMeta, data, successTranIds);
                            invoked = true;

                            data.Clear();
                        }
                    }

                    if (!invoked)
                    {
                        this.WriteLog($"操作完成：没有数据需要处理", formMeta);
                    }

                    UpdateOpStatus(successTranIds, tmpTableName, formMeta, date);
                }
                finally
                {
                    this.DBService.DeleteTempTableByName(this.UserContext, tmpTableName, true);
                }
            }
        }

        private void InvokeAll(Tuple<HtmlForm, List<DynamicObject>> item, HtmlForm formMeta, JArray data, List<string> successTranIds)
        {
            foreach (var fieldMapObj in item.Item2)
            {
                try
                {
                    var result = this.Invoke(this.UserContext, formMeta, fieldMapObj, data);
                    this.Result.MergeResult(result);

                    var thisSuccessTranIds = (result.SrvData as List<string>) ?? new List<string>();
                    successTranIds.AddRange(thisSuccessTranIds);

                    this.WriteLog($"操作完成：" + result.ToString(), formMeta);
                }
                catch (InvokeOperationException ex)
                {
                    this.LogService?.Error("慕思拉取数据", ex);
                    this.WriteLog(ex.Message, formMeta);
                }
                catch (BusinessException ex)
                {
                    this.LogService?.Error(this.TaskObject.TaskName, ex);
                    this.WriteLog(ex.Message, formMeta);
                }
                catch (Exception ex)
                {
                    this.LogService?.Error(this.TaskObject.TaskName, ex);
                    this.WriteLog($"出现未知错误：{ex.Message}, stacktrace:{ex.StackTrace}", formMeta);
                }
                finally
                {
                    this.SaveLog();
                }
            }
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        /// <param name="tmpTableName"></param>
        /// <param name="formMeta"></param>
        /// <returns>是否有数据</returns>
        private List<DynamicObject> InitData(string tmpTableName, HtmlForm formMeta, DateTime date)
        {
            //            // 取失败记录到临时表
            //            string sql = $@"
            //select top {maxSize} fid, ftranid, fbizformid, fopstatus  into {tmpTableName} from t_ms_pulldata with(nolock) 
            //where fbizformid='{formMeta.Id}' and fopstatus='0'
            //";
            //            int count = this.DBServiceEx.Execute(this.UserContext, sql);

            //            sql = $"select COUNT(1) fcount from {tmpTableName}";
            //            count = Convert.ToInt32(this.DBService.ExecuteDynamicObject(this.UserContext, sql)
            //                .First()["fcount"]);
            //            if (count == 0)
            //            {
            //                return new List<DynamicObject>();
            //            }

            //            // 取最新数据的操作状态，更新操作状态到临时表
            //            var tmpTableName2 = this.DBService.CreateTemporaryTableName(this.UserContext);
            //            sql = $@"
            //select t1.ftranid, t1.fopstatus
            //into {tmpTableName2}
            //from t_ms_pulldata t1 with(nolock)
            //inner join 
            //(
            //    select ftranid, max(fid) as fmaxid 
            //    from t_ms_pulldata m with(nolock) 
            //    where fbizformid='{formMeta.Id}' and exists(select 1 from {tmpTableName} t where t.ftranid=m.ftranid)
            //    group by ftranid 
            //) t2 on t1.fid=t2.fmaxid";
            //            this.DBServiceEx.Execute(this.UserContext, sql);

            //            // 更新状态到临时表
            //            sql = $@"/*dialect*/
            //update t1 set fopstatus=t2.fopstatus
            //from {tmpTableName} as t1
            //inner join {tmpTableName2} as t2 on t1.ftranid=t2.ftranid
            //where t2.fopstatus='1'
            //";
            //            this.DBServiceEx.Execute(this.UserContext, sql);

            //            // 取要处理的数据包
            //            sql = $@"
            //select fid, ftranid, fjson from t_ms_pulldata with(nolock)
            //where fid in
            //(
            //    select max(fid) from {tmpTableName}
            //    where fopstatus='0'
            //    group by ftranid
            //)";

            // 取失败的记录ftranid
            string sql = $@"
select distinct ftranid into {tmpTableName} from t_ms_pulldata with(nolock) 
where fbizformid=@fbizformid and (fopstatus='0' or fopstatus='') and fcreatedate<=@date and fretrytimes<5
";
            this.DBServiceEx.Execute(this.UserContext, sql, new List<SqlParam>
            {
                new SqlParam("@fbizformid", DbType.String, formMeta.Id),
                new SqlParam("@date", DbType.DateTime, date)
            });

            // 取ftranid对应的最新数据包
            sql = $@"
select t.fid, t.ftranid, t.fcreatedate, t.fjson, t.fopstatus from t_ms_pulldata t with(nolock)
inner join (
    select fbizformid, ftranid, max(fcreatedate) as fcreatedate from t_ms_pulldata te with(nolock)
    where fbizformid=@fbizformid and ftranid in (select ftranid from {tmpTableName})
        and fcreatedate<=@date
    group by fbizformid, ftranid
) ta on t.fbizformid=ta.fbizformid and t.ftranid=ta.ftranid and t.fcreatedate=ta.fcreatedate
";

            var unhandledObjs = this.DBService.ExecuteDynamicObject(this.UserContext, sql, new List<SqlParam>
            {
                new SqlParam("@fbizformid", DbType.String, formMeta.Id),
                new SqlParam("@date", DbType.DateTime, date)
            }).ToList();

            return unhandledObjs;
        }

        /// <summary>
        /// 更新操作状态
        /// </summary>
        /// <param name="successTranIds"></param>
        private void UpdateOpStatus(List<string> successTranIds, string tmpTableName, HtmlForm formMeta, DateTime date)
        {
            using (var tran = UserContext.CreateTransaction())
            {
                string successWhere = "", failWhere = "";
                if (successTranIds.Count > 50)
                {
                    var pkTableName = this.DBService.CreateTempTableWithDataList(this.UserContext, successTranIds,false);

                    successWhere = $" and ftranid in (select FID from {pkTableName})";
                    failWhere = $@"
                            and ftranid in (
                                select ftranid from {tmpTableName} 
                                where ftranid not in (select FID from {pkTableName})
                            )";
                }
                else if (successTranIds.Count > 0)
                {
                    successWhere = $" and ftranid in ({successTranIds.JoinEx(",", true)})";
                    failWhere = $@" 
                            and ftranid in (
                               select ftranid from {tmpTableName} 
                               where ftranid not in ({successTranIds.JoinEx(",", true)})
                            )";
                }
                else
                {
                    successWhere = $" and 1=2";
                    failWhere = $@" 
                            and ftranid in (
                               select ftranid from {tmpTableName}
                            )";
                }

                // 更新状态
                string sql = $@"/*dialect*/
                            update t1 set fopstatus='1', fretrydate=getdate(), fretrytimes=fretrytimes+1
                            from t_ms_pulldata t1
                            where t1.fbizformid=@fbizformid {successWhere} and fcreatedate<=@date and (fopstatus='0' or fopstatus='');

                            update t1 set fopstatus='0', fretrydate=getdate(), fretrytimes=fretrytimes+1
                            from t_ms_pulldata t1
                            where t1.fbizformid=@fbizformid {failWhere} and fcreatedate<=@date and (fopstatus='0' or fopstatus='');
                            ";
                this.DBServiceEx.Execute(this.UserContext, sql, new List<SqlParam>
                                                                    {
                                                                        new SqlParam("@fbizformid", DbType.String, formMeta.Id),
                                                                        new SqlParam("@date", DbType.DateTime, date)
                                                                    });

                tran.Complete();
            }
        }

        /// <summary>
        /// 执行数据同步逻辑
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="fieldMapObj"></param>
        /// <param name="bizForm"></param>
        /// <returns></returns>
        private IOperationResult Invoke(UserContext userCtx, HtmlForm bizForm, DynamicObject fieldMapObj, JArray data)
        {
            var options = new Dictionary<string, object>();
            options["__taskId__"] = this.TaskId;
            options["__fieldMapObj__"] = fieldMapObj;
            options["extAppId"] = fieldMapObj["fextappid"];
            options["IgnoreCheckPermssion"] = true;
            options["__LocalData__"] = data;
            options["__FromLocalData__"] = true;

            //交给本地操作行为执行，目的是具体同步的代码与按钮操作代码进行复用
            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
            var result = gateway.InvokeBillOperation(userCtx, bizForm.Id, new List<DynamicObject>(), "syncfrommusi", options);
            return result;
        }

        /// <summary>
        /// 加载所有业务映射
        /// </summary>
        /// <returns></returns>
        private List<Tuple<HtmlForm, List<DynamicObject>>> LoadAllLinkFieldMapBills()
        {
            var maps = new List<Tuple<HtmlForm, List<DynamicObject>>>();

            var fextappid = this.JobParameter?.GetString("fextappid");
            var fbizformids = this.JobParameter?.GetString("fbizformids")?.SplitKey(",");
            if (fextappid.IsNullOrEmptyOrWhiteSpace() || fbizformids.IsNullOrEmpty())
            {
                return maps;
            }

            var muSiBizObjMapService = this.UserContext.Container.GetService<IMuSiBizObjMapService>();
            foreach (var fbizformid in fbizformids)
            {
                var bizForm = this.MetaModelService.LoadFormModel(this.UserContext, fbizformid);
                var bizObjMaps = muSiBizObjMapService.GetBizObjMaps(this.UserContext, bizForm, fextappid,
                    Enu_MuSiSyncDir.MuSiToCurrent, Enu_MuSiSyncTimePoint.SyncTimer);

                var map = Tuple.Create(bizForm, bizObjMaps);
                maps.Add(map);
            }

            return maps;
        }
    }
}
