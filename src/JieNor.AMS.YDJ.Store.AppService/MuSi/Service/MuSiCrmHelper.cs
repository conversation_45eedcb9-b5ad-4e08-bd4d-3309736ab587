using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Service
{
    /// <summary>
    /// 招商系统帮助类
    /// </summary>
    public class MuSiCrmHelper
    {
        /// <summary>
        /// 更新《招商经销商》的【售达方】
        /// 注：需要自行保存
        /// </summary>
        /// <param name="topCtx"></param>
        /// <param name="crmDistributors"></param>
        public static void UpdateAgent(UserContext topCtx, IEnumerable<DynamicObject> crmDistributors)
        {
            if (crmDistributors.IsNullOrEmpty()) return;

            // 更新售达方（经销商）

            var ids = crmDistributors.Select(s => Convert.ToString(s["id"])).ToList();

            var sql = $@"
select distinct a.fid as fagentid, a.fname as fagentname, ds.fdistributorid
from t_ms_deliver_series ds with(nolock) 
inner join t_bas_agent a with(nolock) on ds.fagentnumber=a.fnumber
where a.fforbidstatus='0' and ds.fdistributorid in ({ids.JoinEx(",", true)})";

            var dbService = topCtx.Container.GetService<IDBService>();

            var dynObjs = dbService.ExecuteDynamicObject(topCtx, sql);

            foreach (var crmDistributor in crmDistributors)
            {
                var id = Convert.ToString(crmDistributor["id"]);

                var match = dynObjs.Where(s => Convert.ToString(s["fdistributorid"]).EqualsIgnoreCase(id));
                if (!match.IsNullOrEmpty())
                {
                    crmDistributor["fagentid"] = match.Select(s => Convert.ToString(s["fagentid"])).JoinEx(",", false);
                    crmDistributor["fagentid_txt"] = match.Select(s => Convert.ToString(s["fagentname"])).JoinEx(",", false);
                }
            }
        }

        /// <summary>
        /// 更新《经销商》、《送达方》和《门店》的【招商经销商】
        /// </summary>
        /// <param name="topCtx"></param>
        /// <param name="crmDistributors"></param>
        public static void UpdateCrmDistributor(UserContext topCtx, IEnumerable<DynamicObject> crmDistributors)
        {
            if (crmDistributors.IsNullOrEmpty()) return;

            var agentIds = new HashSet<string>();

            foreach (var crmDistributor in crmDistributors)
            {
                if (crmDistributor["fagentid"].IsNullOrEmptyOrWhiteSpace()) continue;

                var ids = Convert.ToString(crmDistributor["fagentid"]).Trim().SplitKey(",");
                foreach (var id in ids)
                {
                    agentIds.Add(id);
                }
            }

            // 没有经销商，跳过后续处理
            if (agentIds.IsNullOrEmpty())
            {
                return;
            }

            var agentService = topCtx.Container.GetService<IAgentService>();
            var deliverService = topCtx.Container.GetService<IDeliverService>();
            var storeService = topCtx.Container.GetService<IStoreService>();

            // 更新经销商
            var agents = topCtx.LoadBizDataById("bas_agent", agentIds);
            if (agents.Any())
            {
                agentService.UpdateCrmDistributor(topCtx, agents);
                topCtx.SaveBizData("bas_agent", agents);
            }

            // 更新送达方
            var delivers = topCtx.LoadBizDataByNo("bas_deliver", "fagentid", agentIds);
            if (delivers.Any())
            {
                deliverService.UpdateCrmDistributor(topCtx, delivers);
                topCtx.SaveBizData("bas_deliver", delivers);
            }

            // 更新门店
            var stores = topCtx.LoadBizDataByNo("bas_store", "fagentid", agentIds);
            if (stores.Any())
            {
                storeService.UpdateCrmDistributor(topCtx, stores);
                topCtx.SaveBizData("bas_store", stores);
            }
        }
    }
}
