using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Store.AppService.Clients.Ewc;
using JieNor.AMS.YDJ.Store.AppService.MuSi.DataTransferObject;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi
{
    public class MuSiHelper
    {
        private static readonly string _successFlag = "pkids";

        /// <summary>
        /// 设置业务成功对象
        /// </summary>
        /// <param name="result"></param>
        /// <param name="pkids"></param>
        public static void SetSuccessPkIds(IOperationResult result, IEnumerable<string> pkids)
        {
            pkids = pkids?.Where(s => !s.IsNullOrEmptyOrWhiteSpace())?.Distinct();

            result.SimpleData[_successFlag] = pkids.ToJson();
        }

        /// <summary>
        /// 获取业务成功对象
        /// </summary>
        /// <param name="result"></param>
        /// <returns></returns>
        public static List<string> GetSuccessPkIds(IOperationResult result)
        {
            return result.SimpleData[_successFlag].FromJson<List<string>>() ?? new List<string>();
        }

        /// <summary>
        /// 获取超时对象（不再获取）
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataType"></param>
        /// <returns></returns>
        public static List<string> GetTimeoutExternalIds(UserContext userCtx, string dataType)
        {
            var metaModelService = userCtx.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(userCtx, "ms_pulldata");

            var dt = htmlForm.GetDynamicObjectType(userCtx);
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, dt);

            string sql = $@"
select ftranid from t_ms_pulldata with(nolock) 
where fcreatedate >= @today and fdatatype=@datatype
group by ftranid
having COUNT(1)>=3";

            var dbService = userCtx.Container.GetService<IDBService>();
            var dynObjs = dbService.ExecuteDynamicObject(userCtx, sql, new[]
              {
                new SqlParam("@today", DbType.DateTime, BeiJingTime.Now.Date),
                new SqlParam("@datatype", DbType.String, dataType),
            });

            return dynObjs.Select(s => Convert.ToString(s["ftranid"])).ToList();
        }

        /// <summary>
        /// 合并数据
        /// </summary>
        /// <param name="resp"></param>
        public static void MergeData(FromMuSiSyncResponse resp)
        {
            if (!resp.Success || resp.Data.List.IsNullOrEmpty()) return;

            string fileDir = PathUtils.GetStartupPath() + @"\musidata";
            if (!Directory.Exists(fileDir))
            {
                Directory.CreateDirectory(fileDir);
            }

            var filePath = Path.Combine(fileDir, resp.Data.Data_Type.ToLower() + ".json");
            using (var fs = new FileStream(filePath, FileMode.OpenOrCreate))
            {
                StreamReader reader = new StreamReader(fs);
                string json = reader.ReadToEnd();

                JArray array = new JArray();

                try
                {
                    array = JArray.Parse(json);
                }
                catch (Exception)
                {
                }

                foreach (var item in resp.Data.List)
                {
                    if (!array.Any(s => s.GetJsonValue("id", "").EqualsIgnoreCase(item.GetJsonValue("id", ""))))
                    {
                        array.Add(item);
                    }
                }

                //fs.SetLength(0);
                fs.Seek(0, SeekOrigin.Begin);

                byte[] content = Encoding.UTF8.GetBytes(array.ToJson());

                fs.Write(content, 0, content.Length);

                //StreamWriter writer = new StreamWriter(fs);
                //writer.Write(array.ToJson());
            }
        }

        /// <summary>
        /// 写入数据
        /// </summary>
        /// <param name="resp"></param>
        public static void WriteData(UserContext userCtx, FromMuSiSyncResponse resp)
        {
            if (!resp.Success || resp.Data.List.IsNullOrEmpty()) return;

            var metaModelService = userCtx.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(userCtx, "ms_pulldata");

            var dt = htmlForm.GetDynamicObjectType(userCtx);
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, dt);

            List<DynamicObject> dynObjs = new List<DynamicObject>();

            foreach (var item in resp.Data.List)
            {
                var dynObj = (DynamicObject)dt.CreateInstance();
                string json = item.ToJson();
                dynObj["fjson"] = json;
                dynObj["fmd5"] = SecurityUtil.HashString(json);
                dynObj["fdatatype"] = resp.Data.Data_Type;
                dynObj["ftranid"] = item.GetJsonValue("id", "");
                dynObj["fcreatedate"] = BeiJingTime.Now;

                dynObjs.Add(dynObj);
            }

            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
            var result = gateway.InvokeBillOperation(userCtx, htmlForm.Id, dynObjs, "draft", new Dictionary<string, object>());
        }
    }
}
