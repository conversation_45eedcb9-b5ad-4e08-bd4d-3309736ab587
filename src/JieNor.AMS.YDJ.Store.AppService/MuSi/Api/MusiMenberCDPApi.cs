using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.Store.AppService.Clients.Ewc;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Client;
using JieNor.AMS.YDJ.Store.AppService.MuSi.DTO;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Response;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.SystemIntegration;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Api
{
    /// <summary>
    /// 慕思会员系统CDPApi
    /// </summary>
    public class MusiMenberCDPApi
    {

        /// <summary>
        /// 获取系统URL
        /// </summary>
        /// <param name="userCtx"></param>
        public static Dictionary<string, string> GetUrl(UserContext userCtx, Dictionary<string, string> requestData,string resquestFormId)
        {
            var topCtx = userCtx.CreateTopOrgDBContext();
            var fanalysisurl = string.Empty;
            if (resquestFormId.Equals("ms_membermanagementandanalysis"))
            {
                fanalysisurl = topCtx.Container.GetService<ISystemProfile>()
                .GetSystemParameter(topCtx, "ms_memberparam", "fanalysisurl", "");

                if (fanalysisurl.IsNullOrEmptyOrWhiteSpace())
                {
                    throw new BusinessException("未配置会员管理与分析URL");
                }
            }
            else if (resquestFormId.Equals("ms_valueaddedservicereport"))
            {
                fanalysisurl = topCtx.Container.GetService<ISystemProfile>()
                .GetSystemParameter(topCtx, "ms_memberparam", "fservicereporturl", "");

                if (fanalysisurl.IsNullOrEmptyOrWhiteSpace())
                {
                    throw new BusinessException("未配置增值服务报表URL");
                }
            }

            if (fanalysisurl.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("未配置会员CDP相关URL");
            }

            var listdata = GetListData(userCtx, requestData);

            if (listdata["success"].Equals("0"))
            {
                return listdata;
            }

            //fanalysisurl中除way外其他均一致，way需要根据code和token来判断，实际用哪种way（token方式 or code方式）
            //注：code和token是不可能同时存在值，code有值，token就是null，token有值，code就是null
            if (!listdata["access_token"].IsNullOrEmptyOrWhiteSpace())
            {
                var token_way = listdata["token_way"];
                token_way = token_way.Replace("{access_token}", listdata["access_token"]);
                token_way = token_way.Replace("{token_type}", listdata["token_type"]);

                fanalysisurl = fanalysisurl.Replace("way", token_way);
            }

            if (!listdata["code"].IsNullOrEmptyOrWhiteSpace())
            {
                var code_way = listdata["code_way"];
                code_way = code_way.Replace("{code}", listdata["code"]);

                fanalysisurl = fanalysisurl.Replace("way", code_way);
            }

            var timestamp = BeiJingTime.Now.TimestampFromBeiJingTime().ToString();
            fanalysisurl = fanalysisurl.Replace("{timestamp}", timestamp);
            listdata["message"] = $"{fanalysisurl}";

            return listdata;
        }

        /// <summary>
        /// 获取参数
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private static Dictionary<string, string>  GetListData(UserContext userCtx, Dictionary<string, string> requestData)
        {
            var listdata = new Dictionary<string, string>();

            var extApp = GetExtApp(userCtx);
            if (extApp == null) return listdata;

            var client = new MuSiMemberAanalysisClient(userCtx, extApp);

            listdata.Add("token_type", client.TargetServer.Headers["token_type"]);
            listdata.Add("timestamp", client.TargetServer.Headers["X-Caller-Timestamp"]);

            listdata.Add("access_token", null);
            listdata.Add("code", null);

            listdata.Add("success", "1");
            listdata.Add("message", "");

            listdata.Add("token_way", null);
            listdata.Add("code_way", null);

            //获取Token的url
            var gettoken_url = $"/{client.api_context_path}/{client.api_version}{client.api_request_path}";

            var responseData = client.Send<JObject>(gettoken_url, requestData, Enu_HttpMethod.Post, Enu_ContentType.Json);

            if (Convert.ToBoolean(responseData["success"]))
            {
                var data = responseData["data"];

                //code和token是不可能同时存在值，code有值，token就是null，，token有值，code就是null
                var token = Convert.ToString(data["token"]).Trim();
                var code = Convert.ToString(data["code"]).Trim();

                //token与code均为null
                if (token.IsNullOrEmptyOrWhiteSpace() && code.IsNullOrEmptyOrWhiteSpace())
                {
                    listdata["success"] = "0";
                    listdata["message"] = "获取token接口返回data参数中，token与code均为null！";
                }

                //token与code均不为null
                if (!token.IsNullOrEmptyOrWhiteSpace() && !code.IsNullOrEmptyOrWhiteSpace())
                {
                    listdata["success"] = "0";
                    listdata["message"] = "获取token接口返回data参数中，token与code均不为null！";
                }

                //token不为null,code为null
                if (!token.IsNullOrEmptyOrWhiteSpace() && code.IsNullOrEmptyOrWhiteSpace())
                {
                    client.api_token = token;
                    client.TargetServer.Headers["access_token"] = client.api_token;

                    listdata["token_way"] = Convert.ToString(client.api_token_way).Trim();
                    listdata["access_token"] = client.TargetServer.Headers["access_token"];
                    listdata["success"] = "1";
                }

                //token为null,code不为null
                if (token.IsNullOrEmptyOrWhiteSpace() && !code.IsNullOrEmptyOrWhiteSpace())
                {
                    client.api_code = code;
                    client.TargetServer.Headers["code"] = client.api_code;

                    listdata["code_way"] = Convert.ToString(client.api_code_way).Trim();
                    listdata["code"] = client.TargetServer.Headers["code"];
                    listdata["success"] = "1";
                }
            }
            else
            {
                listdata["success"] = "0";
                listdata["message"] = Convert.ToString(responseData["message"]).Trim();
            }

            return listdata;

        }

        /// <summary>
        /// 获取外部应用
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private static DynamicObject GetExtApp(UserContext userCtx)
        {
            var sysProfileService = userCtx.Container.GetService<ISystemProfile>();

            var topCtx = userCtx.CreateTopOrgDBContext();

            // 获取慕思会员CDP系统
            var extAppId = sysProfileService.GetSystemParameter<string>(topCtx, "si_datasyncparam", "fmusicdpappid", "");

            if (extAppId.IsNullOrEmptyOrWhiteSpace())
            {
                var formMeta = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "si_datasyncparam");

                throw new BusinessException($"未设置【{formMeta.Caption}】的【{formMeta.GetField("fmusicdpappid")?.Caption}】");
            }

            var extApp = topCtx.LoadBizDataById("sys_externalapp", extAppId);
            return extApp;
        }
    }
}
