using JieNor.AMS.YDJ.Core.DataEntity.Finance;
using JieNor.AMS.YDJ.Core.Interface.Finance;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService
{
    /// <summary>
    /// 财务基础服务
    /// </summary>
    [InjectService]
    public class FinanceBaseService : IFinanceBaseService
    {
        /// <summary>
        /// 模型服务
        /// </summary>
        [InjectProperty]
        protected IMetaModelService MetaModelService { get; set; }

        /// <summary>
        /// 数据库写服务
        /// </summary>
        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        /// <summary>
        /// 数据库读服务
        /// </summary>
        [InjectProperty]
        protected IDBService DBService { get; set; }

        /// <summary>
        /// 财务反关账服务
        /// 作者：zpf
        /// 日期：2022-07-27
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="option">操作项</param>
        /// <returns></returns> 
        public IOperationResult AntiClosing(UserContext userCtx, OperateOption option)
        {
            var result = userCtx.Container.GetService<IOperationResult>();
            //最后一次关账时间
            DateTime? dtLatestCloseDate = this.GetLatestFinanceCloseDate(userCtx);

            if (!dtLatestCloseDate.HasValue)
            {
                throw new BusinessException("系统已没有关账记录，无法进行反关账，若要重新初始化系统，请在初始化控制中进行重新初始化！");
            }


            var dm = userCtx.Container.GetService<IDataManager>();
            var purchaseOrderForm = this.MetaModelService.LoadFormModel(userCtx, "ydj_closedaccountslist");
            dm.InitDbContext(userCtx, purchaseOrderForm.GetDynamicObjectType(userCtx));

            var strCheckSql = @"/*dialect*/ SELECT * FROM t_ydj_closedaccountslist  WHERE  fmainorgid=@currentCompanyId ";

            var lstParams = new List<SqlParam>()
            {
                new SqlParam("currentCompanyId", System.Data.DbType.String, userCtx.Company)
            };
            DynamicObjectCollection dynamicObjects = this.DBService.ExecuteDynamicObject(userCtx, strCheckSql, lstParams);

            List<DynamicObject> closedList = dynamicObjects.OrderByDescending(a => a["foptime"]).ToList();
            DateTime? flastclosuredate = DateTime.Now;
            string closeId = "";
            bool close = false;
            int closeTimes = 0;

            for (int i = 0; i < closedList.Count; i++)
            {
                if (string.IsNullOrWhiteSpace(Convert.ToString(closedList[i]["funcloseid"])))
                {
                    if (closedList[i]["fbusinessoperations"].ToString() == "1")
                    {
                        closeId = Convert.ToString(closedList[i]["fid"]);
                        if (i == closedList.Count - 1 || closedList[i + 1]["fbusinessoperations"].ToString() == "2")
                        {
                            flastclosuredate = null;
                            break;
                        }
                        flastclosuredate = Convert.ToDateTime(closedList[i + 1]["flastclosuredate"]);
                        break;
                    }
                }
            }

            //创建关账记录
            StringBuilder sbRecordInsert = new StringBuilder();
            sbRecordInsert.Append("insert into t_ydj_closedaccountslist (fid,fformid,fagentid,flastclosuredate,fbusinessoperations,foptime,fopuserid,fmainorgid)");
            string guid = Guid.NewGuid().ToString();
            StringBuilder sbRecordSelect = new StringBuilder();
            if (flastclosuredate == null)
            {
                sbRecordSelect.Append($"select '{guid}' as fid, 't_ydj_closedaccountslist' as fformid,'{userCtx.Company}',null as flastclosuredate, '2' as fbusinessoperations,'{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff")}' as foptime,'{userCtx.UserId}','{userCtx.Company}'");
            }
            else
                sbRecordSelect.Append($"select '{guid}' as fid, 't_ydj_closedaccountslist' as fformid,'{userCtx.Company}','{ flastclosuredate }' as flastclosuredate, '2' as fbusinessoperations,'{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff")}' as foptime,'{userCtx.UserId}','{userCtx.Company}'");

            var strSql = $@"{sbRecordInsert.ToString()} 
                            {sbRecordSelect.ToString()}";
            string updSql = $"update t_ydj_closedaccountslist set funcloseid='{guid}' where fid='{closeId}'";
            try
            {

                var recordCount = this.DBServiceEx.Execute(userCtx, strSql, lstParams);
                var updCount = this.DBServiceEx.Execute(userCtx, updSql);
            }
            catch (Exception ex)
            {
                throw new BusinessException($"财务反关账失败，关账记录创建异常：{ex.ToString()}");
                //result.ComplexMessage.ErrorMessages.Add($"财务反关账失败，关账记录创建异常：{ex.ToString()}");
                //return result;
            }
            result.ComplexMessage.SuccessMessages.Add($"财务反关账成功");
            return result;
        }

        /// <summary>
        /// 财务关账服务
        /// 作者：zpf
        /// 日期：2022-07-27
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="dtCloseDate">关账日期</param>
        /// <param name="option"操作项></param>
        /// <returns></returns>
        public IOperationResult Closing(UserContext userCtx, DateTime dtCloseDate, OperateOption option)
        {
            var result = userCtx.Container.GetService<IOperationResult>();

            var strCheckSql = @"/*dialect*/ SELECT flastclosuredate FROM (SELECT ROW_NUMBER() OVER(ORDER BY foptime desc) AS indexs,* FROM t_ydj_closedaccountslist WHERE fbusinessoperations = 1 and ISNULL(funcloseid,'')='' and fmainorgid=@currentCompanyId) AS t WHERE t.indexs = 1 AND t.fbusinessoperations = 1 and t.fmainorgid=@currentCompanyId and flastclosuredate>=@closeDate";

            var lstParams = new List<SqlParam>()
            {
                new SqlParam("currentCompanyId", System.Data.DbType.String, userCtx.Company),
                new SqlParam("closeDate", System.Data.DbType.DateTime, dtCloseDate)
            };

            using (var reader = this.DBService.ExecuteReader(userCtx, strCheckSql, lstParams))
            {
                if (reader.Read())
                {
                    throw new BusinessException($"关账日期不得小于最近一次关账日期且不得小于系统结束初始化日期：{reader[0]}");
                }
            }

            //检查库存单据的合法性
            this.CheckFinanceData(userCtx, dtCloseDate);

            //创建关账记录
            StringBuilder sbRecordInsert = new StringBuilder();
            sbRecordInsert.Append("insert into t_ydj_closedaccountslist (fid,fformid,fagentid,flastclosuredate,fbusinessoperations,foptime,fopuserid,fmainorgid)");

            StringBuilder sbRecordSelect = new StringBuilder();
            sbRecordSelect.Append($"select newid() as fid, 't_ydj_closedaccountslist' as fformid,'{userCtx.Company}','{dtCloseDate.ToString("yyyy-MM-dd")}' as flastclosuredate, '1' as fbusinessoperations,'{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}' as foptime,'{userCtx.UserId}','{userCtx.Company}'");

            var strSql = $@"{sbRecordInsert.ToString()} 
                            {sbRecordSelect.ToString()}";

            try
            {
                var recordCount = this.DBServiceEx.Execute(userCtx, strSql, lstParams);

            }
            catch (Exception ex)
            {
                result.ComplexMessage.SuccessMessages.Add($"财务关账成功，关账记录创建异常：{ex.ToString()}");
            }
            result.ComplexMessage.SuccessMessages.Add($"财务关账成功");
            return result;
        }

        /// <summary>
        /// 获取最近关账日期
        /// 作者：zpf
        /// 日期：2022-07-27
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dtRefDate"></param>
        /// <returns>返回参考日期前的最近一次关账日期</returns>
        public DateTime? GetLatestFinanceCloseDate(UserContext userCtx, DateTime? dtRefDate = null)
        {
            DateTime? dtLatestCloseDate = null;
            var strCheckSql = @"/*dialect*/ SELECT * FROM (SELECT ROW_NUMBER() OVER(ORDER BY foptime desc) AS indexs,* FROM t_ydj_closedaccountslist where fmainorgid=@currentCompanyId and ISNULL(funcloseid,'')='' and fbusinessoperations=1 ) AS t WHERE t.indexs =1  and t.fmainorgid=@currentCompanyId";

            var lstParams = new List<SqlParam>()
            {
                new SqlParam("currentCompanyId", System.Data.DbType.String, userCtx.Company)
            };

            if (dtRefDate.HasValue)
            {
                strCheckSql += "and t.flastclosuredate<=@refDate";
                lstParams.Add(new SqlParam("refDate", System.Data.DbType.DateTime, dtRefDate));
            }
            var reader = this.DBService.ExecuteDynamicObject(userCtx, strCheckSql, lstParams);
            bool flag = false;
            int index = 0;
            foreach (var item in reader)
            {
                if (item["fbusinessoperations"].ToString() == "1")
                {
                    if (index == 0 && !flag)
                    {
                        dtLatestCloseDate = Convert.ToDateTime(item["flastclosuredate"]);
                        break;
                    }
                    if (!flag)
                    {
                        dtLatestCloseDate = Convert.ToDateTime(item["flastclosuredate"]);
                    }
                    if (flag == true && index >= 2)
                    {
                        dtLatestCloseDate = Convert.ToDateTime(item["flastclosuredate"]);
                    }
                    index++;
                }
                else if (item["fbusinessoperations"].ToString() == "2")
                {
                    if (flag && index > 0)
                    {
                        break;
                    }
                    flag = true;
                    index++;
                    continue;
                }
            }
            //using (var reader = this.DBService.ExecuteDynamicObject(userCtx, strCheckSql, lstParams))
            //{
            //    foreach (var item in reader)
            //    {

            //    }
            //    if (reader.Read())
            //    {
            //        if (!(reader[0] is DBNull))
            //        {
            //            dtLatestCloseDate = Convert.ToDateTime(reader[0]);
            //        }
            //    }
            //}
            return dtLatestCloseDate;
        }

        #region 操作相关函数

        /// <summary>
        /// 检查财务相关单据的数据合法性
        /// 作者：zpf
        /// 日期：2022-07-29
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="dtCloseDate">本次关账时间</param>
        private void CheckFinanceData(UserContext userCtx, DateTime dtCloseDate)
        {
            var result = userCtx.Container.GetService<IOperationResult>();

            StringBuilder sbCheckBillStatusSql = new StringBuilder();
            List<SqlParam> lstPara = new List<SqlParam>()
            {
                new SqlParam("closeDate", System.Data.DbType.DateTime, dtCloseDate)
            };
            var dctFormMetaCache = new Dictionary<string, HtmlForm>();

            //获取财务管理参数中关账校验配置
            var profileService = userCtx.Container.GetService<ISystemProfile>();
            var checkoutentrys = profileService.GetSystemParameter<DynamicObjectCollection>(userCtx, "sal_dealersetup", "fcheckoutentry");

            if (checkoutentrys.IsNullOrEmptyOrWhiteSpace())
            {
                //throw new BusinessException("财务管理参数缺失，请检关账校验配置后再进行关账！");
                return;
            }

            var financeBillFormIds = new List<string>();
            try
            {
                financeBillFormIds = checkoutentrys.Select(t => Convert.ToString(t["ferrortypedescription"]).Split('|')[1]).Distinct().ToList();
            }
            catch (Exception ex)
            {
                //throw new BusinessException("财务管理参数配置异常，请检关账校验配置后再进行关账操作！");
                return;
            }

            foreach (var financeBillFormId in financeBillFormIds)
            {
                var checkoutentry = checkoutentrys.FirstOrDefault(t => Convert.ToString(t["ferrortypedescription"]).Contains(financeBillFormId));
                if (!checkoutentry.IsNullOrEmptyOrWhiteSpace() && !checkoutentry.DynamicObjectType.Properties.ContainsKey("fenable"))
                {
                    //throw new BusinessException("财务管理参数缺失，请检关账校验配置后再进行关账！");
                    continue;
                }
                if (!checkoutentry.IsNullOrEmptyOrWhiteSpace() && Convert.ToBoolean(checkoutentry["fenable"]))
                {
                    if (sbCheckBillStatusSql.Length > 0)
                    {
                        sbCheckBillStatusSql.AppendLine(" union all ");
                    }
                    var formMeta = this.MetaModelService.LoadFormModel(userCtx, financeBillFormId);
                    dctFormMetaCache[formMeta.Id] = formMeta;
                    var numberField = formMeta.GetNumberField();
                    SqlBuilderParameter sqlCheckPara = new SqlBuilderParameter(userCtx, formMeta);
                    sqlCheckPara.SelectedFieldKeys.AddRange(new string[]
                    {
                    $"'{financeBillFormId}' as fformid",
                    $"{numberField.Id} as fbillno",
                    });
                    sqlCheckPara.PageCount = -1;
                    sqlCheckPara.PageIndex = -1;
                    sqlCheckPara.QueryUserFieldOnly = true;
                    sqlCheckPara.NoIsolation = false;
                    sqlCheckPara.MergeBillHeadField = false;
                    sqlCheckPara.NoColorSetting = true;
                    sqlCheckPara.IsDistinct = true;
                    var fdatenames = new List<string>() { "ydj_collectreceipt", "ydj_payreceipt", "ste_registfee" };
                    var fdatename = fdatenames.Contains(financeBillFormId) ? "fregistdate" : "fdate";
                    sqlCheckPara.FilterString = $"{formMeta.BizStatusFldKey}<>'E' and ({formMeta.CancelStatusFldKey}='0' or {formMeta.CancelStatusFldKey}='') and {fdatename}<=@closeDate";

                    QueryObject checkQueryObj = QueryService.BuilQueryObject(sqlCheckPara);
                    sbCheckBillStatusSql.AppendLine(checkQueryObj.SqlNoPage);
                    foreach (var sqlPara in sqlCheckPara.DynamicParams)
                    {
                        var isExistPara = lstPara.FirstOrDefault(o => o.Name.EqualsIgnoreCase(sqlPara.Name)) != null;
                        if (!isExistPara)
                        {
                            lstPara.Add(sqlPara);
                        }
                    }
                }
            }

            if (sbCheckBillStatusSql.Length > 0)
            {
                using (var reader = this.DBService.ExecuteReader(userCtx, sbCheckBillStatusSql.ToString().Replace("Order By  t0.fbillno  Desc", ""), lstPara))
                {
                    while (reader.Read())
                    {
                        HtmlForm hForm;

                        var formId = reader["fformid"] as string;
                        var billNo = reader["fbillno"] as string;

                        dctFormMetaCache.TryGetValue(formId, out hForm);

                        result.ComplexMessage.ErrorMessages.Add($"编号为（{billNo}）的【{hForm.Caption}】未审核，不能进行关账操作！");
                    }
                }
            }

            if (result.ComplexMessage.HasMessage)
            {
                throw new BusinessException("财务关账失败，存在未审核的财务单据，请处理后再进行关账！");
            }
        }

        #endregion
    }
}
