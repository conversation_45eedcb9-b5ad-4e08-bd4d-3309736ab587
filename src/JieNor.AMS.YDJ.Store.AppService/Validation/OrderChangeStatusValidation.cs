using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.DataTransferObject;

namespace JieNor.AMS.YDJ.Store.AppService.Validation
{
    /// <summary>
    /// 销售合同变更状态校验器
    /// 禅道任务：http://dmp.jienor.com:81/zentao/task-view-32524.html
    /// 1.2.2 需求中要求的校验逻辑。
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    [ServiceMetaAttribute("validationid", YDJHtmlElementType.HtmlValidator_OrderChangeStatusValidation)]
    public class OrderChangeStatusValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();

            //【取消变更】操作时，校验商品明细的非标审批状态
            if (operationNo.EqualsIgnoreCase("unchange"))
            {
                this.ValidateUnStdTypeStatus(formInfo, dataEntities, result);
                return result;
            }

            //后续可对其它的操作做校验...


            return result;
        }

        /// <summary>
        /// 校验商品明细的非标审批状态
        /// </summary>
        private void ValidateUnStdTypeStatus(HtmlForm formInfo, DynamicObject[] dataEntities, ValidationResult result)
        {
            foreach (var dataEntitie in dataEntities)
            {
                //【变更状态】=“变更中”或“变更已提交”
                var changeStatus = Convert.ToString(dataEntitie["fchangestatus"]).Trim();
                if (changeStatus == "1" || changeStatus == "3")
                {
                    //商品行的【非标审批状态】是否包含“待审批”
                    var exists = (dataEntitie["fentry"] as DynamicObjectCollection)
                        ?.FirstOrDefault(o => Convert.ToString(o["funstdtypestatus"]).EqualsIgnoreCase("02"));
                    if (exists != null)
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"{formInfo.Caption}【{dataEntitie["fbillno"]}】有商品非标审批中，需完成审批才能取消变更！",
                            DataEntity = dataEntitie
                        });
                    }
                }
            }
        }
    }
}