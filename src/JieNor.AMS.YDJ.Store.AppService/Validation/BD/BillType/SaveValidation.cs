using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Validation.BD.BillType
{
    /// <summary>
    /// 单据类型：保存校验器
    /// </summary>
    public class SaveValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            var sql = $"SELECT fnumber,fname,fbizobject FROM T_BD_BILLTYPE WITH(NOLOCK) WHERE fmainorgid='{this.Context.Company}' AND fforbidstatus='0'";
            var allBillTypeObjs = this.DBService.ExecuteDynamicObject(this.Context, sql);

            ValidationResult result = new ValidationResult();
            foreach (var data in dataEntities)
            {
                var fnumber = Convert.ToString(data["fnumber"]);
                var fname = Convert.ToString(data["fname"]).Trim();
                var fbizobject = Convert.ToString(data["fbizobject"]);
                if (allBillTypeObjs.Any(t => !Convert.ToString(t["fnumber"]).EqualsIgnoreCase(fnumber) 
                                            && Convert.ToString(t["fname"]).Trim().EqualsIgnoreCase(fname) 
                                            && Convert.ToString(t["fbizobject"]).EqualsIgnoreCase(fbizobject)))
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"【{formInfo.Caption}】已存在名称为【{fname}】的单据类型，请检查！",
                        DataEntity = data,
                    });
                }
            }
            return result;
        }
    }
}
