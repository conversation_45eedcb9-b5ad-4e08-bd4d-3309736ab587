using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService
{
    /// <summary>
    /// 表关联时使用的特定索引
    /// </summary>
    [InjectService]
    public class LeftJoinWithIndex : ILeftJoinWithIndex
    {

        /// <summary>
        /// 表关联时使用的特定索引
        /// </summary>
        /// <param name="sealInfo"></param>
        public  string WtihIndexName(string tableName)
        {
            string withIndex = "";
            if (tableName.EqualsIgnoreCase("t_bd_material"))
            {
                //withIndex = "idx_BD_MATERIAL_idname";
            }

            return withIndex;
        }


    }
}