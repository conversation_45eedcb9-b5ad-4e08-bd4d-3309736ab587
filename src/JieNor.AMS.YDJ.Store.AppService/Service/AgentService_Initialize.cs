using JieNor.AMS.YDJ.Core.Utils;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Auth;
using JieNor.AMS.YDJ.Store.AppService.Plugin.MP;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Service
{
    /// <summary>
    /// 经销商服务实现（初始化）
    /// </summary>
    public partial class AgentService
    {
        static Dictionary<string, string> _lstRoleNos = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "YHJS01","1老板" },
            { "YHJS02","2跟单" },
            { "YHJS03","3店长" },
            { "YHJS04","4导购" },
            { "YHJS05","5财务" },
            { "YHJS06","6仓管" },
            { "YHJS07","7管理员" },
            { "YHJS08","8服务主管" },
            { "YHJS09","9增值服务专员" },
        };

        static Dictionary<string, string> _lstDKHRoleNos = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "DKHYHJS01","1大客户渠道老板" },
            { "DKHYHJS02","2大客户渠道跟单" },
            { "DKHYHJS03","3大客户渠道店长" },
            { "DKHYHJS04","4大客户渠道导购" },
            { "DKHYHJS05","5大客户渠道财务" },
            { "DKHYHJS06","6大客户渠道仓管" },
            { "DKHYHJS07","7大客户渠道管理员" },
            { "DKHYHJS08","8大客户渠道服务主管" },
            { "DKHYHJS09","9大客户渠道增值服务专员" }
        };

        /// <summary>
        /// 审核后的初始化（角色预设、运维人员预设）
        /// </summary>
        public IOperationResult Initialize(UserContext userCtx, IEnumerable<DynamicObject> agents)
        {
            IOperationResult result = new OperationResult();
            result.IsSuccess = true;

            if (agents == null || !agents.Any()) return result;

            Dictionary<UserContext, List<DynamicObject>> taskInfo = new Dictionary<UserContext, List<DynamicObject>>();
            List<Tuple<string, List<HtmlPermItem>, string, string>> allMdls = new List<Tuple<string, List<HtmlPermItem>, string, string>>();

            var logger = userCtx.Container.GetService<ILogServiceEx>();

            try
            {
                using (var scope = userCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
                {
                    //预设角色
                    //PreAgentRoleInfo(userCtx, agents, out taskInfo, out allMdls);

                    //预设角色（添加大客户渠道角色）
                    PreAgentRoleAndDKHInfo(userCtx, agents, out taskInfo, out allMdls);

                    DebugUtil.WriteLogToFile("预设角色完成", "经销商审核初始化");
                    result.ComplexMessage.SuccessMessages.Add("预设角色完成");

                    //系统运维角色用户更新
                    var topCtx = userCtx.CreateTopOrgDBContext();
                    DataAuthHelp.AddOrUpdateDevOpsUserInfo(topCtx, null, false, agents.Select(s => s["id"].ToString()));

                    DebugUtil.WriteLogToFile("系统运维角色用户更新完成", "经销商审核初始化");
                    result.ComplexMessage.SuccessMessages.Add("系统运维角色用户更新完成");

                    //预设经销商企业的角色权限
                    var permService = userCtx.Container.GetService<IPermissionService>();
                    //更新系统的运维人员信息,预设经销商企业的角色权限
                    //PreAgentRoleInfo(e);--不能放此避免多次反审核审核后多次异步造成角色添加重复
                    foreach (var item in taskInfo)
                    {
                        var agent = agents.FirstOrDefault(s =>
                            Convert.ToString(s["id"]).EqualsIgnoreCase(item.Key.Company));

                        foreach (var roleObj in item.Value)
                        {
                            var roleNo = Convert.ToString(roleObj["fnumber"]);
                            var roleACLDetail = GetRoleAccess(item.Key, allMdls, roleObj, roleNo);
                            permService.SaveRolePermission(item.Key, roleACLDetail);

                            SaveRoleMPMenuAccess(item.Key, roleObj, roleNo);

                            DebugUtil.WriteLogToFile($"预设角色权限完成：{agent?["fnumber"]}_{roleNo}", "经销商审核初始化");
                            result.ComplexMessage.SuccessMessages.Add($"预设角色权限完成：{agent?["fnumber"]}_{roleNo}");
                        }
                    }

                    UpdateBigCustomerAdminRoleUser(userCtx, agents.Where(p => Convert.ToBoolean(p["fcustomchannel"]) == true));
                    UpdateAgentAdminRoleUser(userCtx, agents.Where(p => Convert.ToBoolean(p["fcustomchannel"]) == false));


                    //修复数据：经销商用户都没有关联【经销商管理员】角色的问题，增加关联 
                    //DebugUtil.WriteLogToFile("修复数据：经销商用户都没有关联【经销商管理员】角色的问题，增加关联完成", "经销商审核初始化");
                    //result.ComplexMessage.SuccessMessages.Add($"修复数据：经销商用户都没有关联【经销商管理员】角色的问题，增加关联完成");

                    DebugUtil.WriteLogToFile("修复数据：经销商用户都没有关联【经销商管理员或者大客户渠道管理员】角色的问题，增加关联完成", "经销商审核初始化");
                    result.ComplexMessage.SuccessMessages.Add($"修复数据：经销商用户都没有关联【经销商管理员或者大客户渠道管理员】角色的问题，增加关联完成");
                    scope.Complete();
                }
                PreAgentPositionAndStorehouse(userCtx, agents);
            }
            catch (Exception ex)
            {
                logger.Error("经销商审核初始化失败", ex);

                DebugUtil.WriteLogToFile(ex.Message + Environment.NewLine + ex.StackTrace, "经销商审核初始化");

                throw;
            }

            return result;
        }

        /// <summary>
        /// 预设经销商企业的角色权限
        /// </summary>
        private void PreAgentRoleInfo(UserContext userCtx, IEnumerable<DynamicObject> dataEntities,
            out Dictionary<UserContext, List<DynamicObject>> taskInfo,
            out List<Tuple<string, List<HtmlPermItem>, string, string>> returnMdls)
        {
            taskInfo = new Dictionary<UserContext, List<DynamicObject>>();
            returnMdls = new List<Tuple<string, List<HtmlPermItem>, string, string>>();

            if (dataEntities == null || dataEntities.Count() == 0)
            {
                return;
            }

            //历史数据不需要管了，从2022-06-22开始，新建的经销商审核后自动生成相应角色及权限信息
            var ignoreDateTime = new DateTime(2022, 6, 22);
            var datas = dataEntities.Where(f => Convert.ToDateTime(f["fcreatedate"]) >= ignoreDateTime).ToList();
            if (datas.Count == 0)
            {
                return;
            }

            var metaModelService = userCtx.Container.GetService<IMetaModelService>();
            var dbService = userCtx.Container.GetService<IDBService>();
            var dbServiceEx = userCtx.Container.GetService<IDBServiceEx>();

            var roleMeta = metaModelService.LoadFormModel(userCtx, "sec_role");
            var orgMeta = metaModelService.LoadFormModel(userCtx, "bas_organization");
            var agentMeta = metaModelService.LoadFormModel(userCtx, "bas_agent");

            var roleMetaType = roleMeta.GetDynamicObjectType(userCtx);
            var preService = userCtx.Container.GetService<IPrepareSaveDataService>();

            var sql = @"select fmainorgid,fnumber,fname from {2} with(nolock) where (fnumber in ({0})  or fname in ({1}) )".Fmt(
                _lstRoleNos.Keys.JoinEx(",", true),
                _lstRoleNos.Values.JoinEx(",", true),
                roleMeta.BillHeadTableName);
            var existRoles = dbService.ExecuteDynamicObject(userCtx, sql);
            var grpRoles = existRoles?.GroupBy(f => Convert.ToString(f["fmainorgid"]))?.ToList();
            var permService = userCtx.Container.GetService<IPermissionService>();
            var allMdls = permService.GetBizObjPermitItem(userCtx);
            returnMdls = allMdls;

            var beSave = new List<DynamicObject>();

            foreach (var agent in datas)
            {
                var agentId = Convert.ToString(agent["Id"]);

                // 重试次数
                int retry = 5;
                bool isRegister = false;
                for (int i = 0; i < retry; i++)
                {
                    // 如果在ac库里存在企业，说明注册成功
                    sql = $"select top 1 fid from v_auth_company with(nolock) where fid = '{agentId}' ";

                    using (var reader = dbService.ExecuteReader(userCtx, sql))
                    {
                        if (reader.Read())
                        {
                            isRegister = true;
                        }
                    }

                    if (isRegister)
                    {
                        break;
                    }

                    //经销商还没注册，可能还未更新，等待几秒
                    System.Threading.Thread.Sleep(i * 500);
                }

                // 如果还没有注册，不做角色预设
                if (!isRegister)
                {
                    // 设置标识
                    agent["fisnotprerole"] = true;
                    beSave.Add(agent);
                    continue;
                }

                var agentRoles = grpRoles?.FirstOrDefault(f => f.Key == agentId)?.ToList();

                // 批量创建经销商预置角色
                var roleObjs = new List<DynamicObject>();
                foreach (var roleNo in _lstRoleNos)
                {
                    var agentRole = agentRoles?.FirstOrDefault(f =>
                        Convert.ToString(f["fnumber"]).EqualsIgnoreCase(roleNo.Key)
                        || Convert.ToString(f["fname"]).EqualsIgnoreCase(roleNo.Value));
                    if (agentRole == null)
                    {
                        var roleObj = roleMetaType.CreateInstance() as DynamicObject;
                        roleObj["fnumber"] = roleNo.Key;
                        roleObj["fname"] = roleNo.Value;
                        roleObj["fispreset"] = true;
                        if (roleNo.Key.EqualsIgnoreCase("YHJS07"))
                        {
                            roleObj["fisadmin"] = true;
                        }
                        roleObjs.Add(roleObj);
                    }
                }

                if (!roleObjs.Any() && Convert.ToBoolean(agent["fisnotprerole"]))
                {
                    // 设置标识
                    agent["fisnotprerole"] = false;
                    beSave.Add(agent);
                    continue;
                }

                // 经销商企业上下文
                var agentCtx = userCtx.CreateAgentDBContext(agentId);

                // 批量保存角色
                preService.PrepareDataEntity(agentCtx, roleMeta, roleObjs.ToArray(), OperateOption.Create());
                agentCtx.SaveBizData(roleMeta.Id, roleObjs);

                taskInfo.Add(agentCtx, roleObjs);
            }

            if (beSave.Any())
            {
                userCtx.SaveBizData(agentMeta.Id, beSave);
            }
        }


        /// <summary>
        /// 预设经销商企业的角色权限（普通经销商和大客户渠道经销商）
        /// </summary>
        private void PreAgentRoleAndDKHInfo(UserContext userCtx, IEnumerable<DynamicObject> dataEntities,
              out Dictionary<UserContext, List<DynamicObject>> taskInfo,
              out List<Tuple<string, List<HtmlPermItem>, string, string>> returnMdls)
        {
            taskInfo = new Dictionary<UserContext, List<DynamicObject>>();
            returnMdls = new List<Tuple<string, List<HtmlPermItem>, string, string>>();

            if (dataEntities == null || dataEntities.Count() == 0)
            {
                return;
            }

            //历史数据不需要管了，从2022-06-22开始，新建的经销商审核后自动生成相应角色及权限信息
            var ignoreDateTime = new DateTime(2022, 6, 22);
            var datas = dataEntities.Where(f => Convert.ToDateTime(f["fcreatedate"]) >= ignoreDateTime).ToList();
            if (datas.Count == 0)
            {
                return;
            }

            var metaModelService = userCtx.Container.GetService<IMetaModelService>();
            var dbService = userCtx.Container.GetService<IDBService>();
            var dbServiceEx = userCtx.Container.GetService<IDBServiceEx>();

            var roleMeta = metaModelService.LoadFormModel(userCtx, "sec_role");
            var orgMeta = metaModelService.LoadFormModel(userCtx, "bas_organization");
            var agentMeta = metaModelService.LoadFormModel(userCtx, "bas_agent");

            var roleMetaType = roleMeta.GetDynamicObjectType(userCtx);
            var preService = userCtx.Container.GetService<IPrepareSaveDataService>();

            // var mergedDictionary = _lstRoleNos
            //.Concat(_lstDKHRoleNos)
            //.ToDictionary(kvp => kvp.Key, kvp => kvp.Value, StringComparer.OrdinalIgnoreCase);

            var mergedDictionary = _lstRoleNos
           .Union(_lstDKHRoleNos)
           .ToDictionary(kvp => kvp.Key, kvp => kvp.Value, StringComparer.OrdinalIgnoreCase);


            var sql = @"select fmainorgid,fnumber,fname from {2} with(nolock) where (fnumber in ({0})  or fname in ({1}) )".Fmt(
                mergedDictionary.Keys.JoinEx(",", true),
                mergedDictionary.Values.JoinEx(",", true),
                roleMeta.BillHeadTableName);
            var existRoles = dbService.ExecuteDynamicObject(userCtx, sql);
            var grpRoles = existRoles?.GroupBy(f => Convert.ToString(f["fmainorgid"]))?.ToList();
            var permService = userCtx.Container.GetService<IPermissionService>();
            var allMdls = permService.GetBizObjPermitItem(userCtx);
            returnMdls = allMdls;

            var beSave = new List<DynamicObject>();

            foreach (var agent in datas)
            {
                var agentId = Convert.ToString(agent["Id"]);

                // 重试次数
                int retry = 5;
                bool isRegister = false;
                for (int i = 0; i < retry; i++)
                {
                    // 如果在ac库里存在企业，说明注册成功
                    sql = $"select top 1 fid from v_auth_company with(nolock) where fid = '{agentId}' ";

                    using (var reader = dbService.ExecuteReader(userCtx, sql))
                    {
                        if (reader.Read())
                        {
                            isRegister = true;
                        }
                    }

                    if (isRegister)
                    {
                        break;
                    }

                    //经销商还没注册，可能还未更新，等待几秒
                    System.Threading.Thread.Sleep(i * 500);
                }

                // 如果还没有注册，不做角色预设
                if (!isRegister)
                {
                    // 设置标识
                    agent["fisnotprerole"] = true;
                    beSave.Add(agent);
                    continue;
                }

                var agentRoles = grpRoles?.FirstOrDefault(f => f.Key == agentId)?.ToList();

                // 批量创建经销商预置角色
                var roleObjs = new List<DynamicObject>();

                // 如果是大客户渠道经销商则创建大客户渠道的角色，如果不是，则按照经销商的角色创建
                if (Convert.ToBoolean(Convert.ToString(agent["fcustomchannel"])))
                {
                    foreach (var roleNo in _lstDKHRoleNos)
                    {
                        var agentRole = agentRoles?.FirstOrDefault(f =>
                            Convert.ToString(f["fnumber"]).EqualsIgnoreCase(roleNo.Key)
                            || Convert.ToString(f["fname"]).EqualsIgnoreCase(roleNo.Value));
                        if (agentRole == null)
                        {
                            var roleObj = roleMetaType.CreateInstance() as DynamicObject;
                            roleObj["fnumber"] = roleNo.Key;
                            roleObj["fname"] = roleNo.Value;
                            roleObj["fispreset"] = true;
                            if (roleNo.Key.EqualsIgnoreCase("DKHYHJS07"))
                            {
                                roleObj["fisadmin"] = true;
                            }
                            roleObjs.Add(roleObj);
                        }
                    }
                }
                else
                {
                    foreach (var roleNo in _lstRoleNos)
                    {
                        var agentRole = agentRoles?.FirstOrDefault(f =>
                            Convert.ToString(f["fnumber"]).EqualsIgnoreCase(roleNo.Key)
                            || Convert.ToString(f["fname"]).EqualsIgnoreCase(roleNo.Value));
                        if (agentRole == null)
                        {
                            var roleObj = roleMetaType.CreateInstance() as DynamicObject;
                            roleObj["fnumber"] = roleNo.Key;
                            roleObj["fname"] = roleNo.Value;
                            roleObj["fispreset"] = true;
                            if (roleNo.Key.EqualsIgnoreCase("YHJS07"))
                            {
                                roleObj["fisadmin"] = true;
                            }
                            roleObjs.Add(roleObj);
                        }
                    }
                }



                if (!roleObjs.Any() && Convert.ToBoolean(agent["fisnotprerole"]))
                {
                    // 设置标识
                    agent["fisnotprerole"] = false;
                    beSave.Add(agent);
                    continue;
                }

                // 经销商企业上下文
                var agentCtx = userCtx.CreateAgentDBContext(agentId);

                // 批量保存角色
                preService.PrepareDataEntity(agentCtx, roleMeta, roleObjs.ToArray(), OperateOption.Create());
                agentCtx.SaveBizData(roleMeta.Id, roleObjs);

                taskInfo.Add(agentCtx, roleObjs);
            }

            if (beSave.Any())
            {
                userCtx.SaveBizData(agentMeta.Id, beSave);
            }

        }


        private void UpdateAgentAdminRoleUser(UserContext userCtx, IEnumerable<DynamicObject> agents)
        {
            if (agents == null || !agents.Any()) return;

            var agentIds = agents.Select(s => Convert.ToString(s["id"])).ToList();

            var sql = $@"
----修复数据：经销商用户都没有关联【经销商管理员】角色的问题，增加关联
insert into t_sec_roleuser(fid,fformid,froleid,fuserid)
select fuserid as fid,'sec_roleuser' as fformid,froleid ,fuserid
from 
(
    ---没有关联【经销商管理员】的
    select ( select fid from t_sec_role with(nolock) where fnumber = 'Admin_Agent' and fmainorgid='{userCtx.TopCompanyId}') as froleid,
        b.fname as forgname,b.fnumber as forgnumber,a.fid as fuserid,a.fname as fusername,b.fcreatedate 
    from t_sec_user a with(nolock) 
    inner join T_BAS_ORGANIZATION b  with(nolock) on a.fmainorgid = b.fid and b.forgtype ='4'
    inner join 
        (
            select min(fid) as fid,fmainorgid from t_sec_user with(nolock) 
            where fcreatorid ='sysadmin' and fmainorgid in ({agentIds.JoinEx(",", true)})
            group by fmainorgid 
        ) d on a.fmainorgid =d.fmainorgid and a.fid=d.fid and a.fisdevops='0'
    where  b.fid not in 
    (
        select forgid from 
        (
            select distinct c.fid as forgid,c.fname as forgname, b.fid as fuserid,a.froleid
            from t_sec_roleuser a with(nolock) 
            inner join t_sec_user b  with(nolock) on a.fuserid = b.fid 
            inner join T_BAS_ORGANIZATION c  with(nolock) on b.fmainorgid = c.fid
            where a.froleid in 
            ( 
                select fid from t_sec_role  with(nolock) 
                where fnumber = 'Admin_Agent' and fmainorgid='{userCtx.TopCompanyId}'
            ) and c.fid in ({agentIds.JoinEx(",", true)})
        ) x 
    )
) t";
            var svc = userCtx.Container.GetService<IDBServiceEx>();
            svc.Execute(userCtx, sql);
        }


        private RolePermitInfo GetRoleAccess(UserContext ctx,
            List<Tuple<string, List<HtmlPermItem>, string, string>> allMdls, DynamicObject role, string roleNo)
        {
            List<Tuple<string, List<string>>> funAuths = null;
            List<Tuple<string, List<FieldAuthInfo>>> fldAuths = null;
            List<Tuple<string, List<DataRowAuthInfo>>> dataRowAuths = null;
            switch (roleNo)
            {
                case "DKHYHJS01"://1大客户渠道老板
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_BigCustomerBoss();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_BigCustomerBoss();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_BigCustomerBoss();
                    break;
                case "YHJS01"://1老板
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_Boss();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_Boss();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_Boss();
                    break;
                case "DKHYHJS02"://2大客户渠道跟单
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_BigCustomerFlow();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_BigCustomerFlow();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_BigCustomerFlow();
                    break;
                case "YHJS02"://2跟单
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_Flow();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_Flow();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_Flow();
                    break;
                case "DKHYHJS03"://3大客户渠道店长
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_BigCustomerShopkeeper();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_BigCustomerShopkeeper();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_BigCustomerShopkeeper();
                    break;
                case "YHJS03"://3店长
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_Shopkeeper();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_Shopkeeper();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_Shopkeeper();
                    break;
                case "DKHYHJS04"://4大客户渠道导购
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_BigCustomerSales();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_BigCustomerSales();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_BigCustomerSales();
                    break;
                case "YHJS04"://4导购
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_Sales();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_Sales();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_Sales();
                    break;
                case "DKHYHJS05"://5大客户渠道财务
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_BigCustomerFin();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_BigCustomerFin();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_BigCustomerFin();
                    break;
                case "YHJS05"://5财务
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_Fin();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_Fin();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_Fin();
                    break;
                case "DKHYHJS06"://6大客户渠道仓管
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_BigCustomerStock();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_BigCustomerStock();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_BigCustomerStock();
                    break;
                case "YHJS06"://6仓管
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_Stock();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_Stock();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_Stock();
                    break;
                case "DKHYHJS07"://7大客户渠道管理员
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_BigCustomerAdmin();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_BigCustomerAdmin();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_BigCustomerAdmin();
                    break;
                case "YHJS07"://7管理员
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_Admin();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_Admin();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_Admin();
                    break;
                case "DKHYHJS08"://8大客户渠道服务主管
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_BigCustomerServiceManager();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_BigCustomerServiceManager();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_BigCustomerServiceManager();
                    break;
                case "YHJS08"://8服务主管
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_ServiceManager();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_ServiceManager();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_ServiceManager();
                    break;
                case "DKHYHJS09"://9大客户渠道增值服务专员
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_BigCustomerValueServiceCommissioner();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_BigCustomerValueServiceCommissioner();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_BigCustomerValueServiceCommissioner();
                    break;
                case "YHJS09"://9增值服务专员
                    funAuths = PreRolePermInfo.GetAgentRolePermItem_ValueServiceCommissioner();
                    fldAuths = PreRolePermInfo.GetAgentRoleFldPerm_ValueServiceCommissioner();
                    dataRowAuths = PreRolePermInfo.GetAgentDataRowAuth_ValueServiceCommissioner();
                    break;
                default:
                    break;
            }

            var roleACLDetail = DataAuthHelp.GetAgentRoleAccess(ctx, role, new Dictionary<string, string>(), allMdls, funAuths, fldAuths, dataRowAuths);

            return roleACLDetail;
        }



        private void SaveRoleMPMenuAccess(UserContext ctx, DynamicObject role, string roleNo)
        {
            List<MPTabbarModel> menuItems = null;
            switch (roleNo)
            {
                case "DKHYHJS01"://1大客户渠道老板
                case "YHJS01"://1老板
                    menuItems = PreRolePermInfo.GetAgentRoleMPMenu_Boss();
                    break;
                case "DKHYHJS02"://2大客户渠道跟单
                case "YHJS02"://2跟单
                    menuItems = PreRolePermInfo.GetAgentRoleMPMenu_Flow();
                    break;
                case "DKHYHJS03"://3大客户渠道店长
                case "YHJS03"://3店长
                    menuItems = PreRolePermInfo.GetAgentRoleMPMenu_Shopkeeper();
                    break;
                case "DKHYHJS04"://4大客户渠道导购
                case "YHJS04"://4导购
                    menuItems = PreRolePermInfo.GetAgentRoleMPMenu_Sales();
                    break;
                case "DKHYHJS05"://5大客户渠道财务
                case "YHJS05"://5财务
                    menuItems = PreRolePermInfo.GetAgentRoleMPMenu_Fin();
                    break;
                case "DKHYHJS06"://6大客户渠道仓管
                case "YHJS06"://6仓管
                    menuItems = PreRolePermInfo.GetAgentRoleMPMenu_Stock();
                    break;
                case "DKHYHJS07"://7大客户渠道管理员
                case "YHJS07"://7管理员
                    menuItems = PreRolePermInfo.GetAgentRoleMPMenu_Admin();
                    break;
                case "DKHYHJS08"://8大客户渠道服务主管
                case "YHJS08"://8服务主管
                    menuItems = PreRolePermInfo.GetAgentRoleMPMenu_ServiceManager();
                    break;
                case "DKHYHJS09"://9大客户渠道增值服务专员
                case "YHJS09"://9增值服务专员
                    menuItems = PreRolePermInfo.GetAgentRoleMPMenu_ValueServiceCommissioner();
                    break;

                default:
                    break;
            }

            if (menuItems == null || menuItems.Count == 0)
            {
                return;
            }

            RoleMPMenuPermitInfo auth = new RoleMPMenuPermitInfo();
            auth.RoleId = Convert.ToString(role["Id"]);
            auth.RoleUsers = new Dictionary<string, string>();
            auth.MPMenuPermission = menuItems;

            if (auth.MPMenuPermission == null)
            {
                return;
            }

            foreach (var tabbar in auth.MPMenuPermission)
            {
                foreach (var group in tabbar.groups)
                {
                    foreach (var menu in group.menus)
                    {
                        menu.isAllow = true;
                    }
                }
            }

            MPPermHelper.SaveRolePermission(ctx, auth);
        }


        /// <summary>
        /// 设置大客户渠道管理员
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="agents"></param>
        private void UpdateBigCustomerAdminRoleUser(UserContext userCtx, IEnumerable<DynamicObject> agents)
        {
            if (agents == null || !agents.Any()) return;

            var agentIds = agents.Select(s => Convert.ToString(s["id"])).ToList();

            var sql = $@"
----修复数据：经销商用户都没有关联【设置大客户渠道管理员】角色的问题，增加关联
insert into t_sec_roleuser(fid,fformid,froleid,fuserid)
select fuserid as fid,'sec_roleuser' as fformid,froleid ,fuserid
from 
(
    ---没有关联【设置大客户渠道管理员】的
    select ( select fid from t_sec_role with(nolock) where fnumber = 'Admin_BigCustomer' and fmainorgid='{userCtx.TopCompanyId}') as froleid,
        b.fname as forgname,b.fnumber as forgnumber,a.fid as fuserid,a.fname as fusername,b.fcreatedate 
    from t_sec_user a with(nolock) 
    inner join T_BAS_ORGANIZATION b  with(nolock) on a.fmainorgid = b.fid and b.forgtype ='4'
    inner join 
        (
            select min(fid) as fid,fmainorgid from t_sec_user with(nolock) 
            where fcreatorid ='sysadmin' and fmainorgid in ({agentIds.JoinEx(",", true)})
            group by fmainorgid 
        ) d on a.fmainorgid =d.fmainorgid and a.fid=d.fid and a.fisdevops='0'
    where  b.fid not in 
    (
        select forgid from 
        (
            select distinct c.fid as forgid,c.fname as forgname, b.fid as fuserid,a.froleid
            from t_sec_roleuser a with(nolock) 
            inner join t_sec_user b  with(nolock) on a.fuserid = b.fid 
            inner join T_BAS_ORGANIZATION c  with(nolock) on b.fmainorgid = c.fid
            where a.froleid in 
            ( 
                select fid from t_sec_role  with(nolock) 
                where fnumber = 'Admin_BigCustomer' and fmainorgid='{userCtx.TopCompanyId}'
            ) and c.fid in ({agentIds.JoinEx(",", true)})
        ) x 
    )
) t";
            var svc = userCtx.Container.GetService<IDBServiceEx>();
            svc.Execute(userCtx, sql);
        }


        ///// <summary>
        ///// 修改当前经销商生成的账号的角色，如果当前经销商勾选了大客户渠道，需要把这些账号的角色改成大客户渠道管理员
        ///// </summary>
        ///// <param name="userCtx"></param>
        ///// <param name="agents"></param>
        //private void UpdateAgentAccountRole(UserContext userCtx, IEnumerable<DynamicObject> agents)
        //{

        //    if (agents == null || !agents.Any()) return;

        //    var metaModelService = userCtx.Container.GetService<IMetaModelService>();
        //    var roleUserMeta = metaModelService.LoadFormModel(userCtx, "sec_roleuser");

        //    var orglist = userCtx.LoadBizDataByNo("bas_organization", "fnumber", agents.Select(p => Convert.ToString(p["fnumber"])).ToList());

        //    //大客户渠道管理员和经销商管理员信息
        //    var roles = userCtx.LoadBizDataByNo("sec_role", "fnumber", new string[] { "Admin_BigCustomer", "Admin_Agent" });

        //    var bigCustomerRole = roles.FirstOrDefault(p => Convert.ToString(p["fnumber"]) == "Admin_BigCustomer");

        //    var agentRole = roles.FirstOrDefault(p => Convert.ToString(p["fnumber"]) == "Admin_Agent");

        //    var beSaveRoleUser = new List<DynamicObject>();
        //    foreach (var agent in agents)
        //    {
        //        if (Convert.ToBoolean(agent["fcustomchannel"]))
        //        {
        //            var username = Convert.ToString(agent["fcontacterphone"]);

        //            var fnumber = Convert.ToString(agent["fnumber"]);

        //            var org = orglist.FirstOrDefault(p => Convert.ToString(p["fnumber"]) == fnumber);

        //            var user = userCtx.LoadBizDataByFilter("sec_user", $"fnumber = '{username}' and fmainorgid = '{org["Id"]}'").FirstOrDefault();

        //            var roleuser = userCtx.LoadBizDataByFilter("sec_roleuser", $"froleid = '{agentRole["Id"]}'and fuserid = '{user["Id"]}'").FirstOrDefault();

        //            if (!roleuser.IsNullOrEmptyOrWhiteSpace())
        //            {
        //                roleuser["froleid"] = bigCustomerRole["Id"];

        //                beSaveRoleUser.Add(roleuser);
        //            }
        //            if (beSaveRoleUser.Count > 0)
        //            {
        //                var dmRoleUser = userCtx.Container.GetService<IDataManager>();
        //                dmRoleUser.InitDbContext(userCtx, roleUserMeta.GetDynamicObjectType(userCtx));
        //                dmRoleUser.Save(beSaveRoleUser);

        //            }

        //        }
        //    }
        //}
        /// <summary>
        /// 系统预设自动给该经销商创建岗位ydj_position和总仓仓库ydj_storehouse
        /// </summary>
        private void PreAgentPositionAndStorehouse(UserContext userCtx, IEnumerable<DynamicObject> dataEntities)
        {
            if (dataEntities == null || dataEntities.Count() == 0)
            {
                return;
            }
            //营销助手APP职位-->岗位名称
            var dic = new Dictionary<string, string>();
            dic.Add("经销商老板", "经销商老板");
            dic.Add("经销商跟单员", "跟单");
            dic.Add("门店店长", "店长");
            dic.Add("睡眠顾问", "导购");
            dic.Add("经销商财务", "财务");
            dic.Add("经销商市场主管", "市场主管");
            dic.Add("经销商客服专员", "经销商客服专员");
            dic.Add("安装服务专员", "安装专员");
            dic.Add("安装主管", "安装主管");
            dic.Add("增值服务主管", "增值服务主管");
            dic.Add("增值服务专员", "增值服务专员");
            dic.Add("仓库管理员", "仓管");


            var metaModelService = userCtx.Container.GetService<IMetaModelService>();
            var dbService = userCtx.Container.GetService<IDBService>();

            var positionMeta = metaModelService.LoadFormModel(userCtx, "ydj_position");
            var storehouseMeta = metaModelService.LoadFormModel(userCtx, "ydj_storehouse");

            var positionMetaType = positionMeta.GetDynamicObjectType(userCtx);
            var storehouseMetaType = storehouseMeta.GetDynamicObjectType(userCtx);
            var preService = userCtx.Container.GetService<IPrepareSaveDataService>();

            var sql = $@"select fid,fnumber,fname from t_ms_markingassistant with(nolock) 
                        where fmainorgid='{userCtx.TopCompanyId}' and foutid<>''";
            var markingassistants = dbService.ExecuteDynamicObject(userCtx, sql);

            var beSave = new List<DynamicObject>();
            foreach (var agent in dataEntities)
            {
                var agentId = Convert.ToString(agent["Id"]);

                // 重试次数
                int retry = 5;
                bool isRegister = false;
                for (int i = 0; i < retry; i++)
                {
                    // 如果在ac库里存在企业，说明注册成功
                    sql = $"select top 1 fid from v_auth_company with(nolock) where fid = '{agentId}' ";

                    using (var reader = dbService.ExecuteReader(userCtx, sql))
                    {
                        if (reader.Read())
                        {
                            isRegister = true;
                        }
                    }

                    if (isRegister) break;

                    //经销商还没注册，可能还未更新，等待几秒
                    System.Threading.Thread.Sleep(i * 500);
                }

                // 如果还没有注册，不做预设
                if (!isRegister) continue;

                // 如果存在岗位，不做预设
                sql = $"select top 1 fid from t_ydj_position with(nolock) where fmainorgid = '{agentId}'";
                var positions = dbService.ExecuteDynamicObject(userCtx, sql);
                if (positions != null && positions.Count() > 0) continue;

                //获取岗位-app职位配置数据
                sql = $"select fname,fmarkingassistant from T_YDJ_POSITION_APPJOB_MAP where fforbidstatus='0'";
                var posmapapps = dbService.ExecuteDynamicObject(userCtx, sql).ToDictionary(p=> Convert.ToString(p["fname"]),p=>Convert.ToString(p["fmarkingassistant"]));
              
                // 批量创建经销商预置岗位
                var positionObjs = new List<DynamicObject>();
                foreach (var data in dic)
                {
                    //营销助手岗位
                    var markingassistant = markingassistants?.FirstOrDefault(f => Convert.ToString(f["fname"]).EqualsIgnoreCase(data.Key));
                    if (markingassistant != null)
                    {
                        var position = positionMetaType.CreateInstance() as DynamicObject;
                       
                        position["fname"] = data.Value;
                        position["fstatus"] = "E";
                        //taskid #74934  大客户渠道岗位的营销助手APP职位不预置。
                        if (!Convert.ToBoolean(Convert.ToString(agent["fcustomchannel"]))&& posmapapps !=null&& posmapapps.ContainsKey(data.Value)) {
                            position["fmarkingassistant"] = posmapapps[data.Value];
                        }
                        positionObjs.Add(position);
                    }
                }

                // 经销商企业上下文
                var agentCtx = userCtx.CreateAgentDBContext(agentId);
                // 批量保存岗位
                preService.PrepareDataEntity(agentCtx, positionMeta, positionObjs.ToArray(), OperateOption.Create());
                agentCtx.SaveBizData(positionMeta.Id, positionObjs);

                // 预置仓库
                var storehouseObjs = new List<DynamicObject>();
                var storehouse = storehouseMetaType.CreateInstance() as DynamicObject;
                storehouse["fname"] = "总仓";
                storehouse["fwarehousetype"] = "warehouse_01";
                storehouse["fstockid"] = "311858936800219137";
                storehouse["fstatus"] = "E";
                storehouseObjs.Add(storehouse);
                // 批量保存岗位
                preService.PrepareDataEntity(agentCtx, storehouseMeta, storehouseObjs.ToArray(), OperateOption.Create());
                agentCtx.SaveBizData(storehouseMeta.Id, storehouseObjs);
            }
        }
    }
}
