using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Profile;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace JieNor.AMS.YDJ.Stock.AppService.Report.StockOutStoreSlaer
{
    [InjectService]
    [FormId("rpt_stockout_storesaler")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 库存访问服务
        /// </summary>
        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        /// <summary>
        /// 检查当前过滤界面必须录入的信息 
        /// </summary>
        protected void CheckDataEnvironment()
        {
            // CustomFilterObject 自定义过滤条件（从每个业务表单提供过滤子表单中收集而来的过滤条件）
            if (this.CustomFilterObject.IsNullOrEmpty())
            {
                throw new BusinessException("过滤条件不可以为空！");
            }
            DateTime? dtDateFrom = (DateTime?)this.CustomFilterObject["fdatefrom"];
            if (dtDateFrom == null)
            {
                throw new BusinessException("过滤条件【业务日期从】必录！");
            }
            DateTime? dtDateTo = (DateTime?)this.CustomFilterObject["fdateto"];
            if (dtDateTo == null)
            {
                throw new BusinessException("过滤条件【业务日期至】必录！");
            }
            if (dtDateTo < dtDateFrom)
            {
                throw new BusinessException("过滤条件【业务日期至】不能小于【业务日期从】！");
            }
        }

        /// <summary>
        /// 获得当前库存期间信息
        /// </summary>
        /// <param name="dtStart"></param>
        /// <param name="dtEnd"></param>
        protected void TryGetInventoryPeriodDate(out DateTime? dtStart, out DateTime? dtEnd)
        {
            dtStart = (DateTime?)this.CustomFilterObject["fdatefrom"];
            if (!dtStart.HasValue)
            {
                dtStart = DateTime.Now.GetFirstDayOfMonth();
            }
            dtEnd = (DateTime?)this.CustomFilterObject["fdateto"];
            if (!dtEnd.HasValue)
            {
                dtEnd = DateTime.Now;
            }
        }
        protected override void OnAfterListData(List<Dictionary<string, object>> listData)
        {
            base.OnAfterListData(listData);
            if (listData == null || listData.Count <= 0 || !listData.First().Keys.Contains("fseriessumamt"))
            {
                return;
            }

            //根据用户配置统计显示列改变统计值
            var userProfileService = this.Context.Container.GetService<IUserProfile>();
            FormUserProfile profile = userProfileService.LoadUserProfile(this.Context, this.HtmlForm.Id, "listreport");
            var profileList = profile?.ListLayout?.Where(y => !y.Hidden && y.Id.Contains("fseries") && (y.Id != "fseriessumamt")).Select(y => y.Id).ToList();
            var htmlShowField = this.HtmlForm.FieldList.Where(x => x.Value.Id.Contains("fseries") && x.Value.Id != "fseriessumamt" && x.Value.Visible == -1).Select(x => x.Value.Id);
            if (profileList == null)
            {
                profileList = new List<string>();
            }
            profileList.AddRange(htmlShowField);
            profileList = profileList.Distinct().ToList();
            bool noHasColumn = profileList.IsNullOrEmpty() || profileList.Count == 0;

            var profileEffect = listData.First().Keys.Where(x => profileList.Contains(x));
            foreach (var dataEntity in listData)
            {
                decimal newAmount = 0.00M;
                if (noHasColumn)
                {
                    dataEntity["fseriessumamt"] = newAmount;
                    continue;
                }
                foreach (var item in profileEffect)
                {
                    newAmount += Convert.ToDecimal(dataEntity[item]);
                }
                dataEntity["fseriessumamt"] = newAmount;
            }
        }

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            //this.CheckDataEnvironment();
            this.ProfitListData();
        }

        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>
        protected void ProfitListData()
        {
            DateTime? dtStart;
            DateTime? dtEnd;
            this.TryGetInventoryPeriodDate(out dtStart, out dtEnd);


            //销售员
            //定义一个字典类型的数组，存放多个销售部门,用','隔开
            //如果门店为空值，则不取
            var fsalerid = this.CustomFilterObject["fsalerid"] as string;
            var fsaleridArray = fsalerid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            //门店
            //定义一个字典类型的数组，存放多个销售部门,用','隔开
            //如果门店为空值，则不取
            var fstoreid = this.CustomFilterObject["fstoreid"] as string;
            var fstoreidArray = fstoreid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);


            var sqlParams = new List<SqlParam>
            {
              new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
            };

            var whereBuilder = new StringBuilder();

            if (fstoreidArray != null && fstoreidArray.Length > 0)
            {
                if (fstoreidArray.Length == 1)
                {
                    whereBuilder.Append(" and t0.fid=@fstoreid0");
                    sqlParams.Add(new SqlParam("@fstoreid0", DbType.String, fstoreidArray.First()));
                }
                else
                {
                    whereBuilder.Append(" and t0.fid in(" + string.Join(",", fstoreidArray.Select((x, i) => $"@fstoreid{i}")) + ")");
                    sqlParams.AddRange(fstoreidArray.Select((x, i) => new SqlParam($"@fstoreid{i}", System.Data.DbType.String, x)));
                }
            }

            if (fsaleridArray != null && fsaleridArray.Length > 0)
            {
                if (fsaleridArray.Length == 1)
                {
                    whereBuilder.Append(" and t3.fid=@fstaffid0");
                    sqlParams.Add(new SqlParam("@fstaffid0", DbType.String, fsaleridArray.First()));
                }
                else
                {
                    whereBuilder.Append(" and t3.fid in(" + string.Join(",", fsaleridArray.Select((x, i) => $"@fstaffid{i}")) + ")");
                    sqlParams.AddRange(fsaleridArray.Select((x, i) => new SqlParam($"@fstaffid{i}", System.Data.DbType.String, x)));
                }
            }

            if (dtStart != null)
            {
                whereBuilder.Append(" and t1.fdate >= @fdatefrom ");
                sqlParams.Add(new SqlParam("@fdatefrom", DbType.DateTime, dtStart.Value.ToString("yyyy-MM-dd 00:00:00")));
            }

            if (dtEnd != null)
            {
                whereBuilder.Append(" and t1.fdate < @fdateto ");
                sqlParams.Add(new SqlParam("@fdateto", DbType.DateTime, dtEnd.Value.ToString("yyyy-MM-dd 23:59:59")));
            }
            var rule = new DataQueryRuleParaInfo()
            {
                SrcFormId = "ydj_series",
                SrcFldId = "fseriesid"
            };

            var authView = this.Context.GetAgenAuthSeriDataPKID(rule);
            List<string> seriesIds = new List<string>() { };
            List<DynamicObject> seriesInfo = new List<DynamicObject>();
            if (!authView.Item2.IsNullOrEmptyOrWhiteSpace())
            {
                var sql = "";
                if (authView.Item1 == "=")
                {
                    sql = @"select t0.fid,t0.fname,t0.fnumber from  t_ydj_series t0 
                        where t0.fid ='{0}' and t0.fforbidstatus='0' and t0.fmainorgid in(@fmainorgid,@topfmainorgid) 
                        order by t0.fid".Fmt(authView.Item2);
                }
                else if (authView.Item1.EqualsIgnoreCase("in"))
                {
                    sql = @"select t0.fid,t0.fname,t0.fnumber from  t_ydj_series t0 
                        where t0.fid in ({0}) and t0.fforbidstatus='0' and t0.fmainorgid in(@fmainorgid,@topfmainorgid) 
                        order by t0.fid".Fmt(authView.Item2);
                }
                else if (authView.Item1.EqualsIgnoreCase("exists"))
                {
                    sql = @"select t0.fid,t0.fname,t0.fnumber from  t_ydj_series t0 
                            where exists(select 1 from ({0}) TMPXX where TMPXX.FPKId=t0.fid) 
                                and t0.fforbidstatus='0' and t0.fmainorgid in(@fmainorgid,@topfmainorgid) 
                            order by t0.fid".Fmt(authView.Item2 );
                }
                var serparams = new List<SqlParam> { new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
                                                new SqlParam("@topfmainorgid", DbType.String, this.Context.TopCompanyId)};
                seriesInfo = this.Context.ExecuteDynamicObject(sql, serparams)?.ToList();
                seriesIds = seriesInfo?.Select(x => Convert.ToString(x["fid"])).ToList();
            }
            //向当前报表模型中增加一个自定义字段
            //this.GetOrAddReportField("fseries1", "慕思0769(C5)", HtmlElementType.HtmlField_TextField);
            //this.AddDynamicReportField(new List<ReportFieldModel> {
            //    new ReportFieldModel { Id= "fseries1",Caption= "慕思0769(C5)",ElType= HtmlElementType.HtmlField_TextField }
            //});

            ////根据用户配置统计显示列统计值，此处已移到OnAfterListData去统计，放到此不可取，以为用户变更展示列后将不走此处刷新数据
            //var userProfileService = this.Context.Container.GetService<IUserProfile>();
            //FormUserProfile profile = userProfileService.LoadUserProfile(this.Context, this.HtmlForm.Id, "listreport");
            //if (!seriesIds.IsNullOrEmpty())
            //{
            //    if (profile.IsNullOrEmpty()|| profile.ListLayout.IsNullOrEmpty())
            //    {
            //        seriesIds = new List<string>();
            //    }
            //}

            StringBuilder selectAppendStr = new StringBuilder();
            //var cols = string.Empty;
            //var profileList = profile?.ListLayout?.Where(y => y.Hidden).Select(y => y.Id).ToList();
            //for (int i = 0; i < seriesIds.Count; i++)
            //{
            //    if (!profileList.IsNullOrEmpty()&&profileList.Contains(seriesIds[i]))
            //    {
            //        seriesIds.Remove(seriesIds[i]);
            //        i--;
            //    }
            //    else
            //    {
            //        selectAppendStr.AppendLine($"convert(decimal(18,2),isnull((select sum(t.fsumAmt) fsumAmt from temp t where t.fstoreid=t0.fstoreid and t.fsalerid=t0.fsalerid and t.fseriesid='{seriesIds[i]}'),0))fseries{i},");
            //    }
            //}
            for (int i = 0; i < seriesIds.Count; i++)
            {
                selectAppendStr.AppendLine($"convert(decimal(18,2),isnull((select sum(t.fsumAmt) fsumAmt from temp t where t.fstoreid=t0.fstoreid and t.fsalerid=t0.fsalerid and t.fseriesid='{seriesIds[i]}'),0)) fseries{i},");
            }
            var strSql = $@"/*dialect*/with temp as 
(
select t2.fnumber fstorenumber,t2.fid fstoreid,isnull(t3.fnumber,'') fsalernumber,isnull(t3.fid,'') fsalerid,
isnull(m.fseriesid,'')fseriesid,sum(d.famount*du.fratio/100) fsumAmt
from t_stk_sostockout t1
left join t_ydj_orderduty du on t1.fsourceinterid = du.fid
inner join t_bd_department t2 on t1.fsodeptid=t2.fid
inner join t_stk_sostockoutentry d on t1.fid=d.fid
inner join T_BD_MATERIAL m on d.fmaterialid=m.fid 
left join T_BD_STAFF t3 on du.fdutyid=t3.fid
where m.fseriesid in(
'{(seriesIds.IsNullOrEmpty() || seriesIds.Count == 0 ? "####" : string.Join("','", seriesIds))}'
) and t1.fmainorgid=@fmainorgid and t1.fcancelstatus='0' {whereBuilder}
group by t2.fnumber,t2.fid,t3.fnumber,t3.fid,m.fseriesid
)
insert into {this.DataSourceTableName}
(fid,fstorenumber,fstoreid,fsalernumber,fsalerid,{(seriesIds.IsNullOrEmpty() || seriesIds.Count == 0 ? "" : string.Join(",", seriesIds.Select((x, i) => $"fseries{i}")) + ",")}fseriessumamt)
select ROW_NUMBER()over(order by fstorenumber),fstorenumber,fstoreid,fsalernumber,fsalerid,
{selectAppendStr}
convert(decimal(18,2),isnull((select sum(t.fsumAmt) fsumAmt from temp t where t.fstoreid=t0.fstoreid and t.fsalerid=t0.fsalerid),0)) fseriessumamt --小计
from temp t0
group by fstorenumber,fstoreid,fsalernumber,fsalerid
order by fstorenumber,fsalernumber
";
            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();

            try
            {
                dbServiceExt.Execute(this.Context, strSql, sqlParams);
            }
            catch (Exception ex)
            {
                //动态报表要注意，此项一定要处理异常（ListReportFormController防止用户会话过期后，
                //直接请求并构建临时表时动态列未构建问题，此处暂不到平台处理，应需自行处理），不处理后续将会报错列xxx不存在
                var columns = this.Context.ExecuteDynamicObject($"select COLUMN_NAME from information_schema.COLUMNS where TABLE_NAME='{this.DataSourceTableName}';", new List<SqlParam>())?.ToList();
                var columnNames = columns?.Select(x => Convert.ToString(x["COLUMN_NAME"]));
                if (!seriesInfo.IsNullOrEmpty() && seriesInfo.Count > 0)
                {
                    var addColumns = new List<string>();
                    for (int i = 0; i < seriesInfo.Count; i++)
                    {
                        this.GetOrAddReportField($"fseries{i}", seriesInfo[i]["fname"] + "(" + seriesInfo[i]["fnumber"] + ")", HtmlElementType.HtmlField_AmountField);
                        if (columnNames.Any(c => c == $"fseries{i}")) continue;
                        addColumns.Add($"fseries{i} varchar(100)");
                    }
                    dbServiceExt.Execute(this.Context, $"alter table {this.DataSourceTableName} add ({string.Join(",", addColumns)});");
                    dbServiceExt.Execute(this.Context, strSql, sqlParams);
                }
            }


        }

        //private void builSqlAndAddParams(string[] data, string fieldKey, StringBuilder sql, List<SqlParam> sqlParams)
        //{
        //    if (data == null || data.Length <= 0)
        //    {
        //        return;
        //    }

        //    if (data.Length == 1)
        //    {
        //        sqlParams.Add(new SqlParam($"@{fieldKey}", DbType.String, data[0]));
        //        sql.Append($" and {fieldKey} = @{fieldKey} ");
        //        return;
        //    }

        //    sqlParams.AddRange(data.Select((x, i) => new SqlParam($"@{fieldKey}{i}", DbType.String, x)));
        //    sql.Append($" and {fieldKey} in ( ");
        //    sql.Append(string.Join(",", data.Select((x, i) => $"@{fieldKey}{i}")));
        //    sql.Append(") ");
        //}


    }
}
