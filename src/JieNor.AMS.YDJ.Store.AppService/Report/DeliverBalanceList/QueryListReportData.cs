using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.AMS.YDJ.Core;

namespace JieNor.AMS.YDJ.Store.AppService.Report.DeliverBalanceList
{
    /// <summary>
    /// 经销商报表
    /// </summary>
    [InjectService]
    [FormId("rpt_deliverlist")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {
              
        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {  
            this.GetSaleOrderData();
        }
        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>
        private void GetSaleOrderData()
        {

            StringBuilder sbOrderSql = new StringBuilder();            
            StringBuilder sbInsertSelectSql = new StringBuilder();
            StringBuilder sbOrderSelectSql = new StringBuilder();
            StringBuilder sbOrderFromSql = new StringBuilder();
            StringBuilder sbOrderWhereSql = new StringBuilder();
            StringBuilder sbSelectSql = new StringBuilder();

            DateTime? dtStart;
            DateTime? dtEnd;
            DateTime? dtStart_delivery;
            DateTime? dtEnd_delivery;

            var sqlParam = new List<SqlParam>
            {
              new SqlParam("@fmainorgid", DbType.String, this.Context.Company)
            };


            sbOrderWhereSql.Append(" where 1=1 ");

            //按 实控人 获取当前用户对应的所有经销商组织
            var AgentInfos = new ProductDataIsolateHelper().GetCurrentUserAgentInfosByActualOwner(this.Context);
            var Agents = AgentInfos.Select(o => o.Id).ToList();

            sbSelectSql.Append($@" select fid,fid as fagentid,actualownernumber,actualownername,fcontacter,fcontacterphone,fcity,fcity_txt from 
                                ( select fid,fnumber,fname,actualownernumber,actualownername,fcontacter,fcontacterphone,fcity,fcity_txt from t_bas_agent where fforbidstatus =0 and fid in ('{string.Join("','", Agents)}')
                                ) as a");

            var strSql = "/*dialect*/";
            strSql += $@"insert into {this.DataSourceTableName}
                                  (fid,fagentid,actualownernumber,actualownername,fcontacter,fcontacterphone,fcity,fcity_txt)
                            {sbSelectSql.ToString()}{sbOrderWhereSql.ToString()} 
                            ";
            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();
            dbServiceExt.Execute(this.Context, strSql, sqlParam);
        }
    }
}
