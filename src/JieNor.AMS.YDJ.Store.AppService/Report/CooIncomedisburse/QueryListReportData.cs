using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace JieNor.AMS.YDJ.Store.AppService.Report.CooIncomedisburse
{
    [InjectService]
    [FormId("rpt_incomedisburse")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 库存访问服务
        /// </summary>
        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        ///// <summary>
        ///// 检查当前过滤界面必须录入的信息 
        ///// </summary>
        //protected void CheckDataEnvironment()
        //{
        //    // CustomFilterObject 自定义过滤条件（从每个业务表单提供过滤子表单中收集而来的过滤条件）
        //    if (this.CustomFilterObject.IsNullOrEmpty())
        //    {
        //        throw new BusinessException("过滤条件不可以为空！");
        //    }
        //    DateTime? dtDateFrom = (DateTime?)this.CustomFilterObject["fdatefrom"];
        //    if (dtDateFrom == null)
        //    {
        //        throw new BusinessException("过滤条件【业务日期从】必录！");
        //    }
        //    DateTime? dtDateTo = (DateTime?)this.CustomFilterObject["fdateto"];
        //    if (dtDateTo == null)
        //    {
        //        throw new BusinessException("过滤条件【业务日期至】必录！");
        //    }
        //    if (dtDateTo < dtDateFrom)
        //    {
        //        throw new BusinessException("过滤条件【业务日期至】不能小于【业务日期从】！");
        //    }
        //}

        /// <summary>
        /// 获得当前日期
        /// </summary>
        /// <param name="dtStart"></param>
        /// <param name="dtEnd"></param>
        protected void TryGetInventoryPeriodDate(out DateTime? dtStart, out DateTime? dtEnd)
        {
            dtStart = (DateTime?)this.CustomFilterObject["fdatefrom"];
            if (!dtStart.HasValue)
            {
                dtStart = DateTime.Now.GetFirstDayOfMonth();
            }
            dtEnd = (DateTime?)this.CustomFilterObject["fdateto"];
            if (!dtEnd.HasValue)
            {
                dtEnd = DateTime.Now;
            }
        }

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            //this.CheckDataEnvironment();
            this.ProfitListData();
        }

        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>
        protected void ProfitListData()
        {
            DateTime? dtStart;
            DateTime? dtEnd;
            this.TryGetInventoryPeriodDate(out dtStart, out dtEnd);


            //门店
            //定义一个字典类型的数组，存放多个销售部门,用','隔开
            //如果门店为空值，则不取
            var fstoreid = this.CustomFilterObject["fagentid"] as string;
            var fstoreidArray = fstoreid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);


            var sqlParams = new List<SqlParam>
            {
              new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
            };

            var orderWhere = string.Empty;
            var whereBuilder = new StringBuilder();


            if (fstoreidArray != null && fstoreidArray.Length > 0)
            {
                if (fstoreidArray.Length == 1)
                {
                    whereBuilder.Append(" and t1.fid=@fagentid0");
                    sqlParams.Add(new SqlParam("@fagentid0", DbType.String, fstoreidArray.First()));
                }
                else
                {
                    whereBuilder.Append(" and t1.fid in(" + string.Join(",", fstoreidArray.Select((x, i) => $"@fagentid{i}")) + ")");
                    sqlParams.AddRange(fstoreidArray.Select((x, i) => new SqlParam($"@fagentid{i}", System.Data.DbType.String, x)));
                }
            }

            if (dtStart != null)
            {
                orderWhere += " and t2.fcreatedate >= @fdatefrom ";
                sqlParams.Add(new SqlParam("@fdatefrom", DbType.DateTime, dtStart.Value.ToString("yyyy-MM-dd 00:00:00")));
            }

            if (dtEnd != null)
            {
                orderWhere += " and t2.fcreatedate < @fdateto ";
                sqlParams.Add(new SqlParam("@fdateto", DbType.DateTime, dtEnd.Value.ToString("yyyy-MM-dd 23:59:59")));
            }
            var strSql = $@"/*dialect*/
insert into {this.DataSourceTableName}
(fid,fcooinid,fagentid,fsourceformid,fsourcenumber,fcustomerid,fcustomerphone,fdeptid,fstaffid,fway,fdate,fpurpose,paymentdesc,famount,fcreatedate,fcreatorid,fstatus)
select ROW_NUMBER()over(order by t2.fcreatedate desc),t2.fid fcooinid,t1.fid fagentid,t2.fsourceformid,t2.fsourcenumber,t2.fcustomerid,t3.fphone fcustomerphone,t2.fdeptid,t2.fstaffid,
t2.fway,t2.fdate,t2.fpurpose,t2.paymentdesc,t2.famount,t2.fcreatedate,t2.fcreatorid,t2.fstatus from t_bas_agent t1
inner join t_coo_incomedisburse t2 with(nolock) on t1.fid=t2.fmainorgid
inner join t_ydj_customer t3 with(nolock) on t2.fcustomerid=t3.fid
where t1.forgid=@fmainorgid and t1.fforbidstatus='0'  and t1.fisreseller='1' {whereBuilder} {orderWhere}
order by t2.fcreatedate desc
";

            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();

            dbServiceExt.Execute(this.Context, strSql, sqlParams);

        }

        //private void builSqlAndAddParams(string[] data, string fieldKey, StringBuilder sql, List<SqlParam> sqlParams)
        //{
        //    if (data == null || data.Length <= 0)
        //    {
        //        return;
        //    }

        //    if (data.Length == 1)
        //    {
        //        sqlParams.Add(new SqlParam($"@{fieldKey}", DbType.String, data[0]));
        //        sql.Append($" and {fieldKey} = @{fieldKey} ");
        //        return;
        //    }

        //    sqlParams.AddRange(data.Select((x, i) => new SqlParam($"@{fieldKey}{i}", DbType.String, x)));
        //    sql.Append($" and {fieldKey} in ( ");
        //    sql.Append(string.Join(",", data.Select((x, i) => $"@{fieldKey}{i}")));
        //    sql.Append(") ");
        //}


    }
}
