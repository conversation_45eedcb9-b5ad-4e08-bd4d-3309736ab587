using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace JieNor.AMS.YDJ.Stock.AppService.Rpt
{
    [InjectService]
    [FormId("rpt_profitanalysis")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 库存访问服务
        /// </summary>
        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        /// <summary>
        /// 检查当前过滤界面必须录入的信息 
        /// </summary>
        protected void CheckDataEnvironment()
        {
            // CustomFilterObject 自定义过滤条件（从每个业务表单提供过滤子表单中收集而来的过滤条件）
            if (this.CustomFilterObject.IsNullOrEmpty())
            {
                throw new BusinessException("过滤条件不可以为空！");
            }
            DateTime? dtDateFrom = (DateTime?)this.CustomFilterObject["fdatefrom"];
            if (dtDateFrom == null)
            {
                throw new BusinessException("过滤条件【销售日期从】必录！");
            }
            DateTime? dtDateTo = (DateTime?)this.CustomFilterObject["fdateto"];
            if (dtDateTo == null)
            {
                throw new BusinessException("过滤条件【销售日期至】必录！");
            }
            if (dtDateTo < dtDateFrom)
            {
                throw new BusinessException("过滤条件【结束日期】不能小于【开始日期】！");
            }
        }

        /// <summary>
        /// 获得当前库存期间信息
        /// </summary>
        /// <param name="dtStart"></param>
        /// <param name="dtEnd"></param>
        protected void TryGetInventoryPeriodDate(out DateTime? dtStart, out DateTime? dtEnd)
        {
            dtStart = (DateTime?)this.CustomFilterObject["fdatefrom"];
            if (dtStart.HasValue)
            {
                dtStart = dtStart.Value.DayBegin();
            }
            dtEnd = (DateTime?)this.CustomFilterObject["fdateto"];
            if (dtEnd.HasValue)
            {
                dtEnd = dtEnd.Value.DayEnd();
            }
        }

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            this.CheckDataEnvironment();
            this.ProfitListData();
        }

        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>
        protected void ProfitListData()
        {
            DateTime? dtStart;
            DateTime? dtEnd;
            this.TryGetInventoryPeriodDate(out dtStart, out dtEnd);


            //销售部门 
            //定义一个字典类型的数组，存放多个销售部门,用','隔开
            //如果销售部门为空值，则不取
            var fdeptid = this.CustomFilterObject["fdeptid"] as string;
            var fdeptidArray = fdeptid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            //销售员 多个
            var fstaffid = this.CustomFilterObject["fstaffid"] as string;
            var fstaffidArray = fstaffid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            //客户 多个
            var fcustomerid = this.CustomFilterObject["fcustomerid"] as string;
            var fcustomeridArray = fcustomerid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            //合同编号 模糊查询
            var fbillno = this.CustomFilterObject["fbillno"] as string;

            //成本取值来源
            var fsourcecost = this.CustomFilterObject["fsourcecost"] as string;

            
            var sqlParams = new List<SqlParam>
            {
              new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
            };

            var whereBuilder = new StringBuilder();


            if (fdeptidArray != null && fdeptidArray.Length > 0)
            {
                builSqlAndAddParams(fdeptidArray, "fdeptid", whereBuilder, sqlParams);
               
            }

            if (fstaffidArray != null && fstaffidArray.Length > 0)
            {
                builSqlAndAddParams(fstaffidArray, "fstaffid", whereBuilder, sqlParams);
               
            }
            if (fcustomeridArray != null && fcustomeridArray.Length > 0)
            {
                builSqlAndAddParams(fcustomeridArray, "fcustomerid", whereBuilder, sqlParams);
            }

            if (false == string.IsNullOrWhiteSpace(fbillno))
            {
                whereBuilder.AppendFormat("and fbillno = @fbillno ");
                sqlParams.Add(new SqlParam("@fbillno", DbType.String, fbillno));
            }

            if (dtStart != null)
            {
                whereBuilder.Append(" and forderdate >= @fdatefrom ");
                sqlParams.Add(new SqlParam("@fdatefrom", DbType.DateTime, dtStart.Value));
            }

            if (dtEnd != null)
            {
                whereBuilder.Append(" and forderdate < @fdateto ");
                sqlParams.Add(new SqlParam("@fdateto", DbType.DateTime, dtEnd.Value));
            }

            //采购成本表达式
            var costAmountExpression = string.Empty;
            switch (fsourcecost)
            {
                case "0"://采购订单
                    costAmountExpression = "select isnull(sum(pe.fdealamount),0) from t_ydj_poorderentry pe  with(nolock) where pe.fsoorderinterid=o.fid";
                    break;
                case "1"://先取订单再取价目表后取系数 and (pp.fsupplierid=re.fsupplierid or pp.fsupplierid='') -- pp.fmainorgid=o.fmainorgid and 
                    costAmountExpression = @"
select isnull(sum(
                       case when (rt.fdealamount is null and rt.fpurprice is null and rt.fcostratio is null) then 0
                            when (rt.fdealamount is null and rt.fpurprice is null) then (rt.fprice * rt.fqty * rt.fcostratio)
							when (rt.fdealamount is null) then (rt.fpurprice *  rt.fqty)
                       else rt.fdealamount
                       end 
            ),0) from (
                select (select sum(pe.fdealamount) from t_ydj_poorderentry pe with(nolock) 
				        where pe.fsoorderinterid=o.fid and pe.fsoorderentryid=re.fentryid) as fdealamount,
				       (select top 1 ppe.fpurprice 
                        from t_ydj_purchasepriceentry ppe with(nolock) 
                        inner join t_ydj_purchaseprice pp  with(nolock) on pp.fid = ppe.fid
                        where
                            ppe.fproductid_e=re.fproductid and ltrim(rtrim(ppe.fattrinfo))=ltrim(rtrim(re.fattrinfo)) and ppe.fconfirmstatus='2' and 
                              datediff(d,ppe.fstartdate_e,o.forderdate)>-1 and datediff(d,o.forderdate,ppe.fexpiredate_e)>-1
                        order by pp.fsupplierid desc,(datediff(d,ppe.fstartdate_e,o.forderdate)+datediff(d,o.forderdate,ppe.fexpiredate_e)) asc, pp.fmodifydate desc) as fpurprice,
                       (select br.fcostratio from t_ydj_brand br  with(nolock) 
                        inner join t_bd_material m  with(nolock) on m.fbrandid=br.fid and m.fmainorgid=br.fmainorgid
                        where m.fmainorgid=o.fmainorgid and  m.fid=re.fproductid) as fcostratio,
                       re.fqty,
                       re.fprice
                from t_ydj_orderentry re with(nolock) 
                where re.fid=o.fid
            ) rt
";
                    break;
                case "2"://先取价目表再取系数 and (pp.fsupplierid=re.fsupplierid or pp.fsupplierid='') -- pp.fmainorgid=o.fmainorgid and
                    costAmountExpression = @"
select isnull(sum(
                       case when (rt.fpurprice is null and rt.fcostratio is null) then 0
                            when (rt.fpurprice is null) then (rt.fprice * rt.fqty * rt.fcostratio)
					   else (rt.fpurprice *  rt.fqty)
                       end 
            ),0) from (
                select (select top 1 ppe.fpurprice 
                        from t_ydj_purchasepriceentry ppe with(nolock) 
                        inner join t_ydj_purchaseprice pp  with(nolock) on pp.fid = ppe.fid
                        where 
                              ppe.fproductid_e=re.fproductid and ltrim(rtrim(ppe.fattrinfo))=ltrim(rtrim(re.fattrinfo)) and ppe.fconfirmstatus='2' and 
                              datediff(d,ppe.fstartdate_e,o.forderdate)>-1 and datediff(d,o.forderdate,ppe.fexpiredate_e)>-1
                        order by pp.fsupplierid desc,(datediff(d,ppe.fstartdate_e,o.forderdate)+datediff(d,o.forderdate,ppe.fexpiredate_e)) asc, pp.fmodifydate desc) as fpurprice,
                       (select br.fcostratio from t_ydj_brand br  with(nolock) 
                        inner join t_bd_material m  with(nolock) on m.fbrandid=br.fid and m.fmainorgid=br.fmainorgid
                        where m.fmainorgid=o.fmainorgid and m.fid=re.fproductid) as fcostratio,
                       re.fqty,
                       re.fprice
                from t_ydj_orderentry re with(nolock) 
                where re.fid=o.fid
            ) rt
";
                    break;
                case "3"://合同成本
                    costAmountExpression = "(select isnull(sum(oe.fcostprice * ( oe.fqty -oe.frefundqty) ),0) from t_ydj_orderentry oe  with(nolock) where oe.fid=o.fid)";
                    break;
                default:
                
                    costAmountExpression = "0";
                    break;
            }

            //http://dmp.jienor.com:81/zentao/task-view-38441.html 该需求订单总额已经减了费用支出，这里不做重复减

            var strSql = $@"/*dialect*/
insert into {this.DataSourceTableName}
      (fid,fbillno,forderdate,fdeptid,fstaffid,fcustomerid,fsumamount,fdealamount,fexpense,fsumcost,fbrokerage,fcostamount,ffee,fgain,faginrate,fnumber,fstoreid,fstorenumber,fshortname,assistant,assistantdep  )
select fid,fbillno,forderdate,fdeptid,fstaffid,fcustomerid,
(fsumamount  - factrefundamount) as fsumamount,
--(fsumamount -  fexpenditure - factrefundamount) as fsumamount,
fdealamount,fexpense,
(fbrokerage+fcostamount+ffee) as fsumcost,
fbrokerage,fcostamount,ffee, 
(fdealamount+fexpense-fbrokerage-fcostamount-ffee) as fgain,
(case when((fdealamount+fexpense)=0) then 0 else (fdealamount+fexpense-fbrokerage-fcostamount-ffee)/(fdealamount+fexpense)*100 end) as faginrate,
fnumber,fstoreid,fstorenumber,fshortname,assistant,assistantdep
from (
select  
o.fid,
o.fbillno,
o.forderdate,
o.fdeptid,
o.fstaffid,
o.fcustomerid,
o.fsumamount, 
o.fexpenditure,
o.factrefundamount,
(select isnull(sum((x.fqty - x.frefundqty) * x.fdealprice),0) as fdealamount from t_ydj_orderentry x  with(nolock) where x.fid=o.fid  ) as fdealamount, 
o.fexpense,
(select isnull(sum(fsumtaxamount),0) from t_ste_registfee rf  with(nolock) where rf.fmainorgid=o.fmainorgid and rf.fsourcetype='ydj_order' and rf.fsourcenumber=o.fbillno and rf.fstatus='E') as fbrokerage,
({costAmountExpression}) as fcostamount,
(select isnull(sum(pe.ffee),0) from t_ydj_poorderentry pe  with(nolock) where pe.fsourceformid='ydj_order' and pe.fsourceinterid=o.fid) as ffee,
isnull(dept.fnumber,'') fnumber, isnull(store.fid,'') fstoreid,isnull( store.fnumber,'') as fstorenumber,isnull(store.fshortname,'') fshortname,
ISNULL(stuff((select CONCAT (';' , s.fname , ' ' , convert(nvarchar(10),convert(decimal,od1.fratio)) ,'%') from t_ydj_orderduty od1 with(nolock) left join t_bd_staff s with(nolock) on s.fid=od1.fdutyid where odmx.fid = od1.fid  for xml path('')),1,1,''),'') AS assistant,
ISNULL(stuff((select CONCAT( ';' , s.fname , ' ' , dp.fname) from t_ydj_orderduty od1 with(nolock) left join t_bd_staff s with(nolock) on s.fid=od1.fdutyid left join T_BD_DEPARTMENT dp on dp.fid=od1.fdeptid where odmx.fid = od1.fid  for xml path('')),1,1,''),'') AS assistantdep
from t_ydj_order o with(nolock) 
INNER JOIN T_YDJ_ORDERENTRY odmx WITH(NOLOCK) ON o.fid = odmx.fid
LEFT JOIN T_BD_DEPARTMENT dept WITH(NOLOCK) ON dept.fid = o.fdeptid
LEFT JOIN T_BAS_STORE store WITH(NOLOCK) ON store.fid = o.fstore
where o.fmainorgid=@fmainorgid and o.fcancelstatus='0' {whereBuilder.ToString()}
) t
 group by fid,fbillno,forderdate,fdeptid,fstaffid,fcustomerid,
fsumamount,
fdealamount,fexpense,
fbrokerage,fcostamount,ffee, 
fnumber,fstoreid,fstorenumber,fshortname,assistant,assistantdep,t.factrefundamount
order by t.forderdate desc
";

            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();

            int a= dbServiceExt.Execute(this.Context, strSql, sqlParams);

        }

        private void builSqlAndAddParams(string[] data,string fieldKey,StringBuilder sql,List<SqlParam> sqlParams)
        {
            if (data == null || data.Length <= 0)
            {
                return;
            }

            if (data.Length == 1)
            {
                sqlParams.Add(new SqlParam($"@{fieldKey}", DbType.String, data[0]));
                sql.Append($" and o.{fieldKey} = @{fieldKey} ");
                return;
            }

            sqlParams.AddRange(data.Select((x, i) => new SqlParam($"@{fieldKey}{i}", DbType.String, x)));
            sql.Append($" and o.{fieldKey} in ( ");
            sql.Append(string.Join(",", data.Select((x, i) => $"@{fieldKey}{i}")));
            sql.Append(") ");
        }


    }
}
