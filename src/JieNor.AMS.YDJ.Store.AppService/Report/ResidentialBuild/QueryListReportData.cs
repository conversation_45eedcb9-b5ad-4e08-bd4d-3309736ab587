using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace JieNor.AMS.YDJ.Stock.AppService.Report.ResidentialBuild
{
    [InjectService]
    [FormId("rpt_residentialbuild")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 库存访问服务
        /// </summary>
        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        /// <summary>
        /// 检查当前过滤界面必须录入的信息 
        /// </summary>
        protected void CheckDataEnvironment()
        {
            // CustomFilterObject 自定义过滤条件（从每个业务表单提供过滤子表单中收集而来的过滤条件）
            if (this.CustomFilterObject.IsNullOrEmpty())
            {
                throw new BusinessException("过滤条件不可以为空！");
            }
            DateTime? dtDateFrom = (DateTime?)this.CustomFilterObject["fdatefrom"];
            if (dtDateFrom == null)
            {
                throw new BusinessException("过滤条件【业务日期从】必录！");
            }
            DateTime? dtDateTo = (DateTime?)this.CustomFilterObject["fdateto"];
            if (dtDateTo == null)
            {
                throw new BusinessException("过滤条件【业务日期至】必录！");
            }
            if (dtDateTo < dtDateFrom)
            {
                throw new BusinessException("过滤条件【业务日期至】不能小于【业务日期从】！");
            }
        }

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            //this.CheckDataEnvironment();
            this.ProfitListData();
        }

        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>
        protected void ProfitListData()
        {
            DateTime? dtStart= (DateTime?)this.CustomFilterObject["fdatefrom"];
            DateTime? dtEnd = (DateTime?)this.CustomFilterObject["fdateto"];
            if (!dtStart.HasValue)
            {
                dtStart = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-01"));
            }
            if (!dtEnd.HasValue)
            {
                dtEnd = DateTime.Now;
            }


            //门店
            //定义一个字典类型的数组，存放多个销售部门,用','隔开
            //如果门店为空值，则不取
            var fbuildid = this.CustomFilterObject["fbuildid"] as string;
            var fbuildidArray = fbuildid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);


            var sqlParams = new List<SqlParam>
            {
              new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
            };

            var orderWhere = string.Empty;
            var whereBuilder = new StringBuilder();


            if (fbuildidArray != null && fbuildidArray.Length > 0)
            {
                if (fbuildidArray.Length == 1)
                {
                    whereBuilder.Append(" and t0.fid=@fstoreid0");
                    sqlParams.Add(new SqlParam("@fstoreid0", DbType.String, fbuildidArray.First()));
                }
                else
                {
                    whereBuilder.Append(" and t0.fid in(" + string.Join(",", fbuildidArray.Select((x, i) => $"@fbuildid{i}")) + ")");
                    sqlParams.AddRange(fbuildidArray.Select((x, i) => new SqlParam($"@fbuildid{i}", System.Data.DbType.String, x)));
                }
            }

            if (dtStart != null)
            {
                orderWhere += " and t0.forderdate >= @fdatefrom ";
                sqlParams.Add(new SqlParam("@fdatefrom", DbType.DateTime, dtStart.Value.ToString("yyyy-MM-dd 00:00:00")));
            }

            if (dtEnd != null)
            {
                orderWhere += " and t0.forderdate < @fdateto ";
                sqlParams.Add(new SqlParam("@fdateto", DbType.DateTime, dtEnd.Value.ToString("yyyy-MM-dd 23:59:59")));
            }
            var strSql = $@"/*dialect*/
insert into {this.DataSourceTableName}
(fid,fcreatedate,fmainorgid,fforbidstatus,fname,fgivedate,fhouseholds,finseveral,fdecoratenum,fnotdecoratenum,fbuynumber,fsales,fsumamount,fguestvalue,fresultpercent,foccupypercent )
select *,
case when fbuynumber!=0 then CAST((fsales/fbuynumber) as decimal(18,6)) else '0' end fguestvalue,
case when fsumamount!=0 then CAST((CAST(fsales as decimal(18,6))/CAST(fsumamount as decimal(18,6)))*100 as  decimal(18,6)) else '0' end fresultpercent, 
case when finseveral!=0 then CAST((CAST(fbuynumber as decimal(18,6))/CAST(finseveral as decimal(18,6))) *100 as decimal(18,6))  else '0' end as foccupypercent 
from (
select fid,fcreatedate,fmainorgid,fforbidstatus,fname,fgivedate,fhouseholds,
case when ISNULL(finseveral,'')='' then 0 else finseveral end finseveral ,
case when ISNULL(fdecoratenum,'')='' then 0 else fdecoratenum end fdecoratenum,
case when ISNULL(fnotdecoratenum,'')='' then 0 else fnotdecoratenum end fnotdecoratenum,
(select COUNT(1) from T_YDJ_ORDER as t0 where fstatus='E' and fbuildingid=b.fid and fmainorgid=@fmainorgid {orderWhere}) fbuynumber,
(select ISNULL(CAST(sum(fsumamount) as decimal(18,6)),'0') from T_YDJ_ORDER as t0 with(nolock) where fstatus='E' and fbuildingid=b.fid and fmainorgid=@fmainorgid {orderWhere}) fsales,
(select ISNULL(CAST(sum(fsumamount) as decimal(18,6)),'0') from T_YDJ_ORDER as t0 with(nolock) where fstatus='E'  and fmainorgid=@fmainorgid  {orderWhere}) fsumamount 
from T_YDJ_BUILDING as b) as t0
where t0.fmainorgid=@fmainorgid and t0.fforbidstatus='0' {whereBuilder}
";

            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();

            dbServiceExt.Execute(this.Context, strSql, sqlParams);

        }
    }
}
