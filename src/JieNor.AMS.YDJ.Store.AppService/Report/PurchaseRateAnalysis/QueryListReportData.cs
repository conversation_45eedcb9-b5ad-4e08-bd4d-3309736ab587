using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace JieNor.AMS.YDJ.Stock.AppService.Rpt.PurchaseRateAnalysis
{
    [InjectService]
    [FormId("rpt_purchaseratesanalysis")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 库存访问服务
        /// </summary>
        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        /// <summary>
        /// 检查当前过滤界面必须录入的信息 
        /// </summary>
        protected void CheckDataEnvironment()
        {
            DateTime? dtDateFrom = (DateTime?)this.CustomFilterObject["fdatefrom"];
            DateTime? dtDateTo = (DateTime?)this.CustomFilterObject["fdateto"];
            if (this.CustomFilterObject.IsNullOrEmpty())
            {
                throw new BusinessException("过滤条件不可以为空！");
            }
            if (dtDateFrom == null)
            {
                throw new BusinessException("过滤条件【起始日期】必录！");
            }
            if (dtDateTo == null)
            {
                throw new BusinessException("过滤条件【结束日期】必录！");
            }
            if (dtDateTo < dtDateFrom)
            {
                throw new BusinessException("过滤条件【结束日期】不能小于【开始日期】！");
            }
        }

        /// <summary>
        /// 获得当前库存期间信息
        /// </summary>
        /// <param name="dtStart"></param>
        /// <param name="dtEnd"></param>
        protected void TryGetInventoryPeriodDate(out DateTime? dtStart, out DateTime? dtEnd)
        {
            dtStart = (DateTime?)this.CustomFilterObject["fdatefrom"];
            if (dtStart.HasValue)
            {
                dtStart = dtStart.Value.DayBegin();
            }
            dtEnd = (DateTime?)this.CustomFilterObject["fdateto"];
            if (dtEnd.HasValue)
            {
                dtEnd = dtEnd.Value.DayEnd();
            }
        }

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            this.CheckDataEnvironment();
            this.ProfitListData();
        }

        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>
        protected void ProfitListData()
        {
            DateTime? dtStart;
            DateTime? dtEnd;
            this.TryGetInventoryPeriodDate(out dtStart, out dtEnd);

            if (dtStart.IsNullOrEmptyOrWhiteSpace() || dtEnd.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            var fbrandid = this.CustomFilterObject["fbrandid"] as string;
            var fbrandidArray = fbrandid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            var fseriesid = this.CustomFilterObject["fseriesid"] as string;
            var fseriesidArray = fseriesid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            var fcategoryid = this.CustomFilterObject["fcategoryid"] as string;
            var fcategoryidArray = fcategoryid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            var fstatisticsmode = this.CustomFilterObject["fstatisticsmode"] as string;
            var statisticsModes = fstatisticsmode?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            var sqlParams = new List<SqlParam>
            {
              new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
            };

        
            var filterBuilder = new StringBuilder();
            var brandIdFieldKey = "''";
            var seriesIdFieldKey = brandIdFieldKey;
            var categoryIdFieldKey = brandIdFieldKey;
            var groupByFieldKeys = new List<string>();
            if (fbrandidArray != null && fbrandidArray.Length > 0)
            {
                builSqlAndAddParams(fbrandidArray, "fbrandid", filterBuilder, sqlParams);
            }

            if (fseriesidArray != null && fseriesidArray.Length > 0)
            {
                builSqlAndAddParams(fseriesidArray, "fseriesid", filterBuilder, sqlParams);
            }
            if (fcategoryidArray != null && fcategoryidArray.Length > 0)
            {
                builSqlAndAddParams(fcategoryidArray, "fcategoryid", filterBuilder, sqlParams);
            }

            //当用户全不选时，依然按品牌、系列、产品类别
            if (statisticsModes == null || statisticsModes.Length <= 0)
            {
                brandIdFieldKey = "f.fbrandid";
                seriesIdFieldKey = "f.fseriesid";
                categoryIdFieldKey = "f.fcategoryid";
                groupByFieldKeys.Add(brandIdFieldKey);
                groupByFieldKeys.Add(seriesIdFieldKey);
                groupByFieldKeys.Add(categoryIdFieldKey);
            }
            else
            {
                foreach(var statisticsMode in statisticsModes)
                {
                    switch (statisticsMode.ToLower())
                    {
                        case "fbrandid":
                            brandIdFieldKey = "f.fbrandid";
                            groupByFieldKeys.Add(brandIdFieldKey);
                            break;
                        case "fseriesid":
                            seriesIdFieldKey = "f.fseriesid";
                            groupByFieldKeys.Add(seriesIdFieldKey);
                            break;
                        case "fcategoryid":
                            categoryIdFieldKey = "f.fcategoryid";
                            groupByFieldKeys.Add(categoryIdFieldKey);
                            break;
                    }
                }
            }

            //销售合同的时间段过滤条件
            var orderBuilder = new StringBuilder();
            //采购订单过滤条件
            var purOrderBuilder = new StringBuilder();
            orderBuilder.Append("  fmainorgid =@fmainorgid and fstatus ='E' ");
            purOrderBuilder.Append("  fmainorgid =@fmainorgid and fstatus ='E' ");
            if (dtStart != null)
            {
                orderBuilder.Append(" and forderdate >= @fdatefrom ");
                purOrderBuilder.Append(" and fdate >= @fdatefrom ");
                sqlParams.Add(new SqlParam("@fdatefrom", DbType.DateTime, dtStart.Value));
            }

            if (dtEnd != null)
            {
                orderBuilder.Append(" and forderdate < @fdateto ");
                purOrderBuilder.Append(" and fdate < @fdateto ");
                sqlParams.Add(new SqlParam("@fdateto", DbType.DateTime, dtEnd.Value));
            }

            //获取该时间段的采购订单明细  成交金额、采购数量字段汇总
            var purordersql = $@"select t3.fmaterialid,sum(fdealamount) as proamount,sum(fbizqty) as proqty from (
                            select t2.fmaterialid,t2.fdealamount,t2.fbizqty from  t_ydj_poorderentry t2  left join 
                            t_ydj_purchaseorder t1 on t1.fid =t2.fid where  {purOrderBuilder})t3 group by fmaterialid";
            // 获取该时间段的采购订单明细  成交金额、采购数量字段 (销售合同号为空)
            var customersql = $@"select t3.fmaterialid,sum(fdealamount) as proamount,sum(fbizqty) as proqty from (
                            select t2.fmaterialid,t2.fdealamount,t2.fbizqty from  t_ydj_poorderentry t2  left join 
                            t_ydj_purchaseorder t1 on t1.fid =t2.fid where  {purOrderBuilder} and fsoorderno <>'')t3 group by fmaterialid";
            var sumpurordersql = $@"(
                            select s1.fmaterialid, s1.proamount as fsumpuramount, s1.proqty as fsumpurqty,
                            s2.proamount as fcustomeramount, s2.proqty as fcustomerpurqty 
                            from ({purordersql})s1 
                            left join ({customersql})s2 on s1.fmaterialid = s2.fmaterialid)r1";
            //统计该时间段 销售合同的销售数量和销售额
            var saleordersql = $@"select fproductid ,sum(fdealamount) as fsalesamount,sum(fbizqty) as fsalesqty from 
                            ( select t5.fproductid,t5.fdealamount,t5.fbizqty from t_ydj_orderentry t5 
                            left join t_ydj_order t6 on t5.fid = t6.fid where {orderBuilder})t7 group by t7.fproductid";

            var totalsql = $@"select isnull(r1.fmaterialid,r2.fproductid) as   fmaterialid,
                           isnull(r1.fsumpuramount, 0) as fsumpuramount, isnull(r1.fsumpurqty,0) as fsumpurqty,
						   isnull(r1.fcustomeramount,0) as fcustomeramount,isnull(r1.fcustomerpurqty,0) as fcustomerpurqty ,
						   isnull(r2.fsalesamount,0) as fsalesamount,isnull(r2.fsalesqty,0) as fsalesqty
                           from {sumpurordersql} 
                            full join ({saleordersql})r2 on r1.fmaterialid=r2.fproductid";

            var groupBySql = string.Join(",", groupByFieldKeys);

            var sql = $@"select NEWID() as fid,
                        {brandIdFieldKey} as fbrandid, {seriesIdFieldKey} as fseriesid,{categoryIdFieldKey} as fcategoryid,sum(fsalesamount) as fsalesamount,sum(fsalesqty) as fsalesqty,
                        sum(fsumpurqty) as fsumpurqty ,sum(fsumpuramount) as fsumpuramount,sum(fcustomerpurqty) as fcustomerpurqty,
                        sum(fcustomeramount) as fcustomeramount,
                        case when (sum(fsumpurqty)=0) then 0 else (sum(fcustomerpurqty)/sum(fsumpurqty)*100) end as fcustomerrate,
                        sum(fstorepuramount) as fstorepuramount,
                        sum(fstorepurqty) as fstorepurqty,
                        case when (sum(fsumpurqty)=0) then 0 else (sum(fstorepurqty)/sum(fsumpurqty)*100) end as fstorerate
                        from (select g.*,(fsumpuramount-fcustomeramount) as fstorepuramount,
                        (fsumpurqty - fcustomerpurqty) as fstorepurqty,m.fbrandid,m.fseriesid,m.fcategoryid
                         from ({totalsql})g left join T_BD_MATERIAL m on g.fmaterialid = m.fid)f
                        where 1=1 {filterBuilder.ToString()}
                        group by {groupBySql}";

            var strSql = $@"
                         insert into {this.DataSourceTableName}
                         (fid,fbrandid,fseriesid,fcategoryid,fsalesamount,fsalesqty,fsumpurqty,fsumpuramount,fcustomerpurqty,fcustomeramount,fcustomerrate,fstorepuramount,fstorepurqty,fstorerate)
                         (select * from  ({sql}
)rr)
";
            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();

            dbServiceExt.Execute(this.Context, strSql, sqlParams);

        }

        private void builSqlAndAddParams(string[] data, string fieldKey, StringBuilder sql, List<SqlParam> sqlParams)
        {
            if (data == null || data.Length <= 0)
            {
                return;
            }

            if (data.Length == 1)
            {
                sqlParams.Add(new SqlParam($"@{fieldKey}", DbType.String, data[0]));
                sql.Append($" and {fieldKey} = @{fieldKey} ");
                return;
            }

            sqlParams.AddRange(data.Select((x, i) => new SqlParam($"@{fieldKey}{i}", DbType.String, x)));
            sql.Append($" and {fieldKey} in ( ");
            sql.Append(string.Join(",", data.Select((x, i) => $"@{fieldKey}{i}")));
            sql.Append(") ");
        }
    }
}
