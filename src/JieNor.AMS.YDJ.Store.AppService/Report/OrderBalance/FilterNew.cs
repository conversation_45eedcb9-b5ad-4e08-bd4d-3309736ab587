using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Report.OrderBalance
{
    [InjectService]
    [FormId("rpt_orderbalance_filter")]
    [OperationNo("New")]
    public class FilterNew : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;

            foreach (var dataEntity in e.DataEntitys)
            {
                dataEntity["fdatefrom"] = DateTime.Now.Date.AddMonths(-3);
                dataEntity["fdateto"] = DateTime.Now.Date;
            }
        }
    }
}
