using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface.Log;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Service;
using JieNor.AMS.YDJ.Store.AppService.Plugin.MP;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.WarmUpService
{
    /// <summary>
    /// 通过热身服务初始化缓存数据
    /// </summary>
    [InjectService]
    public class InitCacheData : IOnceWarmUpService
    {
        /// <summary>
        /// 日志服务
        /// </summary>
        [InjectProperty]
        private ILogServiceEx LogEx { get; set; }

        /// <summary>
        /// 执行热身服务逻辑
        /// </summary>
        /// <param name="userCtx"></param>
        public void Execute(UserContext userCtx)
        {
            //Thread.Sleep(30 * 1000);

            ProductDataIsolateHelper.Init(userCtx);
            ProductDataIsolateHelper.WriteDebugLog("初始化商品授权缓存通过【热身服务】执行完毕。");
            this.LogEx.Info("初始化商品授权缓存通过【热身服务】执行完毕。");

            MPMenuHelper.Init(userCtx);
            MPMenuHelper.WriteDebugLog("初始化小程序菜单缓存通过【热身服务】执行完毕。");
            this.LogEx.Info("初始化小程序菜单缓存通过【热身服务】执行完毕。");

            MuSiAIService.Init(userCtx);
            MuSiAIService.WriteDebugLog("初始化慕思AI云缓存通过【热身服务】执行完毕。");
            this.LogEx.Info("初始化慕思AI云缓存通过【热身服务】执行完毕。");

        }
    }
}