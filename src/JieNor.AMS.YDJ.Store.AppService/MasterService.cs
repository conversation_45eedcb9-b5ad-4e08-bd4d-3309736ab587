using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService
{
    /// <summary>
    /// 师傅服务定义
    /// </summary>
    [InjectService]
    public class MasterService: IMasterService
    {
        /// <summary>
        /// 元数据服务
        /// </summary>
        [InjectProperty]
        protected IMetaModelService MetaModelService { get; set; }

        /// <summary>
        /// 数据读取服务
        /// </summary>
        [InjectProperty]
        protected IDBService DBService { get; set; }

        /// <summary>
        /// 获取师傅信息：根据师傅Id 或 当前用户上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="masterId"></param>
        /// <returns></returns>
        public DynamicObject GetMasterByIdOrContext(UserContext userCtx, string masterId = "")
        {
            var htmlForm = this.MetaModelService.LoadFormModel(userCtx, "ydj_master");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));

            DynamicObject master = null;
            if (!masterId.IsNullOrEmptyOrWhiteSpace())
            {
                master = dm.Select(masterId) as DynamicObject;
            }
            if (master == null)
            {
                var where = "fphone=@fphone";
                var sqlParam = new List<SqlParam>
                {
                    new SqlParam("@fphone", System.Data.DbType.String, userCtx.UserName)
                };
                var reader = userCtx.GetPkIdDataReader(htmlForm, where, sqlParam);
                master = dm.SelectBy(reader)?.OfType<DynamicObject>()?.FirstOrDefault();
            }
            if (master == null)
            {
                throw new BusinessException("师傅信息不存在或者已被删除，请检查！");
            }

            return master;
        }

        /// <summary>
        /// 获取师傅未确认的提现收支记录金额
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="masterId"></param>
        /// <returns></returns>
        public decimal GetMasterUnConfirmedAmount(UserContext userCtx, string masterId)
        {
            if (masterId.IsNullOrEmptyOrWhiteSpace()) return 0;

            var sqlText = $@"
            select sum(fmoney) from t_pay_settleorder 
            where fmainorgid=@fmainorgid and foppid=@fmasterid and fbiztype='ydj_master' and fbiznumber=@fmasterid 
            and fpurpose='fpurpose_01' and fdirection='fdirection_02' and fbizpurpose='bizpurpose_03' and fpaystatus='fpaystatus_01'";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fmasterid", System.Data.DbType.String, masterId)
            };

            decimal sumAmount = 0;
            using (var reader = this.DBService.ExecuteReader(userCtx, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    if (!reader[0].IsNullOrEmptyOrWhiteSpace())
                    {
                        sumAmount = Convert.ToDecimal(reader[0]);
                    }
                }
            }

            return sumAmount;
        }

        /// <summary>
        /// 获取师傅的可结算金额
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="masterId"></param>
        /// <returns></returns>
        public decimal GetMasterCanSettleAmount(UserContext userCtx, string masterId)
        {
            if (masterId.IsNullOrEmptyOrWhiteSpace())
            {
                //从当前上下文中获取师傅信息
                var master = this.GetMasterByIdOrContext(userCtx);
                masterId = master["id"] as string;
            }

            //可结算金额 =（结算状态=“可结算/部分结算”的服务订单）的结算金额 - 已提现金额 - 未确认的提现收支记录金额
            var sqlText = $@"
            select sum(fexpectamount)-sum(fcashamount) from t_ydj_service 
            where fmainorgid=@fmainorgid and fmasterid=@fmasterid and fsettlestatus in('settle_status01_01','settle_status02_01')
            and freportdate is not null";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fmasterid", System.Data.DbType.String, masterId)
            };

            decimal settleAmount = 0;
            using (var reader = this.DBService.ExecuteReader(userCtx, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    if (!reader[0].IsNullOrEmptyOrWhiteSpace())
                    {
                        settleAmount = Convert.ToDecimal(reader[0]);
                    }
                }
            }
            if (settleAmount <= 0) return 0;

            //获取师傅未确认的提现收支记录金额
            var unConfirmedAmount = this.GetMasterUnConfirmedAmount(userCtx, masterId);

            settleAmount = settleAmount - unConfirmedAmount;

            return settleAmount > 0 ? settleAmount : 0;
        }

        /// <summary>
        /// 获取师傅的可提现金额
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="masterId"></param>
        /// <returns></returns>
        public decimal GetMasterCanCashAmount(UserContext userCtx, string masterId)
        {
            if (masterId.IsNullOrEmptyOrWhiteSpace())
            {
                //从当前上下文中获取师傅信息
                var master = this.GetMasterByIdOrContext(userCtx);
                masterId = master["id"] as string;
            }

            //可提现金额 = (系统日期 - 完工日期 >= 结算窗口期，并且 结算状态=“可结算/部分结算”的服务订单)的结算金额 - 已提现金额 - 未确认的提现收支记录金额
            //结算窗口期
            var settleWin = 7;
            var spService = userCtx.Container.GetService<ISystemProfile>();
            string paramJson = spService.GetProfile(userCtx, "fw", "ser_servicesysparam_parameter");
            if (!paramJson.IsNullOrEmptyOrWhiteSpace())
            {
                JObject joParamJson = JObject.Parse(paramJson);
                if (joParamJson != null)
                {
                    var settleWinStr = Convert.ToString(joParamJson["fsettlewin"]);
                    int.TryParse(settleWinStr, out settleWin);
                }
            }

            var sqlText = $@"
            select sum(fexpectamount)-sum(fcashamount) from t_ydj_service 
            where fmainorgid=@fmainorgid and fmasterid=@fmasterid and fsettlestatus in('settle_status01_01','settle_status02_01') 
            and freportdate is not null and datediff(day,freportdate,getdate())>=@settleWin";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fmasterid", System.Data.DbType.String, masterId),
                new SqlParam("@settleWin", System.Data.DbType.Int32, settleWin)
            };

            decimal cashAmount = 0;
            using (var reader = this.DBService.ExecuteReader(userCtx, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    if (!reader[0].IsNullOrEmptyOrWhiteSpace())
                    {
                        cashAmount = Convert.ToDecimal(reader[0]);
                    }
                }
            }
            if (cashAmount <= 0) return 0;

            //获取师傅未确认的提现收支记录金额
            var unConfirmedAmount = this.GetMasterUnConfirmedAmount(userCtx, masterId);

            cashAmount = cashAmount - unConfirmedAmount;

            return cashAmount > 0 ? cashAmount : 0;
        }
    }
}