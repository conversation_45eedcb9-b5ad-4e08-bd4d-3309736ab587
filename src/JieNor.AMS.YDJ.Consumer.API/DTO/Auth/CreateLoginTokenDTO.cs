using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.DTO
{
    /// <summary>
    /// 登录接口
    /// </summary>
    [Api("登录接口")]
    [Route("/consumerapi/createLoginToken")]
    public class CreateLoginTokenDTO : BaseAuthDTO
    {
        /// <summary>
        /// 客户id
        /// </summary>
        public string CustomerId { get; set; }
    }
}