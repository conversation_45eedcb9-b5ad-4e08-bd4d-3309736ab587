using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.Utils
{
    /// <summary>
    /// 北京时间类
    /// </summary>
    public static class BeiJingTime
    {
        /// <summary>
        /// 当前北京时间
        /// </summary>
        public static DateTime Now
        {
            get { return DateTime.UtcNow.AddHours(8.00); }
        }

        /// <summary>
        /// 当前北京时间的日期部分
        /// </summary>
        public static DateTime Today
        {
            get { return Now.Date; }
        }
    }
}