using System;
using JieNor.AMS.YDJ.Consumer.API.DTO;
using JieNor.AMS.YDJ.Consumer.API.Model;
using JieNor.AMS.YDJ.Consumer.API.Utils;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.BD.Product
{
    /// <summary>
    /// 微信小程序：商品属性取数接口
    /// </summary>
    public class ProductProfileController : BaseNotAuthController
    {
        /// <summary>
        /// 商品表单模型
        /// </summary>
        protected HtmlForm ProductForm { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ProductProfileDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<ProductProfileModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            //根据唯一标识获取数据
            this.ProductForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            var productObj = this.ProductForm.GetBizDataById(this.Context, dto.Id);
            if (productObj == null)
            {
                resp.Message = "商品不存在或已被删除！";
                resp.Success = false;
                return resp;
            }

            var productId = Convert.ToString(productObj["id"]);
            var salPrice = Convert.ToDecimal(productObj["fsalprice"]);

            //获取销售价，如果没有匹配到销售价目表销售价，则取商品销售价
            salPrice = ProductUtil.GetLocalSalPrice(this.Context, productId, dto.AuxPropVals, salPrice);

            //获取即时库存、在途量、预留量
            var realStockQty = ProductUtil.GetLocalRealStockQty(this.Context, productId, dto.AuxPropVals, dto.CustomDesc);
            var intransitQty = ProductUtil.GetIntransitQty(this.Context, productId, dto.AuxPropVals, dto.CustomDesc);
            var reserveQty = ProductUtil.GetReserveQty(this.Context, productId, dto.AuxPropVals, dto.CustomDesc);

            //可用量 = 库存量 + 在途量 - 预留量
            var usableQty = realStockQty + intransitQty - reserveQty;

            //设置响应数据包
            resp.Message = "取数成功！";
            resp.Success = true;
            resp.Data.SalPrice = salPrice;
            resp.Data.RealStockQty = realStockQty;
            resp.Data.IntransitQty = intransitQty;
            resp.Data.ReserveQty = reserveQty;
            resp.Data.UsableStockQty = usableQty;
            resp.Data.ImageList = ProductUtil.GetImages(this.Context, productObj, dto.AuxPropVals, dto.CustomDesc);

            return resp;
        }
    }
}