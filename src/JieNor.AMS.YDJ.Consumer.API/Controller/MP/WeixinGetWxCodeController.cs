using JieNor.AMS.YDJ.Consumer.API.DTO;
using JieNor.AMS.YDJ.Core.DataEntity.Weixin;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.MP
{
    /// <summary>
    /// 消费者小程序：获取小程序码
    /// </summary>
    public class WeixinGetWxCodeController : BaseNotAuthController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(WeixinGetWxaCodeDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            if (dto.Scene.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "参数 scene 不能为空！";
                resp.Success = false;
                return resp;
            }

            var weixinService = this.Container.GetService<IWeixinService>();

            var wxaCode = weixinService.GetConsumerWxaCode(this.Context, dto.Scene);

            if (wxaCode == null)
            {
                resp.Data = new WeixinWxaCode();
                resp.Message = "获取小程序码失败！";
                resp.Success = false;
            }
            else
            {
                resp.Data = wxaCode;
                resp.Message = "获取小程序码成功！";
                resp.Success = true;
            }

            return resp;
        }
    }
}