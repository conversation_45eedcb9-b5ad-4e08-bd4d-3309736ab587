using JieNor.AMS.YDJ.Consumer.API.DTO.Auth;
using JieNor.AMS.YDJ.Consumer.API.Model.Auth;
using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.Auth
{
    public class InviteRegisterListController : BaseNotAuthController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(InviteRegisterListDto dto)
        {

            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<BaseListPageData<InviteRegisterListModel>>
            {
                Data = new BaseListPageData<InviteRegisterListModel>()
            };

            resp.Data.List = MapTo();
            resp.Success = true;
            return resp;
        }

        /// <summary>
        /// 获取数据列表
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private List<InviteRegisterListModel> MapTo()
        {
            List<InviteRegisterListModel> res = null;
            res = new List<InviteRegisterListModel>();
            var noticeListModel = new InviteRegisterListModel();
            var profileService = this.Container.GetService<ISystemProfile>();
            var systemParameter = profileService.GetSystemParameter(this.Context, "bas_appletparameter");
            noticeListModel.Content = Convert.ToString(systemParameter["fcontent"]);
            res.Add(noticeListModel);
            return res;
        }
    }
}
