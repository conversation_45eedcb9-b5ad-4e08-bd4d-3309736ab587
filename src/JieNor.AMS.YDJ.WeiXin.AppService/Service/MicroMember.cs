using JieNor.AMS.YDJ.WeiXin.AppService.DTO;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using System.Collections.Generic;

namespace JieNor.AMS.YDJ.WeiXin.AppService.Service
{
    public class MicroMember : ServiceStack.Service
    {
        //解(绑)定用户
        public object Post(MicroMenberObject dto)
        {
            string mobile = dto.mobile;
            string openid = dto.openid;
            var container = this.TryResolve<IServiceContainer>().BeginNewLifetimeScope();
            var wxInvoker = container.GetService<IHttpMessageInvoker>();
            var resp = wxInvoker.Invoke(new CallerContext()
            {
                UserId = "ydj",
                UserName = openid,
                Product = "wx"
            }, new CommonBillDTO()
            {
                FormId = "wx_micromember",
                OperationNo = !dto.isbind ? "memberexit" : "memberbind",
                SimpleData = new Dictionary<string, string>() { { "openid", openid }, { "mobile", mobile } },
            });
            return (resp as DynamicDTOResponse).OperationResult;
        }
    }
}
 