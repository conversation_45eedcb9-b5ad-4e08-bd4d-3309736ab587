using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.OpValidation
{
    /// <summary>
    /// 仓位必录的通用校验器
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    [ServiceMetaAttribute("validationid", YDJHtmlElementType.HtmlValidator_StockLocationMustValidation)]
    public class StockLocationMustValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 仓位字段
        /// </summary>
        protected string StoreLocationIdFieldKey { get; set; }

        /// <summary>
        /// 初始化上下文时取得待校验的仓位字段标识
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);

            var jValidationParam = validExpr?.FromJson<JObject>();
            this.StoreLocationIdFieldKey = jValidationParam.GetJsonValue("fieldKey", "fstorelocationid");
            if (this.StoreLocationIdFieldKey.IsNullOrEmptyOrWhiteSpace())
            {
                throw new Exception("仓位校验操作参数配置错误，请显式提供仓位字段标识！");
            }
        }

        /// <summary>
        /// 编写仓位必填校验逻辑
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            //由于系统仓库资料通常不会太多，直接使用内存读取进行校验
            var storeLocationField = formInfo.GetField(this.StoreLocationIdFieldKey) as HtmlBaseDataEntryField;
            if (storeLocationField == null)
            {
                throw new Exception("仓位校验操作参数配置错误，请显式提供仓位字段标识！");
            }

            var linkStoreHouseField = formInfo.GetField(storeLocationField.ControlFieldKey) as HtmlBaseDataField;
            if (linkStoreHouseField == null)
            {
                throw new Exception("模型错误，仓位的控制字段未指向仓库字段！");
            }

            ValidationResult result = new ValidationResult();

            ExtendedDataEntitySet ds = new ExtendedDataEntitySet();
            ds.Parse(userCtx, dataEntities, formInfo);
            var allEntryObjs = ds.FindByEntityKey(storeLocationField.EntityKey);

            var allStoreHousePkIds = allEntryObjs.Select(o => linkStoreHouseField.DynamicProperty.GetValue<string>(o.DataEntity))
                .Distinct(StringComparer.OrdinalIgnoreCase);
            var storehouseFormMeta = this.MetaModelService.LoadFormModel(userCtx, "ydj_storehouse");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, storehouseFormMeta.GetDynamicObjectType(userCtx));
            var allStoreHouseObjs = dm.Select(allStoreHousePkIds)
                .OfType<DynamicObject>();

            foreach (var dataEntity in allEntryObjs)
            {
                var storelocationPkId = storeLocationField.DynamicProperty.GetValue<string>(dataEntity.DataEntity);
                if (!storelocationPkId.IsEmptyPrimaryKey()) continue;

                var storehousePkId = linkStoreHouseField.DynamicProperty.GetValue<string>(dataEntity.DataEntity);
                var storehouseObj = allStoreHouseObjs.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(storehousePkId));
                if (storehouseObj == null) continue;
                //是否开启仓位管理
                var isEnableLocationMgr = ((DynamicObjectCollection)storehouseObj["fentity"]).Where(o => !o["flocnumber"].IsNullOrEmptyOrWhiteSpace()).Any();
                if (isEnableLocationMgr
                    // 临时针对该经销商（1003829）取消仓位必录校验
                    && !(this.Context.Companys.FirstOrDefault(s => s.CompanyId.EqualsIgnoreCase(this.Context.Company))?.CompanyNumber.EqualsIgnoreCase("1003829") ?? false)
                    )
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        DataEntity = dataEntity.DataEntity.GetRoot(),
                        ErrorCode = "storelocation not empty",
                        ErrorMessage = $"第{dataEntity.RowIndex + 1}行{linkStoreHouseField.Caption}【{storehouseObj["fname"]}】已启用了仓位管理，必须录入仓位！"
                    });
                }
            }

            return result;
        }
    }
}
