using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Stock.AppService.SchedulerTask
{
    /// <summary>
    /// 运维问题：计划任务定时自动计算运维问题相关时间
    /// </summary>
    [InjectService]
    [TaskSvrId("autocalculatepropelevanttime")]
    [Caption("自动计算运维问题相关时间")]
    //[TaskMultiInstance()]
    [Browsable(false)]

    public class AutoCalculateProRelevantTime : AbstractScheduleWorker
    {

        /// <summary>
        /// 执行任务逻辑
        /// </summary>
        /// <returns></returns>
        protected override async Task DoExecute()
        {
            //获取未关闭的运维问题单据
            var odjs = this.UserContext.LoadBizDataByFilter("ydj_maintenance", " fquestionprogress!='q_progress_07'");

            foreach (var item in odjs)
            {

                //先重置
                item["fisoutdeadline"] = "0"; //是否延期
                item["foutdeadlinedays"] = ""; //延期天数
                item["fwarningdelay"] = ""; //预警延期
                item["fisresponsedelayed"] = "0"; //响应是否延期

                var maintenanceType = item["fmaintenancetype"];  //运维分类
                var submitDate = item["fsubmitdate"];  //提单时间
                var shouldDoneDate = item["fshoulddonedate"];  // 问题应解决时间
                var actSettleTime = item["factsettletime"];    // 开发实际解决时间
                var endanAlysisTime = item["fendanalysistime"];    // 运维结束分析时间

                var isApplyDelay = JNConvert.ToStringAndTrim(item["fisapplydelay"]);  //已申请延期
                var estReleaseDate = JNConvert.ToStringAndTrim(item["festreleasedate"]);  //预计发版时间

                var zenPathNumber = JNConvert.ToStringAndTrim(item["fzenpathnumber"]);  //禅道号
                var questionProgress = JNConvert.ToStringAndTrim(item["fquestionprogress"]);  //问题进度

                var shouldDoneDateIsOk = DateTime.TryParse(Convert.ToString(shouldDoneDate), out var realShouldDoneDate);
                var actSettleTimeIsOk = DateTime.TryParse(Convert.ToString(actSettleTime), out var realActSettleTime);
                var estReleaseDateIsOk = DateTime.TryParse(Convert.ToString(estReleaseDate), out var realestReleaseDate);
                var submitDateIsOk = DateTime.TryParse(Convert.ToString(submitDate), out var realSubmitDate);

                var currentTime = DateTime.Now;

                if (isApplyDelay.Equals("1") || isApplyDelay.Equals("true") || isApplyDelay.Equals("True"))
                {
                    //勾选了已申请延期

                    if (estReleaseDateIsOk)
                    {
                        if (actSettleTimeIsOk)
                        {
                            if (realActSettleTime > realestReleaseDate)
                            {
                                item["fisoutdeadline"] = "1"; //是否延期

                                TimeSpan timeDifference = realActSettleTime - realestReleaseDate;
                                var days = Math.Round(timeDifference.TotalDays, 5).ToString("0.00000");
                                item["foutdeadlinedays"] = days; //延期天数

                                if (submitDateIsOk)
                                {
                                    TimeSpan timeDifference1 = realestReleaseDate - realSubmitDate;
                                    var days1 = Math.Round(timeDifference1.TotalDays, 5).ToString("0.00000");
                                    item["fwarningdelay"] = days1; //预警延期
                                }
                                
                            }
                        }
                        else
                        {
                            if (currentTime > realestReleaseDate)
                            {
                                item["fisoutdeadline"] = "1"; //是否延期

                                TimeSpan timeDifference = currentTime - realestReleaseDate;
                                var days = Math.Round(timeDifference.TotalDays, 5).ToString("0.00000");
                                item["foutdeadlinedays"] = days; //延期天数

                                if (submitDateIsOk)
                                {
                                    TimeSpan timeDifference1 = realestReleaseDate - realSubmitDate;
                                    var days1 = Math.Round(timeDifference1.TotalDays, 5).ToString("0.00000");
                                    item["fwarningdelay"] = days1; //预警延期
                                }
                            }
                        }
                    }
                    
                }
                else
                {
                    //未勾选已申请延期

                    //原算法
                    if (!maintenanceType.IsNullOrEmptyOrWhiteSpace() && !submitDate.IsNullOrEmptyOrWhiteSpace())
                    {
                        if (submitDateIsOk)
                        {
                            int year = realSubmitDate.Year;
                            int month = realSubmitDate.Month;
                            int day = realSubmitDate.Day;

                            //DateTime specifiedTime = new DateTime(year, month, day, 00, 00, 00);

                            if (shouldDoneDateIsOk)
                            {
                                if (realActSettleTime > realShouldDoneDate)
                                {
                                    item["fisoutdeadline"] = "1"; //是否延期
                                    if (actSettleTimeIsOk)
                                    {
                                        TimeSpan timeDifference = realActSettleTime - realShouldDoneDate;
                                        var days = Math.Round(timeDifference.TotalDays, 5).ToString("0.00000");
                                        item["foutdeadlinedays"] = days; //延期天数

                                    }
                                }

                                TimeSpan timeDifference1 = realShouldDoneDate - realSubmitDate;
                                var days1 = Math.Round(timeDifference1.TotalDays, 5).ToString("0.00000");
                                item["fwarningdelay"] = days1; //预警延期
                            }
                        }

                    }

                    //补偿算法（禅道号不为空，而开发实际解决时间为空，且问题进度不为待观察的）
                    if (!zenPathNumber.IsNullOrEmptyOrWhiteSpace() && !actSettleTimeIsOk && !"q_progress_08".Equals(questionProgress))
                    {
                        switch (maintenanceType)
                        {
                            case "maintenancetype_01":
                            case "maintenancetype_02":
                            case "maintenancetype_03":
                            case "maintenancetype_04":
                            case "maintenancetype_05":
                            case "maintenancetype_06":
                                if (shouldDoneDateIsOk && currentTime>realShouldDoneDate)
                                {
                                    item["fisoutdeadline"] = "1"; //是否延期

                                    TimeSpan timeDifference = currentTime - realShouldDoneDate;
                                    var days = Math.Round(timeDifference.TotalDays, 5).ToString("0.00000");
                                    item["foutdeadlinedays"] = days; //延期天数
                                }
                                if (submitDateIsOk)
                                {
                                    TimeSpan timeDifference1 = currentTime - realSubmitDate;
                                    var days1 = Math.Round(timeDifference1.TotalDays, 5).ToString("0.00000");
                                    item["fwarningdelay"] = days1; //预警延期
                                }
                                break;
                        }
                    }
                }

                //响应相关
                if (!maintenanceType.IsNullOrEmptyOrWhiteSpace() && submitDateIsOk)
                {
                    var httpGateway = this.UserContext.Container.GetService<IHttpServiceInvoker>();

                    var res = httpGateway.InvokeBillOperation(this.UserContext, "ydj_maintenance", null, "getshouldresponsetime", new Dictionary<string, object>() { { "maintenanceType", maintenanceType }, { "submitDate", submitDate } });

                    if (res.IsSuccess)
                    {
                        var shouldResponseTimeIsOk = DateTime.TryParse(Convert.ToString(res.SrvData), out var realShouldResponseTime);
                        var endanAlysisTimeIsOk = DateTime.TryParse(Convert.ToString(endanAlysisTime), out var realEndanAlysisTime);

                        if (shouldResponseTimeIsOk)
                            item["fshouldresponsetime"] = realShouldResponseTime;  //应响应时间

                        if (shouldResponseTimeIsOk && endanAlysisTimeIsOk && realEndanAlysisTime > realShouldResponseTime)
                            item["fisresponsedelayed"] = "1"; //响应是否延期
                    }

                }

            }

            var htmlForm = this.UserContext.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.UserContext, "ydj_maintenance");
            //操作数据库
            var dmService = this.UserContext.Container.GetService<IDataManager>();
            //初始化上下文
            dmService.InitDbContext(this.UserContext, htmlForm.GetDynamicObjectType(this.UserContext));
            // 直接保存数据库
            if (!odjs.IsNullOrEmptyOrWhiteSpace() || odjs.Count > 0)
                dmService.Save(odjs);
        }



    }
}
