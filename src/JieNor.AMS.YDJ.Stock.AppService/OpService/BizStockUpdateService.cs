using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface.StockUpdate;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore;
using Newtonsoft.Json.Linq;
using JieNor.AMS.YDJ.Core.Interface.Stock;
using JieNor.Framework.Interface.Log;

namespace JieNor.AMS.YDJ.Stock.AppService.OpService
{
    /// <summary>
    /// 库存更新服务
    /// </summary>    
    [InjectService("updateinventory")]
    [ServiceMetaAttribute("name", "库存更新服务")]
    [ServiceMetaAttribute("serviceid", YDJHtmlElementType.HtmlBizService_UpdateInventory)]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class BizStockUpdateService : AbstractBaseService
    {
        /// <summary>
        /// 库存更新配置
        /// </summary>
        public StockUpdateSetting Settings { get; private set; }

        /// <summary>
        /// 即时库存中不作为库存维度的字段
        /// </summary>
        private List<string> IgnoreInvFlexFieldKeys = new List<string>(){
                    "fqty",
                    "famount",
                    "fstockqty",
                    "fmainorgid",
                    "ftranid",
                    "fcostprice",
                    "fcostamt",
                    "fvolumeunit",
                    "ftotalvolume",
                    "fsinglevolume",
                };

        private List<string> AllInvFlexFieldKeys = new List<string>();

        private IInventoryService InventoryService { get; set; }

        private LoadReferenceObjectManager RefMgr { get; set; }

        /// <summary>
        /// 服务初始化时，检查库存配置参数是否缺失
        /// </summary>
        /// <param name="servicePara"></param>
        protected override void OnServiceInitialized(string servicePara)
        {
            base.OnServiceInitialized(servicePara);

            //服务参数
            if (servicePara.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("库存更新服务参数未配置！");
            }
            JObject joServicePara = JObject.Parse(servicePara);
            if (joServicePara == null)
            {
                throw new BusinessException("库存更新服务参数未配置！");
            }

            var stockSetting = joServicePara.GetJsonValue("serConfig", "");
            if (stockSetting.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("库存更新服务参数未配置！");
            }

            this.Settings = stockSetting.FromJson<StockUpdateSetting>(true);
            if (this.Settings == null)
            {
                throw new BusinessException("库存更新服务参数未配置！");
            }

            var invFormMeta = this.MetaModelService.LoadFormModel(this.Context, "stk_inventorylist");

            foreach (var invFlexField in invFormMeta.GetFieldList(f => f.ListTabIndex))
            {
                if (this.IgnoreInvFlexFieldKeys.Contains(invFlexField.Id, StringComparer.OrdinalIgnoreCase)) continue;
                if (invFlexField.Entity is HtmlHeadEntity && !(invFlexField is HtmlBasePropertyField))
                {
                    this.AllInvFlexFieldKeys.Add(invFlexField.PropertyName);
                }
            }

            this.InventoryService = this.Container.GetService<IInventoryService>();
            this.RefMgr = this.Container.GetService<LoadReferenceObjectManager>();
        }

        /// <summary>
        /// 库存更新服务逻辑实现
        /// </summary>
        /// <param name="dataEntities"></param>
        public override void ExecuteService(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null
                || dataEntities.Any() == false) return;

            this.InventoryService.CheckStockLock(this.Context, this.HtmlForm, dataEntities);

            var dt = this.HtmlForm.GetDynamicObjectType(this.Context);
            this.RefMgr.Load(this.Context, dt, dataEntities, true);

            var seqSvc = this.Container.GetService<ISequenceService>();
            var seqId = seqSvc.GetSequence<string>();

            // 库存日志
            this.Option.TryGetVariableValue("__StockLog__", out StringBuilder log);

            if (log != null)
            {
                try
                {
                    int times = this.Option.GetVariableValue("__StockTimes__", 0);
                    times++;

                    this.Option.SetVariableValue("__StockTimes__", times);

                    log.AppendLine();
                    log.AppendLine("---------------------库存更新执行事件---------------------------");
                    log.AppendLine("[DataEntity]:" + dataEntities?.ToJson(true));
                }
                catch { }
            }

            StockUpdateObject inventoryData = new StockUpdateObject(this.Context, seqId);
            inventoryData.Setting = this.Settings;
            inventoryData.HtmlForm = this.HtmlForm;

            var extendedDataSet = new ExtendedDataEntitySet();
            extendedDataSet.Parse(this.Context, dataEntities, this.HtmlForm);
            var stockEntryObjs = extendedDataSet.FindByEntityKey(this.Settings.ActiveEntityKey);
            this.AddInvBillEntryObj(inventoryData, stockEntryObjs);

            var stockUpdService = this.Container.GetService<IStockUpdateService>();
            var result = stockUpdService.UpdateInventory(this.Context, new StockUpdateObject[] { inventoryData }, this.Option);

            this.Result.MergeResult(result);

            if (result?.IsSuccess == false)
            {
                //throw new BusinessException($"{this.OperationName}操作失败：");
                result.ThrowIfHasError(true, $"{this.OperationName}操作失败");
            }

            if (log != null)
            {
                try
                {
                    log.AppendLine("[库存更新调试信息]:");
                    log.AppendLine("[StockBillForm]:" + this.HtmlForm.FormCacheId);
                    log.AppendLine("[StockBillObjs]:" + stockEntryObjs?.Select(s => new { s.BillNo, s.DataEntity }).ToJson(true));
                    log.AppendLine("[StockUpdateSetting]:" + inventoryData.Setting.ToJson(true));
                    log.AppendLine("[AllInvFlexFieldKeys]:" + this.AllInvFlexFieldKeys.ToJson(true));
                }
                catch { }
            }

            inventoryData.Dispose(); 
        }

        /// <summary>
        /// 转换库存单据行对象为库存维度字段值字典
        /// </summary>
        /// <param name="inventoryData"></param>
        /// <param name="stockBillObjs"></param>
        private void AddInvBillEntryObj(StockUpdateObject inventoryData, IEnumerable<ExtendedDataEntity> stockBillObjs)
        {
            if (inventoryData == null || stockBillObjs == null) return;

            var activeEntity = inventoryData.HtmlForm.GetEntity(inventoryData.Setting.ActiveEntityKey);

            //销售退货单的成本，取其对应的销售出库单的成本
            var soReturnDatas = new List<DynamicObject>();
            if (inventoryData.HtmlForm.Id.EqualsIgnoreCase("stk_sostockreturn"))
            {
                var soBillIds = stockBillObjs.Select(f => f.DataEntity["fsooutstockinterid"]?.ToString())?.Where(f => f.IsNullOrEmptyOrWhiteSpace() == false).ToList();
                if (soBillIds != null && soBillIds.Any())
                {
                    var sql = " select fid,fentryid,fcostprice,fcostamt from T_STK_SOSTOCKOUTENTRY where fid in ({0}) ".Fmt(soBillIds.JoinEx(",", true));
                    soReturnDatas.AddRange(this.DBService.ExecuteDynamicObject(this.Context, sql).ToList());
                }
            }
            var grpRetDatas = soReturnDatas.GroupBy(f => f["fentryid"]?.ToString()).ToList();

            foreach (var stockBillRowObj in stockBillObjs)
            {
                var rootBillObj = stockBillRowObj.DataEntity.GetRoot();
                var pkId = rootBillObj.GetPrimaryKeyValue<string>();
                if (pkId.IsEmptyPrimaryKey()) continue;

                var billNo = this.HtmlForm.GetNumberField()?.DynamicProperty?.GetValue<string>(rootBillObj) ?? "";

                var invUpdateObj = inventoryData.TempInvDataType.CreateInstance() as DynamicObject;
                invUpdateObj["fbillinterid"] = pkId;
                invUpdateObj["fbillformid"] = this.HtmlForm.Id;
                invUpdateObj["fbillno"] = billNo;
                if (!(activeEntity is HtmlHeadEntity))
                {
                    invUpdateObj["fbillentryid"] = stockBillRowObj["id"];
                }
                invUpdateObj["fbillno"] = billNo;
                invUpdateObj["fbillentryseq"] = stockBillRowObj["fseq"];

                var materialIdFldKey = "";
                inventoryData.Setting.InvFlexFieldSetting.TryGetValue("fmaterialid", out materialIdFldKey);
                if (materialIdFldKey.IsNullOrEmptyOrWhiteSpace()) materialIdFldKey = "fmaterialid";
                var materialIdField = this.HtmlForm.GetField(materialIdFldKey) as HtmlBaseDataField;
                if (materialIdField != null)
                {
                    var materialObj = materialIdField.RefDynamicProperty?.GetValue<DynamicObject>(stockBillRowObj.DataEntity);
                    invUpdateObj["fmaterialname"] = $"{materialObj?["fname"]}({materialObj?["fnumber"]})";

                    //如果是套件商品，则不需要更新库存
                    var isSuite = Convert.ToBoolean(materialObj?["fsuiteflag"] ?? false);
                    if (isSuite)
                    {
                        continue;
                    }
                }


                foreach (var flexKey in this.AllInvFlexFieldKeys)
                {
                    string mapFldKey = "";
                    inventoryData.Setting.InvFlexFieldSetting.TryGetValue(flexKey, out mapFldKey);
                    if (mapFldKey.IsNullOrEmptyOrWhiteSpace()) mapFldKey = flexKey;

                    var mapField = this.HtmlForm.GetField(mapFldKey);
                    if (mapField == null)
                    {
                        continue;
                    }
                    invUpdateObj[flexKey] = stockBillRowObj[mapField.PropertyName];
                }
                var mapQtyField = this.HtmlForm.GetField(inventoryData.Setting.QtyFieldKey);
                var qty = mapQtyField.DynamicProperty.GetValue<decimal>(stockBillRowObj.DataEntity);

                // 非盘点单，且更新操作为入库或出库，且数量为0时，抛出错误提示
                if (!this.HtmlForm.FormCacheId.EqualsIgnoreCase("stk_inventoryverify") && inventoryData.Setting.Factor != 0 && qty == 0)
                {
                    var logger = this.Container.GetService<ILogServiceEx>();

                    StringBuilder info = new StringBuilder();
                    info.AppendLine("[库存更新调试信息]:");
                    info.AppendLine("[StockBillForm]:" + this.HtmlForm.FormCacheId);
                    info.AppendLine("[StockBillObjs]:" + stockBillObjs.Select(s => new { s.BillNo, s.DataEntity }).ToJson());
                    info.AppendLine("[StockUpdateSetting]:" + inventoryData.Setting.ToJson());
                    info.AppendLine("[AllInvFlexFieldKeys]:" + this.AllInvFlexFieldKeys.ToJson());

                    logger.Info(info.ToString());

                    throw new BusinessException("库存更新失败：出库/入库数量不能为0");
                }

                invUpdateObj["fqty"] = qty * inventoryData.Setting.Factor;
                invUpdateObj["fsrcqty"] = qty;
                var mapStockQtyField = this.HtmlForm.GetField(inventoryData.Setting.StockQtyFieldKey);
                invUpdateObj["fstockqty"] = mapStockQtyField.DynamicProperty.GetValue<decimal>(stockBillRowObj.DataEntity) * inventoryData.Setting.Factor;
                var mapAmountField = this.HtmlForm.GetField(inventoryData.Setting.AmountFieldKey);
                invUpdateObj["famount"] = mapAmountField.DynamicProperty.GetValue<decimal>(stockBillRowObj.DataEntity) * inventoryData.Setting.Factor;

                //取出入库成本
                if (!inventoryData.Setting.CostAmtFieldKey.IsNullOrEmptyOrWhiteSpace())
                {
                    var mapCostAmtField = this.HtmlForm.GetField(inventoryData.Setting.CostAmtFieldKey);
                    if (mapCostAmtField != null)
                    {
                        invUpdateObj["fcostamt"] = mapCostAmtField.DynamicProperty.GetValue<decimal>(stockBillRowObj.DataEntity) * inventoryData.Setting.Factor;
                        if (qty > 0)
                        {
                            invUpdateObj["fcostprice"] = Math.Round(mapCostAmtField.DynamicProperty.GetValue<decimal>(stockBillRowObj.DataEntity) / qty, 6);
                        }
                    }
                }

                if (inventoryData.HtmlForm.Id.EqualsIgnoreCase("stk_sostockreturn") && grpRetDatas != null)
                {
                    var retRow = grpRetDatas.FirstOrDefault(f => f.Key == stockBillRowObj["fsourceentryid"].ToString());
                    if (retRow != null && retRow.ToList().Any())
                    {
                        var price = Convert.ToDecimal(retRow.First()["fcostprice"]);
                        invUpdateObj["fcostprice"] = price;
                        invUpdateObj["fcostamt"] = Math.Round(price * qty, 6);
                    }
                }
                else if (inventoryData.HtmlForm.Id.EqualsIgnoreCase("stk_inventorytransfer") && inventoryData.Setting.Factor == 1 && inventoryData.Setting.UpdInvServiceId == "to")
                {
                    //调拨入库成本=调拨出库成本
                    var mapCostAmtField = this.HtmlForm.GetField("fcostamt");
                    if (mapCostAmtField != null)
                    {
                        invUpdateObj["fcostamt"] = mapCostAmtField.DynamicProperty.GetValue<decimal>(stockBillRowObj.DataEntity) * inventoryData.Setting.Factor;
                    }
                    var mapCostPriceField = this.HtmlForm.GetField("fcostprice");
                    if (mapCostPriceField != null)
                    {
                        invUpdateObj["fcostprice"] = mapCostPriceField.DynamicProperty.GetValue<decimal>(stockBillRowObj.DataEntity) * inventoryData.Setting.Factor;
                    }
                }
                inventoryData.InvBillObjs.Add(invUpdateObj);
            }
        }
    }
}
