using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface.StockUpdate;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore;
using Newtonsoft.Json.Linq;
using JieNor.AMS.YDJ.DataTransferObject.Reserve;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.AMS.YDJ.Core.Reserve;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp;

namespace JieNor.AMS.YDJ.Stock.AppService.OpService
{
    /// <summary>
    /// 库存可用量校验服务
    /// </summary>    
    [InjectService("bizinventoryusablecheck")]
    [ServiceMetaAttribute("name", "库存可用量校验服务")]
    [ServiceMetaAttribute("serviceid", YDJHtmlElementType.HtmlBizService_InventoryUsableCheck)]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class BizInventoryUsableCheck : AbstractBaseService
    {
        public override string ParamFormId => "sys_invusablecheckparameditor";

        protected BizInvCheckPara Setting { get; set; }

        /// <summary>
        /// 服务初始化时，检查库存配置参数是否缺失
        /// </summary>
        /// <param name="servicePara"></param>
        protected override void OnServiceInitialized(string servicePara)
        {
            base.OnServiceInitialized(servicePara);

            //服务参数
            if (servicePara.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("库存可用量校验服务参数未配置！");
            }
            JObject joServicePara = JObject.Parse(servicePara);
            if (joServicePara == null)
            {
                throw new BusinessException("库存可用量校验服务参数未配置！");
            }

            var stockSetting = joServicePara.GetJsonValue("serConfig", "");
            if (stockSetting.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("库存可用量校验服务参数未配置！");
            }

            this.Setting = stockSetting.FromJson<BizInvCheckPara>();
            this.Setting.OperationNo = this.OperationNo;
            // 考虑预留
            this.Setting.freserveqty = true;
            // 使用未审核的库存单据作为预留量来源
            this.Setting.ReserveSource = 1;
            // 使用仓库+仓位+库存状态控制
            this.Setting.fctrlfldkey = new BaseDataSummary
            {
                Id = "stk_inv_ctrl_item_fstockstatus,stk_inv_ctrl_item_fstorehouseid,stk_inv_ctrl_item_fstorelocationid"
            };
            // 使用严格控制
            this.Setting.fctrlstrength = new BaseDataSummary
            {
                Id = "1"
            };
        }

        /// <summary>
        /// 库存更新服务逻辑实现
        /// </summary>
        /// <param name="dataEntities"></param>
        public override void ExecuteService(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null
                || dataEntities.Any() == false) return;

            this.Container.GetService<LoadReferenceObjectManager>()?
                .Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), dataEntities, true);

            var stockUpdService = this.Container.GetService<IBizCheckInvService>();
            var result = stockUpdService.BizCheckInventory(this.Context, this.HtmlForm, dataEntities, this.Setting, this.Option);

            var vali = result.Where(f => f.fqty < 0)?.ToList();
            if (vali.Count > 0)
            {
                this.Result.IsSuccess = false;

                var errMsg = "库存可用量校验不通过";
                var materialIds = vali.Select(t => t.fmaterialid).ToList();
                var storeHouseIds = vali.Select(t => t.fstorehouseid).ToList();
                if (materialIds != null && materialIds.Any())
                {
                    var productObjs = this.Context.LoadBizBillHeadDataById("ydj_product", materialIds, "fnumber,fname");
                    var storeHouseObjs = this.Context.LoadBizDataById("ydj_storehouse", storeHouseIds);
                    if (productObjs != null && productObjs.Any())
                    {
                        errMsg += "，商品如下：";
                        foreach (var valiObj in vali)
                        {
                            var product = productObjs.Where(t => Convert.ToString(t["id"]).EqualsIgnoreCase(valiObj.fmaterialid)).FirstOrDefault();
                            var storeHouse = "无";//仓库
                            var storeLocation = "无";//仓位
                            var shObj = storeHouseObjs.Where(t => Convert.ToString(t["id"]).EqualsIgnoreCase(valiObj.fstorehouseid)).FirstOrDefault();
                            if (shObj != null)
                            {
                                storeHouse = Convert.ToString(shObj["fname"]);
                                var locationEnts = shObj["fentity"] as DynamicObjectCollection;
                                if (locationEnts != null && locationEnts.Any())
                                {
                                    var lcObj = locationEnts.Where(t => Convert.ToString(t["id"]).EqualsIgnoreCase(valiObj.fstorelocationid)).FirstOrDefault();
                                    if (lcObj != null)
                                    {
                                        storeLocation = Convert.ToString(lcObj["flocname"]);
                                    }
                                }
                            }
                            errMsg += $"【{storeHouse}：{storeLocation}】【{Convert.ToString(product["fnumber"])}】{Convert.ToString(product["fname"])}，";
                        }
                        errMsg = errMsg.TrimEnd('，') + "。";
                    }
                }

                var invException = new InteractionException("invusablecheck{0}".Fmt(this.Context.UserId), errMsg);
                invException.InteractionData.CustomFormParameter = new Dictionary<string, string>();
                invException.InteractionData.CustomFormId = "sys_invusablecheckresult";
                var meta = this.MetaModelService.LoadFormModel(this.Context, "sys_invusablecheckresult");
                var billData = meta.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;

                billData["fmessge"] = "库存可用量不足！点击【取消】取消本次操作！";

                var enRows = billData["fentry"] as DynamicObjectCollection;
                foreach (var item in vali)
                {
                    var enRow = enRows.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                    enRow["fmaterialid"] = item.fmaterialid;
                    enRow["fattrinfo"] = item.fattrinfo;
                    enRow["fattrinfo_e"] = item?.fattrinfo_e;
                    enRow["fcustomdesc"] = item.fcustomdesc;
                    enRow["fmtono"] = item.fmtono;

                    enRow["funitid"] = item.funitid;
                    enRow["fbizunitid"] = item.fbizunitid;

                    enRow["fstorehouseid"] = item.fstorehouseid;
                    enRow["fstorelocationid"] = item.fstorelocationid;
                    enRow["fstockstatus"] = item.fstockstatus;

                    enRow["fqty"] = item.fqty_bill;
                    enRow["fqty_inv"] = item.fqty_inv;
                    enRow["fqty_reserve"] = item.fqty_reserve;
                    enRow["fqty_onway"] = item.fqty_onWay;
                    enRow["fqty_diff"] = item.fqty;

                    enRows.Add(enRow);
                }

                var dt = meta.GetDynamicObjectType(this.Context, true);
                var svcRef = this.Container.GetService<LoadReferenceObjectManager>();
                svcRef?.Load(this.Context, dt, new DynamicObject[] { billData }, false);

                //根据基本单位数量自动反算关联业务单位数量字段（如库存单位，业务单位对应的数量）
                var unitService = this.Container.GetService<IUnitConvertService>();
                unitService.ConvertByBasQty(this.Context, meta, new List<DynamicObject>() { billData }, this.Option);

                var uiConverter = this.Container.GetService<IUiDataConverter>();
                var billJsonData = uiConverter.CreateUIDataObject(this.Context, meta, billData);
                var uiData = billJsonData.GetJsonValue<JObject>("uiData", new JObject()).ToString();

                invException.InteractionData.CustomFormParameter.Add("uiData", uiData);
                throw invException;
            }

        }
    }
}
