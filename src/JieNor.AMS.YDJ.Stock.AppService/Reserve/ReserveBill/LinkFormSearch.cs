using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.Core.Interface; 
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve.ReserveBill
{
    /// <summary>
    /// 销售合同：联查
    /// </summary>
    [InjectService]
    [FormId("stk_reservebill")]
    [OperationNo("LinkFormSearch")]
    public class LinkFormSearch : AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
            switch (e.EventName)
            {
                case "dealLinkForm":
                    DealLinkForm(e);
                    break;
                default:
                    return;
            }
        }

        private void DealLinkForm(OnCustomServiceEventArgs e)
        {
            if (e.DataEntities == null || e.DataEntities.Length <= 0) return;

            var eventData = e.EventData as Dictionary<string, object>;
            var linkFormDatas = eventData["linkFormDatas"] as List<Dictionary<string, object>>;
            if (linkFormDatas == null)
            {
                return;
            }

            var userContext = eventData["userContext"] as UserContext;
            if (userContext == null)
            {
                return;
            }

            DealLinkForm(userContext, e.DataEntities, linkFormDatas);
        }


        protected void DealLinkForm(UserContext userContext, DynamicObject[] dataEntities, List<Dictionary<string, object>> linkFormDatas)
        {
            var metaModelService = userContext.Container.GetService<IMetaModelService>();
            var grpDatas = dataEntities.Where(f => Convert.ToString(f["fsourcetype"]).IsNullOrEmptyOrWhiteSpace() == false).GroupBy(f => Convert.ToString(f["fsourcetype"])).ToList();
            foreach (var item in grpDatas)
            {
                var formMeta = metaModelService.LoadFormModel(userContext, item.Key);
                var pkids = item.ToList().Select(o => o["fsourcepkid"]?.ToString()).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
                var filterStr = pkids.Count == 1 ? $"fid = '{pkids[0]}'" :
                                               $"fid in ({string.Join(",", pkids.Select(x => $"'{x}'"))})";
                linkFormDatas.Add(new Dictionary<string, object>
                                {
                                    { "formId", formMeta.Id },
                                    { "formCaption", formMeta.Caption },
                                    { "flag", "nextForm" },
                                    { "filterString", filterStr },
                                });
            } 
        }
         


    }
}