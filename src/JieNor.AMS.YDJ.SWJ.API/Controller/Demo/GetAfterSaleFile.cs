using JieNor.AMS.YDJ.SWJ.API.APIs;
using JieNor.AMS.YDJ.SWJ.API.DTO.Order;
using JieNor.AMS.YDJ.SWJ.API.Response;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using System;

namespace JieNor.AMS.YDJ.SWJ.API.Controller.Demo
{
    public class GetAfterSaleFile : BaseController
    {
        public object Any(AfterOrderDTO dto)
        {
            base.InitializeOperationContext(dto, true);
            var resp = new BaseResponse<object>();

            var res = new OrderAPI(this.Context).GetAfterSaleOrder(SWJClient.Instance, dto.factoryOrderId);
            if (res == null) return resp;
            resp.Success = res.success;
            resp.Data = new
            {
                schemeContent = res.data.schemeContent,
                schemeId = res.data.schemeId
            };
            return resp;
        }
    }
}
