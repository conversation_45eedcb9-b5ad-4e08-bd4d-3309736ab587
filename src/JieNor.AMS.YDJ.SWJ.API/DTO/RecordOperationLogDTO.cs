using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.SWJ.API.Filter;

namespace JieNor.AMS.YDJ.SWJ.API.DTO
{
    /// <summary>
    /// 记录集成操作日志
    /// </summary>
    [OperationLogFilter]
    public abstract class RecordOperationLogDTO : BaseDTO
    {
        /// <summary>
        /// 业务对象标识
        /// </summary>
        internal abstract string RecordFormId { get; }

        /// <summary>
        /// 业务对象编码
        /// </summary>
        internal virtual string RecordBillNo { get; set; }

        /// <summary>
        /// 成功对象编码
        /// </summary>
        internal virtual string fsuccessnumber { get; set; }

        /// <summary>
        /// 失败对象编码
        /// </summary>
        internal virtual string ffailnumber { get; set; }
    }
}
