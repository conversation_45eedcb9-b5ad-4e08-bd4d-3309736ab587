using System;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.AMS.YDJ.SWJ.API.Request.SWJ;
using JieNor.AMS.YDJ.SWJ.API.Response.SWJ;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;

namespace JieNor.AMS.YDJ.SWJ.API.APIs
{
    /// <summary>
    /// 采购相关接口
    /// </summary>
    public class PurchaseOrderAPI
    {

        /// <summary>
        /// 数据回传到三维家
        /// </summary>
        /// <param name="client"></param>
        /// <param name="platformOrderId"></param>
        /// <param name="platformOrderCode"></param>
        /// <param name="customSaleOrderNo"></param>
        /// <param name="customOrderItemNo"></param>
        /// <returns></returns>
        public static SWJResponse updateFactoryOrderCustomInfo(SWJClient client, string platformOrderId, string platformOrderCode, string customSaleOrderNo,string customOrderItemNo)
        {
            try
            {
                string accessToken = client.GetAccessToken();
                //正式环境需要使用这个配置
                string url = $"/common/api/v2/aimesOrder/updateFactoryOrderCustomInfo?sysCode=external&access_token={accessToken}";

                var request = new UpdateFactoryOrderCustomInfoRequest
                {
                    platformOrderId = platformOrderId,
                    platformOrderCode = platformOrderCode,
                    customSaleOrderNo = customSaleOrderNo,
                    customOrderItemNo = customOrderItemNo
                };

                var resp = JsonClient.InvokeThirdByJson<UpdateFactoryOrderCustomInfoRequest, SWJResponse>(client.OpenServer, url, request);

                return resp;
            }
            catch (Exception ex)
            {
                return new SWJResponse()
                {
                    errorMessage = ex.Message + "；StackTrace：" + ex.StackTrace
                };
            }
        }
        
        /// <summary>
        /// 采购订单回传三维家
        /// </summary>
        /// <param name="client"></param>
        /// <param name="platformOrderId"></param>
        /// <param name="platformOrderCode"></param>
        /// <param name="externalContractCode"></param>
        /// <returns></returns>
        public static SWJResponse updateOrderExternalContractCode(SWJClient client, string platformOrderId, string platformOrderCode, string externalContractCode)
        {
            try
            {
                string accessToken = client.GetAccessToken();
                //正式环境需要使用这个配置
                string url = $"/common/api/v2/aimesOrder/updateOrderExternalContractCode?sysCode=external&access_token={accessToken}";

                var request = new UpdateOrderExternalContractCodeRequest
                {
                    platformOrderId = platformOrderId,
                    platformOrderCode = platformOrderCode,
                    externalContractCode = externalContractCode
                };

                var resp = JsonClient.InvokeThirdByJson<UpdateOrderExternalContractCodeRequest, SWJResponse>(client.OpenServer, url, request);

                return resp;
            }
            catch (Exception ex)
            {
                return new SWJResponse()
                {
                    errorMessage = ex.Message + "；StackTrace：" + ex.StackTrace
                };
            }
        }

    }
}
