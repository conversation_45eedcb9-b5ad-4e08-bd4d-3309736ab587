using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.SWJ.API.Response.SWJ
{
    /// <summary>
    /// 销售单信息响应
    /// </summary>
    public class SWJResponse
    {
        /// <summary>
        /// 请求是否成功
        /// </summary>
        public bool success { get; set; }


        /// <summary>
        /// 错误信息，success=false时有值
        /// </summary>
        public string errorMessage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool result { get; set; }
    }

    /// <summary>
    /// 销售单信息响应
    /// </summary>
    public class ShopOrderInfoResponse
    {
        /// <summary>
        /// 请求是否成功
        /// </summary>
        public bool success { get; set; }

        /// <summary>
        /// 错误码，success=false时可能有值，详见错误码说明表格
        /// </summary>
        public int errorCode { get; set; }

        /// <summary>
        /// 错误信息，success=false时有值
        /// </summary>
        public string errorMessage { get; set; }

        /// <summary>
        /// 订单信息
        /// </summary>
        public ShopOrderInfoResult result { get; set; }
    }

    public class ShopOrderInfoResult
    {
        /// <summary>
        /// 工厂订单Id
        /// </summary>
        public string platformOrderId { get; set; }
        /// <summary>
        /// 工厂订单号，三维家订单系统的CRM模块使用
        /// </summary>
        public string platformOrderCode { get; set; }
        /// <summary>
        /// 工厂订单名称
        /// </summary>
        public string platformOrderName { get; set; }
        /// <summary>
        /// 工厂订单状态
        /// </summary>
        public string platformStatus { get; set; }
        /// <summary>
        /// 工厂订单状态名称
        /// </summary>
        public string platformStatusName { get; set; }
        /// <summary>
        /// 工厂订单关联的方案id
        /// </summary>
        public string platformOrderschemeId { get; set; }
        /// <summary>
        /// 下单模块编码
        /// </summary>
        public string moduleCode { get; set; }
        /// <summary>
        /// 下单模块名称
        /// </summary>
        public string moduleName { get; set; }
        /// <summary>
        /// 主材质
        /// </summary>
        public string material { get; set; }
        /// <summary>
        /// 订单类型编码
        /// </summary>
        public string orderType { get; set; }
        /// <summary>
        /// 订单类型
        /// </summary>
        public string orderTypeName { get; set; }
        /// <summary>
        /// 订单状态的代码
        /// </summary>
        public string placeType { get; set; }
        /// <summary>
        /// 订单类型
        /// </summary>
        public string placeTypeName { get; set; }
        /// <summary>
        /// 销售订单ID
        /// </summary>
        public string shopOrderId { get; set; }
        /// <summary>
        /// 销售订单号
        /// </summary>
        public string shopOrderCode { get; set; }
        /// <summary>
        /// 关联销售单的自定义单号
        /// </summary>
        public string customOrderNo { get; set; }
        /// <summary>
        /// 门店订单备注
        /// </summary>
        public string shopRemark { get; set; }
        /// <summary>
        /// 门店提交订单时间
        /// </summary>
        public string shopOrderSubmitTime { get; set; }
        /// <summary>
        /// 商品数量，默认固定1
        /// </summary>
        public int productNumber { get; set; }
        /// <summary>
        /// 成交价
        /// </summary>
        public decimal saleTotalPrice { get; set; }

        /// <summary>
        /// 产品总价
        /// </summary>
        public decimal productTotalPrice { get; set; }
        /// <summary>
        /// 产品金额
        /// </summary>
        public decimal productionAmount { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        public string contactRealName { get; set; }
        /// <summary>
        /// 联系方式
        /// </summary>
        public string contactWay { get; set; }
        /// <summary>
        /// 收货地址
        /// </summary>
        public string consigneeAddress { get; set; }
        /// <summary>
        /// 3D设计页面下单备注
        /// </summary>
        public string remarks { get; set; }
        /// <summary>
        /// 订单是否作废 0=否 1=是
        /// </summary>
        public int isValid { get; set; }
        /// <summary>
        /// 作废原因
        /// </summary>
        public string validReason { get; set; }
        /// <summary>
        /// 展开面积（平米）
        /// </summary>
        public decimal totalArea { get; set; }
        /// <summary>
        /// 外部参数
        /// </summary>
        public string urlParam { get; set; }
        /// <summary>
        /// 外部项目ID
        /// </summary>
        public string externalProjectId { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public string createTime { get; set; }
        /// <summary>
        /// 拆单状态 0未拆单 -1退回， 1拆单中
        /// </summary>
        public int splitStatus { get; set; }
        /// <summary>
        /// 企业经销商ID
        /// </summary>
        public string organDealerId { get; set; }
        /// <summary>
        /// 企业经销商名称
        /// </summary>
        public string organDealerName { get; set; }
        /// <summary>
        /// 业主ID
        /// </summary>
        public string customerId { get; set; }
        /// <summary>
        /// 业主姓名
        /// </summary>
        public string customerName { get; set; }
        /// <summary>
        /// 业主电话
        /// </summary>
        public string customerPhone { get; set; }
        /// <summary>
        /// 经销商编码
        /// </summary>
        public string dealerCode { get; set; }
        /// <summary>
        /// 销售报价清单文件
        /// </summary>
        public string quoteInfoData { get; set; }
        /// <summary>
        /// 销售报价清单文件
        /// </summary>
        public string quotaUrl { get; set; }
        /// <summary>
        /// 【弃用】三视图图纸文件
        /// 因为存在多个文件，弃用该字段，使用数组字段
        /// </summary>
        public string threeViewDrawingFile { get; set; }
        /// <summary>
        /// 三视图图纸文件
        /// </summary>
        public List<string> drawUrls { get; set; } = new List<string>();

        /// <summary>
        /// 期望交货日期
        /// </summary>
        public string deliveryDate { get; set; }

        /// <summary>
        /// 发货地址类型
        /// </summary>
        public string receiverType { get; set; }

        /// <summary>
        /// 商品通用料号
        /// </summary>
        public string productCode { get; set; }

        /// <summary>
        /// 订单类型值
        /// </summary>
        public string orderTypeDictVal { get; set; }

        /// <summary>
        /// 是否含台面
        /// </summary>
        public int isTableBoard { get; set; }
        public string basicMaterial { get; set; }
        public string schemePreviewImage { get; set; }
        public string schemeContent { get; set; }
        public string unitCategoryName { get; set; }
        //public string material { get; set; }

    }

    public class AfterSaleInfoResult
    {
        public int code { get; set; }
        public string msg { get; set; }
        public bool success { get; set; }
        public AfterSaleInfoResultModel data { get; set; }
    }

    public class AfterSaleInfoResultModel
    {
        /// <summary>
        /// 
        /// </summary>
        public string id { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string factoryOrderId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string schemeId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string schemeName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int orderSchemeType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string factoryOrderSchemeId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int statusCode { get; set; }
        /// <summary>
        /// 工厂已拆单
        /// </summary>
        public string statusName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string moduleCode { get; set; }
        /// <summary>
        /// 衣柜
        /// </summary>
        public string moduleName { get; set; }
        /// <summary>
        /// 未命名衣柜
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 未命名
        /// </summary>
        public string roomName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string schemeContent { get; set; }
        public string unitCategoryName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string designSchemeContent { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string factoryDesignSchemeContent { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string bomXml { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string bomUpdateXml { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string previewImage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string schemeQuoteXml { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string organId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string splitOrderImage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string versionNumber { get; set; }
    }


    public class OrderDetailResult
    {
        public int code { get; set; }
        public string msg { get; set; }
        public bool success { get; set; }
        public OrderDetailModel data { get; set; }
    }
    public class OrderDetailModel
    {
        public string basicMaterial { get; set; }
        public string schemePreviewImage { get; set; }
        public string material { get; set; }
        public string unitCategoryName { get; set; }
    }

}
