using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.Log;
using ServiceStack;
using ServiceStack.Caching;
using ServiceStack.Web;

namespace JieNor.AMS.YDJ.SWJ.API.Filter
{
    /// <summary>
    /// 响应过滤器基类
    /// </summary>
    public abstract class BaseResponseFilter : ResponseFilterAttribute
    {
        /// <summary>
        /// 服务容器
        /// </summary>
        protected IServiceContainer Container { get; private set; }

        /// <summary>
        /// 用户上下文
        /// </summary>
        protected UserContext Context { get; private set; }

        /// <summary>
        /// 日志服务：直接写文件
        /// </summary>
        protected ILogServiceEx LogService { get; private set; }

        /// <summary>
        /// Redis缓存服务
        /// </summary>
        protected IRedisCache CacheClient { get; private set; }

        /// <summary>
        /// 模型服务引擎
        /// </summary>
        protected IMetaModelService MetaModelService { get; private set; }

        /// <summary>
        /// 数据库服务引擎
        /// </summary>
        protected IDBService DBService { get; private set; }

        protected IRequest Request { get; private set; }

        protected IResponse Response { get; private set; }

        /// <summary>
        /// 执行过滤器逻辑
        /// </summary>
        /// <param name="req"></param>
        /// <param name="res"></param>
        /// <param name="responseDto"></param>
        public override void Execute(IRequest req, IResponse res, object responseDto)
        {
            //请求的生命周期标识
            var lifetimeScopeId = Guid.NewGuid().ToString();

            this.Container = Service.GlobalResolver.TryResolve<IServiceContainer>().BeginLifetimeScope(lifetimeScopeId);
            this.Request = req;
            this.Response = res; 

            //初始化用户上下文
            this.Context = InitUserContext(req.Dto);

            //设置请求标识
            this.Context.RequestId = lifetimeScopeId;

            this.CacheClient = this.Container.GetService<IRedisCache>();
            this.MetaModelService = this.Container.GetService<IMetaModelService>();
            this.DBService = this.Container.GetService<IDBService>();
            this.LogService = this.Container.GetService<ILogServiceEx>();
        }

        /// <summary>
        /// 初始化用户上下文
        /// </summary>
        /// <param name="reqDto"></param>
        /// <returns></returns>
        protected UserContext InitUserContext(object reqDto)
        {
            var cacheClient = this.Container.GetService<ICacheClient>();
            var session = SessionFeature.GetOrCreateSession<UserAuthTicket>(cacheClient, this.Request, this.Response);//this.SessionAs<UserAuthTicket>();
            if (session == null)
            {
                throw HttpError.Unauthorized("您没有权限访问此链接！");
            }
            session.CurrentRequestObject = reqDto;

            var userCtx = new UserContext();
            userCtx.Container = this.Container;
            userCtx.SetUserSession(session);

            if (this.Request.UrlReferrer?.AbsoluteUri?.IndexOf("/shell.html?") > 0)
            {
                userCtx.IsTempToken = true;
            }
            else
            {
                userCtx.IsTempToken = false;
            }

            return userCtx;
        }
    }
}