using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 商品销售详情数据模型
    /// </summary>
    public class ProductSaleDetailModel : ProductProfileModel
    {
        //public string fbiztype { get; set; } = string.Empty;

        public ComboDataModel fbiztype { get; set; } = new ComboDataModel();

        /// <summary>
        /// 商品ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 商品名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 商品编码
        /// </summary>
        public string Number { get; set; } = string.Empty;

        /// <summary>
        /// 商品主图地址
        /// </summary>
        public string ImageUrl { get; set; } = string.Empty;

        /// <summary>
        /// 是否允许定制
        /// </summary>
        public bool IsCustom { get; set; }

        /// <summary>
        /// 辅助属性列表
        /// </summary>
        public List<PropModel> AuxPropInfo { get; set; } = new List<PropModel>();

        /// <summary>
        /// 销售单据表单标识：意向单、合同单
        /// </summary>
        public string FormId { get; set; } = string.Empty;

        /// <summary>
        /// 销售单据ID：意向单ID、合同单ID
        /// </summary>
        public string OrderId { get; set; } = string.Empty;

        /// <summary>
        /// 销售单据明细行ID：意向单明细行ID、合同单明细行ID
        /// </summary>
        public string EntryId { get; set; } = string.Empty;

        /// <summary>
        /// 定制说明
        /// </summary>
        public string CustomDesc { get; set; } = string.Empty;

        /// <summary>
        /// 成交价
        /// </summary>
        public decimal DealPrice { get; set; }

        /// <summary>
        /// 折率：1-10之间(字段名已调整为抹零后折扣)
        /// </summary>
        public decimal DistRate { get; set; }

        /// <summary>
        /// 销售数量
        /// </summary>
        public decimal Qty { get; set; }

        /// <summary>
        /// 经销总价（数量*经销价）其实就是经销金额
        /// </summary>
        public decimal SellAmount { get; set; }

        /// <summary>
        /// 提货方式
        /// </summary>
        public ComboDataModel DeliveryMode { get; set; } = new ComboDataModel();

        /// <summary>
        /// 是否出现货
        /// </summary>
        public bool IsOutSpot { get; set; }

        /// <summary>
        /// 非标产品
        /// </summary>
        public bool IsNonStandard { get; set; }

        public string SofaCombNumber { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 是否赠品
        /// </summary>
        public bool IsGiveaway { get; set; }

        /// <summary>
        /// 是否允许下单
        /// </summary>
        public bool IsAllowOrders { get; set; }

        /// <summary>
        /// 商品非标选配属性值默认个数显示配置
        /// </summary>
        public int MoreNumConf { get; set; }

        /// <summary>
        /// 辅助资料、简单枚举、单据类型 下拉框数据源
        /// </summary>
        public Dictionary<string, List<Dictionary<string, object>>> ComboData { get; set; }
        /// <summary>
        /// 业绩品牌
        /// </summary>
        public ComboDataModel_status ResultBrand { get; set; } = new ComboDataModel_status();

        /// <summary>
        /// 当前商品类别 所有层级是否含有沙发类
        /// </summary>
        public bool IsSofaCategory { get; set; }

        /// <summary>
        /// 空间
        /// </summary>
        public ComboDataModel Space { get; set; } = new ComboDataModel();
        /// <summary>
        /// 库存状态
        /// </summary>
        public ComboDataModel StockStatus { get; set; } = new ComboDataModel();
        /// <summary>
        /// 仓库
        /// </summary>
        public ComboDataModel StoreHouse { get; set; } = new ComboDataModel();
        /// <summary>
        /// 仓位
        /// </summary>
        public ComboDataModel StoreLocation { get; set; } = new ComboDataModel();

        /// <summary>
        /// 品牌
        /// </summary>
        public ComboDataModel Brand { get; set; } = new ComboDataModel();

        /// <summary>
        /// 系列
        /// </summary>
        public ComboDataModel Series { get; set; } = new ComboDataModel();

        /// <summary>
        /// 生产要求
        /// </summary>
        public ComboDataModel ProdRequirement { get; set; } = new ComboDataModel();

        /// <summary>
        /// 家纺套件要求
        /// </summary>
        public ComboDataModel SelSuiteRequire { get; set; } = new ComboDataModel();

        /// <summary>
        /// 是否显示家纺套件要求
        /// </summary>
        public bool IsDisplaySelSuiteRequire { get; set; } = false;

        /// <summary>
        /// 折扣
        /// </summary>
        public double DistRateRaw { get; set; }
    }

    /// <summary>
    /// 返回含状态的下拉
    /// </summary>
    public class ComboDataModel_status:ComboDataModel
    {
        //可编辑状态
        public int Editstate { get; set; }
        public ComboDataModel_status()
        {

        }
    }
}