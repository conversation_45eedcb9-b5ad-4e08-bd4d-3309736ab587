using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 合同列表数据模型
    /// </summary>
    public class OrderListModel : BaseDataModel
    {
        [IgnoreDataMember]
        public new string Name { get; set; }

        public CustomerSimpleModel Customer { get; set; }

        /// <summary>
        /// 成交金额
        /// </summary>
        public decimal BillAmount { get; set; }

        /// <summary>
        /// 订单金额
        /// </summary>
        public decimal SumAmount { get; set; }

        /// <summary>
        /// 负责人
        /// </summary>
        public StaffSimpleModel Staff { get; set; }
        /// <summary>
        /// 销售部门
        /// </summary>
        public ComboDataModel Dept { get; set; }

        /// <summary>
        /// 业务日期、销售日期
        /// </summary>
        public DateTime OrderDate { get; set; }

        /// <summary>
        /// 结算状态
        /// </summary>
        public ComboDataModel ReceiptStatus { get; set; }

        /// <summary>
        /// 变更状态 '0':'正常','1':'变更中','2':'变更完成','3':'变更已提交'
        /// </summary>
        public int Changestatus { get; set; }
    }
}