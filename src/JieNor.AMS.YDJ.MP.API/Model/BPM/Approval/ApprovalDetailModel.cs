using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 审批详情数据模型
    /// </summary>
    public class ApprovalDetailModel
    {
        public bool IsStartFlow { get; set; }

        public List<ApprovalDetailExecutionModel> ExecutionList { get; set; } =
            new List<ApprovalDetailExecutionModel>();

        public ApprovalDetailUserModel Creator { get; set; } = new ApprovalDetailUserModel();

        public string Content { get; set; }

        public string SourceNumber { get; set; }

        public string SourceType { get; set; }

        public string SourceId { get; set; }

        public DateTime CreateDate { get; set; }

        public ComboDataModel Status { get; set; } = new ComboDataModel();

        public bool CanAudit { get; set; }

        public BaseDataSimpleModel Dept { get; set; } = new BaseDataSimpleModel();
    }

    public class ApprovalDetailExecutionModel
    {
        public string Id { get; set; }

        /// <summary>
        /// 节点名称
        /// </summary>
        public string NodeName { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 操作名
        /// </summary>
        public string OpName { get; set; }

        /// <summary>
        /// 执行状态
        /// </summary>
        public ComboDataModel OpStatus { get; set; }

        /// <summary>
        /// 执行明细
        /// </summary>
        public List<ApprovalDetailExecutionEntryModel> Entries { get; set; }
    }

    public class ApprovalDetailExecutionEntryModel
    {
        /// <summary>
        /// 执行对象
        /// </summary>
        public ApprovalDetailUserModel ExecUser { get; set; }

        /// <summary>
        /// 已执行
        /// </summary>
        public bool IsExec { get; set; }

        /// <summary>
        /// 操作意见
        /// </summary>
        public string ExecOpinion { get; set; }

        /// <summary>
        /// 实际执行时间
        /// </summary>
        public DateTime? ExecDate { get; set; }

        /// <summary>
        /// 操作名
        /// </summary>
        public string OpName { get; set; }
    }

    public class ApprovalDetailUserModel
    {
        [IgnoreDataMember]
        public string Id { get; set; }

        public string Number { get; set; }

        public string Name { get; set; }

        public string ImageUrl { get; set; }
    }
}