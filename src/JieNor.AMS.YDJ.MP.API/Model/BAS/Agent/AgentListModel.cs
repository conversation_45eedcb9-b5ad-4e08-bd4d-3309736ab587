using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    public class AgentListModel
    {
        /// <summary>
        /// 经销商id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 经销商编码
        /// </summary>
        public string Number { get; set; }
        /// <summary>
        /// 经销商名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 负责人
        /// </summary>
        public string Contacter { get; set; }
        /// <summary>
        /// 负责人联系电话
        /// </summary>
        public string ContacterPhone { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public string CreateTime { get; set; }
    }
}


