using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 业务单据预留编辑数据模型
    /// </summary>
    public class ReserveBillSourceEditModel
    {
        /// <summary>
        /// 预留单编号
        /// </summary>
        public string ReserveBillNo { get; set; } = string.Empty;

        /// <summary>
        /// 预留对象类型
        /// </summary>
        public ComboDataModel ReserveObjectType { get; set; } = new ComboDataModel();

        /// <summary>
        /// 预留对象
        /// </summary>
        public BaseDataSimpleModel ReserveObject { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 源单类型
        /// </summary>
        public ComboDataModel SourceType { get; set; } = new ComboDataModel();

        /// <summary>
        /// 源单编号
        /// </summary>
        public string SourceNumber { get; set; } = string.Empty;

        /// <summary>
        /// 源单单据ID
        /// </summary>
        public string SourceOrderId { get; set; } = string.Empty;

        /// <summary>
        /// 预留说明
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 预留天数
        /// </summary>
        public int ReserveDay { get; set; }

        /// <summary>
        /// 预留明细列表
        /// </summary>
        public List<ReserveBillSourceEditEntryModel> EntryList { get; set; } = new List<ReserveBillSourceEditEntryModel>();
    }

    /// <summary>
    /// 业务单据预留编辑数据模型
    /// </summary>
    public class ReserveBillSourceEditEntryModel
    {
        /// <summary>
        /// 商品ID
        /// </summary>
        public string ProductId { get; set; } = string.Empty;

        /// <summary>
        /// 商品编码
        /// </summary>
        public string ProductNumber { get; set; } = string.Empty;

        /// <summary>
        /// 商品名称
        /// </summary>
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// 商品图片Url
        /// </summary>
        public string ImageUrl { get; set; } = string.Empty;

        /// <summary>
        /// 辅助属性组合值ID
        /// </summary>
        public string AuxPropValId { get; set; }

        /// <summary>
        /// 辅助属性值
        /// </summary>
        public List<Dictionary<string, string>> AuxPropVals { get; set; } = new List<Dictionary<string, string>>();

        /// <summary>
        /// 定制说明
        /// </summary>
        public string CustomDesc { get; set; } = string.Empty;

        /// <summary>
        /// 销售数量
        /// </summary>
        public decimal Qty { get; set; }

        /// <summary>
        /// 成交价：在商品销售价的基础上打折后的价格，客户最终支付的价格
        /// </summary>
        public decimal DealPrice { get; set; }

        /// <summary>
        /// 预留量
        /// </summary>
        public decimal ReserveQty { get; set; }

        /// <summary>
        /// 库存数量
        /// </summary>
        public decimal StockQty { get; set; }

        /// <summary>
        /// 可预留量
        /// </summary>
        public decimal CanReserveQty { get; set; }

        /// <summary>
        /// 预留日期至
        /// </summary>
        public DateTime? ReserveDateTo { get; set; } = null;

        /// <summary>
        /// 库存状态
        /// </summary>
        public BaseDataSimpleModel StockStatus { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 预留状态
        /// </summary>
        public ComboDataModel ReserveStatus { get; set; } = new ComboDataModel();

        /// <summary>
        /// 仓库
        /// </summary>
        public BaseDataSimpleModel StoreHouse { get; set; } = new BaseDataSimpleModel();

        ///// <summary>
        ///// 仓位
        ///// </summary>
        //public BaseDataSimpleModel StoreLocation { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 物流跟踪号
        /// </summary>
        public string Mtono { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        public string Note { get; set; } = string.Empty;

        /// <summary>
        /// 源单明细ID
        /// </summary>
        public string SourceEntryId { get; set; } = string.Empty;
    }
}