using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    public class VistDetailListModel
    {
        /// <summary>
        /// 回访单数据ID
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 回访单单号
        /// </summary>
        public string BillNo { get; set; }
        /// <summary>
        /// 回访方式
        /// </summary>
        public ComboDataModel VistMode { get; set; }
        /// <summary>
        /// 评价时间
        /// </summary>
        public string FinishDate { get; set; }

        #region 增值服务评价
        /// <summary>
        /// 服务评价
        /// </summary>
        public ComboDataModel ServiceScore { get; set; }
        #endregion

        #region 送装服务评价
        /// <summary>
        /// 送装服务评价-产品质量评价
        /// </summary>
        public ComboDataModel Score { get; set; }

        /// <summary>
        /// 送装服务评价-销售服务评价
        /// </summary>
        public ComboDataModel SaleScore { get; set; }

        /// <summary>
        /// 送装服务评价-送装服务评价
        /// </summary>
        public ComboDataModel InstallScore { get; set; }
        #endregion

        #region 售后服务评价
        /// <summary>
        /// 售后服务评价-售后是否有及时联系
        /// </summary>
        public bool InTime { get; set; }
        /// <summary>
        /// 售后服务评价-售后师傅处理服务是否满意
        /// </summary>
        public bool IsSatisfy { get; set; }
        #endregion

        /// <summary>
        /// 是否愿意推荐
        /// </summary>
        public bool IsRecommend { get; set; }

        /// <summary>
        /// 评价内容
        /// </summary>
        public string VistResult { get; set; }

        /// <summary>
        /// 投诉建议
        /// </summary>
        public string CustomerComplaint { get; set; }
    }
}

