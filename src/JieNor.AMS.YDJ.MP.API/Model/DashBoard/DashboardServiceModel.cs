using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 首页服务/售后待办数据模型
    /// </summary>
    public class DashboardServiceModel
    {
        /// <summary>
        /// 服务/售后待办数据展示
        /// </summary>
        public List<BaseCountModel> serPending { get; set; } = new List<BaseCountModel>();
        /// <summary>
        /// 当月服务/售后数
        /// </summary>
        public List<ServiceCountModel> sameMonthItems { get; set; } = new List<ServiceCountModel>();
    }
}