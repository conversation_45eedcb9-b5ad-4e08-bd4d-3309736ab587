using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 下拉框列表取数接口
    /// </summary>
    [Api("下拉框列表取数接口")]
    [Route("/mpapi/combo/list")]
    [Authenticate]
    public class ComboDataListDTO : BillTypeListDTO
    {
        /// <summary>
        /// 字段标识，多个标识之间用“,”逗号分隔
        /// </summary>
        public string FieldKey { get; set; }
    }
}