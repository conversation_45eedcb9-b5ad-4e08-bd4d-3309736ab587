using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.SYS
{
    /// <summary>
    /// 平台通用删除明细行校验逻辑
    /// </summary>
    [Api("平台通用删除明细行校验逻辑")]
    [Route("/mpapi/sys/deleterow")]
    [Authenticate]
    public class DeleteRowDTO
    {
        /// <summary>
        /// 单据Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 单据表单Id，如：ydj_order
        /// </summary>
        public string FormId { get; set; }
        /// <summary>
        /// 明细行单据标识，如：fentry
        /// </summary>
        public string EntityKey { get; set; }
        /// <summary>
        /// 明细行明细Id
        /// </summary>
        public string EntryId { get; set; }
    }
}