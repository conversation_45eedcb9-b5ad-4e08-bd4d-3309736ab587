using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Filter;

namespace JieNor.AMS.YDJ.MP.API.DTO.SER.Service
{
    /// <summary>
    /// 服务单编辑取数接口
    /// </summary>
    [Api("服务单编辑取数接口")]
    [Route("/mpapi/service/edit")]
    [Authenticate]
    [ApplyBillLockFilter("ydj_service")]
    public class ServiceEditDTO : BaseDetailDTO, IApplyBillLockDTO
    {
    }
}
