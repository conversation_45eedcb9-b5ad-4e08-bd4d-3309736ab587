using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.BD.Customer.Consumption
{
    // <summary>
    /// 消费记录列表取数接口
    /// </summary>
    [Api("消费记录列表取数接口")]
    [Route("/mpapi/customer/consumptionList")]
    [Authenticate]
    public class ConsumptionListDto : BaseListPageDTO
    {
        /// <summary>
        /// 客户id
        /// </summary>
        public string CustomerId { get; set; }
    }
}
