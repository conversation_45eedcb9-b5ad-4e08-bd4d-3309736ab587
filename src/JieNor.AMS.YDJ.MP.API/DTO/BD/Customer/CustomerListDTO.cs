using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Filter;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 客户列表取数接口
    /// </summary>
    [Api("客户列表取数接口")]
    [Route("/mpapi/customer/list")]
    [Authenticate]
    [CheckPermissionFilter("ydj_customer", "fw_view")]
    public class CustomerListDTO : CustomerQueryInputDto
    {
       
    }
}