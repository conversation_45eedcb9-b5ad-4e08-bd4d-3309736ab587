using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{ 
   public  class CustomerQueryInputDto: BaseListPageDTO
    {
        /// <summary>
        /// 客户性质
        /// </summary>
        public string Nature { get; set; }

        /// <summary>
        /// 日期类型（followtime、unfollowtime）
        /// </summary>
        public string DateType { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 客户分类
        /// </summary>
        public string CustomerType { get; set; }

        /// <summary>
        /// 客户来源
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// 明细字段（addressentry）
        /// </summary>
        public string LoadEntryFields { get; set; }

        /// <summary>
        /// 查看范围
        /// myself（本人）：由系统控制；
        /// pool（公海）：本人门店公海或公司公海
        /// </summary>
        public string Scope { get; set; } = "myself";

        /// <summary>
        /// 负责人姓名（模糊匹配）
        /// </summary>
        public string DutyName { get; set; }

        /// <summary>
        /// 来源门店
        /// </summary>
        public string SourceStoreId { get; set; }
    }
}
