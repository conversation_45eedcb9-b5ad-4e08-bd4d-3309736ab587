using JieNor.AMS.YDJ.Core;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.SEL.Combine
{
    /// <summary>
    /// 获取配件列表
    /// </summary>
    [Api("获取配件列表")]
    [Route("/mpapi/combine/get")]
    [Authenticate]
    public class CombineGetDTO
    {
        /// <summary>
        /// 商品ID
        /// </summary>
        public string ProductId { get; set; }


        /// <summary>
        /// 来源业务单据标识
        /// </summary>
        public string SrcFormId { get; set; }


        /// <summary>
        /// 单据类型名称
        /// </summary>
        public string BillTypeName { get; set; }

        /// <summary>
        /// 单据类型编码
        /// </summary>
        public string BillTypeNo { get; set; }


        /// <summary>
        /// 属性列表
        /// </summary>
        public List<PropEntity> PropList { get; set; }

    }
}
