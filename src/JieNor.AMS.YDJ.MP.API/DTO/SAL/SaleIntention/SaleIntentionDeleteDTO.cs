using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 意向单删除接口
    /// </summary>
    [Api("意向单删除接口")]
    [Route("/mpapi/saleintention/delete")]
    [Authenticate]
    public class SaleIntentionDeleteDTO : BaseDTO
    {
        public string Id { get; set; }
    }
}