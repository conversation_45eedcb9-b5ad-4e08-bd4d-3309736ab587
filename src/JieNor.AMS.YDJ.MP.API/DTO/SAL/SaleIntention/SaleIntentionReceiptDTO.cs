using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 意向单收款接口
    /// </summary>
    [Api("意向单收款接口")]
    [Route("/mpapi/saleintention/receipt")]
    [Authenticate]
    public class SaleIntentionReceiptDTO : BaseDataSaveDTO
    {
        /// <summary>
        /// 源单id    fsourceid
        /// </summary>
        public string SourceId { get; set; }

        ///// <summary>
        ///// 是否协同结算  fissyn
        ///// </summary>
        //public bool IsSyn { get; set; }

        /// <summary>
        /// 源单单号    fsourcenumber
        /// </summary>
        public string SourceNumber { get; set; }

        ///// <summary>
        ///// 客户  fsupplierid
        ///// </summary>
        //public string SupplierId { get; set; }

        /// <summary>
        /// 已结算金额   fsettledamount
        /// </summary>
        public decimal SettledAmount { get; set; }

        /// <summary>
        /// 待结算金额   funsettleamount
        /// </summary>
        public decimal UnsettleAmount { get; set; }

        /// <summary>
        /// 本次结算金额  fsettleamount
        /// </summary>
        public decimal SettleAmount { get; set; }

        /// <summary>
        /// 待确认金额   funconfirmamount
        /// </summary>
        public decimal UnconfirmAmount { get; set; }

        /// <summary>
        /// 部门id    fdeptid
        /// </summary>
        public string DeptId { get; set; }

        /// <summary>
        /// 我方银行id  fmybankid
        /// </summary>
        public string MyBankId { get; set; }

        ///// <summary>
        ///// 对方银行账户Id    fsynbankid
        ///// </summary>
        //public string SynBankId { get; set; }

        ///// <summary>
        ///// 开户行     fsynbankname
        ///// </summary>
        //public string SynBankName { get; set; }

        ///// <summary>
        ///// 银行卡号     fsynbanknum
        ///// </summary>
        //public string SynBankNum { get; set; }

        ///// <summary>
        ///// 账户名称     fsynaccountname
        ///// </summary>
        //public string SynAccountName { get; set; }

        ///// <summary>
        ///// 账户  faccount
        ///// </summary>
        //public string Account { get; set; }

        /// <summary>
        /// 结算方式    fway
        /// </summary>
        public string Way { get; set; }

        ///// <summary>
        ///// 银行账号    fbankcard
        ///// </summary>
        //public string BankCard { get; set; }

        ///// <summary>
        ///// 代收单位类型  fcontactunittype
        ///// </summary>
        //public string ContactUnitType { get; set; }

        /// <summary>
        /// 凭证id数组，以英文逗号隔开 fimage
        /// </summary>
        public string CertificateIds { get; set; }

        /// <summary>
        /// 凭证名称数组，以英文逗号隔开
        /// </summary>
        public string CertificateNames { get; set; }

        /// <summary>
        /// 结算日期
        /// </summary>
        public DateTime Date { get; set; }
    }
}