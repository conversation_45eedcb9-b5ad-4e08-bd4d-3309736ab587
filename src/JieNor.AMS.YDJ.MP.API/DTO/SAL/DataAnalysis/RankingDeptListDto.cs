using JieNor.AMS.YDJ.MP.API.Utils;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.SAL.DataAnalysis
{
    /// <summary>
    /// 统计分析-部门排名列表
    /// </summary>
    [Api("统计分析-部门排名列表")]
    [Route("/mpapi/dataanalysis/rankingdeptlist")]
    [Authenticate]
    public class RankingDeptListDto
    {
        private int _PageIndex;
        /// <summary>
        /// 当前页码，默认第1页
        /// </summary>
        public int PageIndex
        {
            get { return _PageIndex; }
            set { _PageIndex = value < 1 ? 1 : value; }
        }

        private int _PageSize;
        /// <summary>
        /// 每页条数，默认每页10条
        /// </summary>
        public int PageSize
        {
            get { return _PageSize; }
            set { _PageSize = value < 1 ? 10 : value; }
        }

        /// <summary>
        /// 排序方式：升序为 asc，降序为 desc，默认为 desc
        /// </summary>
        private string _Sortord = "desc";

        /// <summary>
        /// 排序方式：升序为 asc，降序为 desc，默认为 desc
        /// </summary>
        public string Sortord
        {
            get { return _Sortord; }
            set
            {
                if (value.ToLower().Trim() != "desc" && value.ToLower().Trim() != "asc")
                    _Sortord = "asc";
                else
                    _Sortord = string.IsNullOrWhiteSpace(value) ? "desc" : value;
            }
        }
        /// <summary>
        /// 时间类型：今日，昨日，本周，上周，本月，上月，本季度，上季度，今年，去年，自定义
        /// </summary>
        public string TimeType { get; set; }

        /// <summary>
        /// 开始日期 必填 格式： yyyy-MM-dd HH:mm:ss
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期 必填 格式： yyyy-MM-dd HH:mm:ss
        /// </summary>
        public DateTime? EndDate { get; set; }
    }
}
