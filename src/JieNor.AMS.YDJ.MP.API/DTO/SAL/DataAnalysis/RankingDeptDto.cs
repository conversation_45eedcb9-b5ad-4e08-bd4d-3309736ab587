using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.SAL.DataAnalysis
{
    /// <summary>
    /// 统计分析-部门排名
    /// </summary>
    [Api("统计分析-部门排名")]
    [Route("/mpapi/dataanalysis/rankingdept")]
    [Authenticate]
    public class RankingDeptDto
    {
        /// <summary>
        /// 时间类型：今日，昨日，本周，上周，本月，上月，本季度，上季度，今年，去年，自定义
        /// </summary>
        public string TimeType { get; set; }

        /// <summary>
        /// 开始日期 必填 格式： yyyy-MM-dd HH:mm:ss
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期 必填 格式： yyyy-MM-dd HH:mm:ss
        /// </summary>
        public DateTime? EndDate { get; set; }
    }
}
