using System.Collections.Generic;
using ServiceStack;

namespace JieNor.AMS.YDJ.MP.API.DTO.MS.AIQuestion
{
    /// <summary>
    /// 获取慕思AI云的问卷调查问题内容
    /// </summary>
    [Api("获取问卷调查内容")]
    [Route("/mpapi/aiquestion/getquestion")]
    [Authenticate]
    public class AiQuestionsDTO
    {
        public List<AiQuestion> Questions { set; get; } = new List<AiQuestion>();
    }

    public class AiQuestion
    {
        /// <summary>
        /// 问题ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 问题顺序
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 题目内容
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 选择题选项和选择内容
        /// </summary>
        public List<Choice> Choices { get; set; }

        public AiQuestion()
        {
            Choices = new List<Choice>();
        }

    }

    /// <summary>
    /// 选择题中的选项和选择题内容
    /// </summary>
    public class Choice
    {
        /// <summary>
        /// 选择题ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 选项内容
        /// </summary>
        public string text { get; set; }

        #region 暂时用不到的

        /// <summary>
        /// 顺序
        /// </summary>
        public string score { get; set; }

        // public bool noScore { get; set; }

        #endregion

    }
}