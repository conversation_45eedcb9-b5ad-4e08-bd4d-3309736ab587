using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.DTO.BAS;
using JieNor.AMS.YDJ.MP.API.Model.BAS;
using JieNor.Framework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.BAS
{
    public class MiniProgramController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(MiniProgramDTO dto)
        {
            var resp = new BaseResponse<object>();
            var value = "".GetAppConfig<string>("fw.miniprogramconfig");
            //string MiniProgramConfig = HostConfigView.Develope.MiniProgramConfig;
            if (string.IsNullOrWhiteSpace(value))
            {
                resp.Success = false;
                resp.Message = "参数为空！";
            }
            resp.Data = value;
            resp.Success = true;
            resp.Message = "查询成功！";
            return resp;
        }
    }
}
