using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Response.YDJ.Report.GoalInfo;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.DataTransferObject;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Dashboard
{
    /// <summary>
    /// 微信小程序：首页销售目标接口
    /// </summary>
    public class DashboardSaleGoalController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(DashboardSaleGoalDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<DashboardSaleGoalModel>
            {
                Data = new DashboardSaleGoalModel()
            };

            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ydj_dashboard",
                    OperationNo = "goalinfo",
                    SimpleData = new Dictionary<string, string>
                    {
                        { "dataPermId", dto.DataPerm },
                        { "dtType", dto.DateType },
                        { "evalType", dto.EvalType },
                        { "formId", "ydj_dashboard" },
                        { "rptFormId", "ydj_target" }
                    }
                });
            var result = response?.OperationResult;
            if (result == null || !result.IsSuccess)
            {
                resp.Success = false;
                resp.Message = result.GetErrorMessage();
                return resp;
            }

            var goalInfoResponse = JsonConvert.DeserializeObject<GoalInfoResponse>(result.SrvData as string ?? "[]");
            if (goalInfoResponse != null && goalInfoResponse.Count > 0)
            {
                resp.Data.Goal = goalInfoResponse.First().目标;
                resp.Data.Completion = goalInfoResponse.First().实际完成;
            }

            resp.Message = "操作成功！";
            resp.Success = true;

            return resp;
        }
    }
}