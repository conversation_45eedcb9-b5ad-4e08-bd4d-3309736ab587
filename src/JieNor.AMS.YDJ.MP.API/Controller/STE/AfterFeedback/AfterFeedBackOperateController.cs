using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using ServiceStack;
using System.Collections.Generic;
using JieNor.Framework.DataTransferObject.Poco;
using ServiceStack.Web;
using System;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataManager;
using System.Data;
using System.Linq;
using JieNor.Framework.MetaCore.FormMeta;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.MP.API.Controller.SER.Service
{
    /// <summary>
    /// 微信小程序：售后反馈单操作（受理、转总部等）
    /// </summary>
    public class AfterFeedBackOperateController : BaseController
    {
        /// <summary>
        /// 处理售后反馈单部分操作请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(AfterFeedBackOperateDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<BaseDataModel>();
            string msg = string.Empty;
            if (!CheckOperate(dto, out msg))
            {
                resp.Message = msg;
                return resp;
            }
            IOperationResult result = null;
            switch (dto.OpId.ToLower())
            {
                //case "accept"://受理
                //case "transfer"://转总部
                //case "finish"://完成
                //case "fclose"://关闭
                case "delete"://删除
                case "cancel"://作废
                case "submithq"://提交总部
                case "returnsend"://返厂发货
                    var responseDel = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(this.Request, new CommonBillDTO()
                    {
                        FormId = "ste_afterfeedback",
                        OperationNo = dto.OpId.ToLower(),
                        SelectedRows = new List<SelectedRow> { new SelectedRow { PkValue = dto.Id } },
                        Option = new Dictionary<string, object>
                        {
                            { "callerTerminal", "MPAPI" }
                        },
                        Id = dto.Id
                    });
                    result = responseDel?.OperationResult;
                    break;
                default:
                    resp.Message = "您好，您的操作有误，请勿恶意操作！";
                    resp.Success = false;
                    return resp;
            }
            if (!result.IsSuccess)
            {
                var errMsg = result.SimpleMessage;
                var errMsgs = result.ComplexMessage.ErrorMessages;
                if (errMsgs.Count > 0)
                {
                    if (!errMsg.IsNullOrEmptyOrWhiteSpace())
                    {
                        errMsg += "，";
                    }
                    errMsg += string.Join("，", errMsgs);
                }
                resp.Message = errMsg ?? "操作过程出错！";
                resp.Success = false;
                return resp;
            }
            resp.Message = "操作成功！";
            resp.Success = true;
            return resp;
        }
        private bool CheckOperate(AfterFeedBackOperateDTO dto, out string msg)
        {
            msg = string.Empty;
            if (dto.Id.IsNullOrEmptyOrWhiteSpace() || dto.OpId.IsNullOrEmptyOrWhiteSpace())
            {
                msg = "请补齐请求参数！";
                return false;
            }
            return true;
        }
    }
}
