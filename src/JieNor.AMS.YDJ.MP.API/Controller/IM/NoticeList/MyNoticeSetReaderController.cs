using JieNor.AMS.YDJ.MP.API.DTO.IM.Notice;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.IM.Notice
{
    class MyNoticeSetReaderController : BaseController
    {
        public object Any(MyNoticeSetReaderDTO dto)
        {
            this.InitializeOperationContext(dto);
            var resp = new BaseResponse<BaseDataModel>();
            //参数异常判断
            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "参数 Id 不能为空！";
                resp.Success = false;
                return resp;
            }

            //查找是否存在日志记录
            var logSql = $"select fbizobjno  From T_IM_NOTICELIST_LG with(nolock)where fcreatorid='{this.Context.UserId}' and fbizobjno='{dto.Id}'";
            var havelognoticeid = this.DBService.ExecuteDynamicObject(this.Context, logSql)?.ToList();

            if (!havelognoticeid.Any())
            {
                //记录日志
                var logService = this.Container.GetService<ILogService>();
                var lstLogEntry = new LogEntry()
                {
                    BillFormId = "im_noticelist",
                    OpCode = "compulsoryprompt",
                    Level = Enu_LogLevel.Info.ToString(),
                    Category = 5,
                    Content = "执行了【浏览】操作！",
                    BillIds = dto.Id,
                    BillNos = dto.Id,
                    Detail = "",
                    LogType = Enu_LogType.RecordType_03,
                    OpName = "浏览"
                };
                logService.WriteLog(this.Context, lstLogEntry);
            }
            //返回处理结果
            resp.Message = "已设置为已读";
            resp.Success = true;
            return resp;
        }
    }
}
