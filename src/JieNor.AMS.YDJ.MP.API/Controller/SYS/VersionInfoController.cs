using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using ServiceStack;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface.Log;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using System.Net;

namespace JieNor.AMS.YDJ.MP.API.Controller.SYS
{
    /// <summary>
    /// 微信小程序：产品版本信息取数接口
    /// </summary>
    public class VersionInfoController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(VersionInfoDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<VersionInfoModel>();

            var serviceProvider = this.GetAppConfig("fw.serviceprovider");
            var versionNo = this.GetAppConfig("fw.versionno");
            var versionDate = this.GetAppConfig("fw.versiondate");
            var versionLog = "";

            if (versionNo.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("当前系统未配置版本号，请检查！");
            }

            //根据版本号读取版本日志
            try
            {
                var appPath = Framework.PathUtils.GetStartupPath();
                if (appPath.EndsWith(@"\bin")) appPath = appPath.ParentDirectory();

                var logPath = Path.Combine(appPath, $@"versionlog\{versionNo}.txt");

                var topCompanyContext = this.Context.CreateCompanyDbContext(this.Context.TopCompanyId);//总部视角上下文
                //获取【发版管理参数】
                var profileService = this.Container.GetService<ISystemProfile>();
                var versionLogParam = profileService.GetSystemParameter(topCompanyContext, "sys_versionparam", "fversionlog", "");
                if (!string.IsNullOrWhiteSpace(versionLogParam))
                {
                    logPath= Path.Combine(appPath, $@"versionlog\{versionNo}-{versionLogParam}.txt");
                    if (!File.Exists(logPath))
                    {
                        //没有最新的本地文件，则先下载
                        var logUrl = versionLogParam.GetSignedFileUrl();
                        var dirName = Path.GetDirectoryName(logPath);
                        if (!Directory.Exists(dirName))
                        {
                            Directory.CreateDirectory(dirName);
                        }
                        //下载文件到本地
                        WebClient client = new WebClient();
                        client.Proxy = null;
                        client.DownloadFile(logUrl, logPath);
                        //读取文件
                        versionLog = logPath.ReadObjectFromFile<string>();
                    }
                }

                versionLog = logPath.ReadObjectFromFile<string>();
                versionLog = versionLog?.Replace("\r\n", "<br/>") ?? "";
            }
            catch (Exception ex)
            {
                versionLog = "产品版本信息读取失败：" + ex.Message;

                var logService = this.Container.GetService<ILogServiceEx>();
                logService.Error(logService, ex);
            }

            if (versionLog.IsNullOrEmptyOrWhiteSpace())
            {
                versionLog = $"当前版本 {versionNo} 未配置版本介绍，请检查！";
            }

            var companyInfo = this.Context.Companys.FirstOrDefault(o =>
                Framework.StringUtils.EqualsIgnoreCase(this.Context.Company, o.CompanyId));

            //设置响应数据包
            resp.Data.VersionNo = versionNo;
            resp.Data.VersionDate = versionDate;
            resp.Data.VersionLog = versionLog;
            resp.Data.CompanyId = companyInfo.CompanyId;
            resp.Data.CompanyName = companyInfo.CompanyName;
            resp.Data.ServiceProvider = serviceProvider;
            resp.Message = "取数成功！";
            resp.Success = true;
            return resp;
        }
    }
}