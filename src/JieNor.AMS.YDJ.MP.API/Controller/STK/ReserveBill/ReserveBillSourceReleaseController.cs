using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.MP.API.Model;

namespace JieNor.AMS.YDJ.MP.API.Controller.STK.ReserveBill
{
    /// <summary>
    /// 微信小程序：业务单据预留释放接口
    /// </summary>
    public class ReserveBillSourceReleaseController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ReserveBillSourceReleaseDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            if (dto.SourceFormId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 sourceFormId 不能为空！";
                return resp;
            }
            if (dto.SourceOrderId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 sourceOrderId 不能为空！";
                return resp;
            }

            //调用麦浩PC端已有的接口
            var response = JsonClient.Invoke<CommonBillDTO, CommonBillDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = dto.SourceFormId,
                    OperationNo = "manualrelease",
                    SelectedRows = new List<SelectedRow> { new SelectedRow { PkValue = dto.SourceOrderId } }
                });
            var result = response?.OperationResult;
            if (!result.IsSuccess)
            {
                var errMsg = "麦浩预留释放接口出错！";
                if (!result.SimpleMessage.IsNullOrEmptyOrWhiteSpace())
                {
                    errMsg += " " + result.SimpleMessage;
                }
                var errMsgs = result.ComplexMessage.ErrorMessages;
                if (errMsgs.Count > 0)
                {
                    errMsg += " " + string.Join("，", errMsgs);
                }
                resp.Message = errMsg;
                resp.Success = false;
                return resp;
            }

            resp.Message = "释放成功！";
            resp.Success = true;
            return resp;
        }
    }
}