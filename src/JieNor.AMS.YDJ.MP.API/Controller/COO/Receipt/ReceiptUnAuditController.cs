using JieNor.AMS.YDJ.MP.API.DTO.COO.Receipt;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.COO.Receipt
{
    /// <summary>
    /// 微信小程序：收款单反审核
    /// </summary>
    public class ReceiptUnAuditController : BaseController
    {
        public object Any(ReceiptUnAuditDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            //调用通用反审核方法
            return ApprovalHelper.Reject(this.Context, this.Request, "coo_incomedisburse", dto.Id, dto.Reason, dto.Terminate);
        }
    }
}
