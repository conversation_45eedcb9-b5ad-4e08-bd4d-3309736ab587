using JieNor.AMS.YDJ.MP.API.DTO.COO.IncomeDisburse;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Model.COO.IncomeDisburse;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.COO.IncomeDisburse
{
    /// <summary>
    /// 立即收款
    /// </summary>
    public class RefundController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(RefundDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<BaseDataModel>();

            DynamicDTOResponse response;
            Dictionary<string, string> simpledata = new Dictionary<string, string>();

            // 向麦浩系统发送请求
            response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
              this.Request,
              new CommonBillDTO()
              {
                  FormId = "coo_chargedialog",
                  OperationNo = "Charge",
                  BillData = BuildBillData(dto),
                  SimpleData = SimpleDataDic(dto)
              });

            if (!response.OperationResult.IsSuccess)
            {
                resp.Message = response.OperationResult.GetErrorMessage();
                resp.Success = false;
            }
            else
            {
                resp.Message = response.OperationResult.ToString();
                resp.Success = true;
            }
            return resp;
        }

        /// <summary>
        /// 参数组装
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private Dictionary<string, string> SimpleDataDic(RefundDTO dto)
        {
            var simpledata = new Dictionary<string, string>()
            {
                ["fsourceid"] = dto.fsourceid,
                ["fsourceformid"] = "ydj_customer"
            };
            //var billData = (new List<Dictionary<string, object>> { simpledata }).ToJson();

            return simpledata;
        }

        /// <summary>
        /// 参数组装
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private string BuildBillData(RefundDTO dto)
        {
            var simpledata = new Dictionary<string, object>();
            simpledata.Add("fusagetype", dto.fusagetype);
            simpledata.Add("fmybankid", dto.fsynbankid);//
            simpledata.Add("fmoney", dto.fmoney);
            simpledata.Add("fdate", dto.fdate);
            simpledata.Add("fway", dto.fway);
            simpledata.Add("frefundway", dto.frefundway);
            simpledata.Add("fdeptid", dto.fdeptid);
            //40936 客户充值默认携带业务员，允许业务员修改-后端开发
            simpledata.Add("fstaffid", dto.fstaffid);
            //simpledata.Add("fsourceid", dto.fsourceid);
            simpledata.Add("fcontactunitid", dto.fcontactunitid);
            simpledata.Add("fcusacount", dto.cusacount);
            if (dto.certificates != null && dto.certificates.Any())
            {
                simpledata.Add("fimage", string.Join(",", dto.certificates.Select(s => s.Id)));
                simpledata.Add("fimage_txt", string.Join(",", dto.certificates.Select(s => s.Name)));
            }
            simpledata.Add("fdescription", dto.fdescription);
            simpledata.Add("paymentdesc", dto.paymentdesc);
            //收款小票号
            simpledata.Add("freceiptno", dto.ReceiptNo);

            #region 销售员
            var joinStaffs = new List<Dictionary<string, object>>();
            foreach (var js in dto.JoinStaffs)
            {
                joinStaffs.Add(new Dictionary<string, object>
                {
                    { "id", js.Id },
                    { "fismain", js.IsMain },
                    { "fdutyid", js.Duty?.Id },
                    { "fdeptid_ed", js.Dept?.Id },
                    { "fratio", js.Ratio },
                    { "fdeptperfratio", js.DeptPerfRatio },
                    { "famount_ed", js.Amount },
                    { "fdescription_ed", js.Description }
                });
            }
            simpledata.Add("fdutyentry", joinStaffs);
            #endregion

            var billData = (new List<Dictionary<string, object>> { simpledata }).ToJson();

            return billData;
        }
    }
}
