using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Product
{
    /// <summary>
    /// 微信小程序：商品扫码取数接口
    /// </summary>
    public class ProductScanCodeController : BaseController
    {
        /// <summary>
        /// 目前支持的条码类型
        /// </summary>
        protected string[] codeTypes = new string[] { "barcode", "qrcode" };

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ProductScanCodeDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            if (!codeTypes.Contains(dto.CodeType, StringComparer.OrdinalIgnoreCase))
            {
                resp.Success = false;
                resp.Message = $"条码类型参数 codeType 错误，目前只支持 {string.Join(",", codeTypes)}";
                return resp;
            }
            if (dto.CodeContent.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"条码内容参数 codeContent 不能为空！";
                return resp;
            }

            //如果条码类型是二维码，则把二维码转化成已存在的二维码
            var codeContent = dto.CodeContent;
            //var isConvertQR = false;
            //if (dto.CodeType.EqualsIgnoreCase("qrcode"))
            //{
            //    codeContent = this.ConvertQRcode(codeContent, out isConvertQR);
            //    if (codeContent.IsNullOrEmptyOrWhiteSpace())
            //    {
            //        resp.Success = false;
            //        resp.Message = $"当前条码没有关联的商品信息！";
            //        return resp;
            //    }
            //}

            //查询商品标签中的商品ID和辅助属性组合值ID
            var productId = "";
            var auxPropValId = "";

            var sqlText = @"
            select top 1 pc.fproductid,pc.fattrinfo 
            from v_ydj_productbarcode pc with(nolock) 
            where pc.fmainorgid=@fmainorgid and pc.fbarcode=@fcode
            union 
            select top 1 pc.fproductid,pc.fattrinfo 
            from v_ydj_productbarcode pc with(nolock) 
            where pc.fmainorgid=@fmainorgid and pc.fqrcode=@fcode
            union
            --新增根据商品编码匹配商品
            select top 1 pc.fid as fproductid,pc.fdefattrinfo as fattrinfo 
            from t_bd_material pc with(nolock) 
            where pc.fmainorgid=@fmainorgid and pc.fnumber=@fcode
            ";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("@fcode", System.Data.DbType.String, codeContent)
            };
            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    productId = reader.GetValueToString("fproductid").Trim();
                    auxPropValId = reader.GetValueToString("fattrinfo").Trim();
                }
            }

            if (productId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"当前条码没有关联的商品信息！";
                return resp;
            }

            //设置响应数据包
            resp.Message = "取数成功！";
            resp.Success = true;
            resp.Data = new
            {
                productId = productId,
                auxPropValId = auxPropValId
            };
            return resp;
        }

        /// <summary>
        /// 将二维码转化成已存在的二维码
        /// </summary>
        private string ConvertQRcode(string codeContent, out bool isConvertQR)
        {
            isConvertQR = false;

            //查询条码内容是否在商品标签中存在
            int count = 0;
            var sqlText = @"select count(1) from v_ydj_productbarcode with(nolock) where fmainorgid=@fmainorgid and fqrcode=@fqrcode";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("@fqrcode", System.Data.DbType.String, codeContent)
            };
            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    count = Convert.ToInt32(reader[0]);
                }
            }

            //如果根据条码内容没有查到商品标签，则尝试按商品编码再查一次
            if (count == 0)
            {
                //条码内容由（商品编码+辅助属性组合值编码）组成
                //比如：SP0003,颜色:黑色,材质:真皮,规格:单人位
                var productNumber = codeContent.Split(',').FirstOrDefault();

                sqlText = @"
select top 1 t1.fqrcode 
from t_bd_material t with(nolock) 
left join v_ydj_productbarcode t1 with(nolock) on t.fid = t1.fproductid and t.fmainorgid=t1.fmainorgid 
where t.fmainorgid=@fmainorgid and t.fnumber=@fnumber";
                sqlParam = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                    new SqlParam("@fnumber", System.Data.DbType.String, productNumber)
                };
                using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, sqlParam))
                {
                    if (reader.Read())
                    {
                        codeContent = reader.GetValueToString("fqrcode");
                    }
                    else
                    {
                        codeContent = "";
                    }
                }

                isConvertQR = true;
            }

            return codeContent;
        }
    }
}