using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Customer
{
   public class CustomerBaseController : BaseController
    {
        public static void SetFillter(UserContext userCtx,CustomerQueryInputDto dto, out SqlBuilderParameter param, out IListSqlBuilder listBuilder, out bool ignoreSystemDataScope ) {
            //参数对象
            param = new SqlBuilderParameter(userCtx, "ydj_customer");
            param.ReadDirty = true;
            param.NoColorSetting = true;
            param.PageCount = dto.PageSize;
            param.PageIndex = dto.PageIndex;

            //排序：默认按创建日期降序
            var orderBy = "fcreatedate";
            switch (dto.Sortby)
            {
                case "grade": //等级
                
                    break;
                case "followtime":
                    orderBy = "flastfollowdata";
                    break;
            }
            param.OrderByString = $"{orderBy} {dto.Sortord}";

            //过滤条件
            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                param.AppendFilterString("(fname like @keyword or fphone like @keyword or fwechat like @keyword)");
                param.AddParameter(new SqlParam("@keyword", System.Data.DbType.String, $"%{dto.Keyword.Trim()}%"));
            }
            if (!dto.Nature.IsNullOrEmptyOrWhiteSpace())
            {
                param.AppendFilterString("fcusnature=@nature");
                param.AddParameter(new SqlParam("@nature", System.Data.DbType.String, dto.Nature));
            }

            if (dto.Source.IsNullOrEmptyOrWhiteSpace() == false)
            {
                param.AppendFilterString("fsource=@source");
                param.AddParameter(new SqlParam("@source", System.Data.DbType.String, dto.Source));
            }

            if (dto.CustomerType.IsNullOrEmptyOrWhiteSpace() == false)
            {
                param.AppendFilterString("fcustomertype=@type");
                param.AddParameter(new SqlParam("@type", System.Data.DbType.String, dto.CustomerType));
            }

            if (dto.DateType.IsNullOrEmptyOrWhiteSpace() == false)
            {
                switch (dto.DateType.ToLower())
                {
                    // 未跟进
                    case "unfollowtime":
                        if (dto.StartDate.HasValue && dto.EndDate.HasValue)
                        {
                            param.AppendFilterString("(flastfollowdata<@startdate or flastfollowdata>@enddate)");
                            param.AddParameter(new SqlParam("@startdate", System.Data.DbType.DateTime, dto.StartDate.Value));
                            param.AddParameter(new SqlParam("@enddate", System.Data.DbType.DateTime, dto.EndDate.Value.AddDays(1)));
                        }
                        else if (dto.StartDate.HasValue)
                        {
                            param.AppendFilterString("flastfollowdata<@startdate");
                            param.AddParameter(new SqlParam("@startdate", System.Data.DbType.DateTime, dto.StartDate.Value));
                        }

                        else if (dto.EndDate.HasValue)
                        {
                            param.AppendFilterString("flastfollowdata>@enddate");
                            param.AddParameter(new SqlParam("@enddate", System.Data.DbType.DateTime, dto.EndDate.Value.AddDays(1)));
                        }
                        else
                        {
                            // 都为空，表示所有
                            param.AppendFilterString("flastfollowdata is null");
                        }
                        break;
                    // 已跟进
                    default:
                        if (dto.StartDate.HasValue || dto.EndDate.HasValue)
                        {
                            if (dto.StartDate.HasValue)
                            {
                                param.AppendFilterString("flastfollowdata>=@startdate");
                                param.AddParameter(new SqlParam("@startdate", System.Data.DbType.DateTime, dto.StartDate.Value));
                            }

                            if (dto.EndDate.HasValue)
                            {
                                param.AppendFilterString("flastfollowdata<=@enddate");
                                param.AddParameter(new SqlParam("@enddate", System.Data.DbType.DateTime, dto.EndDate.Value.AddDays(1)));
                            }
                        }
                        else
                        {
                            // 都为空，表示所有
                            param.AppendFilterString("flastfollowdata is not null");
                        }
                        break;
                }
            }

            // 客户负责人姓名全匹配
            if (dto.DutyName.IsNullOrEmptyOrWhiteSpace() == false)
            {
                param.AppendFilterString(" fid in (select fid from t_ydj_customerdutyentry with(nolock) where fdutyid in (select fid from t_bd_staff with(nolock) where fname like @fdutyname)) ");
                param.AddParameter(new SqlParam("@fdutyname", DbType.String, $"%{dto.DutyName.Trim()}%"));
            }

            //当前要查询的字段列表
            var fieldKeys = new string[] { "fnumber", "fname", "fphone", "fcusnature", "fsource", "fcreatedate", "ftype", "flastfollowdata", "fdutyids", "fdutyids_txt", "fdeptids", "fdeptids_txt", "fprovince", "fcity", "fregion", "faddress", "fcustomerlevel", "fsumamount", "fbuildingid", "fchannelid", "fsrcstoreid", "fstatus" };
            foreach (var fieldKey in fieldKeys)
            {
                var field = param.HtmlForm.GetField(fieldKey);
                var columnList = field.ToListColumn(userCtx);
                foreach (var column in columnList)
                {
                    param.SelectedFieldKeys.Add(column.Id);
                }
            }
             ignoreSystemDataScope = false;
            //列表构建器
             listBuilder = userCtx.Container.GetService<IListSqlBuilder>();

            switch (dto.Scope.ToLowerInvariant())
            {
                case "pool":
                    // 注意：如果客户公海，则使用管理员获取数据，忽略数据范围权限
                    ignoreSystemDataScope = true;
                    bool hasPerm =userCtx.HasPermission("ydj_customer", "fw_commoncus");
                    if (hasPerm)
                    {
                        param.AppendFilterString(
                            @"
                    exists(select fid from(
                            select u1.fid from t_ydj_customer u1 with(nolock) where u1.fdeptids='' and u1.fdutyids=''and u1.fmainorgid=@currentCompanyId
                            union all
                            select u1.fid from t_ydj_customer u1 with(nolock)
                            inner join t_ydj_customerdutyentry d with(nolock) on u1.fid=d.fid where d.fdutyid='' and d.fdeptid=@currentDeptId and u1.fmainorgid=@currentCompanyId
                            ) u2 where u2.fid = fid)");
                        param.AddParameter(new SqlParam("@currentDeptId", System.Data.DbType.String, userCtx.GetCurrentDeptId()));
                    }
                    else
                    {
                        param.AppendFilterString("1!=1");
                    }
                    break;
            }
         
            var spService = userCtx.Container.GetService<ISystemProfile>();
            string sysProfileValue = spService.GetProfile(userCtx, "fw", "bas_storesysparam_parameter");
            if (!sysProfileValue.IsNullOrEmptyOrWhiteSpace())
            {
                var storeSysParam = Newtonsoft.Json.Linq.JObject.Parse(sysProfileValue);
                if (storeSysParam != null && storeSysParam["fcustomerunique"] != null)
                {
                    var customerunique = Convert.ToString(storeSysParam["fcustomerunique"]);
                    if (customerunique.IndexOf("store") >= 0 && !dto.SourceStoreId.IsNullOrEmptyOrWhiteSpace())
                    {
                        param.AppendFilterString("fsrcstoreid=@srcstoreid");
                        param.AddParameter(new SqlParam("@srcstoreid", System.Data.DbType.String, dto.SourceStoreId.Trim()));
                    }
                }
            }

            // 成交客户所有人可见
            if (dto.Nature.EqualsIgnoreCase("cusnature_02"))
            {
                var profileService = userCtx.Container.GetService<ISystemProfile>();
                var fdealcustomervisible = profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fdealcustomervisible", false);
                // 成交客户不按负责人隔离，需返回所有数据
                if (fdealcustomervisible)
                {
                    ignoreSystemDataScope = true;
                }
            }
            //设置数据隔离方案的过滤条件
            var accessFilter = listBuilder.GetListAccessControlFilter(userCtx, param.HtmlForm.Id);
            param.SetFilter(accessFilter);
        }
        public static int GetTotalRecordByTab(UserContext userCtx, CustomerQueryInputDto dto)
        {
            SetFillter(userCtx, dto, out var param, out var listBuilder, out var ignoreSystemDataScope);
            ListSqlBuilderUtil.BuildListDesc(userCtx, listBuilder, param, ignoreSystemDataScope, out var listDesc);
            return (int)listDesc.Rows;
        }
    }
}
