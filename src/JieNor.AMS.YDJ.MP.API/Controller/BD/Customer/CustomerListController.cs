using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Customer
{
    /// <summary>
    /// 微信小程序：客户列表取数接口
    /// </summary>
    public class CustomerListController : CustomerBaseController
    {
        /// <summary>
        /// 客户表单模型
        /// </summary>
        protected HtmlForm CustomerForm { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CustomerListDTO dto)
        {
            base.InitializeOperationContext(dto);

            this.CustomerForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customer");

            var resp = new BaseResponse<BaseListPageData<CustomerListModel>>
            {
                Data = new BaseListPageData<CustomerListModel>(dto.PageSize)
            };

            this.SetResponseData(dto, resp);

            return resp;
        }

        /// <summary>
        /// 设置响应数据包
        /// 采用平台标准的列表取数方法，以便和PC端的取数条件保持一致（主要是涉及到数据隔离的问题）
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        private void SetResponseData(CustomerQueryInputDto dto, BaseResponse<BaseListPageData<CustomerListModel>> resp)
        {
            SetFillter(this.Context,dto,out var param, out var listBuilder, out var ignoreSystemDataScope);

            ListSqlBuilderUtil.BuildListDataAndListDesc(this.Context, listBuilder, param, ignoreSystemDataScope, out var listData, out var listDesc);

            var channelData = GetChannel();

            //将平台通用的列表数据结构转换为API数据结构
            var list = new List<CustomerListModel>();
            foreach (var item in listData)
            {
                var channeltype = channelData.Where(x => Convert.ToString(x["fid"]) == Convert.ToString(item["fchannelid"])).FirstOrDefault();
                var model = new CustomerListModel
                {
                    Id = Convert.ToString(item["fbillhead_id"]),
                    Number = Convert.ToString(item["fnumber"]),
                    Name = Convert.ToString(item["fname"]),
                    Phone = Convert.ToString(item["fphone"]).Trim(),
                    Source = new ComboDataModel
                    {
                        Id = Convert.ToString(item["fsource"]),
                        Name = Convert.ToString(item["fsource_fenumitem"])
                    },
                    Nature = new ComboDataModel
                    {
                        Id = Convert.ToString(item["fcusnature"]),
                        Name = Convert.ToString(item["fcusnature_txt"])
                    },
                    Level = new BaseDataSimpleModel
                    {
                        Id = Convert.ToString(item["fcustomerlevel"]),
                        Name = Convert.ToString(item["fcustomerlevel_fname"])
                    },
                    CreateDate = Convert.ToDateTime(item["fcreatedate"]),
                    Type = new ComboDataModel
                    {
                        Id = Convert.ToString(item["ftype"]),
                        Name = Convert.ToString(item["ftype_txt"])
                    },
                    FollowTime = item["flastfollowdata"].IsNullOrEmptyOrWhiteSpace()
                        ? (DateTime?)null
                        : Convert.ToDateTime(item["flastfollowdata"]),
                    Province = new ComboDataModel
                    {
                        Id = Convert.ToString(item["fprovince"]),
                        Name = Convert.ToString(item["fprovince_fenumitem"])
                    },
                    City = new ComboDataModel
                    {
                        Id = Convert.ToString(item["fcity"]),
                        Name = Convert.ToString(item["fcity_fenumitem"])
                    },
                    Region = new ComboDataModel
                    {
                        Id = Convert.ToString(item["fregion"]),
                        Name = Convert.ToString(item["fregion_fenumitem"])
                    },
                    Building = new BaseDataSimpleModel
                    {
                        Id = Convert.ToString(item["fbuildingid"]),
                        Name = Convert.ToString(item["fbuildingid_fname"])
                    },
                    Channel = new
                    {
                        Id = Convert.ToString(item["fchannelid"]),
                        Name = Convert.ToString(item["fchannelid_fname"]),
                        Type = new
                        {
                            id = channeltype == null ? "" : Convert.ToString( channeltype["ftype"]),
                            name = channeltype == null ? "" : Convert.ToString(channeltype["fenumitem"]),
                        }
                    },
                    Address = Convert.ToString(item["faddress"]),
                    SumAmount = Convert.ToDecimal(item["fsumamount"])
                };

                 model.Status = new ComboDataModel
                 {
                     Id = JNConvert.ToStringAndTrim(item["fstatus"]),
                     Name = JNConvert.ToStringAndTrim(item["fstatus_fenumitem"])
                 };

                // 完整地址
                model.District = HtmlFormExtentions.GetDistrictText(model.Province.Name, model.City.Name,
                    model.Region.Name, model.Address);

                model.Duty = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fdutyids"]).TrimStart(',', ' '),
                    Name = JNConvert.ToStringAndTrim(item["fdutyids_txt"]).TrimStart(',', ' ')
                };
                model.Dept = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fdeptids"]).TrimStart(',', ' '),
                    Name = JNConvert.ToStringAndTrim(item["fdeptids_txt"]).TrimStart(',', ' ')
                };
                model.CustomerSrcStore = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fsrcstoreid"]).TrimStart(',', ' '),
                    Name = JNConvert.ToStringAndTrim(item["fsrcstoreid_fname"]).TrimStart(',', ' ')
                };

                list.Add(model);
            }

            //加载含未成交商机标记
            this.LoadHasNotDealOrder(list);
            this.LoadEntry(list, dto.LoadEntryFields);
            this.LoadDefaultEntryId(list);

            //设置响应数据包
            resp.Data.List = list;
            resp.Data.TotalRecord = (int)listDesc.Rows;
            resp.Message = "取数成功！";
            resp.Success = true;
        }

        /// <summary>
        /// 得到合作渠道
        /// </summary>
        /// <returns></returns>
        private IEnumerable<DynamicObject> GetChannel()
        {
            var strSql = @"
select t1.fid,t1.ftype,isnull(t2.fenumitem,'') fenumitem 
from t_ste_channel t1 with(nolock) 
left join T_BD_ENUMDATAENTRY t2 with(nolock) on t1.ftype = t2.fentryid";
            var data = this.Context.ExecuteDynamicObject(strSql, new List<SqlParam>() { });
            return data;
        }

        /// <summary>
        /// 加载含未成交商机标记
        /// </summary>
        /// <param name="list"></param>
        private void LoadHasNotDealOrder(List<CustomerListModel> list)
        {
            //在成交客户里添加字段：含未成交商机
            var dealCustomers = list.Where(s => s.Nature.Id == "cusnature_02");
            if (dealCustomers.Any())
            {
                var dealCustomerIds = dealCustomers.Select(s => s.Id);

                var sqlText = string.Format(@"
select fcustomerid,count(1) fordercount 
from t_ydj_customerrecord with(nolock) 
where fmainorgid=@fmainorgid and fphase<>@fphase and fclosestatus<>'1' and fcustomerid in ({0}) 
group by fcustomerid", string.Join(",", dealCustomerIds.Select(s => $"'{s}'")));

                using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, new List<SqlParam>
                {
                    new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                    new SqlParam("@fphase", System.Data.DbType.String, "customerrecord_phase_05"),
                }))
                {
                    while (reader.Read())
                    {
                        var item = dealCustomers.FirstOrDefault(s => s.Id == reader.GetValueToString("fcustomerid"));
                        if (item != null)
                        {
                            item.HasNotDealOrder = reader.GetValueToInt("fordercount") > 0;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 加载明细
        /// </summary>
        /// <param name="list"></param>
        /// <param name="loadEntryFields"></param>
        private void LoadEntry(List<CustomerListModel> list, string loadEntryFields)
        {
            if (list.Count == 0)
            {
                return;
            }

            if (loadEntryFields.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }

            string[] entryFields = loadEntryFields.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
            if (entryFields.Length == 0)
            {
                return;
            }

            var ids = list.Select(s => s.Id);

            if (entryFields.Any(s => s.EqualsIgnoreCase("addressEntry")))
            {
                // 联系人明细
                var sqlText = $@"select 
                    addressentry.fid, 
                    addressentry.fcuscontacttryid, 
                    addressentry.fcontacter, 
                    addressentry.fphone, 
                    addressentry.fprovince, 
                    province.fenumitem fprovincename, 
                    addressentry.fcity, 
                    city.fenumitem fcityname, 
                    addressentry.fregion, 
                    region.fenumitem fregionname, 
                    addressentry.faddress,
                    addressentry.fisdefault,
					addressentry.fcdescription
                from t_ydj_fcuscontacttry addressentry with(nolock)
                left join v_bd_enum province with(nolock) on addressentry.fprovince=province.fid
                left join v_bd_enum city with(nolock) on addressentry.fcity=city.fid
                left join v_bd_enum region with(nolock) on addressentry.fregion=region.fid
                where addressentry.fid in ({string.Join(",", ids.Select(s => $"'{s}'"))})";

                Dictionary<string, List<CustomerAddressListModel>> addressDict =
                    new Dictionary<string, List<CustomerAddressListModel>>();

                foreach (var id in ids)
                {
                    addressDict[id] = new List<CustomerAddressListModel>();
                }

                using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
                {
                    while (reader.Read())
                    {
                        if (!string.IsNullOrEmpty(reader.GetValueToString("fcontacter").Trim()) && !string.IsNullOrEmpty(reader.GetValueToString("fphone").Trim()))
                        {
                            string customerId = reader.GetValueToString("fid");

                            var model = new CustomerAddressListModel
                            {
                                Id = reader.GetValueToString("fcuscontacttryid"),
                                Phone = reader.GetValueToString("fphone").Trim(),
                                Contact = reader.GetValueToString("fcontacter").Trim(),
                                Province = new ComboDataModel
                                {
                                    Id = reader.GetValueToString("fprovince").Trim(),
                                    Name = reader.GetValueToString("fprovincename").Trim(),
                                },
                                City = new ComboDataModel
                                {
                                    Id = reader.GetValueToString("fcity").Trim(),
                                    Name = reader.GetValueToString("fcityname").Trim(),
                                },
                                Region = new ComboDataModel
                                {
                                    Id = reader.GetValueToString("fregion").Trim(),
                                    Name = reader.GetValueToString("fregionname").Trim(),
                                },
                                Address = reader.GetValueToString("faddress").Trim(),
                                IsDefault = reader.GetValueToString("fisdefault", "0").EqualsIgnoreCase("1"),
                                Description = reader.GetValueToString("fcdescription").Trim(),
                            };

                            model.District = HtmlFormExtentions.GetDistrictText(model.Province.Name, model.City.Name, model.Region.Name, model.Address);

                            addressDict[customerId].Add(model);
                        }

                    }
                }

                foreach (var item in list)
                {
                    item.AddressEntry = addressDict[item.Id].OrderByDescending(s => s.IsDefault).ToList();
                }
            }
        }


        /// <summary>
        /// 加载客户联系人明细默认Id
        /// </summary>
        /// <param name="list"></param>
        private void LoadDefaultEntryId(List<CustomerListModel> list)
        {
            if (list.Count == 0) return;

            var ids = list.Select(s => s.Id).ToList(); 
            if (ids.Count > 20)
            {
                using (var tran = Context.CreateTransaction( ))
                {
                    var tempTableName = this.DBService.CreateTempTableWithDataList(this.Context, ids, false);
                    // 联系人明细
                    var sqlText = $@"select t1.fid, t2.fcuscontacttryid from t_ydj_customer t1 with(nolock)
                                    inner join (
                                        select row_number() over(partition by fid order by fseq) as count,fid,fcuscontacttryid
                                        from t_ydj_fcuscontacttry ta with(nolock)
	                                    where exists(select 1 from {tempTableName} tb where ta.fid=tb.fid)
                                    ) t2 on t1.fid = t2.fid and t2.count = 1
                                    inner join {tempTableName} t3 on t1.fid=t3.fid";
                    using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
                    {
                        while (reader.Read())
                        {
                            var item = list.FirstOrDefault(s => s.Id == reader.GetValueToString("fid"));
                            if (item != null)
                            {
                                item.CustomercontactId = reader.GetValueToString("fcuscontacttryid").Trim();
                            }
                        }
                    }

                    this.DBService.DeleteTempTableByName(this.Context, tempTableName, true);

                    tran.Complete();
                }
            }
            else
            {
                // 联系人明细
                var sqlText = $@" select t1.fid, t2.fcuscontacttryid from t_ydj_customer t1 with(nolock)
                                    inner join (
                                        select row_number() over(partition by fid order by fseq) as count,fid,fcuscontacttryid
                                        from t_ydj_fcuscontacttry ta with(nolock)
	                                    where  ta.fid in ({ids.JoinEx(",", true)})
                                    ) t2 on t1.fid = t2.fid and t2.count = 1
                                    where t1.fid  in ({ids.JoinEx(",", true)}) ";
                using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
                {
                    while (reader.Read())
                    {
                        var item = list.FirstOrDefault(s => s.Id == reader.GetValueToString("fid"));
                        if (item != null)
                        {
                            item.CustomercontactId = reader.GetValueToString("fcuscontacttryid").Trim();
                        }
                    }
                }
            }
        }
    }
}