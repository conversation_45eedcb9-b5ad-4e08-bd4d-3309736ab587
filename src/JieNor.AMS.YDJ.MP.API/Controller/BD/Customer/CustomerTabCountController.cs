using JieNor.AMS.YDJ.MP.API.DTO.BD.Customer;
using JieNor.AMS.YDJ.MP.API.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Customer
{
    public class CustomerTabCountController : CustomerBaseController
    {
        private static Dictionary<string, string> _types = new Dictionary<string, string> {
            {"cusnature_00","线索客户" },
            {"cusnature_01","意向客户" },
            {"cusnature_02","成交客户" },
        };
        public object Any(CustomerTabCountDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<List<BaseCountModel>>();
            resp.Data = new List<BaseCountModel>();
            foreach (var type in _types)
            {
                dto.Nature = type.Key;
                dto.PageIndex = 1;
                dto.PageSize = 1;
                resp.Data.Add(new BaseCountModel
                {
                    Id = type.Key,
                    Name = type.Value,
                    Count = GetTotalRecordByTab(this.Context, dto)
                });
            }
            resp.Message = "操作成功！";
            resp.Success = true;

            return resp;
        }
    }
}
