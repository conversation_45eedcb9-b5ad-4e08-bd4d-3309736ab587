using JieNor.AMS.YDJ.Core.Utils;
using JieNor.AMS.YDJ.MP.API.Config;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller
{
    /// <summary>
    /// 无须认证的控制器基类
    /// </summary>
    public class BaseNotAuthController : ServiceStack.Service
    {
        /// <summary>
        /// 服务容器
        /// </summary>
        protected IServiceContainer Container { get; private set; }

        /// <summary>
        /// 用户上下文（麦浩客户）
        /// </summary>
        protected UserContext Context { get; private set; }

        /// <summary>
        /// 系统用户上下文（麦浩用户）
        /// </summary>
        protected UserContext SysAdminUserCtx { get; set; }

        /// <summary>
        /// 日志服务：直接写文件
        /// </summary>
        protected ILogServiceEx LogService { get; private set; }

        /// <summary>
        /// 模型服务引擎
        /// </summary>
        protected IMetaModelService MetaModelService { get; private set; }

        /// <summary>
        /// 网关服务
        /// </summary>
        protected IHttpServiceInvoker HttpGateway { get; private set; }

        /// <summary>
        /// 数据库服务引擎
        /// </summary>
        protected IDBService DBService { get; private set; }

        /// <summary>
        /// 初始化操作上下文
        /// </summary>
        /// <param name="reqDto"></param>
        protected void InitializeOperationContext(BaseNotAuthDTO reqDto)
        {
            //请求的生命周期标识
            var lifetimeScopeId = Guid.NewGuid().ToString();

            //每一次请求都需要生成一个全新的生命周期标识来确保后续的所有服务实例不会混淆
            this.Request.SetRequestId(lifetimeScopeId);
            this.Container = this.TryResolve<IServiceContainer>().BeginLifetimeScope(lifetimeScopeId);

            //初始化用户上下文
            this.Context = this.InitUserContext(reqDto);

            //设置请求标识
            this.Context.RequestId = lifetimeScopeId;

            this.LogService = this.Container.GetService<ILogServiceEx>();
            this.MetaModelService = this.Container.GetService<IMetaModelService>();
            this.HttpGateway = this.Container.GetService<IHttpServiceInvoker>();
            this.DBService = this.Container.GetService<IDBService>();

            this.SysAdminUserCtx =
                this.CreateContextByCompanyInfo(this.Context.Companys.First(s =>
                    s.CompanyId.EqualsIgnoreCase(this.Context.Company)));
            this.SysAdminUserCtx.UserSession.Roles = new List<string> { $"admin.{this.SysAdminUserCtx.Company}" };
        }

        /// <summary>
        /// 初始化用户上下文
        /// </summary>
        /// <param name="reqDto"></param>
        /// <returns></returns>
        protected UserContext InitUserContext(BaseNotAuthDTO reqDto)
        {
            //// 获取运维系统商户
            //var customer = reqDto.CustomerCode.GetCustomer();
            //string companyId = customer.CompanyId;
            string companyId = reqDto.ComId;

            var session = new UserAuthTicket
            {
                Company = companyId,
                Companys = this.GetAllCompanys().Values.Where(s => s.CompanyId.EqualsIgnoreCase(companyId)).ToList(),
                //QyWxCustomerId = customer.Id//暂屏蔽，后续需支持的话需改造此函数，同时支持comid和运维系统商户入参
            };
            session.CurrentRequestObject = reqDto;
            session.Device = "qywxminiprogram"; // 小程序端

            var userCtx = new UserContext();
            userCtx.Container = this.Container;
            userCtx.SetUserSession(session);

            if (this.Request.UrlReferrer?.AbsoluteUri?.IndexOf("/shell.html?") > 0)
            {
                userCtx.IsTempToken = true;
            }
            else
            {
                userCtx.IsTempToken = false;
            }

            return userCtx;
        }

        /// <summary>
        /// ORM数据读写引擎
        /// </summary>
        protected IDataManager GetDataManager()
        {
            if (this.Context == null) return null;
            var dm = this.Container?.GetService<IDataManager>();
            return dm;
        }

        protected void Debug(string content)
        {
            try
            {
                DebugUtil.WriteLogToFile(content, "MPAPI");
            }
            catch(Exception e)
            {

            }
        }
    }
}
