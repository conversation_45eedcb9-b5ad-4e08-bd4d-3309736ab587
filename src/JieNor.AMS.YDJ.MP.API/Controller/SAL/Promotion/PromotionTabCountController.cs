using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MP.API.Utils;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.Promotion
{
    /// <summary>
    /// 微信小程序：促销活动Tab数量取数接口
    /// </summary>
    public class PromotionTabCountController : BasePromotionController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(PromotionTabCountDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<List<BaseCountModel>>();

            resp.Data = new List<BaseCountModel>();

            foreach (var tab in dto.Tabs)
            {
                if (!Tabs.TryGetValue(tab, out var tabName))
                {
                    continue;
                }

                resp.Data.Add(new BaseCountModel
                {
                    Id = tab,
                    Name = tabName,
                    Count = GetTotalRecordByTab(this.Context, tab)
                });
            }

            resp.Message = "操作成功！";
            resp.Success = true;

            return resp;
        }
    }
}