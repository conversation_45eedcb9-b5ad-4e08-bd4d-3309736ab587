using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Config;
using ServiceStack;
using Newtonsoft.Json;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.CustomerRecord
{
    /// <summary>
    /// 微信小程序：商机推进阶段接口
    /// </summary>
    public class CustomerRecordPushPhaseController : BaseController
    {
        private HtmlForm CustomerRecordForm;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CustomerRecordPushPhaseDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            //直接根据唯一标识获取数据
            this.CustomerRecordForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customerrecord");
            var formDt = this.CustomerRecordForm.GetDynamicObjectType(this.Context);
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, formDt);
            var data = this.CustomerRecordForm.GetBizDataById(this.Context, dto.Id);

            if (data == null)
            {
                resp.Message = "商机不存在或已被删除！";
                resp.Success = false;
                return resp;
            }

            // 只有意向跟进和方案设计阶段才能推进
            string phase = JNConvert.ToStringAndTrim(data["fphase"]);
            if (!CanPush(phase))
            {
                resp.Message = "当前阶段无法推进！";
                resp.Success = false;
                return resp;
            }

            string nextPhase = GetNextPhase(phase);
            if (nextPhase.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "当前阶段没有下一阶段，无法推进！";
                resp.Success = false;
                return resp;
            }

            data["fphase"] = nextPhase;

            resp = AddFollowerRecord(data);
            if (!resp.Success)
            {
                return resp;
            }

            dm.Save(data);

            resp.Message = "操作成功！";
            resp.Success = true;

            return resp;
        }

        /// <summary>
        /// 能否推进
        /// </summary>
        /// <param name="phase"></param>
        /// <returns></returns>
        private bool CanPush(string phase)
        {
            var phases = ConfigExtentions.GetCustomerRecordPhases();

            return phases.Any(s => s.Id == phase && s.Menus.Any(m => m.Code == "push"));
        }

        /// <summary>
        /// 获取下一阶段
        /// </summary>
        /// <param name="phase"></param>
        /// <returns></returns>
        private string GetNextPhase(string phase)
        {
            var phases = ConfigExtentions.GetCustomerRecordPhases();

            for (int i = 0; i < phases.Count; i++)
            {
                var item = phases[i];

                if (item.Id == phase)
                {
                    return i + 1 < phases.Count ? phases[i + 1].Id : null;
                }
            }

            return null;
        }

        private BaseResponse<BaseDataModel> AddFollowerRecord(DynamicObject customerRecordObj)
        {
            FollowerRecordSaveDTO dto = new FollowerRecordSaveDTO();

            string phaseName = this.CustomerRecordForm.GetSimpleSelectItemText(customerRecordObj, "fphase");

            dto.CustomerId = JNConvert.ToStringAndTrim(customerRecordObj["fcustomerid"]);
            dto.Contacts = JNConvert.ToStringAndTrim(customerRecordObj["fcustomername"]);
            dto.Phone = JNConvert.ToStringAndTrim(customerRecordObj["fphone"]);
            dto.Description = $"已推进到{phaseName}阶段";
            dto.ObjectType = "objecttype17";// 推进阶段
            dto.ObjectId = JNConvert.ToStringAndTrim(customerRecordObj["id"]);
            dto.ObjectNo = JNConvert.ToStringAndTrim(customerRecordObj["fbillno"]);
            dto.SourceType = this.CustomerRecordForm.Id;
            dto.SourceNumber = JNConvert.ToStringAndTrim(customerRecordObj["fbillno"]);
            dto.TranId = JNConvert.ToStringAndTrim(customerRecordObj["ftranid"]);

            return this.Context.AddFollowerRecord(this.Request, dto);
        }
    }
}