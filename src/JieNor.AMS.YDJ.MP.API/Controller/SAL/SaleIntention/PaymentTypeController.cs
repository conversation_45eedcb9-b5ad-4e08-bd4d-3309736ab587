using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MP.API.DTO.SAL.SaleIntention;
using JieNor.AMS.YDJ.MP.API.Model.SAL.SaleIntention;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.SaleIntention
{
    /// <summary>
    /// 支付方式
    /// </summary>
    public class PaymentTypeController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(PaymentTypeDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<PaymentTypeModel>();

            // 获取意向单结算的辅助资料和简单下拉框
            resp.Data.ComboData = this.MetaModelService.LoadFormModel(this.Context, "coo_inpourdialog")
                .GetComboDataSource(this.Context, "fway");
            resp.Data.ComboData.Merge(this.MetaModelService.LoadFormModel(this.Context, "coo_chargedialog")
                .GetComboDataSource(this.Context, "frefundway")); 

            HideWay(resp);
            resp.Message = "操作成功！";
            resp.Success = true;

            return resp;
        }

        /// <summary>
        /// 屏蔽【账户支付】
        /// </summary>
        /// <param name="resp"></param>
        private void HideWay(BaseResponse<PaymentTypeModel> resp)
        {

            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_supplier");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            string where = $@"fmainorgid=@fmainorgid";
            var sqlParam = new SqlParam[]
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company)
            };
            var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
            var suppliers = dm.SelectBy(dataReader).OfType<DynamicObject>();
            var isDel = false;
            foreach (var supplier in suppliers)
            {
                var synEntrys = supplier["fentry"] as DynamicObjectCollection;
                var existEntry = synEntrys.FirstOrDefault(o => Convert.ToString(o["fpurpose"]).EqualsIgnoreCase("settleaccount_type_01" as string));
                if (existEntry != null && existEntry["fispayment"].ToString() == "False")
                {
                    isDel = true;
                }
            }

            var wayDS = new List<Dictionary<string, object>>();

            foreach (var item in resp.Data.ComboData["fway"])
            {
                if (Convert.ToString(item["id"]).EqualsIgnoreCase("payway_01") && isDel)
                {
                    continue;
                }

                wayDS.Add(item);
            }
            resp.Data.ComboData["fway"] = wayDS;
        }

    }
}
