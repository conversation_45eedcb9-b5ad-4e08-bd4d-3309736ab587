using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ServiceStack;
using Newtonsoft.Json;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm;
using System.Data;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.SaleIntention
{
    /// <summary>
    /// 微信小程序：销售意向指派设计师接口
    /// </summary>
    public class SaleIntentionArrangeDesignerController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(SaleIntentionArrangeDesignerDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            if (dto.DesignerId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 designerId 不能为空！";
                return resp;
            }

            //直接根据唯一标识获取数据
            var dm = this.GetDataManager();
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_saleintention");
            var formDt = htmlForm.GetDynamicObjectType(this.Context);
            dm.InitDbContext(this.Context, formDt);
            var saleIntentionObj = dm.Select(dto.Id) as DynamicObject;

            if (saleIntentionObj == null)
            {
                resp.Message = "未进行意向报价，无法指派设计！";
                resp.Success = false;
                return resp;
            }

            var designerObj = this.Context.GetBizDataById("ydj_staff", dto.DesignerId);
            if (designerObj == null)
            {
                resp.Message = $"设计师【{dto.DesignerId}】不存在或已删除！";
                resp.Success = false;
                return resp;
            }

            saleIntentionObj["fstylistid"] = dto.DesignerId;
            dm.Save(saleIntentionObj);

            // 添加跟进记录
            AddFollowerRecord(saleIntentionObj, designerObj);

            RewriteCustomerRecordPhase(saleIntentionObj);

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Data = new BaseDataModel();

            return resp;
        }

        private BaseResponse<BaseDataModel> AddFollowerRecord(DynamicObject saleIntentionObj, DynamicObject designerObj)
        {
            FollowerRecordSaveDTO dto = new FollowerRecordSaveDTO();

            dto.CustomerId = JNConvert.ToStringAndTrim(saleIntentionObj["fcustomerid"]);
            dto.Contacts = JNConvert.ToStringAndTrim(saleIntentionObj["flinkstaffid"]);
            dto.Phone = JNConvert.ToStringAndTrim(saleIntentionObj["fphone_e"]);
            dto.Description = $"已指派设计师{JNConvert.ToStringAndTrim(designerObj["fname"])}为客户设计方案";
            dto.ObjectType = "objecttype09";// 指派设计
            dto.ObjectId = JNConvert.ToStringAndTrim(designerObj["id"]);
            dto.ObjectNo = JNConvert.ToStringAndTrim(designerObj["fnumber"]);

            string sourceType = JNConvert.ToStringAndTrim(saleIntentionObj["fsourcetype"]);
            dto.SourceType = sourceType.IsNullOrEmptyOrWhiteSpace()
                ? "ydj_saleintention"
                : sourceType;

            string sourceNumber = JNConvert.ToStringAndTrim(saleIntentionObj["fsourcenumber"]);
            dto.SourceNumber = sourceNumber.IsNullOrEmptyOrWhiteSpace()
                ? JNConvert.ToStringAndTrim(saleIntentionObj["fbillno"])
                : sourceNumber;

            dto.TranId = JNConvert.ToStringAndTrim(saleIntentionObj["ftranid"]);

            return this.Context.AddFollowerRecord(this.Request, dto);
        }

        /// <summary>
        /// 反写商机阶段为“方案设计”
        /// </summary>
        /// <param name="saleIntentionObj"></param>
        private void RewriteCustomerRecordPhase(DynamicObject saleIntentionObj)
        {
            // 获取关联商机
            var saleNo = JNConvert.ToStringAndTrim(saleIntentionObj["fbillno"]);

            var metaModelService = this.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "ydj_customerrecord");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            List<SqlParam> sqlParams = new List<SqlParam>();
            sqlParams.Add(new SqlParam("@fmainorgid", DbType.String, this.Context.Company));
            sqlParams.Add(new SqlParam("@fintentionno", DbType.String, saleNo));
            sqlParams.Add(new SqlParam("@fphase", DbType.String, "customerrecord_phase_02"));
            string sqlWhere = $@"fmainorgid=@fmainorgid and fintentionno = @fintentionno and fphase = @fphase";
            var reader = this.Context.GetPkIdDataReader(htmlForm, sqlWhere, sqlParams);

            var customerRecordObjs = dm.SelectBy(reader).OfType<DynamicObject>().ToList();
            if (customerRecordObjs.Count == 0)
            {
                return;
            }

            foreach (var item in customerRecordObjs)
            {
                // 方案设计
                item["fphase"] = "customerrecord_phase_03";
            }

            dm.Save(customerRecordObjs);
        }
    }
}