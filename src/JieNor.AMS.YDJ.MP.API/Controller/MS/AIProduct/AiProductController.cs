using System;
using System.Collections.Generic;
using System.Text;
using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.DTO.MS.AIProduct;
using JieNor.AMS.YDJ.MP.API.Model.MS.AIProduct;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using Newtonsoft.Json;
using ServiceStack;

namespace JieNor.AMS.YDJ.MP.API.Controller.MS.AIProduct
{
    public class AiProductController : BaseController
    {
        public object Any(AiProductDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            AiProductsModel model;

            if (dto.Answer.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;

                resp.Message = "参数Answer不能为空";

                return resp;
            }

            if (dto.ChoiceId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;

                resp.Message = "参数ChoiceId不能为空";

                return resp;
            }

            if (dto.Score.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;

                resp.Message = "参数Score不能为空";

                return resp;
            }

            model = GetProductListData(dto,out model);

            if (!model.IsNullOrEmptyOrWhiteSpace() && model.AiProducts?.Count > 0)
            {

                resp.Success = true;

                resp.Data = model;

                resp.Message = "获取商品成功";

                return resp;
            }
            else
            {
                resp.Success = false;
                resp.Message = "获取商品失败";
            }

            return resp;
        }

        private AiProductsModel GetProductListData(AiProductDTO dto, out AiProductsModel model)
        {
            var param = new SqlBuilderParameter(Context, "ydj_product");
            param.ReadBillCount = false;
            param.ReadDirty = true;
            param.NoColorSetting = true;
            /*param.PageCount = dto.PageSize;
            param.PageIndex = dto.PageIndex;*/
            param.SrcFormId = "ydj_product";

            /*
             *select tbm.fname,tbm.fimage,tbm.fimage_txt,tbm.fnumber
                        from T_BD_MATERIAL as tbm
                        where exists(select 1
                                     from t_sel_category as tsc
                                              inner join t_sel_categoryentry as tsce on tsce.fid = tsc.fid
                                              inner join t_sel_prop as tsp on tsce.fpropid = tsp.fid
                                     where tsp.fnumber = 'S648'
                                       and tsp.fname = '客户ID'
                                       and tbm.fselcategoryid = tsc.fid);
             */
            var fieldKeys = new string[]
            {
                "fid","fimage","fnumber","fname","fsalprice","fselcategoryid"
            };

            foreach (var fieldKey in fieldKeys)
            {
                var field = param.HtmlForm.GetField(fieldKey);
                var columnList = field.ToListColumn(this.Context);
                foreach (var column in columnList)
                {
                    param.SelectedFieldKeys.Add(column.Id);
                }
            }

            param.AppendFilterString(@"exists(select 1
                                         from t_sel_category as tsc
                                                  inner join t_sel_categoryentry as tsce on tsce.fid = tsc.fid
                                                  inner join t_sel_prop as tsp on tsce.fpropid = tsp.fid
                                         where tsp.fnumber = 'S648'
                                           and tsp.fname = '客户ID'
                                           and fselcategoryid = tsc.fid)");


            //列表构建器
            var listBuilder = this.Context.Container.GetService<IListSqlBuilder>();

            //设置数据隔离方案的过滤条件
            if (param.HtmlForm.EnableDAC)
            {
                var accessFilter = listBuilder.GetListAccessControlFilter(this.Context, param.HtmlForm.Id);
                param.SetFilter(accessFilter);
            }

            //Context.Container.GetService<IMetaModelService>().LoadFormModel(Context,"ydj_product").GetComboDataSource()

            //查询对象
            var queryObj = listBuilder.GetQueryObject(this.Context, param);


            var sql = queryObj.Sql;

            var data = listBuilder.GetQueryData(Context, param, queryObj);

            //var dyCollection = DBService.ExecuteDynamicObject(Context, sql);

            //推荐的编号
            var recommendNumber = String.Empty;

            //根据回答来判断推荐床垫
            switch (dto.Answer)
            {
                case "腰椎不好":
                    // model.RecommendBed = "MCC2-010-2";
                    // model.RecommendBedName = "MCC2-010床垫(客户版/360°自适应3cm)";
                    recommendNumber = "MCC2-010-2";
                    break;

                case "怕热易出汗":
                    recommendNumber = "MCC2-010-4";
                    break;

                case "翻身次数多":
                    recommendNumber = "MCC2-010-3";
                    // model.RecommendBed = "MCC2-010-3";
                    // model.RecommendBedName = "MCC2-010床垫(客户版/多重支撑)";
                    break;

                case "没有上述情况":
                    recommendNumber = "MCC2-010-1";
                    // model.RecommendBed = "MCC2-010-4";
                    // model.RecommendBedName = "MCC2-010床垫(客户版/3D护脊)";
                    break;

                default:
                    break;
            }

            //获取AI床垫辅助资料

            model = new AiProductsModel();

            if (!data.IsNullOrEmptyOrWhiteSpace())
            {
                //用来拼接属性和属性值的
                var sb = new StringBuilder();

                //选配计价服务
                var propSelService = this.Container.GetService<ISelectionPriceService>();

                var defaultAuxProp = GetDefaultAuxProp(Context, data);

                var propEntityDic = GetPropEntityList(Context,defaultAuxProp);


                foreach (var tempDy in data)
                {
                    AiProductModel temp = new AiProductModel();

                    temp.ProductId = Convert.ToString(tempDy["fbillhead_id"]);

                    temp.Number = Convert.ToString(tempDy["fnumber"]);

                    temp.Name = Convert.ToString(tempDy["fname"]);

                    temp.SalPrice = Convert.ToDecimal(tempDy["fsalprice"]);

                    temp.ImgUrl = Convert.ToString(tempDy["fimage"]);

                    //var now = DateTime.Now;

                    //List<Dictionary<string,string>> auxPropList = null;

                    //判断获取到的商品默认属性是否为空和是否含有该键
                    if (!defaultAuxProp.IsNullOrEmptyOrWhiteSpace() && defaultAuxProp.ContainsKey(Convert.ToString(tempDy["fselcategoryid"])))
                    {
                        //获取默认的属性ID和属性值ID
                        var auxPropList = defaultAuxProp[Convert.ToString(tempDy["fselcategoryid"])];
                        //获取售卖价格
                        //temp.ProductId
                        
                        var priceInfo =  ProductUtil.GetPrices(Context, temp.ProductId, default(DateTime), false,auxPropList);
                        //赋值售卖价格
                        temp.SalPrice = priceInfo.SalPrice;

                        if (!propEntityDic.IsNullOrEmptyOrWhiteSpace() && propEntityDic.ContainsKey(Convert.ToString(tempDy["fselcategoryid"])))
                        {
                            var entities = propEntityDic[Convert.ToString(tempDy["fselcategoryid"])];

                            //选配计价服务
                            //if (!priceInfo.IgnorePriceFormula)
                            //{
                            //    var price = propSelService.GetProductPrice_New(Context, temp.ProductId, temp.SalPrice, entities);

                            //    temp.SalPrice = price;

                            //}
                            
                        }

                    }

                    
                    
                    //var priceInfo = ProductUtil.GetPrices(Context, temp.ProductId,now,false,now);

                    //var priceInfo = ProductUtil.GetPrices(this.Context, temp.ProductId, now, false, new Dictionary<string,string>(), now);

                    //var priceInfo = ProductUtil.GetPrices(Context, temp.ProductId, "");

                    //temp.SalPrice = priceInfo.SalPrice;

                    var auxProps = ProductUtil.GetAuxPropInfo(Context, temp.ProductId);

                    
                    //
                    /*if (!priceInfo.IgnorePriceFormula)
                    {
                        temp.SalPrice = propSelService.GetProductPrice(Context, temp.ProductId, temp.SalPrice, null);
                    }*/

                    //var auxPropInfos = ProductUtil.GetAuxPropInfo(Context, temp.Id, null, false);

                    if (!auxProps.IsNullOrEmptyOrWhiteSpace())
                    {
                        foreach (var Prop in auxProps)
                        {
                            var aux = new AuxPropInfo();

                            /*if (!temp.Name.IsNullOrEmptyOrWhiteSpace() && temp.Name.Contains("云感绗缝"))
                            {
                                foreach (var value in Prop?.ValueList)
                                {
                                    aux.PropName = value?.ValueName;
                                }
                            }*/

                            aux.PropId = Prop.PropId;

                            aux.PropName = Prop.PropName;

                            //先拼接属性名
                            sb.Append($"{aux.PropName}：");

                            if (Prop.ValueList.Count > 0)
                            {
                                foreach (var propValueModel in Prop.ValueList)
                                {
                                    //AuxPropValueInfo info = new AuxPropValueInfo();

                                    /*info.ValueName = propValueModel.ValueName;

                                    info.ValueNumber = propValueModel.ValueNumber;

                                    info.IsDefVal = propValueModel.IsDefVal;

                                    info.IsNonStandard = propValueModel.IsNonStandard;

                                    info.IsNosuitCreate = propValueModel.IsNosuitCreate;*/

                                    aux.PropValueId = propValueModel.ValueId;

                                    aux.PropValueName = propValueModel.ValueName;

                                    /*if (!temp.Name.IsNullOrEmptyOrWhiteSpace() && temp.Name.Contains("云感绗缝") &&
                                        Prop.PropName.Equals("支撑层"))
                                    {
                                        temp.SustainLayer = propValueModel.ValueName;
                                    }*/

                                    //拼接对应的属性值
                                    sb.Append($"{propValueModel.ValueName}，");

                                    //aux.AuxPropValueInfos.Add(info);


                                }
                            }
                            else
                            {
                                //如果属性没有对应的属性值的话，那就直接在后面拼接,
                                sb.Append("，");
                            }

                            temp.AuxPropInfos.Add(aux);
                        }
                    }

                    /*if (!temp.Name.IsNullOrEmptyOrWhiteSpace() && temp.Name.Contains("云感绗缝"))
                    {
                        /*if (!temp.Number.IsNullOrEmptyOrWhiteSpace() && )
                        {
                            
                        }#1#

                        temp.SustainLayer = "AGRO双层独立筒";
                    }*/

                    //temp.AuxPropInfoJarray = ProductUtil.GetAuxPropInfo(Context,temp.Id,"");

                    //temp.AuxPropInfo = auxProps;

                    if (recommendNumber.Equals(temp.Number))
                    {
                        temp.IsRecommend = true;
                    }

                    //将拼接好的属性和属性值赋值给AuxPropsString，把最后的一个'，'去掉
                    temp.AuxPropsString = sb.ToString().TrimEnd('，');

                    //将之前的拼接内容情况，等待下一次的拼接
                    sb.Clear();

                    model.AiProducts.Add(temp);


                }

            }
            else
            {
                return null;
            }

            return model;
        }

        /// <summary>
        /// 获取商品的默认属性
        /// </summary>
        /// <param name="ucx">上下文</param>
        /// <param name="dataList">获取到的数据</param>
        /// <returns></returns>
        private Dictionary<string, List<Dictionary<string, string>>> GetDefaultAuxProp(UserContext ucx,List<Dictionary<string, object>> dataList)
        {
            if (!dataList.IsNullOrEmptyOrWhiteSpace())
            {
                List<string> selectionCategoryIdList = new List<string>();


                foreach (var tempData in dataList)
                {
                    if (tempData.ContainsKey("fselcategoryid"))
                    {
                        var fselcategoryid = Convert.ToString(tempData["fselcategoryid"]);

                        selectionCategoryIdList.Add(fselcategoryid);

                    }
                }

                var auxPropVals = ProductUtil.LoadProductAuxPropVals(ucx,selectionCategoryIdList);
                return auxPropVals;
            }
            return null;
        }


        private Dictionary<string, List<PropEntity>> GetPropEntityList(UserContext ucx, Dictionary<string, List<Dictionary<string, string>>> DefaultAuxProp)
        {
            if (!DefaultAuxProp.IsNullOrEmptyOrWhiteSpace())
            {
                var propEntityDic = ProductUtil.LoadPropEntityList(ucx,DefaultAuxProp);

                return propEntityDic;
            }


            return null;
        }
    }
}