using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.MP.API.Controller.User
{
    /// <summary>
    /// 微信小程序：用户信息取数接口
    /// </summary>
    public class UserProfileController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(UserProfileDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<UserProfileModel>();
            resp.Data.UserId = this.Context.UserId;
            resp.Data.UserName = this.Context.UserName;
            resp.Data.DisplayName = this.Context.DisplayName;

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fid", System.Data.DbType.String, this.Context.UserId)
            };

            var sqlText = $@"select u.fname,u.fimage,u.fgender,u.fphone,u.femail,u.fjob,u.fwechatid,u.fqqid, ISNULL(up.forderdatascope, 'all') as forderdatascope from t_sec_user u with(nolock)
left join t_ydj_userprofile up with(nolock) on u.fid=up.fuserid
where u.fid=@fid";

            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    resp.Data.DisplayName = reader.GetValueToString("fname").Trim();
                    resp.Data.AvatarUrl = reader.GetValueToString("fimage").GetSignedFileUrl(true);
                    resp.Data.Gender = reader.GetValueToString("fgender").Trim();
                    resp.Data.Phone = reader.GetValueToString("fphone").Trim();
                    resp.Data.Email = reader.GetValueToString("femail").Trim();
                    resp.Data.Job = reader.GetValueToString("fjob").Trim();
                    resp.Data.Wechat = reader.GetValueToString("fwechatid").Trim();
                    resp.Data.qq = reader.GetValueToString("fqqid").Trim();
                    resp.Data.OrderDataScope = reader.GetValueToString("forderdatascope").Trim();
                }
            }

            //当前登录用户的企业
            var company = this.Context.Companys.FirstOrDefault(o => o.CompanyId.EqualsIgnoreCase(this.Context.Company));
            resp.Data.Company.Id = company?.CompanyId ?? "";
            resp.Data.Company.Number = company?.CompanyNumber ?? "";
            resp.Data.Company.Name = company?.CompanyName ?? "";
            //获取组织信息
            var orgInfo = GetOrganizationInfo(resp.Data.Company.Id);
            resp.Data.Company.ActualControl = orgInfo["actualControl"];
            resp.Data.Company.orgNumber = orgInfo["orgNumber"];
            resp.Data.Company.orgName = orgInfo["orgName"];

            //当前登录用户的部门和员工信息
            var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
            var dept = baseFormProvider.GetMyDepartment(this.Context);
            var staff = baseFormProvider.GetMyStaff(this.Context);
            var staffId = staff?.Id ?? "";

            resp.Data.Department.Id = dept?.Id ?? "";
            resp.Data.Department.Number = dept?.Number ?? "";
            resp.Data.Department.Name = dept?.Name ?? "";

            resp.Data.Employee.Id = staffId;
            resp.Data.Employee.Number = staff?.Number ?? "";
            resp.Data.Employee.Name = staff?.Name ?? "";

            //加载员工岗位信息
            this.LoadStaffPosition(staffId, resp);

            resp.Message = "取数成功！";
            resp.Success = true;
            return resp;
        }

        /// <summary>
        /// 加载员工岗位信息，优先取主岗位，如果没有主岗位则默认取第一个任岗明细中的岗位
        /// </summary>
        /// <param name="staffId"></param>
        /// <param name="resp"></param>
        private void LoadStaffPosition(string staffId, BaseResponse<UserProfileModel> resp)
        {
            if (staffId.IsNullOrEmptyOrWhiteSpace()) return;

            var staffObj = this.Context.LoadBizDataById("ydj_staff", staffId);
            var staffEntrys = staffObj?["fentity"] as DynamicObjectCollection;
            var mainPost = staffEntrys?.FirstOrDefault(o => Convert.ToBoolean(o["fismain"]));
            if (mainPost == null)
            {
                mainPost = staffEntrys?.FirstOrDefault();
            }
            var positionId = Convert.ToString(mainPost?["fpositionid"]);
            if (positionId.IsNullOrEmptyOrWhiteSpace()) return;

            var positionObj = this.Context.LoadBizDataById("ydj_position", positionId);

            resp.Data.Position.Id = Convert.ToString(positionObj?["id"]);
            resp.Data.Position.Number = Convert.ToString(positionObj?["fnumber"]);
            resp.Data.Position.Name = Convert.ToString(positionObj?["fname"]);
        }

        /// <summary>
        /// 获取企业关联的组织信息
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        private Dictionary<string, string> GetOrganizationInfo(string companyId)
        {
            Dictionary<string, string> result = new Dictionary<string, string>();
            result.Add("actualControl", "");
            result.Add("orgNumber", "");
            result.Add("orgName", "");
            if (companyId.IsNullOrEmptyOrWhiteSpace())
            {
                return result;
            }
            var sql = $"SELECT TOP 1 fcontacter,fnumber,fname  FROM T_BAS_ORGANIZATION AS A WITH(NOLOCK) WHERE fid='{companyId}' AND forgtype='4'";
            using (var reader = this.DBService.ExecuteReader(this.Context, sql))
            {
                while (reader.Read())
                {
                    var corporateName = reader.GetString("fcontacter");
                    result["actualControl"] = corporateName.Trim();
                    var orgNumber = reader.GetString("fnumber");
                    result["orgNumber"] = orgNumber.Trim();
                    var orgName = reader.GetString("fname");
                    result["orgName"] = orgName.Trim();
                }
            }
            return result;
        }
    }
}