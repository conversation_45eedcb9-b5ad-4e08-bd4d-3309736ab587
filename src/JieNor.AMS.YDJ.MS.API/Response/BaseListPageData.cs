using System;
using System.Runtime.Serialization;

namespace JieNor.AMS.YDJ.MS.API.Response
{
    /// <summary>
    /// 列表分页数据模型基类
    /// </summary>
    /// <typeparam name="T">列表数据泛型类型</typeparam>
    public class BaseListPageData<T> : BaseListData<T> where T : class, new()
    {
        /// <summary>
        /// 列表分页数据模型基类
        /// </summary>
        public BaseListPageData()
        {

        }

        /// <summary>
        /// 列表分页数据模型基类
        /// </summary>
        /// <param name="pageSize">每页条数</param>
        public BaseListPageData(int pageSize)
        {
            this.PageSize = pageSize;
        }

        /// <summary>
        /// 每页条数：该属性不需要被序列化到前端
        /// </summary>
        [IgnoreDataMember]
        public int PageSize { get; set; }

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalRecord { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPage
        {
            get
            {
                if (this.PageSize <= 0)
                {
                    this.PageSize = this.TotalRecord;
                }
                if (this.PageSize <= 0)
                {
                    return this.TotalRecord;
                }
                return decimal.ToInt32(Math.Ceiling(this.TotalRecord * 1.0m / this.PageSize));
            }
        }
    }
}