using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.MS.API.Controller.Order;
using JieNor.AMS.YDJ.MS.API.DTO.Order;
using JieNor.AMS.YDJ.MS.API.DTO.PurchaseOrder;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.AMS.YDJ.SWJ.API;
using JieNor.AMS.YDJ.SWJ.API.APIs;
using JieNor.Framework;
using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using StringUtils = JieNor.Framework.StringUtils;

namespace JieNor.AMS.YDJ.MS.API.Controller.PurchaseOrder
{
    /// <summary>
    /// 采购订单：收发
    /// </summary>
    public class PurchaseOrderReNewCancelController : BaseController<PurchaseOrderReNewCancelDTO>
    {
        public string FormId
        {
            get { return "ydj_purchaseorder"; }
        }

        protected HtmlForm HtmlForm { get; set; }

        protected ILogService loger { get; set; }

        protected override bool IsAsync => false;

        protected override string UniquePrimaryKey => "billNo";

        protected override string BizObjectFormId => this.FormId;

        UserContext agentCtx { get; set; }

        UserContext subagentCtx { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(PurchaseOrderReNewCancelDTO dto)
        {
            var resp = new BaseResponse<object>();

            if (!Valid(dto, resp)) return resp;

            resp.Code = 200;
            resp.Success = true;
            resp.Message = "焕新订单取消成功！";

            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

            this.agentCtx = this.Context.CreateAgentDBContext(dto.BizAgentId);

            ResetSysAdminUserContext(this.agentCtx);
            if (!string.IsNullOrWhiteSpace(dto.SourceType) && dto.SourceType.Equals("ZY10"))
            {
                {
                    string orderFormId = "ydj_order";
                    var order = agentCtx.LoadBizDataByNo(orderFormId, "fbillno", new string[] { dto.BillNo })
                        ?.FirstOrDefault();
                    if (order == null)
                    {
                        throw new WarnException($"销售合同{dto.BillNo}不存在，请检查！");
                    }
                    var fiszbrefund = Convert.ToBoolean(order["fiszbrefund"]);
                    if (fiszbrefund)
                    {
                        throw new WarnException($"销售合同{dto.BillNo}总部已退款！");
                    }

                    this.SAL_SetCloseStateOrder(order);
                    OrderReNewCancelNotifyDTO ordDto = new OrderReNewCancelNotifyDTO();
                    ordDto.Data = dto.Data;
                    ordDto.AgentNo = dto.AgentNo;
                    ordDto.BillNo = dto.BillNo;
                    ordDto.BizAgentId = dto.BizAgentId;
                    ordDto.IsRefund = dto.IsRefund;
                    // 反写销售合同的【总部已退款】、生成 收支记录-退
                    this.SAL_BackWriteOrderRefundState(order, resp, ordDto);

                    if (resp.Success)
                    {
                        this.HttpGateway.InvokeBillOperation(this.Context, orderFormId, new List<DynamicObject> { order }, "draft", new Dictionary<string, object>());
                    }
                    else
                    {
                        resp.Code = 400;
                        resp.Success = false;
                    }

                }
            }
            else
            {
                // 增加单据编号
                //dto.Data["fbillno"] = dto.BillNo;
                var purchaseOrder = agentCtx.LoadBizDataByNo(this.HtmlForm.Id, "fbillno", new string[] { dto.BillNo }, true)
                    ?.FirstOrDefault();
                //接收状态 需将对应状态同步更新至合同 并生成 收支记录（退）发起退款。
                if (purchaseOrder == null)
                {
                    throw new WarnException($"采购订单{dto.BillNo}不存在，请检查！");
                }
                var fiszbrefund = Convert.ToBoolean(purchaseOrder["fiszbrefund"]);
                if (fiszbrefund)
                {
                    throw new WarnException($"采购订单{dto.BillNo}总部已退款！");
                }

                purchaseOrder["fiszbrefund"] = dto.IsRefund;
                SetCloseStatePur(purchaseOrder);
                // 反写采购订单的上游销售合同的【总部已退款】、生成 收支记录-退
                this.BackWriteOrderRefundState(purchaseOrder, resp);

                if (resp.Success)
                {
                    this.HttpGateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new List<DynamicObject> { purchaseOrder }, "draft", new Dictionary<string, object>());
                }
                else
                {
                    resp.Code = 400;
                    resp.Success = false;
                }
            }

            return resp;
        }

        /// <summary>
        /// 反写采购订单的上游销售合同的【总部已退款】
        /// 如果是二级提交过来的销售合同 
        //①一级合同，二级合同，二级采购订单均勾选【总部已退款】。
        //②一级合同，二级合同均更新【结算进度】= 已退款。
        //③更新一级合同【已收款金额】= 0，【未收款金额】= 财务信息.成交金额，【结算状态】= 全款未收。
        //④基于二级合同生成退款单，并自动提交审核。（字段映射逻辑请看下方单据转换：销售合同->退款单）
        //⑤自动更新一级、二级合同 单据头.【关闭状态】= 整单关闭；明细.【行关闭状态】= 手动关闭。
        /// </summary>
        private void BackWriteOrderRefundState(DynamicObject purchaseOrder, BaseResponse<object> resp)
        {
            var dm = this.agentCtx.Container.GetService<IDataManager>();
            var htmlForm_od = this.agentCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(this.agentCtx, "ydj_order");
            var orderNo = Convert.ToString(purchaseOrder["fsourcenumber"]);
            if (orderNo.IsNullOrEmptyOrWhiteSpace()) return;

            var OrderData = this.agentCtx.LoadBizDataByNo("ydj_order", "fbillno", new string[] { orderNo }).FirstOrDefault();
            if (OrderData.IsNullOrEmptyOrWhiteSpace()) return;

            //处理其它销售合同一对多时，第一个采购订单 总部取消时 联动其它采购订单一起更新手动关闭状态
            var otherPurDatas = this.agentCtx.LoadBizDataByFilter("ydj_purchaseorder", $"fsourcenumber = '{orderNo}' and fbillno != '{Convert.ToString(purchaseOrder["fbillno"])}'");
            if (otherPurDatas.Any())
            {
                foreach (var otherPurData in otherPurDatas)
                {
                    otherPurData["fiszbrefund"] = purchaseOrder["fiszbrefund"];
                    SetCloseStatePur(otherPurData);
                }
                this.agentCtx.SaveBizData("ydj_purchaseorder", otherPurDatas);
            }

            //若上游销售合同【总部已退款】=是，则不做进一步处理
            if (Convert.ToBoolean(OrderData["fiszbrefund"]))
            {
                resp.Code = 400;
                resp.Message = $"销售合同{OrderData["fbillno"]}【总部已退款】=是,不做处理！";
                resp.Success = false;
                return;
            }
            ;

            //是否二级
            var fisresellorder = Convert.ToBoolean(OrderData["fisresellorder"]);
            if (fisresellorder)
            {
                //1、找到当前销售合同上游的二级采购订单。 
                var purid_sub = Convert.ToString(OrderData["fsourceid"]);
                //LoadBizDataById 可以跨组织取数但是 LoadBizDataByNo 取不到
                var purchaseOrder_sub = this.agentCtx.LoadBizDataById("ydj_purchaseorder", purid_sub);
                if (purchaseOrder_sub.IsNullOrEmptyOrWhiteSpace())
                {
                    resp.Code = 400;
                    resp.Message = $"销售合同{OrderData["fbillno"]}上游二级采购订单不存在！";
                    resp.Success = false;
                    return;
                }

                var fmainorgid = Convert.ToString(purchaseOrder_sub["fmainorgid"]);
                this.subagentCtx = this.Context.CreateAgentDBContext(fmainorgid);

                purchaseOrder_sub["fiszbrefund"] = purchaseOrder["fiszbrefund"];
                SetCloseStatePur(purchaseOrder_sub);
                var htmlForm_pur = this.subagentCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(this.subagentCtx, "ydj_purchaseorder");

                //2、找到二级采购订单上游的 二级销售合同。
                var orderno_sub = Convert.ToString(purchaseOrder_sub["fsourcenumber"]);
                var order_sub = this.subagentCtx.LoadBizDataByNo("ydj_order", "fbillno", new string[] { orderno_sub }).FirstOrDefault();
                if (order_sub.IsNullOrEmptyOrWhiteSpace())
                {
                    resp.Code = 400;
                    resp.Message = $"二级采购订单{purchaseOrder_sub["fbillno"]}不存在上游二级销售合同！";
                    resp.Success = false;
                    return;
                }

                order_sub["fiszbrefund"] = purchaseOrder["fiszbrefund"];
                var resp_pushod = PushReNewOrder(order_sub, this.subagentCtx);
                if (resp_pushod.IsSuccess)
                {
                    order_sub["fsettlprogress"] = Enu_RenewalSettleProgress.已退款;
                    SetCloseState(order_sub);
                    //基于二级合同生成退款单

                    var RefundResult = this.CreateRefund(order_sub, this.subagentCtx);
                    if (!RefundResult.IsSuccess)
                    {
                        resp.Code = 400;
                        resp.Message = RefundResult.ComplexMessage?.ToString();
                        resp.Success = false;
                        return;
                    }
                    OrderData["fiszbrefund"] = purchaseOrder["fiszbrefund"];
                    //结算进度：已退款
                    OrderData["fsettlprogress"] = Enu_RenewalSettleProgress.已退款;
                    //已收
                    OrderData["freceivable"] = 0;
                    //未收款金额：成交总额
                    OrderData["funreceived"] = OrderData["fdealamount"];
                    //结算状态：全款未收
                    OrderData["freceiptstatus"] = "receiptstatus_type_01";
                    SetCloseState(OrderData);

                    dm.InitDbContext(this.subagentCtx, htmlForm_pur.GetDynamicObjectType(this.subagentCtx));
                    dm.Save(purchaseOrder_sub);

                    dm.InitDbContext(this.subagentCtx, htmlForm_od.GetDynamicObjectType(this.subagentCtx));
                    dm.Save(order_sub);

                    dm.InitDbContext(this.agentCtx, htmlForm_od.GetDynamicObjectType(this.agentCtx));
                    dm.Save(OrderData);
                }
                else
                {
                    resp.Code = 400;
                    resp.Message = resp_pushod.ComplexMessage?.ToString();
                    resp.Success = false;
                    return;
                }
            }
            else
            {
                var resp_pushod = PushReNewOrder(OrderData, this.agentCtx);
                if (resp_pushod.IsSuccess)
                {
                    var RefundResult = this.CreateRefund(OrderData, this.agentCtx);
                    if (!RefundResult.IsSuccess)
                    {
                        resp.Code = 400;
                        resp.Message = RefundResult.ComplexMessage?.ToString();
                        resp.Success = false;
                        return;
                    }
                    OrderData["fiszbrefund"] = purchaseOrder["fiszbrefund"];
                    //结算进度：已退款
                    OrderData["fsettlprogress"] = Enu_RenewalSettleProgress.已退款;
                    //已收
                    OrderData["freceivable"] = 0;
                    //未收款金额：成交总额
                    OrderData["funreceived"] = OrderData["fdealamount"];
                    //结算状态：全款未收
                    OrderData["freceiptstatus"] = "receiptstatus_type_01";
                    SetCloseState(OrderData);

                    dm.InitDbContext(this.agentCtx, htmlForm_od.GetDynamicObjectType(this.agentCtx));
                    dm.Save(OrderData);
                }
                else
                {
                    resp.Code = 400;
                    resp.Message = resp_pushod.ComplexMessage?.ToString();
                    resp.Success = false;
                    return;
                }
            }
            //调用 定制，主动同步oms中台
            var operationResult = this.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(this.agentCtx,
            "ydj_order",
            new List<DynamicObject> { OrderData },
            "synctooms",
            new Dictionary<string, object>());

        }

        /// <summary>
        /// 焕新订单发起退款
        /// </summary>
        /// <param name="order"></param>
        /// <param name="ctx"></param>
        /// <returns></returns>
        private IOperationResult PushReNewOrder(DynamicObject order, UserContext ctx)
        {
            //调用 焕新订单发起退款
            var operationResult = this.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(ctx,
            "ydj_order",
            new List<DynamicObject> { order },
            "RenewalRefund",
            new Dictionary<string, object>());
            return operationResult;
        }


        //设置关闭状态
        private void SetCloseState(DynamicObject order)
        {
            var typename = Convert.ToString((order["fbilltype_ref"] as DynamicObject)?["fname"]);
            //V6 ⑤更新所有商品明细【定制订单进度】= 单据作废。
            var IsV6 = Convert.ToString(order["fbilltype"]) == "ydj_order_vsix" || typename == "v6定制柜合同";
            order["fclosestatus"] = CloseStatusConst.Manual;
            var fentrys = order["fentry"] as DynamicObjectCollection;
            foreach (var entry in fentrys)
            {
                entry["fclosestatus_e"] = CloseStatusConst.Manual;
                if (IsV6)
                {
                    entry["fomsprogress"] = "-1";
                }
            }
        }

        private void SetCloseStatePur(DynamicObject pur)
        {
            pur["fclosestatus"] = CloseStatusConst.Manual;
            var fentrys = pur["fentity"] as DynamicObjectCollection;
            foreach (var entry in fentrys)
            {
                entry["fclosestatus_e"] = CloseStatusConst.Manual;
            }
        }

        private IOperationResult CreateRefund(DynamicObject Order, UserContext ctx)
        {
            // 基于合同生成退款单，系统自动提交审核。（字段映射逻辑请看下方单据转换：销售合同->退款单）
            var incomeDisburseMeta = this.MetaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            var payOrder = incomeDisburseMeta.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;

            var svc = this.Container.GetService<IComboDataService>();
            var datas = svc.GetFormComboDatas(this.Context, "coo_settledyn", "paymentdesc");

            payOrder["frenewalflag"] = true;
            payOrder["fdate"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            payOrder["fway"] = "payway_15"; //焕新合同退款
            payOrder["fpurpose"] = "bizpurpose_06";  //订单退
            payOrder["fdirection"] = "direction_01";    //增
            payOrder["fbizdirection"] = "bizdirection_02";  //支出
            payOrder["fsourceformid"] = "ydj_order";
            payOrder["fsourcenumber"] = Order["fbillno"];
            payOrder["fsourceid"] = Order["id"];
            payOrder["fstaffid"] = Order["fstaffid"];
            payOrder["fdeptid"] = Order["fdeptid"];
            payOrder["fcustomerid"] = Order["fcustomerid"];
            // payOrder["fphone"] = order["fphone"]; 
            payOrder["famount"] = Order["fdealamount"];
            payOrder["paymentdesc"] = datas.Where(x => x.Name == "合同款").Select(x => x.Id).FirstOrDefault();   //合同款

            //判断是否已经退过，避免焕新取消多次调用根据总部已退没校验住（焕新取消接口时间太短导致标记还没写入）
            var Isexists = ctx.LoadBizBillHeadDataByACLFilter(incomeDisburseMeta.Id,
                $" fsourcenumber = '{Order["fbillno"]}' and fpurpose = 'bizpurpose_06' and famount = {Order["fdealamount"]}", "fid").Any();

            var resp = new OperationResult();
            if (Isexists)
            {
                resp.IsSuccess = false;
                resp.ComplexMessage.ErrorMessages.Add($"销售合同{Order["fbillno"]}总部已退款，不允许重复退款");
                return resp;
            }

            var result = this.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(ctx, incomeDisburseMeta.Id, new[] { payOrder }, "save",
                 new Dictionary<string, object> { { "IgnoreValidateDataEntities", true }, { "NotAutoSubmit", true } });
            if (!result.IsSuccess) return result;

            result = this.Container.GetService<IHttpServiceInvoker>()
            .InvokeBillOperation(ctx, incomeDisburseMeta.Id, new[] { payOrder }, "submit", new Dictionary<string, object> { { "IgnoreValidateDataEntities", true } });

            if (!result.IsSuccess) return result;

            result = this.Container.GetService<IHttpServiceInvoker>()
                .InvokeBillOperation(ctx, incomeDisburseMeta.Id, new[] { payOrder }, "audit", new Dictionary<string, object> { { "IgnoreValidateDataEntities", true } });

            if (!result.IsSuccess) return result;

            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private Dictionary<string, string> SimpleDataData(DynamicObject order)
        {
            var svc = this.Container.GetService<IComboDataService>();
            var datas = svc.GetFormComboDatas(this.Context, "coo_settledyn", "paymentdesc");
            Dictionary<string, string> data = new Dictionary<string, string>
            {
                { "fsourceid", Convert.ToString(order["id"]) },
                { "fsourcenumber", Convert.ToString(order["fbillno"]) },
                { "fsourceformid", "ydj_order" },
                { "fsettlemaintype", "ydj_customer" },
                { "fsettlemainid", Convert.ToString(order["fcustomerid"])  },
                { "fpurpose", "bizpurpose_06" },//默认退款
                { "fdirection", "direction_01" },
                { "fbizdirection", "bizdirection_02" },

                { "fsettleamount",  Convert.ToString(order["fdealamount"]) },
                { "famount",  Convert.ToString(order["fdealamount"])  },
                //{ "funconfirmamount",  dto.UnconfirmAmount },
                { "fdeptid",  Convert.ToString(order["fdeptid"]) },
                { "fstaffid",  Convert.ToString(order["fstaffid"]) },
                //默认“焕新合同退款”
                { "fway",  "payway_15" },
                //{ "fmybankid",  dto.MyBankId },
                {"fmembershiptranid", Convert.ToString(order["fmembershiptranid"]) },
                { "fdate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") },
                //{ "fdescription", dto.Description },
                //款项说明：合同款
                { "paymentdesc", datas.Where(x => x.Name == "合同款").Select(x => x.Id).FirstOrDefault()}
            };

            // 新建时带上流水号
            //data["ftranid"] = dto.TranId;
            //代收单位
            //data["fcontactunitid"] = dto.fcontactunitid;
            //data["fcontactunittype"] = dto.UnitType;
            ////收款小票号
            //data["freceiptno"] = dto.ReceiptNo;
            //data["fcusacount"] = dto.cusacount;
            #region 销售员
            var joinStaffs = new List<Dictionary<string, object>>();

            joinStaffs.Add(new Dictionary<string, object>
            {
                { "Id", "" },
                { "IsMain", "true" },
                { "DutyId", Convert.ToString(order["fstaffid"]) },
                { "DeptId", Convert.ToString(order["fdeptid"]) },
                { "Ratio", "100" },
                { "Amount", Convert.ToString(order["fdealamount"]) },
                { "Description", "" }
            });
            data.Add("fdutyentry", joinStaffs.ToJson());
            data.Add("fsettletype", "退款");
            #endregion

            return data;

        }
        private string ResultSaveId(string fsourcenumber)
        {
            string dic = string.Empty;
            string strSql = string.Format(@"select  top 1 fid from t_coo_incomedisburse with(nolock) where fsourcenumber='{0}' order by fcreatedate desc", fsourcenumber);
            using (var dr = this.DBService.ExecuteReader(this.Context, strSql.ToString()))
            {
                if (dr.Read())
                {
                    dic = dr["fid"].ToString();
                }
            }
            return dic;
        }
        private string BuildBillData(DynamicObject order)
        {
            Dictionary<string, object> data = new Dictionary<string, object>
            {
                { "id", order["id"] },
                { "fstatus", order["fstatus"] },
                { "freceivable", order["freceivable"] },
                { "funreceived", order["funreceived"] },
                { "fentry", order["fentry"] },
            };
            var billData = (new List<Dictionary<string, object>> { data }).ToJson();

            return billData;
        }

        /// <summary>
        /// 校验
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool Valid(PurchaseOrderReNewCancelDTO dto, BaseResponse<object> resp)
        {
            string billNo = dto.BillNo;

            if (billNo.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = "参数billNo不能为空！";
                resp.Success = false;

                return false;
            }

            if (dto.AgentNo.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = "参数agentNo不能为空！";
                resp.Success = false;

                return false;
            }


            if (dto.IsRefund.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = "参数IsRefund不能为空！";
                resp.Success = false;

                return false;
            }

            dto.BizAgentId = this.Context.GetBizAgentId(dto.AgentNo);
            if (dto.BizAgentId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = $"经销商【{dto.AgentNo}】不存在！";
                resp.Success = false;

                return false;
            }

            return true;
        }

        /// <summary>
        /// 设置编码
        /// </summary>
        /// <returns></returns>
        protected override void SetNumbers()
        {
            this.Request.SetBillNo(MSKey.BillNo, (this.Request.Dto as PurchaseOrderReNewCancelDTO)?.BillNo);
        }

        /// <summary>
        /// 重置为系统管理员
        /// </summary>
        private void ResetSysAdminUserContext(UserContext context)
        {
            UserAuthTicket session = new UserAuthTicket();

            // 用系统预设的管理员身份操作
            session.UserId = "sysadmin";
            session.DisplayName = "系统管理员";
            session.UserName = "系统管理员";

            session.Product = context.Product;
            session.Company = context.Company;
            session.BizOrgId = context.Company;
            session.TopCompanyId = context.TopCompanyId;
            session.ParentCompanyId = context.ParentCompanyId;
            session.Companys = context.Companys.ToList();
            session.Id = context.Id;

            context.SetUserSession(session);
        }

        protected override Dictionary<string, string> CreateDistributedLocks(PurchaseOrderReNewCancelDTO dto)
        {
            return new Dictionary<string, string>
            {
                { $"DistributedLock:{this.FormId}:{dto.BillNo}", $"采购订单 {dto.BillNo} 正在锁定中，请稍后再操作！" }
            };
        }



        private void SAL_SetCloseStateOrder(DynamicObject order)
        {
            order["fclosestatus"] = CloseStatusConst.Manual;
            var fentrys = order["fentry"] as DynamicObjectCollection;
            foreach (var entry in fentrys)
            {
                entry["fclosestatus_e"] = CloseStatusConst.Manual;
            }
        }/// <summary>
         /// 反写销售合同的【总部已退款】
         /// </summary>
        private void SAL_BackWriteOrderRefundState(DynamicObject order, BaseResponse<object> resp, OrderReNewCancelNotifyDTO dto)
        {
            var dm = this.agentCtx.Container.GetService<IDataManager>();
            var htmlForm_od = this.agentCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(this.agentCtx, "ydj_order");

            var OrderData = order;
            if (OrderData.IsNullOrEmptyOrWhiteSpace()) return;

            //若销售合同【总部已退款】=是，则不做进一步处理
            if (Convert.ToBoolean(OrderData["fiszbrefund"]))
            {
                resp.Code = 400;
                resp.Message = $"销售合同{OrderData["fbillno"]}【总部已退款】=是,不做处理！";
                resp.Success = false;
                return;
            }
            ;

            order["fiszbrefund"] = dto.IsRefund;

            //是否二级
            var fisresellorder = Convert.ToBoolean(OrderData["fisresellorder"]);
            if (fisresellorder)
            {
                //1、找到当前销售合同上游的二级采购订单。 
                var purid_sub = Convert.ToString(OrderData["fsourceid"]);
                //LoadBizDataById 可以跨组织取数但是 LoadBizDataByNo 取不到
                var purchaseOrder_sub = this.agentCtx.LoadBizDataById("ydj_purchaseorder", purid_sub);
                if (purchaseOrder_sub.IsNullOrEmptyOrWhiteSpace())
                {
                    resp.Code = 400;
                    resp.Message = $"销售合同{OrderData["fbillno"]}上游二级采购订单不存在！";
                    resp.Success = false;
                    return;
                }

                var fmainorgid = Convert.ToString(purchaseOrder_sub["fmainorgid"]);
                this.subagentCtx = this.Context.CreateAgentDBContext(fmainorgid);

                purchaseOrder_sub["fiszbrefund"] = order["fiszbrefund"];
                SetCloseStatePur(purchaseOrder_sub);
                var htmlForm_pur = this.subagentCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(this.subagentCtx, "ydj_purchaseorder");

                //2、找到二级采购订单上游的 二级销售合同。
                var orderno_sub = Convert.ToString(purchaseOrder_sub["fsourcenumber"]);
                var order_sub = this.subagentCtx.LoadBizDataByNo("ydj_order", "fbillno", new string[] { orderno_sub }).FirstOrDefault();
                if (order_sub.IsNullOrEmptyOrWhiteSpace())
                {
                    resp.Code = 400;
                    resp.Message = $"二级采购订单{purchaseOrder_sub["fbillno"]}不存在上游二级销售合同！";
                    resp.Success = false;
                    return;
                }
                //总部已退款
                order_sub["fiszbrefund"] = order["fiszbrefund"];
                var resp_pushod = PushReNewOrder(order_sub, this.subagentCtx);
                if (resp_pushod.IsSuccess)
                {
                    order_sub["fsettlprogress"] = Enu_RenewalSettleProgress.已退款;
                    SetCloseState(order_sub);
                    //基于二级合同生成退款单

                    var RefundResult = this.CreateRefund(order_sub, this.subagentCtx);
                    if (!RefundResult.IsSuccess)
                    {
                        resp.Code = 400;
                        resp.Message = RefundResult.ComplexMessage?.ToString();
                        resp.Success = false;
                        return;
                    }
                    OrderData["fiszbrefund"] = order["fiszbrefund"];
                    //结算进度：已退款
                    OrderData["fsettlprogress"] = Enu_RenewalSettleProgress.已退款;
                    //已收
                    OrderData["freceivable"] = 0;
                    //未收款金额：成交总额
                    OrderData["funreceived"] = OrderData["fdealamount"];
                    //结算状态：全款未收
                    OrderData["freceiptstatus"] = "receiptstatus_type_01";
                    SetCloseState(OrderData);

                    dm.InitDbContext(this.subagentCtx, htmlForm_pur.GetDynamicObjectType(this.subagentCtx));
                    dm.Save(purchaseOrder_sub);

                    dm.InitDbContext(this.subagentCtx, htmlForm_od.GetDynamicObjectType(this.subagentCtx));
                    dm.Save(order_sub);

                    dm.InitDbContext(this.agentCtx, htmlForm_od.GetDynamicObjectType(this.agentCtx));
                    dm.Save(OrderData);
                }
                else
                {
                    resp.Code = 400;
                    resp.Message = resp_pushod.ComplexMessage?.ToString();
                    resp.Success = false;
                    return;
                }
            }
            else
            {
                var resp_pushod = PushReNewOrder(OrderData, this.agentCtx);
                if (resp_pushod.IsSuccess)
                {
                    var RefundResult = this.CreateRefund(OrderData, this.agentCtx);
                    if (!RefundResult.IsSuccess)
                    {
                        resp.Code = 400;
                        resp.Message = RefundResult.ComplexMessage?.ToString();
                        resp.Success = false;
                        return;
                    }
                    OrderData["fiszbrefund"] = order["fiszbrefund"];
                    //结算进度：已退款
                    OrderData["fsettlprogress"] = Enu_RenewalSettleProgress.已退款;
                    //已收
                    OrderData["freceivable"] = 0;
                    //未收款金额：成交总额
                    OrderData["funreceived"] = OrderData["fdealamount"];
                    //结算状态：全款未收
                    OrderData["freceiptstatus"] = "receiptstatus_type_01";
                    SetCloseState(OrderData);

                    dm.InitDbContext(this.agentCtx, htmlForm_od.GetDynamicObjectType(this.agentCtx));
                    dm.Save(OrderData);
                }
                else
                {
                    resp.Code = 400;
                    resp.Message = resp_pushod.ComplexMessage?.ToString();
                    resp.Success = false;
                    return;
                }
            }
            //调用 定制，主动同步oms中台
            var operationResult = this.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(this.agentCtx,
            "ydj_order",
            new List<DynamicObject> { OrderData },
            "synctooms",
            new Dictionary<string, object>());

        }
    }
}