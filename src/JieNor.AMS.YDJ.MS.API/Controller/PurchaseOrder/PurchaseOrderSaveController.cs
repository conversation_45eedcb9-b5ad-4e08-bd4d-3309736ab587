using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MS.API.DTO.PurchaseOrder;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.AMS.YDJ.SWJ.API;
using JieNor.AMS.YDJ.SWJ.API.APIs;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using ServiceStack;
using StringUtils = JieNor.Framework.StringUtils;

namespace JieNor.AMS.YDJ.MS.API.Controller.PurchaseOrder
{
    /// <summary>
    /// 采购订单：保存接口
    /// </summary>
    public class PurchaseOrderSaveController : BaseController<PurchaseOrderSaveDTO>
    {
        public string FormId
        {
            get { return "ydj_purchaseorder"; }
        }

        protected HtmlForm HtmlForm { get; set; }

        protected ILogService loger { get; set; }

        protected override bool IsAsync => true;

        protected override string UniquePrimaryKey => "billNo";

        protected override string BizObjectFormId => this.FormId;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(PurchaseOrderSaveDTO dto)
        {
            var resp = new BaseResponse<object>();

            if (!Valid(dto, resp)) return resp;

            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

            var agentCtx = this.Context.CreateAgentDBContext(dto.BizAgentId);

            // 增加单据编号
            dto.Data["fbillno"] = dto.BillNo;

            var response = this.HttpGateway.InvokeLocal<CommonBillDTOResponse>(agentCtx, new CommonBillDTO()
            {
                FormId = this.FormId,
                OperationNo = "MSSave",
                PageId = Guid.NewGuid().ToString("N"),
                SimpleData = new Dictionary<string, string>
                {
                    { "data", ObjectUtils.ToJson(dto.Data) },
                    { "IgnoreCheckPermssion", "true" }
                }
            });

            #region 需求5000

            try
            {
                loger = this.Context.Container.GetService<ILogService>();

                //当采购订单<提交总部>后，总部审核通过并回传【总部合同号】时，
                //此时需要判断当前采购订单的【第三方来源】=“三维家”时，则需要调用三维家采购订单回传接口，将数据回传到三维家。
                var thirdsource = string.Empty;
                var platformOrderId = string.Empty;
                var platformOrderCode = string.Empty;
                var customSaleOrderNo = string.Empty;
                var customOrderItemNo = string.Empty;
                var sqlText = $@" select t0.fthirdsource,t0.fplatformorderid,t0.fthirdbillno,t0.fhqderno,t2.fseq_e
                              from t_ydj_purchaseorder t0
                              right join t_bas_organization t1 on t0.fmainorgid=t1.fid
                              right join t_ydj_poorderentry t2 on t0.fid=t2.fid
                              where t0.fbillno='{dto.BillNo}' and  t1.fnumber='{dto.AgentNo}' ";

                using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
                {
                    if (reader.Read())
                    {
                        thirdsource = Convert.ToString(reader["fthirdsource"]);
                        platformOrderId = Convert.ToString(reader["fplatformorderid"]);
                        platformOrderCode = Convert.ToString(reader["fthirdbillno"]);
                        customSaleOrderNo = Convert.ToString(reader["fhqderno"]);
                        customOrderItemNo = Convert.ToString(Convert.ToInt32(reader["fseq_e"]) * 10);
                    }
                }

                //customSaleOrderNo格式化 10位，不够前面补0
                if (!StringExtensions.IsNullOrEmpty(customSaleOrderNo))
                {
                    char[] temp = customSaleOrderNo.ToCharArray();
                    char[] newTemp = new char[10] { '0', '0', '0', '0', '0', '0', '0', '0', '0', '0' };
                    for (int i = 0; i < temp.Length; i++)
                    {
                        newTemp[10 - temp.Length + i] = temp[i];
                    }

                    customSaleOrderNo = new string(newTemp);
                }

                //customOrderItemNo格式化 6位，不够前面补0
                if (!StringExtensions.IsNullOrEmpty(customOrderItemNo))
                {
                    char[] temp = customOrderItemNo.ToCharArray();
                    char[] newTemp = new char[6] { '0', '0', '0', '0', '0', '0' };
                    for (int i = 0; i < temp.Length; i++)
                    {
                        newTemp[6 - temp.Length + i] = temp[i];
                    }

                    customOrderItemNo = new string(newTemp);
                }

                if (thirdsource.Equals("三维家"))
                {
                    var backinfo = PurchaseOrderAPI.updateFactoryOrderCustomInfo(SWJClient.Instance, platformOrderId,
                        platformOrderCode, customSaleOrderNo, customOrderItemNo);

                    try
                    {
                        loger.WriteLogToFile(StringUtils.Fmt("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，采购订单数据回传,修改工厂单业务信息】，内容:{4}", this.Context.UserName,
                                this.Context.UserPhone, this.Context.Company,
                                DateTime.Now.ToString("HH:mm:ss"), "backinfo：" + JsonConvert.SerializeObject(backinfo)),
                            "SWJLogFile");
                    }
                    catch (Exception)
                    {
                        //日志文件可能会出现被占用的情况，不做处理
                    }
                }
            }
            catch (Exception ex)
            {
                try
                {
                    loger.WriteLogToFile(StringUtils.Fmt("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，Error】，内容:{4}", this.Context.UserName,
                            this.Context.UserPhone, this.Context.Company,
                            DateTime.Now.ToString("HH:mm:ss"), "ex.Message：" + ex.Message + "ex.StackTrace：" + ex.StackTrace),
                    "SWJLogFile");
                }
                catch (Exception)
                {
                    //日志文件可能会出现被占用的情况，不做处理
                }
                resp.Success = false;
                resp.Code = 10001;
                return resp;
            }

            #endregion



            var result = response?.OperationResult;
            return result.ToResponseModel<object>();
        }

        /// <summary>
        /// 校验
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool Valid(PurchaseOrderSaveDTO dto, BaseResponse<object> resp)
        {
            string billNo = dto.BillNo;

            if (billNo.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = "参数billNo不能为空！";
                resp.Success = false;

                return false;
            }

            if (dto.AgentNo.IsNullOrEmptyOrWhiteSpace())
            {
                string sql = "SELECT fmainorgid FROM T_YDJ_PURCHASEORDER WITH(nolock) WHERE FBILLNO=@billno AND fcancelstatus='0'  ";
                SqlParam sqlParam = new SqlParam("@billno", System.Data.DbType.String, dto.BillNo);

                using (var dr = this.DBService.ExecuteReader(this.Context, sql, sqlParam))
                {
                    if (dr.Read())
                    {
                        dto.BizAgentId = Convert.ToString(dr["fmainorgid"]);
                        goto ZYTag;
                    }
                }
                ;
                var purOrderNo = this.Context.LoadBizDataByNo(FormId, "fbillno", new List<string>() { dto.BillNo }).FirstNonDefault();
                if (purOrderNo != null)
                {
                    dto.BizAgentId = Convert.ToString(purOrderNo["fmainorgid"]);
                    goto ZYTag;
                }
                resp.Code = 400;
                resp.Message = "参数agentNo不能为空！";
                resp.Success = false;

                return false;
            }

            dto.BizAgentId = this.Context.GetBizAgentId(dto.AgentNo);
        ZYTag:
            if (dto.BizAgentId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = $"经销商【{dto.AgentNo}】不存在！";
                resp.Success = false;

                return false;
            }

            return true;
        }

        /// <summary>
        /// 设置编码
        /// </summary>
        /// <returns></returns>
        protected override void SetNumbers()
        {
            this.Request.SetBillNo(MSKey.BillNo, (this.Request.Dto as PurchaseOrderSaveDTO)?.BillNo);
        }

        protected override Dictionary<string, string> CreateDistributedLocks(PurchaseOrderSaveDTO dto)
        {
            return new Dictionary<string, string>
            {
                { $"DistributedLock:{this.FormId}:{dto.BillNo}", $"采购订单 {dto.BillNo} 正在锁定中，请稍后再操作！" }
            };
        }
    }
}