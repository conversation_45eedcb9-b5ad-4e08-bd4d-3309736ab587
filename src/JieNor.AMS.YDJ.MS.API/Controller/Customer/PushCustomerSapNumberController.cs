using JieNor.AMS.YDJ.MS.API.DTO.Customer;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Interface;
using JieNor.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MS.API.Utils;
using ServiceStack;

namespace JieNor.AMS.YDJ.MS.API.Controller.Customer
{
    public class PushCustomerSapNumberController : BaseController<PushCustomerSapNumberDTO>
    {
        public string FormId
        {
            get { return "ydj_customer"; }
        }

        protected UserContext agentCtx { get; set; }

        /// <summary>
        /// 是否异步执行
        /// </summary>
        protected override bool IsAsync => false;

        /// <summary>
        /// 请求唯一主键
        /// </summary>
        protected override string UniquePrimaryKey => "CustomerNo";

        /// <summary>
        /// 业务对象表单ID（用于异步操作日志记录）
        /// </summary>
        protected override string BizObjectFormId
        {
            get { return "ydj_customer"; }
        }
        
        /// <summary>
        /// 销售合同数据包
        /// </summary>
        private List<DynamicObject> customerDys { get; set; }
        
        public override object Execute(PushCustomerSapNumberDTO dto)
        {
            var resp = new BaseResponse<object>();

            try
            {
                // 1. 参数验证
                if (!ValidateParameters(this.Context,dto, resp))
                    return resp;

                if (this.agentCtx != null && this.customerDys != null && this.customerDys.Any())
                {
                    var option = GetOption(dto);
                    var getWay = this.agentCtx.Container.GetService<IHttpServiceInvoker>();
                    var result = getWay.InvokeBillOperation(this.agentCtx,this.FormId, this.customerDys.ToArray(), "pushcustomersapnumber",option);
                    
                    if (result.IsSuccess && !result.ComplexMessage.ErrorMessages.Any() && !result.ComplexMessage.WarningMessages.Any())
                    {
                        var msgList = new List<string>();
                        foreach (var tempMessage in result.ComplexMessage.SuccessMessages)
                        {
                            msgList.Add(tempMessage);
                        }

                        if (result.ComplexMessage.ErrorMessages != null && result.ComplexMessage.ErrorMessages.Any())
                        {
                            foreach (var tempMessage in result.ComplexMessage.ErrorMessages)
                            {
                                msgList.Add(tempMessage);
                            }
                        }
                        resp.Code = 200;
                        resp.Message = string.Join(",",msgList.Select(x=>x));
                        resp.Success = true;
                        // 设置成功编码到集成操作日志
                        this.Request.SetBillNo(MSKey.SuccessNumber, dto.CustomerNo);
                    }
                    else
                    {
                        resp.Code = 400;
                        resp.Message = string.Join(",",result.ComplexMessage.ErrorMessages.Select(x=>x));
                        resp.Success = false;

                        // 设置失败编码到集成操作日志
                        this.Request.SetBillNo(MSKey.FailNumber, dto.CustomerNo);
                    }
                }
                else
                {
                    resp.Code = 400;
                    resp.Message = "经销商上下文创建失败，请检查对应数据!";
                    resp.Success = false;

                    // 设置失败编码到集成操作日志
                    this.Request.SetBillNo(MSKey.FailNumber, dto.CustomerNo);
                }
            }
            catch (Exception ex)
            {
                resp.Success = false;
                resp.Message = $"操作失败：{ex.Message}";
                resp.Code = 500;
                this.LogService?.Error($"更新客户SAP编码失败：{ex.Message}", ex);
                // 设置失败编码到集成操作日志
                this.Request.SetBillNo(MSKey.FailNumber, dto.CustomerNo);
            }

            return resp;
        }

        /// <summary>
        /// 参数验证
        /// </summary>
        private bool ValidateParameters(UserContext userCtx,PushCustomerSapNumberDTO dto, BaseResponse<object> resp)
        {
            if (dto.CustomerNo.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = "客户编码不能为空！";
                resp.Code = 400;
                return false;
            }

            if (dto.CustomerSapNumber.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = "客户SAP编码不能为空！";
                resp.Code = 400;
                return false;
            }

            var sqlStr = " select fid,fnumber,fmainorgid from t_ydj_customer with(nolock) where fnumber=@fnumber and fforbidstatus = '0' ";

            var sqlParams = new List<SqlParam>();
            
            sqlParams.Add(new SqlParam("@fnumber", System.Data.DbType.String, dto.CustomerNo));

            var customerDys = userCtx.ExecuteDynamicObject(sqlStr,sqlParams);

            if (customerDys == null || !customerDys.Any())
            {
                resp.Success = false;
                resp.Message = $"客户编码为{dto.CustomerNo}查找不到，或者已被删除，请检查！";
                resp.Code = 400;
                return false;
            }

            if (customerDys != null && customerDys.Count > 1)
            {
                resp.Success = false;
                resp.Message = $"客户编码为{dto.CustomerNo}存在多个，无法确定是哪个，请检查！";
                resp.Code = 400;
                return false;
            }

            var orgId = Convert.ToString(customerDys.FirstNonDefault()?["fmainorgid"]);

            var cusId = Convert.ToString(customerDys.FirstNonDefault()?["fid"]);

            this.agentCtx = userCtx.CreateAgentDBContext(orgId);

            this.customerDys = this.agentCtx.LoadBizDataById("ydj_customer",new List<string>(){cusId},false);

            return true;
        }
        
        private Dictionary<string, object> GetOption(PushCustomerSapNumberDTO dto)
        {
            var option = new Dictionary<string,object>();

            option.Add("customerSapNumberInfos",new List<PushCustomerSapNumberDTO>(){dto}.ToJson());
            
            return option;
        }
        
        /// <summary>
        /// 创建分布式锁
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        protected override Dictionary<string, string> CreateDistributedLocks(PushCustomerSapNumberDTO dto)
        {
            return new Dictionary<string, string>
            {
                { $"DistributedLock:{this.FormId}:{dto.CustomerNo}", $"销售合同 {dto.CustomerNo} 正在处理回传客户sap编码，请稍后再操作！" }
            };
        }
        
        /// <summary>
        /// 设置单据编码到请求上下文中（用于集成操作日志记录）
        /// </summary>
        protected override void SetNumbers()
        {
            var dto = this.Request.Dto as PushCustomerSapNumberDTO;
            if (dto != null)
            {
                this.Request.SetBillNo(MSKey.BillNo, dto.CustomerNo);
            }
        }
    }
}