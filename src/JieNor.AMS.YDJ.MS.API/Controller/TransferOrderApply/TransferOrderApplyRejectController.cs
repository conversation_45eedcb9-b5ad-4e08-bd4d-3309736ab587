using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MS.API.DTO.TransferOrderApply;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.AMS.YDJ.MS.API.Controller.TransferOrderApply
{
    /// <summary>
    /// 转单申请：审批驳回接口
    /// </summary>
    public class TransferOrderApplyRejectController : BaseController<TransferOrderApplyRejectDTO>
    {
        public HtmlForm HtmlForm { get; set; }

        protected string FormId
        {
            get { return "ydj_transferorderapply"; }
        }

        protected string OperationName
        {
            get { return "审批驳回"; }
        }

        protected override bool IsAsync => false;

        protected override string UniquePrimaryKey => "billNo";

        protected override string BizObjectFormId => this.FormId;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(TransferOrderApplyRejectDTO dto)
        {
            var resp = new BaseResponse<object>();

            if (!Valid(dto, resp)) return resp;

            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

            // 经销商
            string agentId = dto.ReceiverAgentId;
            var billData = new List<Dictionary<string, object>>();
            var agentCtx = this.Context.CreateAgentDBContext(agentId);
            var transferOrderApply = agentCtx.LoadBizDataByNo(this.FormId, "fbillno", new[] { dto.BillNo }).FirstOrDefault();
            if (transferOrderApply != null)
            {
                billData.Add(new Dictionary<string, object>
                {
                    { "id", Convert.ToString(transferOrderApply["id"]) },
                    //{ "ftransferstatus", dto.TransferStatus },
                    { "frejectdate", dto.RejectDate },
                    { "frejectreason", dto.RejectReason }
                });
            }
            else
            {
                resp.Message = $"转单申请单【{dto.BillNo}】不存在！";
                resp.Code = 200;
                resp.Success = true;
                return resp;
            }

            // 向麦浩系统发送请求
            var response = this.HttpGateway.InvokeLocal<CommonBillDTOResponse>(agentCtx, new CommonBillDTO()
            {
                FormId = this.FormId,
                OperationNo = "MSReject",
                BillData = billData.ToJson()
            });

            var result = response?.OperationResult;
            resp = result.ToResponseModel<object>();

            return resp;
        }

        /// <summary>
        /// 校验
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool Valid(TransferOrderApplyRejectDTO dto, BaseResponse<object> resp)
        {
            if (dto.BillNo.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = "参数billNo不能为空！";
                resp.Success = false;

                return false;
            }

            // 获取接单方业务经销商
            var receiverAgentId = this.Container.GetService<IAgentService>().GetBizAgentIdByNo(this.Context, dto.ReceiverAgentNo);
            if (receiverAgentId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = $"接单方经销商【{dto.ReceiverAgentNo}】不存在！";
                resp.Success = false;

                return false;
            }

            var receiverAgent = this.Context.LoadBizDataById("bas_agent", receiverAgentId);

            dto.ReceiverAgentId = Convert.ToString(receiverAgent["id"]);
            dto.ReceiverAgentName = Convert.ToString(receiverAgent["fname"]);

            return true;
        }

        /// <summary>
        /// 设置编码
        /// </summary>
        /// <returns></returns>
        protected override void SetNumbers()
        {
            this.Request.SetBillNo(MSKey.BillNo, (this.Request.Dto as TransferOrderApplyRejectDTO)?.BillNo);
        }

        protected override Dictionary<string, string> CreateDistributedLocks(TransferOrderApplyRejectDTO dto)
        {
            return new Dictionary<string, string>
            {
                { $"DistributedLock:{this.FormId}:{dto.BillNo}", $"转单申请单 {dto.BillNo} 正在锁定中，请稍后再操作！" }
            };
        }

        #region 注释：批量处理
        ///// <summary>
        ///// 处理请求
        ///// </summary>
        ///// <param name="dto"></param>
        ///// <returns></returns>
        //public object Any(TransferOrderApplyRejectDTO dto)
        //{
        //    base.InitializeOperationContext(dto);

        //    var resp = new BaseResponse<List<SuccessBillData>>
        //    {
        //        Data = new List<SuccessBillData>()
        //    };

        //    if (!Valid(dto, resp)) return resp;

        //    this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

        //    // 分组执行 
        //    foreach (var group in dto.Data.GroupBy(s => s.ReceiverAgentNo))
        //    {
        //        // 经销商
        //        string agentNo = group.Key;

        //        var billData = new List<Dictionary<string, object>>();
        //        var billNos = group.Select(s => s.BillNo).Distinct();
        //        var ctx = CreateAgentUserContext(agentNo);
        //        var transferOrderApplys = ctx.LoadBizDataByNo(this.FormId, "fbillno", billNos);

        //        foreach (var entry in dto.Data)
        //        {
        //            var transferOrderApply =
        //                transferOrderApplys.FirstOrDefault(s =>
        //                    Convert.ToString(s["fbillno"]).EqualsIgnoreCase(entry.BillNo));
        //            if (transferOrderApply != null)
        //            {
        //                billData.Add(new Dictionary<string, object>
        //                {
        //                    { "id", Convert.ToString(transferOrderApply["id"]) },
        //                    { "ftransferstatus", entry.TransferStatus },
        //                    { "frejectdate", entry.RejectDate },
        //                    { "frejectreason", entry.RejectReason }
        //                });
        //            }
        //            else
        //            {
        //                resp.ErrorMessages.Add($"转单申请单【{entry.BillNo}】不存在！");
        //                continue;
        //            }
        //        }

        //        Invoke(ctx, agentNo, billData, group, resp);
        //    }

        //    if (resp.Data.Any())
        //    {
        //        resp.Success = true;
        //        resp.Code = 200;
        //        resp.Message = resp.ErrorMessages.Any() ? $"【{this.HtmlForm.Caption}】部分{this.OperationName}成功！" : $"【{this.HtmlForm.Caption}】{this.OperationName}成功！";
        //    }
        //    else
        //    {
        //        resp.Success = false;
        //        resp.Code = 500;
        //        resp.Message = $"【{this.HtmlForm.Caption}】{this.OperationName}失败！";
        //    }

        //    return resp;
        //}

        ///// <summary>
        ///// 执行
        ///// </summary>
        ///// <param name="ctx"></param>
        ///// <param name="agentNo"></param>
        ///// <param name="billData"></param>
        ///// <param name="entrys"></param>
        ///// <param name="resp"></param>
        //private void Invoke(UserContext ctx, string agentNo, List<Dictionary<string, object>> billData, IEnumerable<TransferOrderApplyRejectDTO.TransferOrderApplyRejectEntry> entrys, BaseResponse<List<SuccessBillData>> resp)
        //{
        //    if (billData == null || billData.Count == 0) return;

        //    // 向麦浩系统发送请求
        //    var response = this.HttpGateway.InvokeLocal<CommonBillDTOResponse>(ctx, new CommonBillDTO()
        //    {
        //        FormId = this.FormId,
        //        OperationNo = "MSReject",
        //        PageId = Guid.NewGuid().ToString("N"),
        //        BillData = billData.ToJson()
        //    });

        //    var result = response?.OperationResult;
        //    // 出现错误，后续不执行
        //    if (result == null)
        //    {
        //        foreach (var item in entrys)
        //        {
        //            resp.ErrorMessages.Add($"接单经销商【{item.ReceiverAgentNo}】转单申请单【{item.BillNo}】{this.OperationName}失败！");
        //        }

        //        return;
        //    }

        //    // 加载错误信息
        //    resp.ErrorMessages.AddRange(result.ComplexMessage.ErrorMessages);
        //    // 执行成功，填充执行成功的单据
        //    if (result.IsSuccess)
        //    {
        //        var srvData = result.SrvData as List<InternalSuccessBillData>;
        //        if (srvData != null)
        //        {
        //            resp.Data.AddRange(srvData.Select(s => new SuccessBillData
        //            {
        //                AgentNo = agentNo,
        //                BillNo = s.BillNo
        //            }));
        //        }
        //    }
        //}

        ///// <summary>
        ///// 校验
        ///// </summary>
        ///// <param name="dto"></param>
        ///// <param name="resp"></param>
        ///// <returns></returns>
        //private bool Valid(TransferOrderApplyRejectDTO dto, BaseResponse<List<SuccessBillData>> resp)
        //{
        //    if (dto.Data == null || dto.Data.Count == 0)
        //    {
        //        resp.Code = 400;
        //        resp.Message = "参数data不能为空！";
        //        resp.Success = false;

        //        return false;
        //    }

        //    var agentNos = new List<string>();
        //    agentNos.AddRange(dto.Data.Select(s => s.ReceiverAgentNo));

        //    var agents = this.Context.LoadBizDataByNo("bas_agent", "fnumber", agentNos.Distinct());

        //    string errorMsg = string.Empty;
        //    foreach (var entry in dto.Data)
        //    {
        //        // 接单方经销商
        //        var receiverAgent = agents.FirstOrDefault(s =>
        //            Convert.ToString(s["fnumber"]).EqualsIgnoreCase(entry.ReceiverAgentNo));
        //        if (receiverAgent == null)
        //        {
        //            errorMsg += $"接单方经销商【{entry.ReceiverAgentNo}】不存在！";
        //        }
        //        else
        //        {
        //            entry.ReceiverAgentId = Convert.ToString(receiverAgent["id"]);
        //            entry.ReceiverAgentName = Convert.ToString(receiverAgent["fname"]);
        //        }
        //    }

        //    if (!errorMsg.IsNullOrEmptyOrWhiteSpace())
        //    {
        //        resp.Code = 400;
        //        resp.Message = errorMsg;
        //        resp.Success = false;

        //        return false;
        //    }

        //    return true;
        //}
        #endregion
    }
}