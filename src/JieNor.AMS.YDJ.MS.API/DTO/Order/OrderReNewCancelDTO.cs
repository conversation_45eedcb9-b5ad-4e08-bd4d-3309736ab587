using System;
using System.Collections.Generic;
using JieNor.AMS.YDJ.MS.API.Filter;
using Newtonsoft.Json.Linq;
using ServiceStack;

namespace JieNor.AMS.YDJ.MS.API.DTO.PurchaseOrder
{
    /// <summary>
    /// 销售合同：OMS焕新预收合同退款接口
    /// </summary>
    [Api("OMS焕新预收合同退款接口")]
    [Route("/msapi/order/recrefund")]
    [Authenticate]
    [OperationLogFilter("ydj_order")]
    public class OrderRecRefundDTO : BaseDTO
    {
        /// <summary>
        /// 合同编号
        /// </summary>
        public string BillNo { get; set; }

        /// <summary>
        /// 业务经销商id
        /// </summary>
        internal string BizAgentId { get; set; }

        /// <summary>
        /// 经销商编码
        /// </summary>
        public string AgentNo { get; set; }

        public JObject Data { get; set; }

        /// <summary>
        /// 请求时间戳
        /// </summary>
        public string RequestStamp { get; set; }


        private bool _isRetry = false;
        /// <summary>
        /// 是否错误重试（默认：否）
        /// </summary>
        public bool IsRetry
        {
            get
            {
                return _isRetry;
            }
            set
            {
                _isRetry = value;
            }
        }
    }
}