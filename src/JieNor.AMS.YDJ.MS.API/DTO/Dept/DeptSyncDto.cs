using System;
using System.Collections.Generic;
using JieNor.AMS.YDJ.MS.API.Filter;
using Newtonsoft.Json.Linq;
using ServiceStack;

namespace JieNor.AMS.YDJ.MS.API.DTO.Dept
{
    /// <summary>
    /// 部门：同步接口
    /// </summary>
    [Api("认证同步接口")]
    [Route("/msapi/dept/sync")]
    [Authenticate]
    [OperationLogFilter("ydj_dept")]
    public class DeptSyncDto : BaseDTO
    {
        public List<DeptData> Data { get; set; }

        /// <summary>
        /// 请求时间戳
        /// </summary>
        public string RequestStamp { get; set; }

        private bool _isRetry = false;
        /// <summary>
        /// 是否错误重试（默认：否）
        /// </summary>
        public bool IsRetry
        {
            get
            {
                return _isRetry;
            }
            set
            {
                _isRetry = value;
            }
        }
    }

    public class DeptData
    {
        /// <summary>
        /// 内部易到家部门Id
        /// </summary>
        public string id { get; set; }
        public string name { get; set; }
        /// <summary>
        /// 父级Id  内部易到家部门Id
        /// </summary>
        public string parentid { get; set; }
        public string linkstore_num { get; set; }
        public string linkstore { get; set; }
        public string storetype { get; set; }
        public string description { get; set; }
        public string operatorid { get; set; }
        public string agentid { get; set; }
        public string agentno { get; set; }
        public string secagentid { get; set; }
        public string secagentno { get; set; }
    }
}
