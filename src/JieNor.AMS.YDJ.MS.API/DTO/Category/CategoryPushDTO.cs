using Newtonsoft.Json.Linq;
using ServiceStack;

namespace JieNor.AMS.YDJ.MS.API.DTO.Category
{
    /// <summary>
    /// 产品类别：总部下发接口
    /// </summary>
    [Api("总部下发接口")]
    [Route("/msapi/ydj_category/push")]
    [Authenticate]
    //[OperationLogFilter("ydj_category")]
    public class CategoryPushDTO : BaseDTO
    {
        public JArray Data { get; set; }

        /// <summary>
        /// 请求时间戳
        /// </summary>
        public string RequestStamp { get; set; }

        private bool _isRetry = false;
        /// <summary>
        /// 是否错误重试（默认：否）
        /// </summary>
        public bool IsRetry
        {
            get
            {
                return _isRetry;
            }
            set
            {
                _isRetry = value;
            }
        }
    }
}