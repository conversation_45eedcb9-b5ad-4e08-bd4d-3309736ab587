using System;
using System.Collections.Generic;
using JieNor.AMS.YDJ.MS.API.Filter;
using Newtonsoft.Json.Linq;
using ServiceStack;

namespace JieNor.AMS.YDJ.MS.API.DTO.Staff
{
    /// <summary>
    /// 员工：同步接口
    /// </summary>
    [Api("认证同步接口")]
    [Route("/msapi/staff/sync")]
    [Authenticate]
    [OperationLogFilter("ydj_staff")]
    public class StaffSyncDto : BaseDTO
    {
        public List<StaffData> Data { get; set; }

        /// <summary>
        /// 请求时间戳
        /// </summary>
        public string RequestStamp { get; set; }

        private bool _isRetry = false;
        /// <summary>
        /// 是否错误重试（默认：否）
        /// </summary>
        public bool IsRetry
        {
            get
            {
                return _isRetry;
            }
            set
            {
                _isRetry = value;
            }
        }
    }

    public class StaffData
    {
        /// <summary>
        /// 外部Id
        /// </summary>
        public string id { get; set; }

        public string number { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 关联用户
        /// </summary>
        public string userid { get; set; }
        /// <summary>
        /// 手机号
        /// </summary>
        public string phone { get; set; }
        /// <summary>
        /// Email
        /// </summary>
        public string email { get; set; }
        /// <summary>
        /// 微信
        /// </summary>
        public string wechat { get; set; }
        /// <summary>
        /// 性别 sex1 :男; sex2 :女
        /// </summary>
        public string sex { get; set; }
        /// <summary>
        /// 生日
        /// </summary>
        public string birthday { get; set; }
        /// <summary>
        /// 入职日期
        /// </summary>
        public string entrydate { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        public string address { get; set; }
        /// <summary>
        /// 任岗明细
        /// </summary>
        public List<StaffPosition> staffpositions { get; set; } = new List<StaffPosition>();
        public string description { get; set; }

        public string operatorid { get; set; }
        public string agentid { get; set; }
        public string agentno { get; set; }
        public string secagentid { get; set; }
        public string secagentno { get; set; }
    }

    public class StaffPosition
    {
        /// <summary>
        /// 外部Id
        /// </summary>
        public string id { get; set; }
        /// <summary>
        /// 岗位ID  外部Id
        /// </summary> 
        public string positionid { get; set; }
        /// <summary>
        /// 是否主岗位
        /// </summary>
        public string ismain { get; set; }
        /// <summary>
        /// 部门Id  内部易到家部门Id
        /// </summary>
        public string deptid { get; set; }
        /// <summary>
        /// 是否主要负责人
        /// </summary>
        public string isleader { get; set; }
        /// <summary>
        /// 业务类别  0:'通用',1:'销售',2:'采购', 4:'库存', 5:'设计', 6:'服务',7:'售后'
        /// </summary>
        public string type { get; set; }
    }
}
