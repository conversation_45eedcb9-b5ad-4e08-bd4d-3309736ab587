using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.MS.API
{
    /// <summary>
    /// 操作结果接口扩展类
    /// </summary>
    public static class IOperationResultExtentions
    {
        /// <summary>
        /// 转响应数据模型
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="result">操作结果</param>
        /// <returns></returns>
        public static BaseResponse<T> ToResponseModel<T>(this IOperationResult result) where T : class, new()
        {
            var resp = new BaseResponse<T>();
            if (result == null)
            {
                return resp;
            }

            if (!result.IsSuccess)
            {
                resp.Code = 500;
                resp.Success = false;
                resp.Message = result.GetMessage();
                return resp;
            }

            resp.Code = 200;
            resp.Success = true;
            resp.Message = result.GetMessage("操作成功！");

            return resp;
        }

        /// <summary>
        /// 获取错误消息
        /// </summary> 
        /// <param name="result">操作结果</param> 
        /// <param name="defaultMsg">默认消息</param> 
        /// <returns></returns>
        public static string GetMessage(this IOperationResult result, string defaultMsg = "操作失败！")
        {
            string msg = result?.ToString();
            if (msg.IsNullOrEmptyOrWhiteSpace())
            {
                msg = defaultMsg;
            }
            return msg;
        }
    }
}
