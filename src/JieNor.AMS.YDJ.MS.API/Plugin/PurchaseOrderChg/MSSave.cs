using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MS.API.DTO.PurchaseOrder;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Serialization;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MS.API.Plugin.PurchaseOrderChg
{
    /// <summary>
    /// 采购订单变更：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder_chg")]
    [OperationNo("MSSave")]
    public class MSSave : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var dataStr = this.GetQueryOrSimpleParam("data", "");

            JObject purchaseOrderChgObj;
            try
            {
                purchaseOrderChgObj = JObject.Parse(dataStr);
            }
            catch (Exception ex)
            {
                throw new BusinessException("参数格式不正确！");
            }

            string fhqderstatus = purchaseOrderChgObj.GetJsonValue("fhqderstatus", "");
            ////【总部变更状态】= "关闭" 02
            //string fhqderchgstatus = purchaseOrderChgObj.GetJsonValue("fhqderchgstatus", "");
            ////总部同步信息
            //string fhqsyncdesc = purchaseOrderChgObj.GetJsonValue("fhqsyncdesc", "");

            string billNo = purchaseOrderChgObj.GetJsonValue("fbillno", "");
            if (billNo.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("采购订单变更单编号不能为空！");
            //bug 29259
            var purchaseOrderChg = this.Context.LoadBizDataByACLFilter(this.HtmlForm.Id, $"fbillno = '{billNo}'  ")
                ?.FirstOrDefault();
            if (purchaseOrderChg == null)
            {
                throw new WarnException($"采购订单变更单【{billNo}】已审核或不存在，请检查！");
            }

            var sourcenumber = Convert.ToString(purchaseOrderChg["fsourcenumber"]);
            //找到对应采购订单
            var PurOrderObj = this.Context.LoadBizDataByACLFilter("ydj_purchaseorder", $" fbillno ='{sourcenumber}' ").FirstOrDefault();
            if (PurOrderObj != null)
            {
                // 加载引用数据
                var refMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
                var metaModelService = this.Context.Container.GetService<IMetaModelService>();
                var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
                var purForm = metaModelService.LoadFormModel(this.Context, "ydj_purchaseorder");
                refObjMgr?.Load(this.Context, new[] { PurOrderObj }, true, purForm, new List<string> { "fmaterialid" });

                var entitydatas = purchaseOrderChgObj?["fentity"] as JArray;
                //审核通过、驳回 更新采购订单时根据更新的行号去反写采购订单
                var entrys = PurOrderObj["fentity"] as DynamicObjectCollection;
                if (this.Context.IsDirectSale)
                {
                    List<JObject> headEntitys = new List<JObject>();
                    foreach (var item in entitydatas)
                    {
                        string _id = Convert.ToString(item["id"]);
                        //item.GetJsonValue("id", _id);
                        if (!_id.IsNullOrEmptyOrWhiteSpace())
                        {
                            var entryItem = entrys.Where(a => Convert.ToString(a["fseq_e"]).Equals(_id)).FirstOrDefault();
                            if (entryItem == null)
                            {
                                throw new WarnException($"采购订单变更单行【{_id}】未找到关联明细行，请检查数据！");
                            }
                            if (!string.IsNullOrWhiteSpace(Convert.ToString(entryItem["fpartscombnumber"])))
                            {
                                //找出套件头的商品，并添加到array里
                                var headEntryItem = entrys.Where(a => Convert.ToString(a["fsuitcombnumber"]).Equals(Convert.ToString(entryItem["fsuitcombnumber"])) && Convert.ToBoolean((a["fmaterialid_ref"] as DynamicObject)?["fsuiteflag"])).FirstOrDefault();
                                if (headEntryItem != null && !headEntitys.Any(a => Convert.ToString(a["id"]).Equals(Convert.ToString(headEntryItem["id"]))))
                                {
                                    JObject headEntity = new JObject();
                                    headEntity.Add("id", Convert.ToString(headEntryItem["id"]));
                                    headEntity.Add("fhqderchgstatus", Convert.ToString(item["fhqderchgstatus"]));
                                    headEntitys.Add(headEntity);
                                }
                            }
                            if (!string.IsNullOrWhiteSpace(Convert.ToString(entryItem["fsuitcombnumber"])))
                            {
                                //找出套件头的商品，并添加到array里
                                var headEntryItem = entrys.Where(a => Convert.ToString(a["fsuitcombnumber"]).Equals(Convert.ToString(entryItem["fsuitcombnumber"])) && Convert.ToBoolean((a["fmaterialid_ref"] as DynamicObject)?["fsuiteflag"])).FirstOrDefault();
                                if (headEntryItem != null&&!headEntitys.Any(a =>Convert.ToString(a["id"]).Equals(Convert.ToString(headEntryItem["ftranid"]))))
                                {
                                    
                                    JObject headEntity = new JObject();
                                    headEntity.Add("id", Convert.ToString(headEntryItem["ftranid"]));
                                    headEntity.Add("fhqderchgstatus", Convert.ToString(item["fhqderchgstatus"]));
                                    headEntitys.Add(headEntity);
                                }
                            }
                            //item["id"].SelectToken(Convert.ToString(entryItem["id"]));
                            item["id"] = Convert.ToString(entryItem["ftranid"]);
                        }
                    }
                    headEntitys = headEntitys.Distinct().ToList();
                    foreach (var item in headEntitys)
                    {
                        entitydatas.Add(item);
                    }
                                
                    //entitydatas.add(headEntity);
                }
                //获取
                var cancelentrys = entitydatas.Where(o => Convert.ToString(o["fhqderchgstatus"]).EqualsIgnoreCase("03")).Select(o => o?["id"].ToString()).ToList();
                var entity_sort = entrys.OrderBy(o => Convert.ToInt32(o["Fseq"])).ToList();
                entrys.Clear();
                foreach (var fentry in entity_sort)
                {
                    entrys.Add(fentry);
                }
                //是否存在 将已关闭的总部变更行状态 再进行驳回
                if (entrys.Any(o => cancelentrys.Contains(o["ftranid"].ToString()) && o["fhqderchgstatus"].ToString().EqualsIgnoreCase("02")))
                {
                    throw new WarnException("采购订单总部变更行状态为已关闭，不能驳回！");
                }
                //var chgstatus = Convert.ToString(PurOrderObj["fhqderchgstatus"]);
                ////如果采购订单 总部变更状态为 已关闭 02，则不能进行驳回
                //if (chgstatus.EqualsIgnoreCase("02") && fhqderchgstatus.EqualsIgnoreCase("03"))
                //{
                //    throw new BusinessException("采购订单总部变更状态为已关闭，不能驳回！");
                //}
                //PurOrderObj["fhqderchgstatus"] = fhqderchgstatus;
                //PurOrderObj["fhqsyncdesc"] = fhqsyncdesc;
            }


            // 【总部合同状态】=【驳回】，不允许调整财务字段，并且不需要重新计算
            var ignoreFinField = fhqderstatus.EqualsIgnoreCase("05");
            //总部驳回变更单
            //var ignoreFinField_chg = fhqderchgstatus.EqualsIgnoreCase("03");


            MapField(purchaseOrderChgObj, purchaseOrderChg, ignoreFinField);
            DoPartPurchaseOrder(purchaseOrderChgObj, purchaseOrderChg, PurOrderObj);
            //if (!ignoreFinField_chg)
            //{
            ////操作数据库
            //var dm = this.Container.GetService<IDataManager>();
            ////初始化上下文
            //dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            //dm.Save(purchaseOrderChg);

            //ApprovalHelper.Submit(this.Context, this.GetCurrentRequest(), "ydj_purchaseorder_chg", Convert.ToString(purchaseOrderChg?["id"]));
            //ApprovalHelper.Audit(this.Context, this.GetCurrentRequest(), "ydj_purchaseorder_chg", Convert.ToString(purchaseOrderChg?["id"]));
            //变更理由给上默认值 防止变更保存成功但是 采购订单审核未通过导致不能再次保存
            //if (Convert.ToString(purchaseOrderChg?["fchangereason"]).IsNullOrEmptyOrWhiteSpace()) {
            //    purchaseOrderChg["fchangereason"] = "同意";
            //}
            //var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new[] { purchaseOrderChg }, "save", new Dictionary<string, object>());
            //result.ThrowIfHasError(true, $"调用{this.HtmlForm.Caption}保存失败！");

            //不需要调用变更单审核、采购订单提交审核 会同步更新 变更单状态
            //if (!purchaseOrderChg["fstatus"].Equals("D") && !purchaseOrderChg["fstatus"].Equals("E")) {
            //    var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new[] { purchaseOrderChg }, "submit", new Dictionary<string, object>());
            //    result.ThrowIfHasError(true, $"调用{this.HtmlForm.Caption}提交失败！");
            //}
            //if (purchaseOrderChg["fstatus"].Equals("E"))
            //{
            //     var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new[] { purchaseOrderChg }, "audit", new Dictionary<string, object>());
            //    result.ThrowIfHasError(true, $"调用{this.HtmlForm.Caption}审核失败！");
            //}
            //}
            //RewritePurchaseOrder(PurOrderObj, ignoreFinField_chg);



            //if (!ignoreFinField)
            //{
            //    Calculate(purchaseOrderChg);
            //}

            //var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new[] { purchaseOrderChg }, "save", new Dictionary<string, object>());
            //result.ThrowIfHasError(true, $"调用{this.HtmlForm.Caption}保存失败！");


            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "操作成功！";
        }
        /// <summary>
        /// 处理中台部分驳回、部分关闭采购订单变更明细的情况
        /// 所有行都为 关闭或者驳回状态时才去对整单进行提交变更并审核，并且如果是驳回要还原采购数量（获取变更前的采购数量更新到采购订单变更明细上，最后一起审核通过）
        /// </summary>
        /// <param name="purchaseOrderChgObj">变更单接口参数</param>
        /// <param name="purchaseOrderChg">采购订单变更单</param>
        /// <param name="PurOrderObj">采购订单</param>
        private void DoPartPurchaseOrder(JObject purchaseOrderChgObj, DynamicObject purchaseOrderChg, DynamicObject PurOrderObj)
        {
            var purchaseorderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_purchaseorder");
            //获取传入的变更单明细
            var entitydatas = purchaseOrderChgObj?["fentity"] as JArray;
            var entrys = PurOrderObj["fentity"] as DynamicObjectCollection;
            var entrys_chg = purchaseOrderChg["fentity"] as DynamicObjectCollection;

            //获取采购订单变更快照信息（目的是驳回时要获取变更前的数量）
            var metaSnap = this.MetaModelService.LoadFormModel(this.Context, "bas_billchangesnapshot");
            var dmSnap = this.GetDataManager();
            dmSnap.InitDbContext(this.Context, metaSnap.GetDynamicObjectType(this.Context));
            var serializer = this.Context.Container.GetService<IDynamicSerializer>();
            var ids = new DynamicObject[] { PurOrderObj }.Where(t => Convert.ToString(t["fchangestatus"]) == "1").Select(t => Convert.ToString(t["id"])).ToList();
            List<DynamicObject> snaps = dmSnap.SelectBy("fbillinterid in ('{0}') and fmainorgid='{1}' and fworkobject='{2}'".Fmt(string.Join("','", ids),
            this.Context.Company, purchaseorderForm.Id)).OfType<DynamicObject>().OrderByDescending(s => Convert.ToDateTime(s["fcreatedate"])).ToList();
            //解析快照信息
            var fbillsnapshots = snaps?.Select(t => Convert.ToString(t["fbillsnapshot"])).ToList();
            List<DynamicObject> snapDatas = new List<DynamicObject>();
            List<DynamicObject> snapDataEntrys = new List<DynamicObject>();
            fbillsnapshots?.ForEach(t => snapDatas.Add(serializer.FromDynamicJson<DynamicObject>(this.HtmlForm.GetDynamicObjectType(this.Context),
                    t)));
            //遍历参数明细
            if (snapDatas.Any())
            {
                //变更数据明细集合
                snapDataEntrys = (snapDatas.SelectMany(t => t["fentity"] as DynamicObjectCollection).ToList());
            }

            foreach (var item in entitydatas)
            {
                //遍历采购订单
                foreach (var fentry in entrys)
                {
                    //因为这里变更单的ftranid 其实跟上游的采购订单一致所以可以直接匹配
                    var ftranid = Convert.ToString(fentry["ftranid"]);
                    if (Convert.ToString(item["id"]).EqualsIgnoreCase(ftranid))
                    {
                        fentry["fhqderchgstatus"] = Convert.ToString(item["fhqderchgstatus"]);
                        fentry["fhqderchgcancelreason"] = Convert.ToString(item["fhqderchgcancelreason"]);
                        if (Convert.ToString(item["fhqderchgstatus"]).EqualsIgnoreCase("03"))
                        {
                            if (!snapDataEntrys.Any(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))) continue;
                            decimal changeBeforeQty = 0;
                            changeBeforeQty = Convert.ToDecimal(snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))?["fbizqty"]);
                            fentry["fqty"] = changeBeforeQty;
                            fentry["fbizqty"] = changeBeforeQty;
                            fentry["fclosestatus_e"] = Convert.ToString(snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))?["fclosestatus_e"]);
                            //促销字段
                            fentry["fpromotion"] = Convert.ToString(snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))?["fpromotion"]);
                            fentry["fcombinenumber"] = Convert.ToString(snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))?["fcombinenumber"]);
                            fentry["fcombineremark"] = Convert.ToString(snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))?["fcombineremark"]);
                            fentry["fcombineqty"] = Convert.ToDecimal(snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))?["fcombineqty"]);
                            fentry["fcombinerate"] = Convert.ToDecimal(snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))?["fcombinerate"]);
                            fentry["fgroupnumber"] = Convert.ToDecimal(snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))?["fgroupnumber"]);
                            //if (!string.IsNullOrWhiteSpace(Convert.ToString(fentry["fcombinenumber"])))
                            //{
                            //    //如果促销不为空，那快照里的其他促销行，也要做一遍赋值。
                            //    foreach (var _entryItem in entrys)
                            //    {
                            //        var snapItem = snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(_entryItem["ftranid"]) && Convert.ToString(x["fcombinenumber"]).Equals(Convert.ToString(fentry["fcombinenumber"])));
                            //        //促销字段 fentry["ftranid"]
                            //        if (snapItem != null)
                            //        {
                            //            //促销字段
                            //            _entryItem["fpromotion"] = Convert.ToString(snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))?["fpromotion"]);
                            //            _entryItem["fcombinenumber"] = Convert.ToString(snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))?["fcombinenumber"]);
                            //            _entryItem["fcombineremark"] = Convert.ToString(snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))?["fcombineremark"]);
                            //            _entryItem["fcombineqty"] = Convert.ToDecimal(snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))?["fcombineqty"]);
                            //            _entryItem["fcombinerate"] = Convert.ToDecimal(snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))?["fcombinerate"]);
                            //            _entryItem["fgroupnumber"] = Convert.ToDecimal(snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))?["fgroupnumber"]);
                            //        }
                            //    }
                            //}

                            var comnumber = string.Empty;
                            //如果是返回的套件头、沙发头 还原的时候要关联的把同组合的其他数量一起还原
                            if (!Convert.ToString(fentry?["fsuitcombnumber"]).IsNullOrEmptyOrWhiteSpace())
                            {
                                comnumber = Convert.ToString(fentry?["fsuitcombnumber"]);
                                CalculateSuitQty(entrys, comnumber, "0", snapDataEntrys, Convert.ToString(item["fhqderchgstatus"]));
                            }
                            if (!Convert.ToString(fentry?["fsofacombnumber"]).IsNullOrEmptyOrWhiteSpace())
                            {
                                comnumber = Convert.ToString(fentry?["fsofacombnumber"]);
                                CalculateSuitQty(entrys, comnumber, "1", snapDataEntrys, Convert.ToString(item["fhqderchgstatus"]));
                            }
                            //if (!Convert.ToString(fentry?["fpartscombnumber"]).IsNullOrEmptyOrWhiteSpace())
                            //{
                            //    comnumber = Convert.ToString(fentry?["fpartscombnumber"]);
                            //    CalculateSuitQty(entrys, comnumber, "2", snapDataEntrys, Convert.ToString(item["fhqderchgstatus"]));
                            //}
                            //if (!Convert.ToString(fentry?["fcombinenumber"]).IsNullOrEmptyOrWhiteSpace())
                            //{
                            //    comnumber = Convert.ToString(fentry?["fcombinenumber"]);
                            //    CalculateSuitQty(entrys, comnumber, "3", snapDataEntrys, Convert.ToString(item["fhqderchgstatus"]));
                            //}
                        }
                        //如果是通过的话数量应该取变更单的变更新数量，防止驳回回滚数量后 然后又再次通过的情况
                        if (Convert.ToString(item["fhqderchgstatus"]).EqualsIgnoreCase("02"))
                        {
                            var entry_chgobj = entrys_chg.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid));
                            if (entry_chgobj.IsNullOrEmptyOrWhiteSpace()) continue;
                            decimal Qty_chg = Convert.ToDecimal(entry_chgobj?["fbizqty_chg"]);
                            fentry["fqty"] = Qty_chg;
                            fentry["fbizqty"] = Qty_chg;
                            fentry["fclosestatus_e"] = Convert.ToString(entry_chgobj?["fclosestatus_e_chg"]);
                            var comnumber = string.Empty;
                            //如果是返回的套件头、沙发头 还原的时候要关联的把同组合的其他数量一起还原
                            if (!Convert.ToString(fentry?["fsuitcombnumber"]).IsNullOrEmptyOrWhiteSpace())
                            {
                                comnumber = Convert.ToString(fentry?["fsuitcombnumber"]);
                                CalculateSuitQty(entrys, comnumber, "0", (purchaseOrderChg["fentity"] as DynamicObjectCollection).ToList(), Convert.ToString(item["fhqderchgstatus"]));
                            }
                            if (!Convert.ToString(fentry?["fsofacombnumber"]).IsNullOrEmptyOrWhiteSpace())
                            {
                                comnumber = Convert.ToString(fentry?["fsofacombnumber"]);
                                CalculateSuitQty(entrys, comnumber, "1", (purchaseOrderChg["fentity"] as DynamicObjectCollection).ToList(), Convert.ToString(item["fhqderchgstatus"]));
                            }
                            //if (!Convert.ToString(fentry?["fpartscombnumber"]).IsNullOrEmptyOrWhiteSpace())
                            //{
                            //    comnumber = Convert.ToString(fentry?["fpartscombnumber"]);
                            //    CalculateSuitQty(entrys, comnumber, "2", (purchaseOrderChg["fentity"] as DynamicObjectCollection).ToList(), Convert.ToString(item["fhqderchgstatus"]));
                            //}
                            //if (!Convert.ToString(fentry?["fcombinenumber"]).IsNullOrEmptyOrWhiteSpace())
                            //{
                            //    comnumber = Convert.ToString(fentry?["fcombinenumber"]);
                            //    CalculateSuitQty(entrys, comnumber, "3", (purchaseOrderChg["fentity"] as DynamicObjectCollection).ToList(), Convert.ToString(item["fhqderchgstatus"]));
                            //}
                        }
                    }
                }
                //遍历采购订单变更单
                //foreach (var fentry_chg in entrys_chg)
                //{
                //    var ftranid = Convert.ToString(fentry_chg["ftranid"]);
                //    if (Convert.ToString(item["id"]).EqualsIgnoreCase(ftranid) && Convert.ToString(item["fhqderchgstatus"]).EqualsIgnoreCase("03"))
                //    {
                //        if (!snapDataEntrys.Any(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))) continue;
                //        decimal changeBeforeQty = 0;
                //        changeBeforeQty = Convert.ToDecimal(snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))?["fbizqty"]);

                //        //因为如果是驳回明细的话采购订单变更单明细需要重写成变更前的数量
                //        //var fqty = entrys.Where(o => Convert.ToString(o["ftranid"]).EqualsIgnoreCase(ftranid)).Select(o => Convert.ToDecimal(o["fbizqty"])).FirstOrDefault();
                //        fentry_chg["fqty_chg"] = changeBeforeQty;
                //        fentry_chg["fbizqty_chg"] = changeBeforeQty;
                //    }
                //}
            }
            if (Convert.ToString(purchaseOrderChg?["fchangereason"]).IsNullOrEmptyOrWhiteSpace())
            {
                purchaseOrderChg["fchangereason"] = "同意";
            }
            //处理套件、配件、沙发、促销总部变更行状态
            ProductUtil.DoCombinesNumber(this.Context, entrys);
            //采购订单变更单保存
            var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new[] { purchaseOrderChg }, "save", new Dictionary<string, object>());
            result.ThrowIfHasError(true, $"调用{this.HtmlForm.Caption}保存失败！");

            Calculate(PurOrderObj);

            //采购订单保存 目的是更新总部变更行状态和驳回原因
            //var dm = this.Container.GetService<IDataManager>();
            ////初始化上下文
            //dm.InitDbContext(this.Context, purchaseorderForm.GetDynamicObjectType(this.Context));
            //dm.Save(PurOrderObj);

            //改成调用draft
            this.Gateway.InvokeBillOperation(this.Context,
            purchaseorderForm.Id,
            new List<DynamicObject> { PurOrderObj },
            "draft",
            new Dictionary<string, object>());

            //var result = this.Gateway.InvokeBillOperation(this.Context, purchaseorderForm.Id, new DynamicObject[] { PurOrderObj }, "save", new Dictionary<string, object>());
            //result.ThrowIfHasError(true, $"调用{purchaseorderForm.Caption}保存失败！");
            //是否存在未进行关闭或者驳回的采购订单明细
            if (entrys.Any(o => Convert.ToString(o["fhqderchgstatus"]).EqualsIgnoreCase("01")))
            {
                //只是保存变更单但不走提交变更\审核的操作
                return;
            }
            RewritePurchaseOrder(PurOrderObj, false);
        }

        private void CalculateSuitQty(DynamicObjectCollection entrys, string combnumber, string mark, List<DynamicObject> snapDataEntrys, string fhqderchgstatus)
        {
            var comKey = string.Empty;
            var chgKey = string.Empty;
            if (!combnumber.IsNullOrEmptyOrWhiteSpace())
            {
                //套件
                if (mark.EqualsIgnoreCase("0"))
                {
                    comKey = "fsuitcombnumber";
                }
                //沙发
                if (mark.EqualsIgnoreCase("1"))
                {
                    comKey = "fsofacombnumber";
                }
                ////配件
                //if (mark.EqualsIgnoreCase("2")) 
                //{
                //    comKey = "fpartscombnumber";
                //}
                ////促销
                //if (mark.EqualsIgnoreCase("3"))
                //{
                //    comKey = "fcombinenumber";
                //}

                //如果是关闭需要去变更记录的_chg数据
                if (fhqderchgstatus.EqualsIgnoreCase("02"))
                {
                    chgKey = "_chg";
                }
                var entry_coms = entrys.Where(o => Convert.ToString(o[comKey]).EqualsIgnoreCase(combnumber)).ToList();
                foreach (var entry_com in entry_coms)
                {
                    var ftranid = Convert.ToString(entry_com["ftranid"]);
                    var changeBeforeQty = Convert.ToDecimal(snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))?[$"fbizqty{chgKey}"]);
                    entry_com["fqty"] = changeBeforeQty;
                    entry_com["fbizqty"] = changeBeforeQty;
                    entry_com["fclosestatus_e"] = Convert.ToString(snapDataEntrys.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == Convert.ToString(ftranid))?[$"fclosestatus_e{chgKey}"]);
                }
            }
        }

        private void RewritePurchaseOrder(DynamicObject PurOrderObj, bool ignoreFinField_chg)
        {
            var purchaseorderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_purchaseorder");

            //如果是驳回的话 只需要反写采购订单 总部变更状态 和 总部同步信息 其它都不用去执行
            if (!ignoreFinField_chg)
            {
                //判断是否

                //调用通用变更方法
                var result = this.Gateway.InvokeBillOperation(this.Context, purchaseorderForm.Id, new DynamicObject[] { PurOrderObj }, "submitchange", new Dictionary<string, object>
                    {
                        { "changeReason", "SAP总部回写" }
                    });
                if (result.IsSuccess == false)
                {
                    this.Result.IsSuccess = false;
                    this.Result.SimpleMessage = result.SimpleMessage;
                    return;
                }
                //result.ThrowIfHasError(true, $"调用{this.HtmlForm.Caption}提交变更失败！");

                if (!PurOrderObj["fstatus"].Equals("D") && !PurOrderObj["fstatus"].Equals("E"))
                {
                    result = this.Gateway.InvokeBillOperation(this.Context, purchaseorderForm.Id, new DynamicObject[] { PurOrderObj }, "submit", new Dictionary<string, object>());
                    if (result.IsSuccess == false)
                    {
                        this.Result.IsSuccess = false;
                        this.Result.SimpleMessage = result.SimpleMessage;
                        return;
                    }
                    //result.ThrowIfHasError(true, $"调用{this.HtmlForm.Caption}提交失败！");
                }

                if (!PurOrderObj["fstatus"].Equals("E"))
                {
                    result = this.Gateway.InvokeBillOperation(this.Context, purchaseorderForm.Id, new DynamicObject[] { PurOrderObj }, "auditflow", new Dictionary<string, object> { { "execOpinion", "同意" } });
                    if (result.IsSuccess == false)
                    {

                        this.Result.IsSuccess = false;
                        this.Result.SimpleMessage = result.SimpleMessage;
                        return;
                    }
                    else
                    {
                        this.Gateway.InvokeBillOperation(this.Context, purchaseorderForm.Id, new DynamicObject[] { PurOrderObj }, "submithqagain", null);
                    }
                    //result.ThrowIfHasError(true, $"调用{this.HtmlForm.Caption}审核失败！");
                }
                //ApprovalHelper.Submitchange(this.Context, this.GetCurrentRequest(), "ydj_purchaseorder", Convert.ToString(PurOrderObj[0]?["id"]));
                ////result.ThrowIfHasError(true, "采购变更单对应采购订单提交变更失败！");
                //ApprovalHelper.Submit(this.Context, this.GetCurrentRequest(), "ydj_purchaseorder", Convert.ToString(PurOrderObj[0]?["id"]));
                //ApprovalHelper.Audit(this.Context, this.GetCurrentRequest(), "ydj_purchaseorder", Convert.ToString(PurOrderObj[0]?["id"]));
            }
        }

        /// <summary>
        /// 允许映射字段
        /// </summary>
        static HashSet<string> allowMapFieldIds = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            // 单据头
            "fhqderstatus", "fhqderno", "fdescription","fdescription_chg", "fhqsyncdesc", "fdeliveryplandate", "fhqdertype",
            // 单据体：fentity
            "fprice", "fdistrate", "fdistamount_e", "fprice_chg", "fdistrate_chg", "fdistamount_e_chg","fdemanddate"//, "ftaxprice", "fstardiscount", "fdepthdiscount", "fnewdiscount", "fexpenserebate", "fotherdiscount", "fsapdiscount"
        };


        /// <summary>
        /// 财务字段
        /// </summary>
        static HashSet<string> finFieldIds = new HashSet<string>(StringComparer.OrdinalIgnoreCase) { "fprice", "fdistrate", "fdistamount_e" };

        /// <summary>
        /// 映射字段
        /// </summary>
        /// <param name="jObject"></param>
        /// <param name="dataEntity"></param>
        /// <param name="ignoreFinField">排除</param>
        private void MapField(JObject jObject, DynamicObject dataEntity, bool ignoreFinField = false)
        {
            // 字段按需填写
            foreach (JProperty item in jObject.Properties())
            {
                var e = new BeforeMapFieldEventArgs
                {
                    Property = item,
                    Data = jObject,
                    DataEntity = dataEntity
                };

                BeforeMapField(e);

                if (e.Cancel) continue;

                string fieldId = item.Name;

                var htmlField = this.HtmlForm.GetField(fieldId);
                if (htmlField != null)
                {
                    // 不包括允许映射字段列表里
                    if (!allowMapFieldIds.Contains(fieldId)) continue;

                    // 忽略财务字段
                    if (ignoreFinField && finFieldIds.Contains(htmlField.Id)) continue;
                    if (this.Context.IsDirectSale)
                    {
                        if ("fhqderno".IndexOf(fieldId) >= 0)
                        {
                            continue;
                        }
                    }
                    dataEntity[htmlField.PropertyName] = item.Value;
                    continue;
                }

                // 实体键
                string entityKey = item.Name;
                var htmlEntity = this.HtmlForm.GetEntity(entityKey);
                if (htmlEntity != null)
                {
                    JArray array = item.Value as JArray;
                    if (array != null)
                    {
                        var entities = dataEntity[entityKey] as DynamicObjectCollection;
                        foreach (var row in array)
                        {
                            var id = row.GetJsonValue("id", "");
                            var entity = entities.FirstOrDefault(s => s["ftranid"].ToString().EqualsIgnoreCase(id));
                            if (entity != null && row.Type == JTokenType.Object)
                            {
                                MapField(JObject.FromObject(row), entity);
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 计算
        /// </summary>
        /// <param name="purchaseOrder"></param>
        private void Calculate(DynamicObject purchaseOrder)
        {
            PurchaseOrderUtil.Calcuate(this.Context, purchaseOrder);
        }

        private void BeforeMapField(BeforeMapFieldEventArgs e)
        {
            var property = e.Property;
            var dataEntity = e.DataEntity;
            var data = e.Data;

            switch (property.Name.ToLower())
            {
                case "fdiscounts":

                    e.Cancel = true;

                    // 新增总部折扣字段
                    decimal ftaxprice = property.Value.FirstOrDefault(s => s.GetJsonValue<string>("ftype").EqualsIgnoreCase(Enu_DiscountType.ZP01.ToString()))?.GetJsonValue<decimal>("fvalue") ?? 0;
                    decimal fstardiscount = property.Value.FirstOrDefault(s => s.GetJsonValue<string>("ftype").EqualsIgnoreCase(Enu_DiscountType.ZK03.ToString()))?.GetJsonValue<decimal>("fvalue") ?? 0;
                    decimal fdepthdiscount = property.Value.FirstOrDefault(s => s.GetJsonValue<string>("ftype").EqualsIgnoreCase(Enu_DiscountType.ZK05.ToString()))?.GetJsonValue<decimal>("fvalue") ?? 0;
                    decimal fnewdiscount = property.Value.FirstOrDefault(s => s.GetJsonValue<string>("ftype").EqualsIgnoreCase(Enu_DiscountType.ZK06.ToString()))?.GetJsonValue<decimal>("fvalue") ?? 0;
                    decimal fexpenserebate = property.Value.FirstOrDefault(s => s.GetJsonValue<string>("ftype").EqualsIgnoreCase(Enu_DiscountType.ZK99.ToString()))?.GetJsonValue<decimal>("fvalue") ?? 0;
                    decimal fsapdiscount = data.GetJsonValue<decimal>("fdistamount_e");
                    // 其他折扣=SAP该行产品折扣总额-以上五种折扣金额
                    decimal fotherdiscount = fsapdiscount - ftaxprice - fstardiscount - fdepthdiscount - fnewdiscount - fexpenserebate;

                    dataEntity["ftaxprice"] = ftaxprice;
                    dataEntity["fstardiscount"] = fstardiscount;
                    dataEntity["fdepthdiscount"] = fdepthdiscount;
                    dataEntity["fnewdiscount"] = fnewdiscount;
                    dataEntity["fexpenserebate"] = fexpenserebate;
                    dataEntity["fotherdiscount"] = fotherdiscount;
                    dataEntity["fsapdiscount"] = fsapdiscount;

                    break;
            }
        }
    }
}
