; (function () {
    var stk_inventorytransfer = (function (_super) {
        var _child = function (args) {
            var that = this;
            that.fentity = "fentity";
            that.notGetFIFOStock = {};//不获取库位推荐
            that.isqueryinventory = false;
            _super.call(that, args);

            //各单据类型调出仓库锁定状态
            that.storeHouseLockStatus = [];
            that.remindnumbers = "";

            //库存管理参数
            that.stocksysparam = {};

            
            that.zyneedsync = false;
        };
        __extends(_child, _super);

        //页面初始化
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            // 默认禁用
            that.Model.setEnable({ id: '#tbProductTagPrintSeting', value: false });
            if (that.formContext.status == 'new') {
                that.SetDefaultDeptInfo();
                //22230 bug。确认新增和复制，清空调入调出属性，调入调出说明
                that.Clearfattrinfo();
            }

            //加载单据类型参数设置
            that.loadBillTypeParamSet();
            that.loadStockParam();
            that.setHeadStoreHouseVisible();
            that.SetZYClassVisible();
            that.initSetEnable();
            that.checkModelIsDirectlyOrAgentHidePurButton(args);
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fmtono':
                    that.fillMtoNoFieldValue(e);
                    break;
                case 'fownerid':
                    that.fillBaseFieldValue(e);
                    break;
                case 'fstorelocationid':
                    //22230 bug。确认不需要带出
                    //that.fillBaseFieldValue(e);
                    break;
                case 'fmaterialid':
                    // that.SetDefaultStockInfo(true, 'fstockdeptid', 'fstorehouseid', e.row)
                    // that.SetDefaultStockInfo(true, 'fstockdeptidto', 'fstorehouseidto', e.row)
                    that.Model.setValue({ id: 'fcustomdesc', row: e.row, value: '' });
                    that.Model.setValue({ id: 'fcallupcustomdescto', row: e.row, value: '' });


                    that.Model.setValue({ id: 'fattrinfoto', value: { fentity: [] }, row: e.row });
                    that.Model.setValue({ id: "fattrinfo", value: { fentity: [] }, row: e.row });
                    break;
                case 'fstockdeptid':
                case 'fstockdeptidto':
                    // that.SetDefaultStockInfo(false, 'fstockdeptidto', 'fstorehouseidto', e.row)
                    break;
                case 'fbilltype':
                    that.SetDefaultDeptInfo()
                    that.SetZYClassVisible();
                    break;
                case 'ftype':
                    if (e.value.id == "1" || e.value.id == "3") {
                        var fentity = that.Model.getEntryData({ id: "fentity" });
                        for (let i = 0; i < fentity.length; i++) {
                            if (fentity[i].fattrinfo != "" && fentity[i].fattrinfo.fentity != null && fentity[i].fattrinfo.fentity.length > 0) {
                                var auxEntry = [];
                                auxEntry = [].concat(fentity[i].fattrinfo.fentity);
                                fentity[i].fattrinfoto = { fentity: auxEntry };
                            }
                            if (fentity[i].fcustomdesc != "") {
                                fentity[i].fcallupcustomdescto = fentity[i].fcustomdesc;
                                // that.Model.setValue({ id: "fcallupcustomdescto", value: fentity[i].fcustomdesc, row: fentity[i].id });
                            }
                        }
                        that.Model.refreshEntry({ id: "fentity" });
                    }
                    break;
                case "fbizqty":
                    that.Model.setValue({ id: 'fstockoutqty', row: e.row, value: e.value });
                    that.Model.setValue({ id: 'fstockinqty', row: e.row, value: e.value });
                    break;
                case "fstorehouseid_h":
                    var fentity = that.Model.getEntryData({ id: 'fentity' });
                    if (fentity.length == 0) {
                        return;
                    }
                    for (let i = 0; i < fentity.length; i++) {
                        that.Model.setValue({ id: "fstorehouseid", value: e.value.id, row: fentity[i].id });
                        that.Model.setValue({ id: "fstorelocationid", value: "", row: fentity[i].id });//清空仓位
                    }
                    break;
                case "fstorehouseidto_h":
                    var fentity = that.Model.getEntryData({ id: 'fentity' });
                    if (fentity.length == 0) {
                        return;
                    }
                    for (let i = 0; i < fentity.length; i++) {
                        that.Model.setValue({ id: "fstorehouseidto", value: e.value.id, row: fentity[i].id });
                        that.Model.setValue({ id: "fstorelocationidto", value: "", row: fentity[i].id });//清空仓位
                    }
                    break;
                case 'ftransfertype':
                    that.SetZYClassVisible();
                    break;
                case 'fmulstore':
                    if (e.value.id) {
                        var storeid = e.value.id;
                        if (storeid.indexOf(',') < 0) {
                            that.Model.setValue({ id: "fstore", value: storeid })
                        }
                    }
                    break;
                case 'fmulstoreto':
                    if (e.value.id) {
                        var storeid = e.value.id;
                        if (storeid.indexOf(',') < 0) {
                            that.Model.setValue({ id: "fstoreto", value: storeid })
                        }
                    }
                    break;
            }

            // 库位推荐
            var isqueryinventory = e && e.ctx && e.ctx.isqueryinventory;//如果是从库存综合查询选中的数据，不需要做库位推荐
            switch (e.id.toLowerCase()) {
                case 'fmaterialid':
                // case 'fattrinfo':
                //case 'funitid':
                //case 'fstockunitid':
                case 'fownertype':
                case 'fownerid':
                case 'fmtono':
                case 'flotno':
                    if (isqueryinventory == false || isqueryinventory == undefined) {
                        that.getFIFOStock(e.row);
                    }
                    if (isqueryinventory == true) {
                        that.isqueryinventory = true;//设置标记，以便从库存综合查询返回填充fqty时不要做库位推荐
                    }
                    break;
                case 'fbizqty':
                    if (that.isqueryinventory == false && (isqueryinventory == false || isqueryinventory == undefined)) {
                        if (!that.notGetFIFOStock.hasOwnProperty(e.row)) {
                            that.getFIFOStock(e.row);
                        }
                    }
                    break;
                case 'fstorehouseid':
                    // 更改仓库时，需要清空标记，以便修改数量时，触发推荐
                    if (that.notGetFIFOStock.hasOwnProperty(e.row)) {
                        delete that.notGetFIFOStock[e.row];
                    }
                    break;
                case 'fstorelocationid':
                    // 清空仓位时，需要清空标记，以便修改数量时，触发推荐
                    if (that.notGetFIFOStock.hasOwnProperty(e.row) && e.value.id === '') {
                        delete that.notGetFIFOStock[e.row];
                    }
                    break;
            }
        };


        _child.prototype.Clearfattrinfo = function () {
            var that = this;
            var fentity = that.Model.getEntryData({ id: 'fentity' });
            if (fentity.length == 0) {
                return;
            }
            for (let i = 0; i < fentity.length; i++) {


                that.Model.setValue({ id: 'fattrinfoto', value: { fentity: [] }, row: fentity[i].id });
                that.Model.setValue({ id: "fattrinfo", value: { fentity: [] }, row: fentity[i].id });

                that.Model.setValue({ id: "fcallupcustomdescto", value: '', row: fentity[i].id });
                that.Model.setValue({ id: "fcustomdesc", value: '', row: fentity[i].id });
            }

        }

        _child.prototype.SetDefaultDeptInfo = function () {
            var that = this;
            var fbilltype = that.Model.getValue({ id: 'fbilltype' });
            if (!fbilltype) {
                return;
            }

            if (fbilltype.fname == "标准要货调拨") {
                //设置收货人、收货部门默认值
                if (Consts.mystaff) {
                    that.Model.setValue({ id: 'fstockstaffidto', value: Consts.mystaff.id });
                }
                if (Consts.mydept) {
                    that.Model.setValue({ id: 'fstockdeptidto', value: Consts.mydept.id });
                }
                else if (Consts.mydepts && Consts.mydepts.length > 0) {
                    that.Model.setValue({ id: 'fstockdeptidto', value: Consts.mydepts[0].id });
                }
                that.Model.setValue({ id: 'fstockstaffid', value: '' });
                that.Model.setValue({ id: 'fstockdeptid', value: '' });
                that.Model.setEnable({ id: 'fstockdeptid', value: true });
            }
            else {
                //设置发货人、发货部门默认值
                if (Consts.mystaff) {
                    that.Model.setValue({ id: 'fstockstaffid', value: Consts.mystaff.id });
                }
                if (Consts.mydept) {
                    that.Model.setValue({ id: 'fstockdeptid', value: Consts.mydept.id });
                }
                else if (Consts.mydepts && Consts.mydepts.length > 0) {
                    that.Model.setValue({ id: 'fstockdeptid', value: Consts.mydepts[0].id });
                }
                that.Model.setValue({ id: 'fstockstaffidto', value: '' });
                that.Model.setValue({ id: 'fstockdeptidto', value: '' });
            }

        }

        _child.prototype.SetZYClassVisible = function () {
            var that = this;
            var fbilltype = that.Model.getValue({ id: 'fbilltype' });
            if (!fbilltype) {
                return;
            }
            if (!Consts.isdirectsale) {
                that.Model.setVisible({ id: '.zyclass', value: false });
                that.Model.setVisible({ id: '.zyclass-billno', value: false });
                that.Model.setVisible({ id: '.zyclass-store', value: false });
                that.Model.setVisible({ id: '[menu=cancelintransfer]', value: false });//不显示
                that.Model.setVisible({ id: '[menu=createintransfer]', value: false });//不显示
            }
            //else {
            //    that.Model.setValue({ id: 'foutagentid', value: '' });
            //    that.Model.setVisible({ id: '[menu=createintransfer]', value: false });//不显示
            //    that.Model.setVisible({ id: '[menu=cancelintransfer]', value: false });//不显示
            //    that.Model.setVisible({ id: '.zyclass', value: false });
            //    that.Model.setVisible({ id: '.zyclass-billno', value: false });
            //    that.Model.setVisible({ id: '.zyclass-store', value: false });

            //    setTimeout(function () {
            //        that.setFieldMustFlag({ id: "finagentid", caption: "调入经销商", must: false });
            //        that.setFieldMustFlag({ id: "fstorehouseidto", caption: "调入仓库", must: true });
            //    }, 200)
            //}
            if (fbilltype.fname == "跨组织调拨") {
                //业务类型;默认为“仓库调拨”，不允许编辑
                that.Model.setEnable({ id: 'ftype', value: false });
                that.Model.setValue({ id: 'ftype', value: { id: '1', value: '仓库调拨', name: '仓库调拨' } });
                that.Model.setVisible({ id: '[menu=push]', value: false });//不显示,跨组织调拨，不支持反向调拨



                var stype = that.Model.getSimpleValue({ id: 'fsourcetype' });
                if (stype) {
                    that.Model.setEnable({ id: 'ftransfertype', value: false });
                    that.Model.setValue({ id: 'ftransfertype', value: 'invtransfer_biztype_05' });//跨组织调入
                } else {
                    that.Model.setEnable({ id: 'ftransfertype', value: true });
                    that.Model.setValue({ id: 'ftransfertype', value: 'invtransfer_biztype_04' });//跨组织调出
                }

                that.Model.setVisible({ id: '.zyclass', value: true });
                var ftransfertype = that.Model.getValue({ id: 'ftransfertype' });
                if (ftransfertype.id == 'invtransfer_biztype_04') {
                    that.Model.setVisible({ id: '.zyclass-billno', value: true });//【调拨类型=跨组织调出】时，默认可见，反之不显示
                    that.Model.setValue({ id: 'foutagentid', value: Consts.loginCompany.id });
                    that.Model.setVisible({ id: '[menu=createintransfer]', value: true });//显示
                    that.Model.setVisible({ id: '[menu=cancelintransfer]', value: true });//显示
                    that.Model.setEnable({ id: 'fstoreto', value: false });//调出方，不允许选择
                } else if (ftransfertype.id == 'invtransfer_biztype_05') {//跨组织调入，锁定
                    that.Model.setVisible({ id: '.zyclass-billno', value: false });//【调拨类型=跨组织调入】时，不显示
                    that.Model.setEnable({ id: 'finagentid', value: false });
                    that.Model.setEnable({ id: 'fstorehouseid', value: false });//调入方，不允许选择调出仓库
                    that.Model.setEnable({ id: 'fstorelocationid', value: false });//调入方，不允许选择调出仓位
                    that.Model.setEnable({ id: 'fstockstatus', value: false });//调入方，不允许选择调出状态
                    that.Model.setEnable({ id: 'fdeliverywayid', value: false });//调入方，不允许选择
                    that.Model.setEnable({ id: 'fdeliveryman', value: false });//调入方，不允许选择
                    that.Model.setEnable({ id: 'fstockstaffid', value: false });//调入方，不允许选择
                    that.Model.setEnable({ id: 'fstockdeptid', value: false });//调入方，不允许选择
                    that.Model.setEnable({ id: 'fbilltype', value: false });//调入方，不允许选择
                    that.Model.setEnable({ id: 'fstore', value: false });//调入方，不允许选择
                    that.Model.setVisible({ id: '[menu=createintransfer]', value: false });//不显示
                    that.Model.setVisible({ id: '[menu=cancelintransfer]', value: false });//不显示
                }

                setTimeout(function () {
                    that.setFieldMustFlag({ id: "finagentid", caption: "调入经销商", must: true });
                    that.setFieldMustFlag({ id: "fstorehouseidto", caption: "调入仓库", must: false });
                    that.setFieldMustFlag({ id: "fstorehouseid_h", caption: "调出仓库", must: true });//表头仓库必填
                    that.setFieldMustFlag({ id: "fstorehouseidto_h", caption: "调入仓库", must: false });//表头仓库取消必填
                }, 200)


            }
            else {
                that.Model.setValue({ id: 'foutagentid', value: '' });
                that.Model.setEnable({ id: 'fstoreto', value: true });//调出方，不允许选择
                that.Model.setVisible({ id: '[menu=createintransfer]', value: false });//不显示
                that.Model.setVisible({ id: '[menu=cancelintransfer]', value: false });//不显示
                that.Model.setVisible({ id: '.zyclass', value: false });
                that.Model.setVisible({ id: '.zyclass-billno', value: false });
                that.Model.setValue({ id: 'ftransfertype', value: 'invtransfer_biztype_01' });//标准

                setTimeout(function () {
                    that.setFieldMustFlag({ id: "finagentid", caption: "调入经销商", must: false });
                    that.setFieldMustFlag({ id: "fstorehouseidto", caption: "调入仓库", must: true });
                }, 200)
                that.Model.setVisible({ id: '.zyclass-store', value: true });
            }

        }


        //设置必录标签
        _child.prototype.setFieldMustFlag = function (e) {
            var that = this;
            var elem = that.Model.getEleMent({ id: '[name=' + e.id + ']' });
            if (elem) {
                var $label = elem.parent().parent().siblings('.control-label');
                if ($label) {
                    if (e.must) {
                        that.Model.setAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html('<span class="required">*</span>' + e.caption);
                    } else {
                        that.Model.removeAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html(e.caption);
                    }
                }
            }
        };


        _child.prototype.isAllSameStore = function () {
            var that = this;
            debugger
            var entitys = that.Model.getEntryData({ id: "fentity" });
            for (var i = 0; i < entitys.length; i++) {
                var entity = entitys[i];
                var storehouseOut = entity.fstorehouseid;
                var storehouseIn = entity.fstorehouseidto;
                var outStore = storehouseOut.fmulstore || '';
                var inStore = storehouseIn.fmulstore || '';
                if (!outStore || !inStore || outStore !== inStore) {
                    return false;
                }
            }
            return true;

        }

        // //获取默认的仓库
        // _child.prototype.SetDefaultStockInfo = function (isMatChange, deptfld, stockfld, rowIndex) {
        //     var that = this;
        //     var deptid = that.Model.getSimpleValue({ id: deptfld });
        //     if (!deptid) {
        //         //没有设置部门，不需要请求获取数据
        //         return;
        //     }
        //     if (isMatChange) {
        //         var fmaterialid = that.Model.getSimpleValue({ id: 'fmaterialid', row: rowIndex });
        //         var fstorehouseid = that.Model.getSimpleValue({ id: stockfld, row: rowIndex });
        //         if (fmaterialid && fstorehouseid) {
        //             //修改物料时，如果已经有仓库，不需要再设置默认的
        //             return;
        //         }
        //     }

        //     yiAjax.p('/bill/ydj_storehouse?operationno=getdefaultstockinfo&srcformid=stk_sostockout&deptid=' + deptid, null,
        //         function (r) {
        //             var data = r.operationResult;
        //             if (data.isSuccess) {
        //                 //库存参数中启用了部门仓库控制
        //                 var stockInfo = data.srvData;
        //                 if (stockInfo) {
        //                     //设置默认仓库
        //                     if (isMatChange) {
        //                         that.Model.setValue({ id: stockfld, row: rowIndex, value: stockInfo.id });
        //                     }
        //                     else {
        //                         var ds = that.Model.getEntryData({ id: 'fentity' });
        //                         for (var i = 0, j = ds.length; i < j; i++) {
        //                             that.Model.setValue({ id: stockfld, row: ds[i].id, value: stockInfo.id });
        //                         }
        //                     }
        //                 }
        //                 else {
        //                     //清空仓库
        //                     if (isMatChange) {
        //                         that.Model.setValue({ id: stockfld, row: rowIndex, value: "" });
        //                     }
        //                     else {
        //                         var ds = that.Model.getEntryData({ id: 'fentity' });
        //                         for (var i = 0, j = ds.length; i < j; i++) {
        //                             that.Model.setValue({ id: stockfld, row: ds[i].id, value: '' });
        //                         }
        //                     }
        //                 }
        //             } else {

        //             }
        //         }, null, null, null, { async: false }
        //     );

        // }


        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                //发货仓库：按库存参数中控制可选仓库范围
                case 'fstorehouseid':
                    //debugger;
                    var deptid = that.Model.getSimpleValue({ id: 'fstockdeptid' });
                    var srcPara = {
                        formid: 'stk_inventorytransfer',
                        deptid: deptid,
                    };
                    //直营的，出库仓库，不允许选择虚拟仓。调入仓库不控制
                    if (Consts.isdirectsale) {
                        e.result.filterString = " fwarehousetype!='warehouse_03' ";
                    }
                    e.result.simpleData = {
                        srcPara: JSON.stringify(srcPara)
                    };
                    break;
                //收货仓库：按库存参数中控制可选仓库范围
                case 'fstorehouseidto':
                    var deptid = that.Model.getSimpleValue({ id: 'fstockdeptidto' });
                    var srcPara = {
                        formid: 'stk_inventorytransfer',
                        deptid: deptid,
                    };
                    //直营的，出库仓库，不允许选择虚拟仓。调入仓库不控制
                    if (Consts.isdirectsale) {
                        e.result.filterString = " fwarehousetype!='warehouse_03' ";
                    }
                    e.result.simpleData = {
                        srcPara: JSON.stringify(srcPara)
                    };
                    break;
                case 'fbilltype':
                    //非直营，不允许选择跨组织调拨
                    if (!Consts.isdirectsale) {
                        e.result.filterString = " fname!='跨组织调拨' ";
                    }
                    break;
                case 'finagentid':
                    if (Consts.isdirectsale) {
                        e.result.filterString = " fmanagemodel='1' AND fid<>'" + Consts.loginCompany.id + "' AND fstatus='E'  ";

                    }
                    break;
                case 'fstorehouseid_h':
                    //直营的，出库仓库，不允许选择虚拟仓。调入仓库不控制
                    if (Consts.isdirectsale) {
                        e.result.filterString = " fwarehousetype!='warehouse_03' ";
                    }
                    break;
            }
        };




        //自定义填充引用字段值
        _child.prototype.fillBaseFieldValue = function (e) {
            var that = this;
            var fieldId = e.id.toLowerCase();
            var fieldValue = e.value;
            var row = e.row;
            if (!fieldValue && $.trim(fieldValue.id) == "") {
                return;
            }
            var targetFiledIdKey = fieldId + "to";
            var targetValue = that.Model.getValue({ id: targetFiledIdKey, row: row });
            if (targetValue && $.trim(targetValue.id) != "") {
                return;
            }
            that.Model.setValue({ id: targetFiledIdKey, value: fieldValue, row: row });
        }

        //自定义填充物流跟踪号字段值
        _child.prototype.fillMtoNoFieldValue = function (e) {
            var that = this;
            var mtono = $.trim(e.value);
            if (mtono) {
                that.Model.setValue({ id: "fmtonoto", value: mtono, row: e.row });
            }
        };

        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fcustomdesc':
                case 'fcallupcustomdescto':
                    //var status = that.Model.getSimpleValue({ id: 'fstatus' });
                    //if (status == 'E') return;
                    //var type = that.Model.getSimpleValue({ id: 'ftype' });
                    //if (type == '1') {
                    //    e.result.enabled = true;
                    //    return;
                    //}
                    //else {
                    //    e.result.enabled = false;
                    //    return;
                    //}
                    break;
                case 'fstorehouseid':
                    var fsourcetype = that.Model.getSimpleValue({ id: 'fsourcetype' });
                    if (fsourcetype=='ydj_order') {
                        e.result.enabled = false;
                    }
                    break;
            }

            if (that.checkZYInAgent()) {
                switch (e.id.toLowerCase()) {
                    case 'fstorehouseidto':
                    case 'fstorelocationidto':
                        e.result.enabled = true;
                        break;
                    default:
                        e.result.enabled = false;
                        break;
                }
            }
        }

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                // // 标签打印
                // case 'printproducttag':
                //     e.result = true;

                //     var selectedRows = that.Model.getSelectRows({ id: 'fentity' });
                //     if (selectedRows.length > 0) {
                //         var rows = [];

                //         for (var i = 0; i < selectedRows.length; i++) {
                //             rows.push({
                //                 PKValue: selectedRows[i].data.fproductid.id
                //             });
                //         }

                //         that.Model.invokeFormOperation({
                //             id: 'printproducttag',
                //             opcode: 'printproducttag',
                //             selectedRows: [{ PKValue: that.Model.pkid }],
                //             param: {
                //                 // formId: 'ydj_productbarcode',
                //                 dataChangeWarn: true
                //             }
                //         });
                //     }

                //     break;
                case 'querybarcode':
                    that.querybarcode(e);
                    break;
                case 'packorder':
                    debugger
                    that.packorder(e);
                    break;
                case 'save':
                    debugger;
                    that.isAllSameStore();
                    var that = this;
                    var fbillno = that.Model.getSimpleValue({ id: 'fbillno' });
                    var salOrderId = that.Model.getSimpleValue({ id: "fsourceinterid_h" });
                    if (salOrderId) {
                        var params = {
                            simpleData: {
                                formId: "stk_inventorytransfer",
                                orderId: salOrderId,
                                currBillNo: fbillno
                            }
                        };
                        yiAjax.p('/dynamic/ydj_order?operationno=checktransferorder', params, function (r) {
                            var srvData = r.operationResult.srvData;
                            if (r.operationResult.isSuccess && srvData.length > 0) {
                                e.result = true;
                                yiDialog.c("该合同己存在未审核的库存调拨单【" + srvData + "】，您确定继续吗？", function () {
                                    that.checkProducts(e);
                                }, function () {
                                    e.result = true;
                                }, '温馨提示');
                            }
                            else {
                                that.checkProducts(e);
                            }
                        }, null, null, null, { async: false });
                    }
                    else {
                        that.checkProducts(e);
                    }
                    break;
                case 'entrycopy':
                    e.result = true;
                    that.entrycopy();
                    break;
                case 'rejectflow':
                case 'auditflow':
                    var checkorder = that.checkOrder(e.opcode.toLowerCase());
                    var needZYCheck = false;
                    if (Consts.isdirectsale) {
                        needZYCheck = that.checkOrderNeedSync(e.opcode.toLowerCase());
                    }
                    //跨组织调出的时候，不需要提示
                    var ftransfertype = that.Model.getValue({ id: 'ftransfertype' });
                    var fstore = that.Model.getValue({ id: 'fstore' });
                    var fstoreto = that.Model.getValue({ id: 'fstoreto' });
                    debugger
                    if (needZYCheck && Consts.isdirectsale && e.opcode.toLowerCase() == 'auditflow' && ftransfertype?.id != 'invtransfer_biztype_04' && ftransfertype?.id != 'invtransfer_biztype_05') {
                        //if ((fstoreto?.fnumber == fstore?.fnumber)) {
                        //    //var param = e.param;
                        //    //param.formId = "stk_inventorytransfer";
                        //    //that.Model.invokeFormOperation({
                        //    //    id: 'tbAudit',
                        //    //    opcode: e.opcode.toLowerCase(),
                        //    //    param: param
                        //    //});
                        //} else
                        {
                            e.result = true;
                            yiDialog.c('审核通过后自动同步总部且不可反审核，确定继续审核？', function () {
                                if (checkorder && that.remindnumbers) {
                                    e.result = true;
                                    yiDialog.d({
                                        id: 'remindnumbers',
                                        type: 1,
                                        resize: false,
                                        maxmin: false,
                                        title: '系统提示',
                                        content: that.remindnumbers,
                                        area: ['400px', '200px'],
                                        btn: ['确定'],
                                        yes: function (index, layero) {
                                            layer.close(index);
                                        }
                                    });
                                } else {
                                    var param = e.param;
                                    param.formId = "stk_inventorytransfer";
                                    that.Model.invokeFormOperation({
                                        id: 'tbAudit',
                                        opcode: e.opcode.toLowerCase(),
                                        param: param
                                    });
                                }
                            }, function () {
                                e.result = true;
                                return;
                            });
                        }
                    }
                    //else if (Consts.isdirectsale && ftransfertype?.id != 'invtransfer_biztype_04' && (e.opcode.toLowerCase() == 'saveaudit' || e.opcode.toLowerCase() == 'auditflow')) {
                    //     e.result = true;
                    //     yiDialog.c('审核通过后自动同步总部且不可反审核，确定继续审核？', function () {
                    //         if (checkorder && that.remindnumbers) {
                    //             e.result = true;
                    //             yiDialog.d({
                    //                 id: 'remindnumbers',
                    //                 type: 1,
                    //                 resize: false,
                    //                 maxmin: false,
                    //                 title: '系统提示',
                    //                 content: that.remindnumbers,
                    //                 area: ['400px', '200px'],
                    //                 btn: ['确定'],
                    //                 yes: function (index, layero) {
                    //                     layer.close(index);
                    //                 }
                    //             });
                    //         } else {
                    //             var param = e.param;
                    //             param.formId = "stk_inventorytransfer";
                    //             that.Model.invokeFormOperation({
                    //                 id: 'tbAudit',
                    //                 opcode: e.opcode.toLowerCase(),
                    //                 param: param
                    //             });
                    //         }
                    //     }, function () {
                    //         e.result = true;
                    //         return;
                    //     });

                    // }
                    else {
                        if (checkorder && that.remindnumbers) {
                            e.result = true;
                            yiDialog.d({
                                id: 'remindnumbers',
                                type: 1,
                                resize: false,
                                maxmin: false,
                                title: '系统提示',
                                content: that.remindnumbers,
                                area: ['400px', '200px'],
                                btn: ['确定'],
                                yes: function (index, layero) {
                                    layer.close(index);
                                }
                            });
                        }
                    }
                    break;
                case 'cancelintransfer':
                    e.result = true;
                    var finagentid = that.Model.getValue({ id: 'finagentid' });
                    var fintransferbillno = that.Model.getValue({ id: 'fintransferbillno' });
                    var param = {
                        simpleData: {
                            formId: 'stk_inventorytransfer',
                            finagentid: finagentid.id,
                            fintransferbillno: fintransferbillno
                        }
                    };
                    yiAjax.p('/bill/stk_inventorytransfer?operationno=checkinagentbillstatus', param, function (r) {
                        that.Model.unblockUI({ id: '#page#' });
                        var res = r.operationResult;
                        var srvData = r.operationResult.srvData;
                        if (res.isSuccess && srvData) {
                            yiDialog.c(srvData, function () {
                                that.Model.invokeFormOperation({
                                    id: 'canceiintransfer',
                                    opcode: 'canceiintransfer',
                                    param: e.param
                                });
                            }, function () {
                                e.result = true;
                            }, '温馨提示');
                        }
                    }, null, null, null, { async: false });
                    break;
            }
        }

        //检查订单是否满足条件
        _child.prototype.checkOrder = function (opname) {
            var that = this;
            var isremind = false;

            var selectedRows;
            if (that.Model.viewModel.domainType == Consts.domainType.bill) {
                selectedRows = [{ pkValue: that.Model.pkid }];
            }
            if (that.Model.viewModel.domainType == Consts.domainType.list) {
                selectedRows = that.Model.getSelectRows();
                if (!selectedRows || selectedRows.length <= 0) {
                    yiDialog.mt({ msg: '请至少选择一条数据。', skinseq: 2 });
                    return;
                }
            }

            var ids = [];
            for (var i = 0; i < selectedRows.length; i++) {
                ids.push(selectedRows[i].pkValue);
            }
            var param = {
                simpleData: {
                    formId: 'stk_inventorytransfer',
                    Ids: ids.join(","),
                    opname: opname
                }
            };

            yiAjax.p('/bill/stk_inventorytransfer?operationno=verifyproduct', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                var srvData = r.operationResult.srvData;
                if (res.isSuccess && srvData) {
                    isremind = true;
                    that.remindnumbers = srvData;
                }
            }, null, null, null, { async: false });
            return isremind;
        };


        //检查订单是否需要同步
        _child.prototype.checkOrderNeedSync = function (opname) {
            var that = this;
            var zyneedsync = false;

            var selectedRows;
            if (that.Model.viewModel.domainType == Consts.domainType.bill) {
                selectedRows = [{ pkValue: that.Model.pkid }];
            }
            if (that.Model.viewModel.domainType == Consts.domainType.list) {
                selectedRows = that.Model.getSelectRows();
                if (!selectedRows || selectedRows.length <= 0) {
                    yiDialog.mt({ msg: '请至少选择一条数据。', skinseq: 2 });
                    return;
                }
            }

            var ids = [];
            for (var i = 0; i < selectedRows.length; i++) {
                ids.push(selectedRows[i].pkValue);
            }
            var param = {
                simpleData: {
                    formId: 'stk_inventorytransfer',
                    Ids: ids.join(","),
                    opname: opname
                }
            };

            yiAjax.p('/bill/stk_inventorytransfer?operationno=checkzyneedsync', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                var srvData = r.operationResult.srvData;
                if (res.isSuccess && srvData) {
                    zyneedsync = true;
                }
            }, null, null, null, { async: false });
            return zyneedsync;
        };

        //打码
        _child.prototype.packorder = function (e) {
            var that = this;
            e.result = true;
            var fbillno = "";
            if (that.Model.viewModel.domainType === 'list') {
                var rows = that.Model.getAllSelectedRows();
                if (rows.length <= 0) {
                    yiDialog.warn('请先选中行再操作！');
                    return;
                }
                else if (rows.length > 1) {
                    yiDialog.a('仅允许选择一个【已提交】状态的库存调拨单做调入打码！');
                    return;
                }
                else {
                    var status = rows[0].data["fstatus"];
                    if (status != "D") {
                        yiDialog.a('仅允许选择一个【已提交】状态的库存调拨单做调入打码！');
                        return;
                    }
                    fbillno = rows[0].data["fbillno"];
                }
            } else {
                fbillno = that.Model.getSimpleValue({ id: 'fbillno' });
            }
            that.Model.invokeFormOperation({
                id: 'IsExistPackOrder',
                opcode: 'IsExistPackOrder',
                param: {
                    'formId': 'bcm_packorder',
                    'fsourcetype': 'stk_inventorytransfer',
                    'fsourcenumber': fbillno,
                }
            });
        };


        //条码联查
        _child.prototype.querybarcode = function (e) {
            var that = this;
            e.result = true;

            var selRows = that.Model.getSelectRows({ id: "fentity" });
            if (!selRows || selRows.length < 1) {
                yiDialog.warn('请先选中行再操作!');
                return;
            }
            //if (selRows.length > 1) {
            //    yiDialog.warn('只允许勾选一行进行条码联查!');
            //    return;
            //}
            var fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
            var datas = [];
            for (var i = 0; i < selRows.length; i++) {
                datas.push({ seldata: selRows[i].data.fmaterialid.id });
            }
            //JSON.stringify(datas)
            that.Model.invokeFormOperation({
                id: 'isexsitbarcode',
                opcode: 'isexsitbarcode',
                param: {
                    'formId': 'bcm_barcodemaster',
                    'fsourcetype': 'stk_inventorytransfer',
                    'fsourcenumber': fbillno,
                    'fmaterialid': JSON.stringify(datas),
                }
            });
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                //case 'isexsitbarcode':
                //    //debugger
                //    //if (!isSuccess) {
                //    //    yiDialog.warn("当前商品未查询到对应条码信息!");
                //    //    return false;
                //    //}
                //    //弹出《条码主档》列表界面
                //    var filterString = "fmainorgid = '{0}' and fid in ({1})"
                //        .format(Consts.loginCompany.id, srvData.split(','));
                //    that.Model.showForm({
                //        formId: 'bcm_barcodemaster',
                //        domainType: Consts.domainType.list,
                //        param: {
                //            openStyle: Consts.openStyle.modal,
                //            filterstring: filterString
                //        }
                //    });
                //    break;
                case 'cancelallotout':
                case 'allotout':
                    that.Model.refresh();
                    break;
                case 'isexistpackorder':
                    //已经存在对应的《包装清单》时, 将对应的《包装清单》在弹出的视窗中打开表单
                    if (isSuccess) {
                        //var fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
                        var fbillno = "";
                        if (that.Model.viewModel.domainType === 'list') {
                            var rows = that.Model.getAllSelectedRows();
                            fbillno = $.trim(rows[0].data["fbillno"]);
                        } else {
                            fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
                        }
                        var filterString = " fsourcetype ='{0}' and fsourcenumber='{1}' and fmainorgid = '{2}'"
                            .format('stk_inventorytransfer', fbillno, Consts.loginCompany.id);
                        that.Model.showForm({
                            formId: 'bcm_packorder',
                            domainType: Consts.domainType.list,
                            param: {
                                openStyle: Consts.openStyle.modal,
                                filterstring: filterString
                            }
                        });
                    } else {
                        //不存在对应的《包装清单》时, 系统自动弹出全新的《包装清单》新增界面,
                        that.Model.invokeFormOperation({
                            id: 'push',
                            opcode: 'push',
                            param: {
                                'ruleId': "stk_inventorytransfer2bcm_packorder"
                            }
                        });
                    }
                    break;
                case 'getfifostock':
                    that.fillFIFOStock(srvData);
                    break;
                case 'getstorehouselockset':
                    if (srvData) {
                        that.storeHouseLockStatus = srvData;
                    }
                    break;
                case 'loadstocksysparam':
                    if (isSuccess && srvData) {
                        //设置本地缓存数据
                        var stocksysparam = {};
                        for (var data in srvData) {
                            stocksysparam[data] = srvData[data];
                        }
                        that.stocksysparam = stocksysparam;
                    }
                    break;
            }
        };

        // 表格选中行变化时触发
        _child.prototype.onSelectedRowsChanged = function (e) {
            var that = this;
            switch (e.id) {
                case 'fentity':
                    // 选中行设置为可用
                    that.Model.setEnable({ id: '#tbProductTagPrintSeting', value: e.data.length > 0 && that.Model.pkid != '' });
                    break;
            }
        }

        // 库存查询设置了标准字段后的回调，每行返回了rownumber
        _child.prototype.queryInventoryCallback = function (e) {
            var srvData = e.srvData;
            var that = this;

            //恢复标记位
            that.isqueryinventory = false;

            if (!srvData || !srvData.length) return;
            for (var i = 0; i < srvData.length; i++) {
                var r = srvData[i];
                if (r["row"] == undefined) {
                    r["row"] = that.Model.getSelectRows({ id: "fentity" })[0].data.id;
                }
                that.Model.setValue({ id: "fcustomdesc", value: r["fcustomdesc"], row: r["row"], ctx: { isqueryinventory: true } });
                that.Model.setValue({ id: "fcallupcustomdescto", value: r["fcustomdesc"], row: r["row"], ctx: { isqueryinventory: true } });
                //库存查询返回已经同意处理了数量逻辑，这里不要单独处理
                //that.Model.setValue({ id: "fqty", value: r["fqty"], row: r["row"] });

                var auxEntry = [];
                auxEntry = [].concat(r.fattrinfo.fentity);
                var auxPropArgs = { id: 'fattrinfoto', row: r["row"], value: { fentity: auxEntry }, ctx: { isqueryinventory: true } };

                that.Model.setValue(auxPropArgs);

                //根据当前单据类型设置【调出仓库】锁定状态
                var billTypeId = $.trim(that.Model.getSimpleValue({ id: 'fbilltype' }));
                var typeSet = that.storeHouseLockStatus[billTypeId];
                if (typeSet && typeSet.indexOf("-1") >= 0) {
                    that.Model.setEnable({ id: 'fstorehouseid', value: false, row: r["row"] });
                }
            }

            //仓库可用控制
            if (that.stocksysparam && that.stocksysparam.fistransferbystorehouse) {
                ////调出仓库
                var fstorehouseid_h = that.Model.getSimpleValue({ id: 'fstorehouseid_h' });
                if (fstorehouseid_h == "") {
                    var fentity = that.Model.getEntryData({ id: that.fentity });

                    for (var i = 0; i < fentity.length; i++) {
                        if (fentity[i].id == srvData[0].row) {
                            that.Model.setValue({ id: "fstorehouseid_h", value: srvData[0].fstorehouseid, ctx: { isqueryinventory: true } });
                            break;
                        }
                    }
                }
            }
            //if (that.Model.getValue({id:"ftype"}).id!="2") {

            //var fentity = that.Model.getEntryData({ id: "fentity" });
            //for (let i = 0; i < fentity.length; i++) {

            //    if (that.Model.getSelectRows({ id: "fentity" }).length == 0) {
            //        //未选择行，直接加在最后一行
            //        if (i == fentity.length - 1) {
            //            if (fentity[i].fattrinfo != "" && fentity[i].fattrinfo.fentity != null && fentity[i].fattrinfo.fentity.length > 0) {

            //                debugger
            //                var auxEntry = [];
            //                auxEntry = [].concat(fentity[i].fattrinfo.fentity);
            //                var auxPropArgs = { id: 'fattrinfoto', row: fentity[i].id, value: { fentity: auxEntry } };

            //                that.Model.setValue(auxPropArgs);
            //            } if (fentity[i].fcustomdesc != "") {
            //                that.Model.setValue({ id: "fcallupcustomdescto", value: fentity[i].fcustomdesc, row: fentity[i].id });
            //            }
            //        }
            //    } else {
            //        var rowid = that.Model.getSelectRows({ id: "fentity" })[0].data.id;
            //        var fattrinfo = that.Model.getSelectRows({ id: "fentity" })[0].data;
            //        var auxEntry = [];
            //        auxEntry = [].concat(fattrinfo.fattrinfo.fentity);
            //        var auxPropArgs = { id: 'fattrinfoto', row: rowid, value: { fentity: auxEntry } };
            //        that.Model.setValue(auxPropArgs);

            //        if (fattrinfo.fcustomdesc != "") {
            //            that.Model.setValue({ id: "fcallupcustomdescto", value: fattrinfo.fcustomdesc, row: rowid });
            //        }
            //        //that.Model.getSelectRows({ id: "fentity" }).data.id;
            //    }
            //}
            //}
        }

        //获取先进先出库位
        _child.prototype.getFIFOStock = function (rowId) {
            if (!rowId) {
                return;
            }
            var that = this;
            var vm = that.Model.viewModel;
            var rowData = that.Model.getEntryRowData({ id: that.fentity, row: rowId });
            if (!rowData) {
                return;
            }

            if (rowData.fbizqty <= 0) {
                return;
            }

            var fisnofifostock = rowData.fisnofifostock;
            if (fisnofifostock) {
                return;
            }

            // 设置了仓库和仓位的，不触发获取
            if (rowData.fstorehouseid && rowData.fstorehouseid.id.trim() && rowData.fstorelocationid && rowData.fstorelocationid.id.trim()) {
                return;
            }

            that.Model.invokeFormOperation({
                id: 'getfifostock',
                opcode: 'getfifostock',
                param: {
                    formId: vm.formId,
                    rowData: JSON.stringify(rowData),
                    rowId: rowId
                }
            });

            if (that.notGetFIFOStock.hasOwnProperty(rowId)) {
                delete that.notGetFIFOStock[rowId];
            }
        }

        //填充推荐库位
        _child.prototype.fillFIFOStock = function (srvData) {
            var that = this;
            if (srvData && srvData.rowDatas) {

                var rowDatas = srvData.rowDatas;
                var firstRowData = rowDatas[0];
                var rowId = srvData.rowId;

                //先清理标记位，避免死循环
                that.notGetFIFOStock[rowId] = "";

                that.Model.setValue({ id: 'fstorehouseid', value: firstRowData.fstorehouseid, row: rowId });
                that.Model.setValue({ id: 'fstorelocationid', value: firstRowData.fstorelocationid, row: rowId });
                that.Model.setValue({ id: 'fstockstatus', value: firstRowData.fstockstatus, row: rowId });
                that.Model.setValue({ id: 'fstockqty', value: firstRowData.fstockqty, row: rowId });
                that.Model.setValue({ id: 'fqty', value: firstRowData.fqty, row: rowId });

                if (that.stocksysparam && that.stocksysparam.fistransferbystorehouse) {
                    //调出仓库
                    that.Model.setValue({ id: "fstorehouseid_h", value: firstRowData.fstorehouseid, tgChange: false });
                }

                if (rowDatas.length > 1) {
                    for (var i = rowDatas.length - 1; i > 0; i--) {
                        var rowData = rowDatas[i];
                        var id = that.Model.addRow({ id: that.fentity, row: rowId, data: rowData });

                        that.notGetFIFOStock[id] = "";
                    }
                }
            }
        };

        _child.prototype.checkProducts = function (e) {
            // 商品档案勾选了<允许选配>或<非标产品>校验合同辅助属性和定制说明,
            var that = this;
            var productEntry = that.Model.getEntryData({ id: that.fentity });
            var checkEntry = [];
            for (var i = 0; i < productEntry.length; i++) {
                var attrinfoEntity = productEntry[i].fattrinfo.fentity;
                if (attrinfoEntity == null) attrinfoEntity = [];
                var entryitem = {
                    fproductid: productEntry[i].fmaterialid.id,
                    fseq: productEntry[i].FSeq,
                    fattrinfo: attrinfoEntity.length > 0 ? JSON.stringify(attrinfoEntity) : "",
                    fcustomdesc: productEntry[i].fcustomdesc,
                };
                checkEntry.push(entryitem);
            }
            var param = {
                simpleData: {
                    formId: 'stk_inventorytransfer',
                    entry: JSON.stringify(checkEntry),
                    status: that.Model.getSimpleValue({ id: 'fstatus' }),
                    domainType: 'dynamic'
                }
            };
            yiAjax.p('/bill/stk_inventorytransfer?operationno=checkproducts', param, function (r) {
                var res = r.operationResult;
                e.result = true;
                var param = e.param;
                param.formId = "stk_inventorytransfer";
                if (res.isSuccess && res.srvData.length > 0) {
                    yiDialog.c(res.srvData, function () {
                        that.Model.invokeFormOperation({
                            id: 'tbSave',
                            opcode: 'save',
                            param: param
                        });
                    });
                }
                else {
                    that.Model.invokeFormOperation({
                        id: 'tbSave',
                        opcode: 'save',
                        param: param
                    });
                }
            }, null, null, null, { async: false });
        };

        //加载单据类型参数设置
        _child.prototype.loadBillTypeParamSet = function () {
            var that = this;
            that.Model.invokeFormOperation({
                id: 'getstorehouselockset',
                opcode: 'getstorehouselockset'
            });
        };

        //加载库存管理参数
        _child.prototype.loadStockParam = function () {
            var that = this;
            that.Model.invokeFormOperation({
                id: 'loadstocksysparam',
                opcode: 'loadstocksysparam',
                async: false,   // 同步请求
                param: {
                    'formId': 'stk_stockparam',
                    'domainType': 'parameter'
                }
            });
        };

        //加载库存管理参数
        _child.prototype.setHeadStoreHouseVisible = function () {
            var that = this;
            // 勾选<库存调拨单按仓库调拨>
            if (that.stocksysparam && that.stocksysparam.fistransferbystorehouse) {
                $('.fistransferbystorehouse').show();
                //设置明细行调出/调入仓库不可编辑
                that.Model.setEnable({ id: "fstorehouseid", value: false });
                //that.Model.setEnable({ id: "fstorelocationid", value: false });
                that.Model.setEnable({ id: "fstorehouseidto", value: false });
                //that.Model.setEnable({ id: "fstorelocationidto", value: false });

            }
        };

        //表单操作前触发
        _child.prototype.onBeforeDoOperation = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                case 'queryinventory':
                    //debugger;
                    //仓库可用控制
                    if (that.stocksysparam && that.stocksysparam.fistransferbystorehouse) {
                        //调出仓库
                        var fstorehouseid_h = that.Model.getSimpleValue({ id: 'fstorehouseid_h' });
                        if (fstorehouseid_h != "") {
                            var filterString = "fstorehouseid='" + fstorehouseid_h + "'";
                            e.result = { 'filterString': filterString };
                        }
                    }
                    break;
            }
        };

        //表格行创建后触发（列表只读情况下无效）
        _child.prototype.onEntryRowCreated = function (e) {
            var that = this;
            if (that.stocksysparam && that.stocksysparam.fistransferbystorehouse) {
                //调出仓库
                var storeLocationId_h = that.Model.getSimpleValue({ id: "fstorehouseid_h" });
                that.Model.setValue({ id: "fstorehouseid", value: storeLocationId_h, row: e.row });
                //调入仓库
                var storeLocationIdto_h = that.Model.getSimpleValue({ id: "fstorehouseidto_h" });
                that.Model.setValue({ id: "fstorehouseidto", value: storeLocationIdto_h, row: e.row });
            }
        };

        //选单后事件
        _child.prototype.onAfterPull = function (e) {
            var that = this;
            if (that.stocksysparam && that.stocksysparam.fistransferbystorehouse) {
                //调入仓库
                var storehouseid_h = that.Model.getValue({ id: "fstorehouseid_h" });
                var storeLocationIdto_h = that.Model.getValue({ id: "fstorehouseidto_h" });
                var entrys = e.uidata.fentity;
                if (entrys && entrys.length > 0) {
                    for (var i = 0; i < entrys.length; i++) {
                        //entrys[i].fstorehouseidto = storeLocationIdto_h;
                        //entrys[i].fstorehouseid = storehouseid_h;
                        //that.Model.setValue({ id: "fstorehouseidto", value: storeLocationIdto_h, row: entrys[i].id });
                    }
                }
                //if (entrys && entrys.length > 0) {
                //    for (var i = 0; i < entrys.length; i++) {
                //        entrys[i].fstorehouseid = storehouseid_h;
                //        //that.Model.setValue({ id: "fstorehouseidto", value: storeLocationIdto_h, row: entrys[i].id });
                //    }
                //}
            }
        };

        _child.prototype.entrycopy = function () {
            //选中行
            var that = this;
            var ds = that.Model.getSelectRows({ id: that.fentity });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行关联复制行！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('关联复制行不支持多选！');
                return;
            };
            if (ds) {
                var newentry = $.extend(true, [], ds);
                var newid = yiCommon.uuid(18);
                newentry[0].data.id = newid;
                newentry[0].pkid = newid;
                newentry[0].data.fiscopy = 1;
                var row = that.Model.addRow({ id: that.fentity, pid: ds[0].pkid, data: newentry[0].data });
                that.Model.setEnable({ id: "fmaterialid", row: row, value: false });
                var fields = that.Model.uiForm.getAllFields();
                //数量、仓库、仓位不锁定，其它都锁定
                var noLockFields = ["fbizqty", "fstorehouseid", "fstorelocationid", "fstorehouseidto", "fstorelocationidto"];
                for (var i = 0; i < fields.length; i++) {
                    if (that.uiForm.getField(fields[i]).entityKey != that.fentity) {
                        continue;
                    }
                    if (noLockFields.indexOf(fields[i]) > -1) {
                        continue;
                    }
                    that.Model.setEnable({ id: fields[i], row: row, value: false });
                }
            }
        };


        //表格行删除前事件，设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.fentity:
                    if (that.checkZYInAgent()) {
                        e.result = true;
                        yiDialog.mt({ msg: '跨组织调拨调入方经销商不允许新增行！', skinseq: 2 });
                        return;
                    }
                    break;
            }
        };


        _child.prototype.onEntryRowCreating = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.fentity:
                    if (that.checkZYInAgent()) {
                        e.result = true;
                        yiDialog.mt({ msg: '跨组织调拨调入方经销商不允许新增行！', skinseq: 2 });
                        return;
                    }
                    break;

            }
        };


        //判断当前经销商是否是直营的跨组织调拨的调入方
        _child.prototype.checkZYInAgent = function () {
            var that = this;
            var fbilltype = that.Model.getValue({ id: 'fbilltype' });
            var ftransfertype = that.Model.getValue({ id: 'ftransfertype' });
            if (fbilltype && fbilltype.fname == "跨组织调拨" && Consts.isdirectsale && ftransfertype && ftransfertype.id == 'invtransfer_biztype_05') {
                return true;
            } return false;
        }

        //初始化设置控件是否可编辑
        _child.prototype.initSetEnable = function () {
            var that = this;
            var fsourcetype = that.Model.getSimpleValue({ id: 'fsourcetype' });
            //源单类型为销售合同时,调出仓库不可编辑
            if (fsourcetype == 'ydj_order') {
                that.Model.setEnable({ id: "fstorehouseid_h", value: false });
            }
        }

        _child.prototype.checkModelIsDirectlyOrAgentHidePurButton = function (e) {
            var that = this;
            if (Consts.isdirectsale) {
                if (e.id) {
                    switch (e.id.toLocaleLowerCase()) {
                        case 'list':
                            $('div[menu="Audit"]').hide();
                            $('div[menu="auditflow"]').hide();
                            break;
                    }
                }
            }
        }

        return _child;
    })(BasePlugIn);
    window.stk_inventorytransfer = window.stk_inventorytransfer || stk_inventorytransfer;
})();