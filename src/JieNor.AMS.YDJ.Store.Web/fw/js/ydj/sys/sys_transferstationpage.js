/**
   @ sourceURL=/fw/js/ydj/sys/sys_transferstationpage.js
*/

; (function () {
    var sys_transferstationpage = {
        initData: {}, //初始化数据
        allUploaded: [], //所有已上传成功的文件，不包括重复文件（也就是每个文件的hash值不一样）
        tempUploaded: [], //每次点击文件上传按钮后通过对话框上传的文件（用于临时存储）
        addFiles: "",//新增的文件 

        //初始化
        init: function () {
            var that = this;
            var keys = $.query.keys;
            keys.type = 0;

            var url = '/dynamic/sys_detailsynergism' + document.location.search + '&operationno=getdetailline';
            yiAjax.p(url, keys, function (r) {
                var result = r.operationResult;
                var isSuccess = result.isSuccess;
                var srvData = result.srvData;
                debugger
                if (isSuccess) {

                    //获取系统信息
                    that.getSysInfo(function () {

                        //获取文件列表
                        var url = Consts.upApi.fsApiUrl + 'FileInfo/FetchFiles';
                        var params = {
                            authCode: Consts.upApi.authCode,
                            sysCode: Consts.upApi.sysCode,
                            fileIds: srvData["fmulfile"]
                        };
                        yiAjax.p(url, params, function (r) {
                            if (!r) {
                                yiDialog.warn('您当前没有文件查看权限！');
                                return;
                            }
                            var data = [];
                            debugger
                            var source = srvData["fmulfile_source"].split(',');
                            for (var k in r) {
                                var i = 0;
                                var v = r[k];
                                var c = v['caption'];
                                var s = c.split('.');
                                data.push({
                                    fileId: k,
                                    fileName: c,
                                    fileFormat: s[s.length - 1],
                                    fileSize: v['size'],
                                    uploader: '',
                                    uploaderId: '',
                                    uploadTime: v['uploadTime'],
                                    fmulfile_source: source[i]
                                });
                                i++;
                            }

                            //初始化表格
                            that.initGrid(data);

                            //绑定事件
                            that.bindEvent();

                        }, null, null, $('body'));

                    });

                } else {
                    var msg = '您当前没有文件查看权限！';
                    if (result.simpleMessage) {
                        msg = result.simpleMessage;
                    } else {
                        var complexMsg = result.complexMessage;
                        if (complexMsg && complexMsg.errorMessages && complexMsg.errorMessages[0]) {
                            msg = complexMsg.errorMessages[0];
                        }
                    }
                    yiDialog.warn(msg);
                }
            }, null, null, $('body'));

        },

        //初始化表格
        initGrid: function (data) {
            var that = this;
            that.$grid = $('#fdrawentity').css({ width: '100%', height: '400px' });
            data = data || [];

            //自动生成行号
            for (var i = 0; i < data.length; i++) {
                if (!$.trim(data[i].id)) {
                    data[i].id = yiCommon.uuid();
                }
            }

            //按钮列表
            var template = '<button type="button" class="btn btn-sm btn-entry" btnid="{0}" rowId="{2}"{3}>{1}</button> ';
            var btns = [{ id: 'download', text: '下载' }, { id: 'preview', text: '预览' }, { id: 'delete', text: '删除' }];

            //表格选项
            var options = {
                editorLock: new Slick.EditorLock(),
                editable: true,
                editorCellNavOnLRKeys: true,
                enableAddRow: false,
                enableCellNavigation: true,
                asyncEditorLoading: false,
                multiSelect: false,
                multiColumnSort: true,
                numberedMultiColumnSort: true,
                tristateMultiColumnSort: true,
                sortColNumberInSeparateSpan: false,
                explicitInitialization: true,
                headerRowHeight: 40,
                rowHeight: 41
            };

            //表格列模型
            var columns = [
                { id: 'fileName', field: 'fileName', name: '文件名', width: 350, selectable: true, sortable: false, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-left' },
                { id: 'fileFormat', field: 'fileFormat', name: '文件格式', width: 70, selectable: true, sortable: false, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-left' },
                { id: 'fileSize', field: 'fileSize', name: '文件大小', width: 170, selectable: true, sortable: false, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-left' },
                {
                    id: 'description', field: 'description', name: '备注', width: 260, selectable: true, sortable: false, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-left',
                    editor: function (args) {
                        var scope = this;
                        var $input;
                        var defVal = '';

                        this.init = function () {
                            $input = $('<input type="text" class="y-editor-text" />')
                                .appendTo(args.container).focus().select();
                        };

                        this.destroy = function () {
                            $input.remove();
                        };

                        this.focus = function () {
                            $input.focus();
                        };

                        this.getValue = function () {
                            return $input.val();
                        };

                        this.setValue = function (val) {
                            $input.val(val);
                        };

                        this.loadValue = function (item) {
                            defVal = item[args.column.field] || defVal;
                            $input.val(defVal).select();
                        };

                        this.serializeValue = function () {
                            return $input.val();
                        };

                        this.applyValue = function (item, state) {
                            item[args.column.field] = state;
                        };

                        this.isValueChanged = function () {
                            return $input.val() !== defVal;
                        };

                        this.validate = function () {
                            if (args.column.validator) {
                                var validationResults = args.column.validator($input.val());
                                if (!validationResults.valid) {
                                    return validationResults;
                                }
                            }
                            return { valid: true, msg: null };
                        };

                        this.init();
                    }
                },
                { id: 'fileName', field: 'fileName', name: '文件名', width: 350, selectable: true, sortable: false, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-left' },
                { id: 'uploader', field: 'uploader', name: '上传人', width: 65, selectable: true, sortable: false, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-left' },
                { id: 'fmulfile_source', field: 'fmulfile_source', name: '上传方', width: 120, selectable: true, sortable: false, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-center' },
                {
                    id: 'foplist', field: 'foplist', name: '操作', width: 175, selectable: true, sortable: false, resizable: true, headerCssClass: 'y-head-center', cssClass: 'y-cell-center',
                    formatter: function (row, cell, value, column, item, grid) {
                        var html = '';
                        for (var i = 0; i < btns.length; i++) {
                            var btn = btns[i];
                            var disabled = btn.disabled ? ' disabled' : '';
                            html += template.format(btn.id, btn.text, item.id, disabled);
                        }
                        return html;
                    }
                }
            ];

            //创建数据视图实例
            that.dataView = new Slick.Data.DataView({ inlineFilters: true });

            //创建表格实例
            that.grid = new Slick.Grid(that.$grid, that.dataView, columns, options);

            //设置选择模式
            that.grid.setSelectionModel(new Slick.RowSelectionModel({ selectActiveRow: false }));

            //按照设置的宽高初始化表格大小
            that.grid.resizeCanvas();

            //在修改总行数时触发
            that.dataView.onRowCountChanged.subscribe(function (e, args) {
                that.grid.updateRowCount();
                that.grid.render();
            });

            //在修改行数据时触发
            that.dataView.onRowsChanged.subscribe(function (e, args) {
                that.grid.invalidateRows(args.rows);
                that.grid.render();
            });

            //单元格变化时触发
            that.grid.onCellChange.subscribe(function (e, args) {
                that.saveData();
            });

            //单击行时触发
            that.grid.onClick.subscribe(function (e, args) {
                var $target = $(e.target);
                if ($target.hasClass('btn-entry')) return;
            });

            //设置数据源
            that.dataView.beginUpdate();
            that.dataView.setItems(data);
            that.dataView.endUpdate();

            //初始化表格
            that.grid.init();
        },

        //预览文件
        previewFile: function (r) {
            var url = '';
            var srvData = r.operationResult.srvData;
            if (srvData && srvData.length > 0 && srvData[0]) {
                url = $.trim(srvData[0].url);
            }
            if (!url) {
                yiDialog.warn('该文件不存在或已被删除，无法预览！');
                return;
            }

            var $showImg = $('.img-show');

            //先销毁图片预览器
            $showImg.empty().viewer('destroy');

            var html = '<li v-id="{0}"><img data-original="{0}" src="{0}"></li>'.format(url);
            $showImg.append(html);

            //初始化图片预览器
            $showImg.viewer({
                shown: function () {
                    //设置从第一张图片开始预览 
                    $('#imgWin').viewer('view', 0);
                }
            });
            $showImg.viewer('show');
        },

        //获取文件服务器配置信息
        getSysInfo: function (callback) {
            yiAjax.p('/dynamic/sys_mainfw?operationno=getsysinfo', {}, function (r) {
                var srvData = r.operationResult.srvData;
                //加载文件服务器配置
                Consts.upApi = srvData.fileServerInfo;
                //自动补全 fsApiUrl 最后的斜杠
                if (Consts.upApi && Consts.upApi.fsApiUrl) {
                    if (Consts.upApi.fsApiUrl[Consts.upApi.fsApiUrl.length - 1] !== '/') {
                        Consts.upApi.fsApiUrl += '/';
                    }
                }
                if (callback) {
                    callback();
                }
            }, null, null, $('body'));
        },

        //绑定事件
        bindEvent: function () {
            var that = this;
            debugger
            //上传
            $('.icon-upload').on('click', function () {
                that.initUpload();
            });

            //表格行中按钮字段的点击事件
            that.$grid.on('click', '.btn-entry', function (e) {
                var $btn = $(this);
                var rowId = $btn.attr('rowid');
                var btnId = $btn.attr('btnid');
                var rowData = that.dataView.getItemById(rowId);
                var fileId = $.trim(rowData.fileId);
                var fileName = $.trim(rowData.fileName);
                switch (btnId) {
                    case 'download':
                        yiCommon.downloadFile({
                            data: {
                                fileIds: fileId,
                                fileNames: fileName
                            }
                        });
                        break;
                    case 'preview':
                        yiAjax.p('/fileinfo', { fileId: fileId }, function (r) {
                            that.previewFile(r);
                        }, null, null, $('body'));
                        break;
                    case 'delete':
                        if (that.initData && rowData.fmulfile_source =="金蝶云") {
                            yiDialog.c('您确定要删除吗？', function () {
                                that.dataView.deleteItem(rowId);
                                that.addFiles = that.dataView.getItems();
                                that.saveData('delete');
                            });
                        } else {
                            yiDialog.warn('该文件不是在金蝶云上传的，无法删除！');
                        }
                        break;
                }
            });
        },

        //初始化文件上传
        initUpload: function () {
            var that = this;
            debugger
            //清空临时文件存储区
            that.tempUploaded = [];
            that.allUploaded = [];

            //0 表示修改，1 表示只读
            if ($.query.keys.type == 1) {
                yiDialog.warn('您当前没有上传文件的权限，无法上传！');
                return;
            }
            var html = '<div class="uploader-file mul-type" sizelimit="102400"></div>';
            yiDialog.d({
                id: 'upload-dialog',
                type: 1,
                resize: false,
                content: html,
                title: '请选择要上传的文件',
                area: '600px',
                btn: ['取消', '提交'],
                btncls: ['', '0'],
                yes: function (index, layero) {
                    layer.close(index);
                },
                btn2: function (index, layero) {
                    var tempEntry = [];
                    var temps = that.tempUploaded;
                    var alls = that.allUploaded;
                    if (!that.flag || temps.length <= 0 || alls.length <= 0) {
                        yiDialog.warn('请选择要上传的文件 或 等待文件上传完成后，再提交！');
                        return false;
                    }
                    for (var i = 0; i < temps.length; i++) {
                        var uuId = yiCommon.uuid();
                        var tempFileId = '';
                        for (var s = 0, k = alls.length; s < k; s++) {
                            //靠大小和名字来判定
                            if (alls[s].name == temps[i].name && alls[s].size == temps[i].size) {
                                tempFileId = alls[s].id;
                                break;
                            }
                        }
                        if (!tempFileId) continue;

                        var name = $.trim(temps[i].name);
                        var names = name.split('.');
                        var fileFormat = '';
                        if (names.length > 1) {
                            fileFormat = names[1];
                        }
                        var uploader = '';
                        var uploaderId = '';
                        if (that.initData) {
                            uploader = that.initData.uploader;
                            uploaderId = that.initData.uploaderId;
                        }
                        tempEntry.push({
                            id: uuId,
                            rowId: uuId,
                            fileId: tempFileId, //文件Id
                            fileName: name, //文件名
                            fileFormat: fileFormat, //文件格式
                            fileSize: yiCommon.bytesToSize(temps[i].size), //文件大小
                            uploader: uploader, //上传人名称
                            uploaderId: uploaderId, //上传人Id
                            uploadTime: new Date().ToString('yyyy-MM-dd HH:mm:ss'),//上传时间
                            fmulfile_source: "金蝶云" //上传方
                        });
                    }
                    if (tempEntry.length > 0) {
                        var sortArr = [];
                        var hasRepreatFile = false;
                        var dataEntry = that.dataView.getItems();
                        for (var i = 0, l = tempEntry.length; i < l; i++) {
                            var flag = true;
                            var lm = tempEntry[i];
                            if (dataEntry) {
                                for (var s = 0, m = dataEntry.length; s < m; s++) {
                                    if (dataEntry[s] && dataEntry[s].fileId == lm.fileId) {
                                        flag = false;
                                        hasRepreatFile = true;
                                        break;
                                    }
                                }
                            }
                            if (flag) {
                                sortArr.push(lm);
                            }
                        }
                        if (hasRepreatFile) {
                            yiDialog.warn('本次上传存在重复的文件，系统已自动忽略，请注意！');
                        }
                        if (sortArr.length > 0) {
                            for (var i = 0, l = sortArr.length; i < l; i++) {
                                dataEntry.push(sortArr[i]);
                            }
                            //先清空一下
                            that.dataView.setItems([]);

                            //设置数据源
                            that.dataView.beginUpdate();
                            that.dataView.setItems(dataEntry);
                            that.dataView.endUpdate();
                            that.addFiles = dataEntry;
                            that.saveData('submit');
                        }
                    }
                    //关闭对话框
                    layer.close(index);
                },
                success: function (layero, index) {
                    var $layero = $(layero);
                    //初始化文件上传控件
                    var uploaders = $layero.find('div.uploader-file');
                    if ($.isFunction(uploaders.uploader)) {
                        uploaders.uploader({
                            auto: true,
                            //为弹出框设置的
                            layero: $layero,
                            //文件加入队列
                            beforeFile: function (file) {
                                that.flag = false;
                            },
                            mulfile: function (files) {
                                that.tempUploaded = files || [];
                            },
                            //文件加载完成
                            uploadSuc: function (fileId, type, file) {
                                if (type == 'onUploadSuccess') {
                                    that.flag = true;
                                    var revert = true;
                                    for (var i = 0, l = that.allUploaded.length; i < l; i++) {
                                        if (that.allUploaded[i] && that.allUploaded[i].id == fileId) {
                                            revert = false;
                                            break;
                                        }
                                    }
                                    if (revert) {
                                        that.allUploaded.push({
                                            id: file.fileId,
                                            name: file.name,
                                            size: file.size
                                        });
                                    }
                                }
                            }
                        });
                    }
                }
            });
        },

        //保存数据
        saveData: function (op) {
            var that = this;

            //结束表格编辑状态，避免取到的是旧数据。
            var editorLock = that.grid.getEditorLock();
            if (editorLock) {
                editorLock.commitCurrentEdit();
            }
            var params = {
                simpleData: {
                    ftranid: $.query.keys.data,
                    addFiles: JSON.stringify(that.addFiles)
                }
            };
            yiAjax.p('/dynamic/sys_detailsynergism?operationno=addordeletefile', params, function (r) {
                var msg = '保存成功！';
                if (op === 'delete') msg = '删除成功！';
                if (op === 'submit') msg = '提交成功！';
                yiDialog.success(msg);
            }, null, null, $('body'));
        }

    };

    sys_transferstationpage.init();
})();