; (function () {
    var coo_selectcom = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);
		
		
		//初始化企业下拉菜单
        _child.prototype.onInitialized  = function (args) {
        	var that = this;
        	if(that.Model.uiData.sendType == 'ydj_customer'){
        		var sendPhone = that.Model.uiData.fcusphone;
        	}else{
        		var sendPhone = that.Model.uiData.fsupphone;
        	}
        	
        	var url = '/bill/coo_company?operationno=getCompanyBaseList',
            param = {
                simpledata: {
				 	phone: sendPhone
                }
            };
        	that.Model.blockUI({ id: '#page#' });
        	yiAjax.p(url, param, function (r) {
        	    that.Model.unblockUI({ id: '#page#' });
            	that.setSelData(r.operationResult.srvData);
            },
            function (m) {
                that.Model.unblockUI({ id: '#page#' });
                yiDialog.m({ msg: '服务处理错误：' + yiCommon.extract(m) });
            });
        };
		
		//处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            if (!args.opcode) return;
            switch (args.opcode) {
             	case 'syncancel':
                    //点击取消关闭企业选择弹窗
                    args.result = true;
                    that.Model.close();
                    break;
                case 'synconfirm':
                    //确定
                    args.result = true;
                	that.selectCom();
                    break;
            }
        };
        
        //渲染下拉菜单
        _child.prototype.setSelData = function (res) {
            if (!res) {
                return;
            }
            var that = this;

            //数据一次性下发。变量储存
            that.data = res;
            var aData = res;
            var setStr = '';
            //数据提取
            for (var i = 0, l = aData.length; i < l; i++) {
                var lm = aData[i];
                setStr += '<option value="{1}" farea="{2}" fproductid="{3}">{0}</option>'.format(lm.fcompanyname, lm.fcompanyid, lm.farea,lm.fproductid);
            }

            //数据选择
            that.Model.setHtml({ id: '.company_model', value: setStr });

        };
        
        //确定选择企业
        _child.prototype.selectCom = function (args) {
            var that = this;
            var selected = that.Model.getEleMent({ id: '.company_model option:selected' });
            that.Model.setReturnData({
                newRow: {
                    companyid: selected.val(),
                    companyname: selected.text(),
                    area: selected.attr('farea'),
                    productid: selected.attr('fproductid')
                }
            });
        	//关闭对话框
            that.Model.close();
        }
        
		
        return _child;
    })(BasePlugIn);
    window.coo_selectcom = window.coo_selectcom || coo_selectcom;
})();