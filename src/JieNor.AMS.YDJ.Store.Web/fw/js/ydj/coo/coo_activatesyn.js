; (function () {
    var coo_activatesyn = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);
		
		//初始化编辑页面插件
        _child.prototype.onInitialized  = function (args) {
        	var that = this;
        	var serviceType = that.Model.uiData.serviceType;
        	if(serviceType == '供应商'){
        		that.Model.setVisible({id:'.y-supplier',value:true});
        		that.Model.setVisible({id:'.y-customer',value:false});
        	}else if(serviceType == '客户'){
        		that.Model.setVisible({id:'.y-supplier',value:false});
        		that.Model.setVisible({id:'.y-customer',value:true});
        	}
        };
		
		//处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            if (!args.opcode) return;
            switch (args.opcode) {
             	case 'activatecancel':
                    //点击取消关闭协同发布弹窗
                    args.result = true;
                    that.Model.close();
                    break;
                case 'activatesyn':
                    //保存发布操作
                    args.result = true;
                	that.activateSyn();
                    break;
            }
        };
        
        //激活操作
        _child.prototype.activateSyn = function (args) {
            var that = this;

            var customer = that.Model.getValue({ id: 'fcustomerid' }),
        		supplier = that.Model.getValue({ id: 'fsupplierid' });

            if (customer.fname == '' && supplier.fname == '') {
                yiDialog.mt({ msg: '请选择发布对象！', skinseq: 2 });
            } else {
                var companyId = that.Model.uiData.companyId,
	    			serviceType = that.Model.uiData.serviceType;
                var CusorSupId,
	        		CusorSupName;
                if (serviceType == '供应商') {
                    CusorSupId = supplier.id;
                    CusorSupName = supplier.fname;
                } else if (serviceType == '客户') {
                    CusorSupId = customer.id;
                    CusorSupName = customer.fname;
                }
                //var url = '/bill/coo_company?operationno=synCompanyActivate',
                //param = {
                //    simpledata: {
                //	 	id: companyId,
                //	 	relationid: CusorSupId,
                //	 	relationname: CusorSupName
                //    }
                //}
                yiDialog.c('确定要重新向该企业发起协同邀请？', function () {
                    that.Model.invokeFormOperation({
                        id: 'activateSyn',
                        opcode: 'setCompanyRelationStatus',
                        param: {
                            formId: 'coo_company',
                            domainType: 'dynamic',
                            optype: '2',
                            id: companyId,
                            relationid: CusorSupId,
                        }
                    });
                    //yiAjax.p(url, param, function (r) {
                    //	that.Model.close();
                    //	if(r.operationResult.isSuccess == true){
                    //		yiDialog.mt({msg:'邀请成功，请等待对方企业处理！', skinseq: 1});
                    //	}
                    //});
                });
            }
        };
        
        _child.prototype.onAfterDoOperation = function (args) {
            var that = this;
            switch (args.id) {
                case 'activateSyn':
                    if (args.result && args.result.operationResult && args.result.operationResult.isSuccess) {
                        that.Model.close();
                        yiDialog.mt({ msg: '邀请成功，请等待对方企业处理！', skinseq: 1 });
                    }
                    break;
            }
        };
		
        return _child;
    })(BasePlugIn);
    window.coo_activatesyn = window.coo_activatesyn || coo_activatesyn;
})();