///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/ste/ydj_order_param.js
*/
; (function () {
    var ydj_order_param = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;
            _super.prototype.onInitialized.call(that, e);

            if (Consts.isdirectsale) {
                that.Model.setVisible({ id: '.zyclass', value: true }); //显示直营字段
            } else {
                that.Model.setVisible({ id: '.zyclass', value: false }); //隐藏直营字段
            }
        };

        return _child;
    })(BasePlugIn);
    window.ydj_order_param = window.ydj_order_param || ydj_order_param;
})();