/*
 * description:费用申请业务控制插件
 * author: 
 * create date: 
 * modify by: <PERSON><PERSON><PERSON><PERSON>.
 * modify date: 2019-02-21
 * remark: 
 *@ sourceURL=/fw/js/ydj/ste/ste_registfee.js
*/
;(function () {
    var ste_registfee = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        _child.prototype.entryId = "fentry";

        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;

            that.renewalHeadquart();
        }

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'frelatecusid':
                    that.getTaxRate(e.value.id);
                    that.deleteRow();
                    break;
                case 'frelatetype':
                    that.Model.setValue({id: "frelatecusid", value: ""});
                    that.renewalHeadquart();
                    break;
                case 'fbilltype':
                    that.queryPlanbrokerage();
                    break;
            }
        };

        //总部相关字段 当【登录账号所属经销商.经营类型=直营】默认可见，反之不显示
        _child.prototype.renewalHeadquart = function () {
            var that = this;
            var enable = false;
            var enablea = false;
            var frelatetype = that.Model.getSimpleValue({id: "frelatetype"});
            //直营且【来往单位类型=合作渠道】
            if (Consts.isdirectsale) {
                if (frelatetype === "ste_channel"){
                    enable = true;
                }
                enablea = true;
            } else {
                enable = false;
                enablea = true;
            }
            that.Model.setVisible({id: '.renewal-info-head', value: enable});
            that.Model.setVisible({id: '.renewal-info-head-a', value: enablea});
            that.Model.setVisible({id: 'fsharecostbillno', value: enable});
        }


        //查询销售合同的应付佣金
        _child.prototype.queryPlanbrokerage = function (e) {
            var that = this;
            var fbilltype = that.Model.getSimpleValue({id: "fbilltype"});
            var frelatetype = that.Model.getSimpleValue({id: "frelatetype"});
            //在单据类型“标准费用单”，往来单位类型为“合作渠道时”，带出合同应付佣金这个字段
            if (fbilltype == 'registfee_billtype_01' && frelatetype == 'ste_channel') {
                var fsourcetype = that.Model.getSimpleValue({id: "fsourcetype"});
                var fsourcenumber = that.Model.getSimpleValue({id: "fsourcenumber"});
                if (fsourcetype == "ydj_order" && fsourcenumber != "") {
                    that.Model.setVisible({id: '.planbrokerage', value: true});
                    //that.Model.invokeFormOperation({
                    //    id: 'LoadOrderPrice',
                    //    opcode: 'LoadOrderPrice',
                    //    selectedRows: [{PKValue: that.Model.pkid}],
                    //    param: {
                    //        fsourcetype: fsourcetype,
                    //        fsourcenumber: fsourcenumber
                    //    }
                    //});
                    //改为同步接口，因为业务反馈刷新后数据渲染不及时
                    var param = {
                        simpleData: {
                            fsourcetype: fsourcetype,
                            fsourcenumber: fsourcenumber
                        } 
                    };
                    yiAjax.p('/bill/ste_registfee?operationno=LoadOrderPrice', param, function (r) {
                        that.Model.unblockUI({ id: '#page#' });
                        var res = r.operationResult;
                        if (res.isSuccess) {
                            var srvData = res.srvData;
                            if (srvData) {
                                var dataChanged = that.Model.dataChanged;
                                that.Model.setValue({ id: "fplanbrokerage", value: JSON.stringify(srvData), tgChange: false });
                                if (!dataChanged) {//tgChange不生效
                                    that.Model.dataChanged = dataChanged;
                                }
                            }
                        }
                    }, null, null, null, { async: false }); 
                }
            } else {
                that.Model.setVisible({id: '.planbrokerage', value: false});
                var dataChanged = that.Model.dataChanged;
                that.Model.setValue({id: "fplanbrokerage", value: '0', tgChange: false});
                if (!dataChanged) {//tgChange不生效
                    that.Model.dataChanged = dataChanged;
                }
            }
        }

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                //付款，退款
                case 'payment':
                case 'refund':
                    e.result = true;
                    that.Model.invokeFormOperation({
                        id: 'LoadSettleInfo',
                        opcode: 'LoadSettleInfo',
                        selectedRows: [{PKValue: that.Model.pkid}],
                        param: {settleType: e.opcode}
                    });
                    break;
                //收支记录
                case 'incomedisburse':
                    e.result = true;
                    //that.Model.showForm({
                    //    formId: 'coo_incomedisburserptsal',
                    //    domainType: Consts.domainType.report,
                    //    param: { openStyle: Consts.openStyle.modal },
                    //    cp: {
                    //        sourceId: that.Model.pkid,
                    //        callback: function (result) {
                    //            if (!result || !result.newRow) { return; }
                    //            if (result.newRow.flag == 'Y') {
                    //                //操作成功刷新页面
                    //                that.Model.refresh();
                    //            }
                    //        }
                    //    }
                    //});
                    //改成直接打开收支记录列表
                    var filterString = "fsourceid='{0}'"
                        .format(that.Model.pkid, Consts.loginCompany.id);
                    that.Model.showForm({
                        formId: 'coo_incomedisburse',
                        domainType: Consts.domainType.list,
                        param: {
                            openStyle: Consts.openStyle.modal,
                            filterstring: filterString
                        }
                    });
                    break;
                case 'mergeregistfee':
                    e.result = true;
                    var relateType = that.Model.getSimpleValue({"id": "frelatetype"});
                    var relateCusId = that.Model.getSimpleValue({"id": "frelatecusid"});
                    if (relateType != "ste_channel") {
                        yiDialog.warn('操作失败,往来单位类型必须是合作渠道才能进行【合并付佣】操作!');
                        return;
                    }
                    if ($.trim(relateCusId).length <= 0) {
                        yiDialog.warn('操作失败,往来单位名称不能为空!');
                        return;
                    }
                    var param = JSON.parse(localStorage.getItem("storesysparam"));
                    var enableBrokerageRatio = param && param.hasOwnProperty('fenablebrokerageratio') ? param.fenablebrokerageratio : false;
                    if (false == enableBrokerageRatio) {
                        yiDialog.warn('操作失败,[合作渠道按比例计算佣金]参数尚未开启!');
                        return;
                    }
                    var filterString = '';
                    if (Consts.isdirectsale) {
                        // 从费用应付单创建时，详情页点击 <合并付佣>，可选销售合同新增过滤条件：整单关闭，且无下游费用应付单，或者是整单关闭但是费用应付单=已作废。
                        //是整单关闭但是费用应付单=已作废。
                        var tempStr1 = " select 1 from t_ste_registfee tsr1 with(nolock ) left join t_ste_registfeeentry tsre1 with(nolock ) on tsr1.fid = tsre1.fid where tsr1.fcancelstatus = '1' and ((tsre1.fsourceformid = 'ydj_order' and tsre1.fsourcebillno = t0.fbillno) or (tsr1.fsourcetype = 'ydj_order' and tsr1.fsourcenumber = t0.fbillno) ) ";
                        //整单关闭，且无下游费用应付单
                        var tempStr2 = " select 1 from t_ste_registfee tsr2 with(nolock ) left join t_ste_registfeeentry tsre2 with(nolock ) on tsr2.fid = tsre2.fid where tsr2.fcancelstatus = '0' and ((tsre2.fsourceformid = 'ydj_order' and tsre2.fsourcebillno = t0.fbillno) or (tsr2.fsourcetype = 'ydj_order' and tsr2.fsourcenumber = t0.fbillno) ) ";
                        //filterString = " fstatus='E' and fcancelstatus='0' and fchannel='" + relateCusId + "' and ( ( fclosestatus = '1' and not exists( "+ tempStr2 +" ) ) or ( fclosestatus = '1' and exists(  “+ tempStr1  + " ) ) )  ";
                        filterString = `fstatus='E' and fcancelstatus='0' and fchannel='${relateCusId}' and ((fclosestatus='1' and not exists(${tempStr2})) or (fclosestatus='1' and exists(${tempStr1})))`;
                    } else {
                        filterString = "fstatus='E' and fcancelstatus='0' and fchannel='" + relateCusId + "' and not exists(select 1 from t_ste_registfeeentry e where e.fsourceformid='ydj_order' and e.fsourcebillno=fbillno)";
                    }
                    that.Model.showSelectForm({
                        formId: 'ydj_order',
                        selectMul: true,
                        dynamicParam: {
                            filterString: filterString
                        }
                    });
                    break;
            }
        };

        //多选列表选择后的数据。
        _child.prototype.onAfterSelectFormData = function (e) {
            if (!e || !e.formId) return;
            var that = this;
            switch (e.formId) {
                case 'ydj_order':
                    e.result = true;
                    if (!e.data || e.data.length <= 0) return;
                    var selRows = [];
                    e.data.map(function (item) {
                        selRows.push({
                            PKValue: item.fbillhead_id
                        });
                    });
                    that.Model.invokeFormOperation({
                        id: 'mergeregistfee',
                        opcode: 'mergeregistfee',
                        selectedRows: selRows,
                        param: {
                            formId: 'ydj_order',
                            domainType: Consts.domainType.bill
                        }
                    });
                    break;
            }
        };

        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'initbill':
                    if (isSuccess && srvData) {
                        var taxrate = srvData.uiData.fcommontaxrate;
                        that.Model.setValue({"id": "ftaxrate_h", "value": taxrate});
                        var entryData = that.Model.getEntryData({"id": "fentry"});
                        if (entryData && entryData.length > 0) {
                            for (var i = 0; i < entryData.length; i++) {
                                var rowData = entryData[i];
                                that.Model.setValue({"id": "ftaxrate", "value": taxrate, "row": rowData.id});
                            }
                        }
                    }
                    break;
                case 'loadsettleinfo':
                    if (isSuccess && $.isPlainObject(srvData)) {
                        that.Model.showForm({
                            formId: 'coo_settledyn',
                            param: {openStyle: Consts.openStyle.modal},
                            cp: $.extend({}, srvData, {
                                bizFields: [
                                    {id: 'fsourcenumber', cation: '费用申请单号'},
                                    {id: 'fsettlemainname', cation: srvData.fsettlemaintypename},
                                    {id: 'fsettledamount', cation: '已结算金额'},
                                    {id: 'funsettleamount', cation: '待结算金额'},
                                    {id: 'funconfirmamount', cation: '待确认金额'}
                                ],
                                fimage: {id: '', name: ''},
                                callback: function (result) {
                                    if (result && result.isSuccess) {
                                        that.Model.refresh();
                                    }
                                }
                            })
                        });
                    }
                    break;
                case 'mergeregistfee':
                    if (isSuccess && srvData && srvData.length > 0) {
                        var entryDatas = that.Model.getEntryData({id: that.entryId});
                        for (var i = 0; i < srvData.length; i++) {
                            var data = srvData[i];
                            var sourceFormId = data.fsourceformid.id;
                            var sourceInterId = data.fsourceinterid;
                            var entryData = entryDatas.find(function (x) {
                                return x.fsourceformid.id == sourceFormId && x.fsourceinterid == sourceInterId
                            });
                            if (entryData == null) {
                                that.Model.addRow({id: that.entryId, data: data});
                            }
                        }
                        that.Model.refreshEntry({id: that.entryId});
                    }
                    break;
                case 'loadstoresysparam':
                    if (isSuccess && srvData) {
                        //设置本地缓存数据
                        var storesysparam = {};
                        for (var data in srvData) {
                            storesysparam[data] = srvData[data];
                        }
                        localStorage.setItem("storesysparam", JSON.stringify(storesysparam));
                    }
                    break;
                //case 'loadorderprice':
                //    if (isSuccess && srvData) {
                //        var dataChanged = that.Model.dataChanged;
                //        that.Model.setValue({id: "fplanbrokerage", value: JSON.stringify(srvData), tgChange: false});
                //        if (!dataChanged) {//tgChange不生效
                //            that.Model.dataChanged = dataChanged;
                //        }
                //    }
                //    break;
            }
        };

        _child.prototype.onEntryRowCreated = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case "fentry":
                    var taxrate = that.Model.getValue({"id": "ftaxrate_h"});
                    ;
                    that.Model.setValue({"id": "ftaxrate", "value": taxrate, "row": e.row});
                    break;
            }
        };

        _child.prototype.getTaxRate = function (id) {
            var that = this;
            if (id == null || id.length <= 0) {
                return;
            }
            var frelatetype = that.Model.getSimpleValue({id: "frelatetype"});
            if (frelatetype != "ydj_supplier") {
                return;
            }
            that.Model.invokeFormOperation({
                id: 'initbill',
                opcode: 'initbill',
                selectedRows: [{PKValue: id}],
                param: {
                    formId: "ydj_supplier",
                    domainType: Consts.domainType.bill
                }
            });
        };

        _child.prototype.deleteRow = function () {
            var that = this;
            var param = JSON.parse(localStorage.getItem("storesysparam"));
            var enableBrokerageRatio = param && param.hasOwnProperty('fenablebrokerageratio') ? param.fenablebrokerageratio : false;
            if (false == enableBrokerageRatio) {
                return;
            }
            var entryDatas = that.Model.getEntryData({id: that.entryId});
            if (entryDatas == null || entryDatas.length <= 0) {
                return;
            }
            var deleteIds = [];
            for (var i = 0; i < entryDatas.length; i++) {
                var data = entryDatas[i];
                var sourceFormId = data.fsourceformid.id;
                if (sourceFormId == "ydj_order") {
                    deleteIds.push(data.id);
                }
            }
            if (deleteIds == null || deleteIds.length <= 0) {
                return;
            }
            //如果删除的行数等于总行数需要先添加一行空行
            if (entryDatas.length == deleteIds.length) {
                that.Model.addRow({"id": that.entryId});
            }
            for (var i = 0; i < deleteIds.length; i++) {
                var row = deleteIds[i];
                that.Model.deleteRow({id: that.entryId, row: row});
            }
            that.Model.refreshEntry({id: that.entryId});
        }

        //初始化页面插件
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            var param = JSON.parse(localStorage.getItem("storesysparam"));
            if (!param) {
                //本地缓存不存在则请求
                that.Model.invokeFormOperation({
                    id: 'loadstoresysparam',
                    opcode: 'loadstoresysparam',
                    param: {
                        'formId': 'bas_storesysparam',
                        'domainType': 'parameter'
                    }
                });
            }

            //查询销售合同应付佣金
            that.queryPlanbrokerage();
        };

        //选单前事件
        _child.prototype.onBeforPull = function (e) {
            e.sourceFormId = 'pur_receiptnotice';
        };

        return _child;
    })(BillPlugIn);
    window.ste_registfee = window.ste_registfee || ste_registfee;
})();