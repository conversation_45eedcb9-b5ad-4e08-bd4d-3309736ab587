/// <reference path="/fw/js/platform/mvvm/baseplugin.js" />
//@ sourceURL=/fw/js/ydj/mt/mt_selectionrule.js
//选配规则
; (function () {
    var mt_selectionrule = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        }

        __extends(_child, _super);

        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode) {
                //删除规则明细
                case 'delselectionrule':
                    var rows = that.Model.getSelectRows({ id: "fentity" });
                    if (!rows || rows.length == 0) {
                        yiDialog.warn('请先选择需要删除的规则明细');
                    }
                    for (var i = 0; i < rows.length; i++) {
                        that.Model.deleteEntryData({ id: "fentity", row: rows[i].pkid });
                    }
                    e.result = true;
                    break;
                //添加规则明细
                case 'selectionruleform':
                    e.result = true;

                    that.editRule();
                    break;
            }
        };

        _child.prototype.editRule = function (rule, rowid) {
            var that = this;
            var selectionrangeid = that.Model.getSimpleValue({ id: "fselectionrangeid" });
            if (!selectionrangeid) {
                yiDialog.warn('请先选择选配范围。');
                return;
            }

            var param = {
                formId: "mt_selectionrule",
                selectionrangeid: selectionrangeid
            };
            if (rule) {
                param.rule = JSON.stringify(rule);
                param.rowid = rowid;
            }

            that.Model.invokeFormOperation({
                id: 'selectionruleform',
                opcode: 'selectionruleform',
                param: param
            });
        };

        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fselectionrangeid':
                    e.result.filterString = "fid not in (select fselectionrangeid from t_mt_selectionrule)";
                    break;
            }
        };

        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fselectionrangeid':
                    that.Model.deleteEntryData({ id: "fentity" });
                    break;
            }
        };

        _child.prototype.setRule = function (result) {
            var that = this;
            var rowid = result.rowid;
            var rule = result.rule;
            if (!rowid) {
                rowid = that.Model.addRow({ id: "fentity" });
            }
            var conditions = [];
            if (rule.condition && rule.condition.length > 0) {
                for (var i = 0; i < rule.condition.length; i++) {
                    var cdn = rule.condition[i];
                    if (cdn["dimension"]["id"]) {
                        conditions.push(cdn["dimension"]["fname"] + ":" + cdn["text"]);
                    }
                }
            }
            var cdntxt = conditions.length > 0 ? conditions.join("\r\n") : "";

            var contents = [];
            if (rule.content.length > 0) {
                for (var i = 0; i < rule.content.length; i++) {
                    var ctn = rule.content[i];
                    if (ctn["dimension"]["id"]) {
                        var content = ctn["dimension"]["fname"] + ":";
                        var dms = [];
                        if (ctn["text"]) {
                            dms.push("[值范围]" + ctn["text"]);
                        }
                        if (ctn["deftext"]) {
                            dms.push("[默认值]" + ctn["deftext"]);
                        }
                        if (ctn["lock"]) {
                            dms.push("[锁定]");
                        }
                        if (dms.length == 0) { //没有维度值，跳过
                            continue;
                        }
                        content += dms.join(";");
                        contents.push(content);
                    }
                }
            }
            var ctntxt = contents.length > 0 ? contents.join("\r\n") : "";

            that.Model.setValue({ id: "fcondition", row: rowid, value: cdntxt });
            that.Model.setValue({ id: "fcontent", row: rowid, value: ctntxt });
            that.Model.setValue({ id: "frule", row: rowid, value: JSON.stringify(rule) });
        };

        _child.prototype.onEntryRowDblClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fentity':
                    var ruletxt = that.Model.getSimpleValue({ id: "frule", row: e.row });
                    if (ruletxt) {
                        var rule = JSON.parse(ruletxt);
                        if (rule) {
                            that.editRule(rule, e.row);
                        }
                    }
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.mt_selectionrule = window.mt_selectionrule || mt_selectionrule;
})();