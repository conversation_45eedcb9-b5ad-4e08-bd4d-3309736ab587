
; (function () {
    var ydj_internaldisputeconfirm = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            debugger
            var that = this;
            if (!args.opcode) return;
            switch (args.opcode.toLowerCase()) {
                case 'cancel':
                    args.result = true;
                    that.Model.close();
                    break;
                case 'confirm':
                    args.result = true;
                    var internalDisputeRemark = that.Model.getValue({ id: 'finternaldisputeremark' });
                    var internalDisputeConfirmdate = that.Model.getValue({ id: 'finternaldisputeconfirmdate' });

                    if (!internalDisputeRemark) {
                        yiDialog.warn('对不起，内部争议确认失败,内部争议备注不能为空！');
                        return;
                    }

                    if (!internalDisputeConfirmdate) {
                        yiDialog.warn('对不起，内部争议确认失败,请指定内部确认日期！');
                        return;
                    }

                    var parentViewModel = Index.getPage(that.formContext.cp.parentPageId);

                    //从运维问题上操作
                    if (parentViewModel.formId == 'ydj_maintenance') {
                        parentViewModel.Model.invokeFormOperation({
                            id: 'internaldisputeconfirm',
                            opcode: 'internaldisputeconfirm',
                            param: {
                                'formId': 'ydj_maintenance',
                                'internalDisputeRemark': internalDisputeRemark,
                                'internalDisputeConfirmdate': internalDisputeConfirmdate
                            }
                        });

                        that.Model.close();
                    }
                    break;
            }
        };





        return _child;
    })(BasePlugIn);
    window.ydj_internaldisputeconfirm = window.ydj_internaldisputeconfirm || ydj_internaldisputeconfirm;
})();