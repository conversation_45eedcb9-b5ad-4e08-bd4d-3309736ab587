///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/rpt/rpt_purchaseprice.js
*/
; (function () {
    var rpt_purchaseprice = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）
        _child.prototype.entryId = 'freportlist';

        //初始化事件
        _child.prototype.onInitialized = function () {
            var that = this;

        }

        //创建明细表格
        _child.prototype.onCreateGrid = function (args) {
            if (!args.id) return;
            var that = this;
            switch (args.id.toLowerCase()) {
                case that.entryId:
                    args.result = { multiselect: false };
                    break;
            }
        };

        //页面元素点击事件
        _child.prototype.onElementClick = function (e) {
            var that = this;
            var opcode = e.id.toLowerCase();
            switch (e.id.toLowerCase()) {
                case 'search':
                    that.Model.refresh();
                    break;
            }
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
            }
        };

        /**
         * @description 在刷新报表前触发：可以在该事件中收集报表查询参数
         * @param {object} args
         */
        _child.prototype.onBeforeRefresh = function (args) {
            var that = this;

            var product = that.Model.getValue({ id: 'fproductnumber_h' });

            args.param = {
                fproductnumber: product.fnumber
            };
        };

        return _child;
    })(ReportPlugIn);
    window.rpt_purchaseprice = window.rpt_purchaseprice || rpt_purchaseprice;
})();