///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/rpt/rpt_centerstocksummary.js
*/
; (function () {
    var rpt_centerstocksummary = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复用

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************

        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************
        //表格行双击后事件
        _child.prototype.onEntryRowDblClick = function (e) {
            var that = this;
            //如果列表页面行双击，根据商品类别打开对应的商品品类库存明细分析表
            if (e.id === 'list' && e.listMode.toLowerCase() === 'default') {
                e.result = true;

                debugger
                that.Model.showListReport({ formId: 'rpt_provincestocksummary', openStyle: 'modal', param: { fcategoryid: e.data.fcategoryid, fsaleregionalid: e.data.fsaleregionalid, fserviceregionalid: e.data.fserviceregionalid } });
           
            }
        };

        return _child;
    })(ListReportPlugin);
    window.rpt_centerstocksummary = window.rpt_centerstocksummary || rpt_centerstocksummary;
})();