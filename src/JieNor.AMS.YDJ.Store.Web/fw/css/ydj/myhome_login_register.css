html { /* height: 100%;
    width: 100%;
    background: #fff; */
}

body {
    height: 100%;
    background: none;
    top: 0;
    position: absolute;
    overflow-y: inherit !important;
}
.showText {
    position: relative;
}

.closeX {
    position: absolute;
    top: 0;
    right: 50px;
    padding: 5px;
    cursor: pointer;
    line-height: 30px;
    font-weight: bolder;
}
.divShowText{
    color:#000000;
    text-align:left;
    font-size:18px;
    line-height: 40px;
    /*font-weight: 800;*/
    height: 40px;
    margin-left: 40px;
}

input:-webkit-autofill {
    -webkit-box-shadow: 0 0 0px 1000px #fff inset !important;
    -webkit-text-fill-color: rgba(0,0,0,1) !important;
}

.login-center-box {
    width: 100%;
    height: 100%;
    max-width: 1200px;
    padding: 0 0 30px 0;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

.login-box {
    position: relative;
}

.login header {
    height: 120px;
    position: fixed;
    top: 0;
    z-index: 100;
    background: #fff;
    width: 100%;
    border-bottom: 1px #dcdcdc solid;
}

.login header.header-border {
    border-bottom: 1px #CACCCD solid;
}

.login header .header_box {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.login header .header_box .droplist-btn {
    position: absolute;
    font-size: 20px;
    font-weight: bold;
    right: 20px;
    display: inline-block;
    display: none;
}

.login header .header_box .logo-image {
    height: 40px;
    margin-top: 17px;
    float: left;
}

.login header .header_box .logo-image img {
    height: 40px;
}

.login header .header_box .title {
    font-size: 20px;
    color: #666666;
    display: inline-block;
    height: 40px;
    line-height: 40px;
    padding-left: 20px;
    margin-left: 20px;
    border-left: 1px #CACCCD solid;
    margin-top: 17px;
}

.login header .header_box .right-mes {
    font-size: 16px;
    color: #666666;
    float: right;
    display: inline-block;
    height: 17px;
    line-height: 17px;
    margin-top: 28px;
}

.login header .header_box .right-mes a {
    color: #666666;
    padding-right: 17px;
    border-right: 1px #CACCCD solid;
    margin-right: 17px;
}

.login header .header_box .right-mes a:hover {
    color: #000;
    text-decoration: none;
}


.login-box .switch-box {
    width: 346px;
    padding-bottom: 10px;
    float: right;
    background: #fff;
    opacity: 0;
    margin-right: 140px;
    margin-top: 250px;
}

.login-box .switch-box .switch-lab {
    height: 42px;
    line-height: 42px;
    font-size: 14px;
}

.login-box .switch-box .switch-lab li {
    width: 50%;
    height: 42px;
    color: #666;
    background: #F3F3F3;
    box-sizing: border-box;
    cursor: pointer;
    line-height: 36px;
    text-align: center;
    float: left;
    border-top: 3px #F3F3F3 solid;
}

.login-box .switch-box .switch-lab li.active {
    background: #fff;
    border-top: 3px #00AAEF solid;
    cursor: default;
}

.login-box .login-form {
    padding: 33px;
}

.input-icon {
    margin-bottom: 10px;
}

.input-icon i {
    color: #019fe8;
    margin: 13px;
}

.default-input {
    width: 100%;
    height: 40px;
    border: 1px #E7E7E7 solid;
    border-radius: 4px;
    font-size: 14px;
    text-indent: 15px;
    outline: none;
}

.default-input:focus {
    border: 1px #019FE8 solid;
}

.default-input.icon-input {
    text-indent: 40px;
}

.login-box .login-form .auto-login {
    color: #666;
    display: inline-block;
}

.login-box .login-form .auto-login .checkbox-inline {
    font-size: 12px;
}

.login-box .login-form .auto-login .checker {
    margin-right: 5px !important;
}

div.checker span {
    background-image: url("/fw/images/all-icon.png");
    background-repeat: no-repeat;
    -webkit-font-smoothing: antialiased;
    background-position: -85px -75px;
}

div.checker span.checked {
    background-position: -125px -75px;
}

div.checker.focus span {
    background-position: -85px -75px;
}

div.checker.focus span.checked {
    background-position: -125px -75px;
}

.login-box .login-form .forget-pwd {
    font-size: 12px;
    color: #0086E4;
    float: right;
}

.login-box .login-form .pwd-eye {
    right: 0;
    cursor: pointer;
}

/* 隐藏Edge浏览器自带密码可见图标 */
.login-box input[type=password]::-ms-reveal {
    display: none;
}

/*微信登录*/
.login-options {
    display: none;
}

.social-icons li.sso_wechat {
    text-indent: initial;
}

.social-icons li.sso_wechat img {
    width: 28px;
    height: 28px;
    cursor: pointer;
}

.layui-layer .layui-layer-title {
    background-color: #009FE8 !important;
    color: white;
    height: 50px;
    line-height: 50px;
    font-size: 16px;
}

.blue-btn {
    width: 100%;
    height: 40px;
    line-height: 38px;
    border: 1px #019FE8 solid;
    border-radius: 4px !important;
    background: #019FE8;
    color: #fff;
    font-size: 16px;
    display: inline-block;
    outline: none;
    cursor: pointer;
    border: none;
    text-align: center;
    text-decoration: none;
    transition: .1s;
    -webkit-transition: .1s;
    -moz-transition: .1s;
    -o-transition: .1s;
    -ms-transition: .1s;
}

.blue-btn-lock {
    background: #b8c6d6;
    cursor: no-drop;
}

.blue-btn-lock:hover {
    background: #b8c6d6 !important;
}

.marT30 {
    margin-top: 30px;
}

.marT20 {
    margin-top: 20px;
}

.marT10 {
    margin-top: 10px;
}

.blue-btn:hover {
    background: #0095da;
    transition: .1s;
    -webkit-transition: .1s;
    -moz-transition: .1s;
    -o-transition: .1s;
    -ms-transition: .1s;
}

.border-btn {
    width: 100%;
    height: 42px;
    border: 1px #019FE8 solid !important;
    border-radius: 4px !important;
    background: #fff;
    color: #019FE8;
    font-size: 16px;
    margin-top: 30px;
    display: inline-block;
    outline: none;
    border: none;
    text-align: center;
    text-decoration: none;
    line-height: 37px;
    transition: .1s;
    -webkit-transition: .1s;
    -moz-transition: .1s;
    -o-transition: .1s;
    -ms-transition: .1s;
}

.border-btn:hover {
    background: #ebf9ff;
    transition: .1s;
    -webkit-transition: .1s;
    -moz-transition: .1s;
    -o-transition: .1s;
    -ms-transition: .1s;
}

.copyright {
    font-size: 14px;
    color: #fff;
    width: 100%;
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 0px 0px 1px #000;
    position: fixed;
    bottom: 0;
}

.select-enterprise .enterprise_p {
    line-height: 30px;
    margin-bottom: 10px;
    color: #888888;
}

.login-form .help-block {
    margin-bottom: 10px;
    color: #f00;
}


/* 忘记密码类似页面样式 */
.default-white-box {
    width: 100%;
    max-width: 1180px;
    margin: 110px auto 30px auto;
    border: 1px #E7E7E7 solid;
}

.default-white-box .title {
    height: 60px;
    background: #FAFAFA;
    border-bottom: 1px #E7E7E7 solid;
}

.default-white-box .title p {
    font-size: 24px;
    color: #333;
    text-indent: 25px;
    line-height: 60px;
    padding: 0;
}

.default-white-box .content {
    padding: 70px 70px 200px 70px;
}

.default-white-box .content .center-box {
    width: 100%;
    max-width: 420px;
    margin: 0 auto;
}

.default-white-box .content .center-box .form-box {
    margin-bottom: 15px;
}

.default-white-box .content .center-box .form-box label {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    text-align: right;
}

.default-white-box .content .center-box .form-box .authcode {
    font-size: 14px;
}

.default-white-box .content .center-box .form-box .grey {
    font-size: 14px;
    color: #999;
}

.default-white-box .content .center-box .form-box .to-login {
    font-size: 14px;
    color: #0086E4;
}

.alerts {
    display: inline-block;
    color: #f00;
}

.form-box .alerts {
    margin-top: 10px;
}

.register-box .alerts {
    margin-top: 10px;
}

.default-white-box .content .center-box .form-box .success {
    width: 42px;
    height: 42px;
    display: inline-block;
    background: url(../../../fw/images/oem/success-icon.png);
}

.default-white-box .content .center-box .form-box .success-mes {
    font-size: 24px;
    color: #333;
    line-height: 42px;
}

.to-login-span {
    color: #999;
    font-size: 18px;
}

.to-login-span b {
    font-weight: normal;
    color: #0086E4;
}



/* 企业注册页面样式 */
.enterprise-box {
    position: relative; /* background: url(../../../fw/images/oem/enterprise-bg.png) 50% 50% no-repeat; */
}

.enterprise-center-box {
    width: 100%;
    height: 100%;
    max-width: 1200px;
    padding-bottom: 60px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

.register-box {
    width: 430px;
    box-sizing: border-box;
    padding: 0 47px 60px 47px;
    background: #fff;
    float: right;
    opacity: 0;
    margin-right: 140px;
    margin-top: 200px;
    border-top: 3px #00AAEF solid;
    box-shadow: 0px 0px 6px #252525;
    -webkit-box-shadow: 0px 0px 6px #252525;
    -moz-box-shadow: 0px 0px 6px #252525;
    -o-box-shadow: 0px 0px 6px #252525;
    -ms-box-shadow: 0px 0px 6px #252525;
}

.register-box .register-title {
    font-size: 18px;
    line-height: 60px;
    text-align: center;
}

.register-box .process_ul ul {
    padding: 0 25px;
    display: flex;
}

.register-box .process_ul {
    position: relative;
}
/* 流程线段 */
.register-box .process_ul .process-tab-box {
    width: 70%;
    margin: 0 auto;
}

.register-box .process_ul .process-tab-box .process-tab {
    height: 2px;
    width: 50%;
    background: #B8C6D6;
    float: left;
}

.register-box .process_ul .process-tab-box .process-tab.active {
    background: #38B1EB;
}

.register-box .process_ul ul li .circle {
    width: 40px;
    height: 40px;
    background: #B8C6D6;
    border-radius: 50%;
    display: inline-block;
}

.register-box .process_ul ul li.active .circle {
    background: #38B1EB;
}

.register-box .process_ul ul li {
    list-style-type: none;
    display: inline-block;
    text-align: center;
}

.register-box .process_ul ul li .circle i {
    width: 18px;
    height: 18px;
    margin-top: 10px;
    display: inline-block;
}

.register-box .process_ul ul .process1 {
    position: absolute;
}

.register-box .process_ul ul .process2 {
    margin: 0 auto;
}

.register-box .process_ul ul .process3 {
    position: absolute;
    right: 25px;
}

.register-box .process_ul ul li p {
    line-height: 45px;
    color: #666;
}

.register-box .process_ul ul li.active p {
    color: #38B1EB;
}

.register-box .process_ul ul li .circle i.phone-icon {
    background: url(../../../fw/images/oem/icon-list.png) -5px -6px;
    margin-left: 5px;
}

.register-box .process_ul ul li .circle i.info-icon {
    background: url(../../../fw/images/oem/icon-list.png) -24px -6px;
    margin-left: 4px;
}

.register-box .process_ul ul li .circle i.success-icon {
    background: url(../../../fw/images/oem/icon-list.png) -44px -6px;
}

.register-box .help-mes {
    font-size: 12px;
    color: #666;
}

.agree {
    font-size: 12px;
}

.agree .checkbox-inline {
    font-size: 12px;
}

.blue-a {
    color: #019FE8;
}
/* 修改省市区控件部分样式 */
.input-icon .city-border-bottom {
    border: 1px solid #e7e7e7;
    border-radius: 4px !important;
    min-height: 40px;
}

.input-icon .city-border-bottom .col-xs-12 {
    border: none;
}

.input-icon .city-border-bottom .arrow {
    width: 20px;
    margin-top: 16px;
}

.input-icon .city-border-bottom .col-xs-12 {
    background: none;
}

.input-icon .city-border-bottom .city-picker-span > .placeholder {
    font-size: 14px;
    padding-left: 5px;
    color: #757575;
}

.input-icon .select2-container .select2-choice {
    border: 1px solid #e7e7e7;
    height: 40px;
}

.input-icon .select2-container .select2-choice > .select2-chosen {
    line-height: 38px;
    color: #757575;
    text-indent: 8px;
}

.input-icon .select2-container .select2-choice .select2-arrow {
    right: 13px;
    top: 3px;
}

.register-success .success-icon {
    width: 42px;
    height: 42px;
    display: inline-block;
    background: url(../../../fw/images/oem/success-icon.png);
    margin-left: 148px;
}

.register-success .success-mes {
    font-size: 24px;
    line-height: 60px;
    text-align: center;
}

.ydj-diagram {
    width: 530px;
    height: 350px;
    float: left;
    margin-top: 240px;
    margin-left: 20px;
    position: relative;
}

.ydj-diagram div {
    position: absolute;
}

.ydj-diagram .connection {
    width: 0px;
    height: 130px;
    top: 93px;
    overflow: hidden;
    background: url(../../../fw/images/oem/icon-list.png) -4px -30px;
}

.ydj-diagram .bottomside {
    width: 530px;
    height: 240px;
    bottom: -80px;
    opacity: 0;
    overflow: hidden;
    background: url(../../../fw/images/oem/icon-list.png) -2px -167px;
}

.ydj-diagram .subject-box {
    width: 220px;
    height: 270px;
    bottom: 94px;
    left: 152px;
    overflow: hidden;
}

.ydj-diagram .subject {
    width: 220px;
    height: 270px;
    bottom: -267px;
    background: url(../../../fw/images/oem/icon-list.png) -2px -410px;
}

.ydj-diagram .diagrammes {
    width: 120px;
    height: 100px;
    bottom: 250px;
    right: 130px;
    opacity: 0;
    background: url(../../../fw/images/oem/icon-list.png) -470px -470px;
}

.exp-result p {
    font-size: 14px;
    color: #666;
    line-height: 35px;
}

.registered-company {
    font-size: 14px;
}

.registered-company tt {
    color: #019fe8;
}

.registered-company ul li {
    display: block;
    text-align: left;
}

.registered-company ul li a {
    color: #019fe8;
}

.clause-content {
    background: #fff;
    height: 320px;
    padding: 12px;
    border: 1px #DCDCDC solid;
    overflow: auto;
}

.clause-content p {
    font-size: 16px;
    color: #333;
    line-height: 24px;
}

.clause-content p b {
    line-height: 30px;
}

/* 窗口宽度低样式兼容 */
@media screen and (max-width:1050px) {
    .ydj-diagram {
        display: none;
    }

    .register-box {
        float: none !important;
        margin: 200px auto 0 auto !important;
    }
}

@media screen and (max-width:800px) {
    html {
        background: #fff !important;
    }

    body {
        height: auto;
    }

    .login header .header_box .right-mes {
        display: none !important;
    }

    .login header .header_box .title {
        display: none;
    }

    .login header .header_box .logo-image {
        margin: 17px auto 0 auto;
        float: none;
        text-align: center;
    }

    .register-box {
        width: 100% !important;
        height: 100% !important;
        margin: 75px auto 0 auto !important;
        padding: 0 20px 30px 20px !important;
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        -o-box-shadow: none;
        -ms-box-shadow: none;
        box-shadow: none;
    }

    .input-icon .city-border-bottom .arrow {
        right: 3.5%;
    }

    .copyright {
        text-shadow: none;
        color: #999;
    }

    .enterprise-box {
        height: 100%;
    }

    .register-box .process_ul .process-tab-box {
        width: 82% !important;
    }

    .login-box .switch-box {
        margin-right: 0px !important;
        margin-top: 75px;
        width: 100%;
    }

    .default-white-box .content {
        padding: 30px 20px 100px 20px;
    }

    .default-white-box {
        border: none;
        margin: 75px auto 30px auto;
    }

    .default-white-box .title {
        height: 40px;
    }

    .default-white-box .title p {
        font-size: 18px;
        text-align: center;
        line-height: 40px;
        text-indent: 0;
    }

    .default-white-box .content .center-box .form-box label {
        display: none;
    }

    .default-white-box .content .center-box {
        max-width: none;
    }

    .default-white-box .content .center-box .form-box .col-xs-9 {
        width: 100%;
    }
}

@media screen and (max-width:450px) {
    .register-box .process_ul .process-tab-box {
        width: 70% !important;
    }
}
