 use JNSaaSYDJDb
 GO

 --商户升级为客户的脚本

 --如果当前客户表和商户表存在相同id，则提示升级者
if exists(select top(1) 1 from SER_YDJ_DEALER d where d.fid in (select c.fid from T_YDJ_CUSTOMER c where c.fid=d.fid))
begin
	print '当前客户表已存在和当前商户表相同id的记录'
end

--创建客户账户信息
insert into t_ydj_CustomerAccount (fentryid,FSeq,fpurpose,fisnegative,fispayment,fisbalance,fbalance_e,fissyninit,FTranId,fid,fiscash)
select lower(REPLACE(NEWID(),'-','')) as fentryid,convert(int,REPLACE(e.fid,'settleaccount_type_','')) as FSeq,e.fid as fpurpose,'0' as fisnegative,'0' as fispayment,'0' as fisbalance,0 as fbalance_e,'0' as fissyninit,'' as FTranId,d.fid as fid,'0' as fiscash 
from [SER_YDJ_DEALER] as d
cross apply v_bd_enum as e
where e.fcategory='结算单账户类型' and d.fid not in (select c.fid from T_YDJ_CUSTOMER c where c.fid=d.fid)
order by d.fid

--创建客户
insert into [T_YDJ_CUSTOMER]
           ([fid],[fnumber],[fname],[fdescription],[fcreatorid],[fcreatedate],[fmodifierid],[fmodifydate],[fispreset],[fstatus],[fapproveid],[fapprovedate],[fname_py],[fname_py2],[fmainorgid],[fmainorgid_txt],[fmainorgid_pid],[fforbidstatus],[fforbidid],[fforbiddate],[fbizruleid],[fflowinstanceid],[ftranid],[fsendstatus],[fdataorigin],[fpublishcid],[fpublishcid_txt],[fpublishcid_pid],[fsenddate],[fdownloaddate],[fchaindataid],[ffromchaindataid],[fchangestatus],[fphone],[fprovince],[fcity],[fregion],[faddress],[fcustype],[FFormId])
     select [fid],[fnumber],[fname],[fdescription],[fcreatorid],[fcreatedate],[fmodifierid],[fmodifydate],[fispreset],[fstatus],[fapproveid],[fapprovedate],[fname_py],[fname_py2],[fmainorgid],[fmainorgid_txt],[fmainorgid_pid],[fforbidstatus],[fforbidid],[fforbiddate],[fbizruleid],[fflowinstanceid],[ftranid],[fsendstatus],[fdataorigin],[fpublishcid],[fpublishcid_txt],[fpublishcid_pid],[fsenddate],[fdownloaddate],[fchaindataid],[ffromchaindataid],[fchangestatus],[fphone],[fprovince],[fcity],[fregion],[faddress],'customercate_03','ydj_customer'
     from [SER_YDJ_DEALER] d 
	 where d.fid not in (select c.fid from T_YDJ_CUSTOMER c where c.fid=d.fid)