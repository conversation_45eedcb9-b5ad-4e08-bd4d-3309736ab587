
-- 客户新增字段：当前会员积分和累计消费金额
if not exists(select * from syscolumns where id=object_id('t_ydj_customer') and name='favailableintegral')
	begin
		alter table t_ydj_customer add [favailableintegral] int NOT NULL default(0)
	end
else
	begin
		declare @constraintName varchar(200);
		select @constraintName = b.name from syscolumns a,sysobjects b where a.id=object_id('t_ydj_customer') and b.id=a.cdefault and a.name='favailableintegral' and b.name like 'DF%'; 
		exec('alter table t_ydj_customer drop constraint '+@constraintName)
		alter table t_ydj_customer drop column [favailableintegral];
		alter table t_ydj_customer add [favailableintegral] int NOT NULL default(0);
	end 
go

if not exists(select * from syscolumns where id=object_id('t_ydj_customer') and name='fsumamount')
	begin
		alter table t_ydj_customer add [fsumamount] decimal(23,10) NOT NULL default(0)
	end
else
	begin
		declare @constraintName varchar(200);
		select @constraintName = b.name from syscolumns a,sysobjects b where a.id=object_id('t_ydj_customer') and b.id=a.cdefault and a.name='fsumamount' and b.name like 'DF%'; 
		exec('alter table t_ydj_customer drop constraint '+@constraintName)
		alter table t_ydj_customer drop column [fsumamount];
		alter table t_ydj_customer add [fsumamount] decimal(23,10) NOT NULL default(0);
	end 
go

-- 收支记录新增字段：是否已用于计算积分
if not exists(select * from syscolumns where id=object_id('t_coo_incomedisburse') and name='fishistory')
	begin
		alter table t_coo_incomedisburse add [fishistory] NCHAR(1) NOT NULL default('0');
	end
else
	begin
		declare @constraintName varchar(200);
		select @constraintName = b.name from syscolumns a,sysobjects b where a.id=object_id('t_coo_incomedisburse') and b.id=a.cdefault and a.name='fishistory' and b.name like 'DF%'; 
		exec('alter table t_coo_incomedisburse drop constraint '+@constraintName)
		alter table t_coo_incomedisburse drop column [fishistory];
		alter table t_coo_incomedisburse add [fishistory] NCHAR(1) NOT NULL default('0');
	end 
go


begin
-- 更新《客户》档案中的【累计消费金额】/【当前会员积分】2021-08-26

-- 初始化，清空数值
update t_ydj_customer set favailableintegral=0,fsumamount=0;

-- 确认收款：【业务状态】=已确认 且 【账户方向】=减
update t_ydj_customer set fsumamount += t.fsumamount, favailableintegral += t.fsumamount
from (
	select fcustomerid,SUM(famount) fsumamount from t_coo_incomedisburse 
	where fbizstatus='bizstatus_02' and fdirection='direction_02'
	group by fcustomerid
) t where t_ydj_customer.fid=t.fcustomerid;


-- 确认扣款：【业务状态】=已确认 且 【账户方向】=增
update t_ydj_customer set fsumamount -= t.fsumamount , favailableintegral -= t.fsumamount
from (
	select fcustomerid,SUM(famount) fsumamount from t_coo_incomedisburse 
	where fbizstatus='bizstatus_02' and fdirection='direction_01'
	group by fcustomerid
) t where t_ydj_customer.fid=t.fcustomerid;

-- 更新为已用于历史积分统计
update t_coo_incomedisburse set fishistory = 1 
where fbizstatus='bizstatus_02' and fdirection in ('direction_01','direction_02')
end
go

begin
--更新《客户》档案中的【累计消费金额】2021-08-26

-- 初始化，清空数值
update t_ydj_customer set favailableintegral=0,fsumamount=0;

-- 确认收款：【业务状态】=已确认 且 【账户方向】=减
update t_ydj_customer set fsumamount += t.fsumamount 
from (
	select fcustomerid,SUM(famount) fsumamount from t_coo_incomedisburse 
	where fbizstatus='bizstatus_02' and fdirection='direction_02'
	group by fcustomerid
) t where t_ydj_customer.fid=t.fcustomerid;


-- 确认扣款：【业务状态】=已确认 且 【账户方向】=增
update t_ydj_customer set fsumamount -= t.fsumamount 
from (
	select fcustomerid,SUM(famount) fsumamount from t_coo_incomedisburse 
	where fbizstatus='bizstatus_02' and fdirection='direction_01'
	group by fcustomerid
) t where t_ydj_customer.fid=t.fcustomerid;

-- 更新为已用于历史积分统计
update t_coo_incomedisburse set fishistory = 1 
where fbizstatus='bizstatus_02' and fdirection in ('direction_01','direction_02')
end
go
