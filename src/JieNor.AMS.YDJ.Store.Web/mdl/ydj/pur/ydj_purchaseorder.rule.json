{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [
    //《采购订单》单据体-商品明细的【合作渠道】【渠道类型】默认默认可见, 可编辑 , 但如果是销售转采购的, 不允许编辑 (有来源单据时)--#35388任务
    {
      "id": "lock_fchanneltype",
      "expression": "field:fgoodschanneltype$|fsourceentryid_e!='' "
    },
    {
      "id": "unlock_fchanneltype",
      "expression": "field:$fgoodschanneltype|fsourceentryid_e=='' "
    },
    //当【送达方】不为空时, 不允许修改【供应商】, 否则允许编辑【Oscar5】---2022-04-29新需求：业务说明：经销商之间会有相互买卖行为，因此采购订单上选择送达方后允许修改供应商
    {
      "id": "lock_fsupplierid",
      "expression": "field:fsupplierid$|fdeliverid!='' and fsourcetype=='ydj_order' "
    },
    {
      "id": "unlock_fsupplierid",
      "expression": "field:$fsupplierid|fdeliverid=='' and fsourcetype!='ydj_order' "
    },
    //基本信息字段的锁定与解锁，相关信息字段的锁定与解锁
    {
      "id": "lock_basefield",
      "expression": "other:.base-mes,.y-pur-related|fpublishstatus=='publish_status_02' and fchargebackstatus=='0'"
    },
    //促销活动显示与隐藏
    {
      "id": "lock_addpromotion",
      "expression": "menu:btnaddpromotion|fstatus=='D' or fstatus=='E'"
    },
    {
      "id": "unlock_addpromotion",
      "expression": "menu:$btnaddpromotion|fstatus!='D' and fstatus!='E'"
    },
    //结算按钮的隐藏与显示
    {
      "id": "lock_payment",
      "expression": "menu:tbPayment|id=='' or fpaystatus=='paystatus_type_03' or (fbilltypeid=='po_type_02' and fbizstatus!='business_status_09') or fstatus!='E'"
    },
    {
      "id": "release_payment",
      "expression": "menu:$tbPayment|id!='' and fpaystatus!='paystatus_type_03' and (fbilltypeid=='po_type_01' or (fbilltypeid=='po_type_02' and fbizstatus=='business_status_09')) and fstatus=='E'"
    },
    //退款按钮的锁定与解锁
    {
      "id": "lock_refund",
      "expression": "menu:tbRefund|fstatus!='E'"
    },
    {
      "id": "release_refund",
      "expression": "menu:$tbRefund|fstatus=='E'"
    },
    //审核状态可以修改交货日期
    {
      "id": "unlock_fpickdate",
      "expression": "field:$fpickdate;menu:$tbStockIn,tbReturnGood|fstatus=='E'"
    },
    {
      "id": "unlock_stockinmenu",
      "expression": "menu:tbStockIn,tbReturnGood|fstatus!='E'"
    },
    //下推的采购订单，锁定商品、单位、辅助属性
    {
      "id": "lock_material",
      "expression": "field:fmaterialid,fattrinfo,fcustomdes_e$|fsourceentryid_e!='' and fstatus!='E'"
    },
    {
      "id": "lock_save",
      "expression": "menu:tbSave,tbSaveSubmit,tbSaveAudit,tbDelete$|fbizstatus!=' ' and fbizstatus!='' and fbizstatus!='business_status_01' and fbizstatus!='business_status_02' and fchargebackstatus!='1'"
    },
    {
      "id": "release_save",
      "expression": "menu:$tbSave,tbSaveSubmit,tbSaveAudit,tbDelete|fbizstatus==' ' or fbizstatus=='' or fbizstatus=='business_status_01' or fbizstatus=='business_status_02' or fchargebackstatus=='1'"
    },
    //经销商已发送的采购订单锁定部分字段
    {
      "id": "lock_businessfield",
      "expression": "field:fmtrlimage,fmulfile,factualspec,fbizunitid,fbizqty,fsalprice,fdealprice,fdemanddate,fsuppliernumber|fbizstatus!='business_status_01'"
    },
    // 原协同完成，锁定
    //{
    //  "id": "lock_ChangeMenu",
    //  "expression": "menu:tbChange$|fbizstatus!='business_status_09'"
    //},    

    // //变更前
    // {
    //   "id": "before_change_lock_btns",
    //   "expression": "menu:tbUnChange,tbSubmitChange$|fchangestatus!='1'"
    // },
    // //变更后
    // {
    //   "id": "changing_lock_btns",
    //   "expression": "menu:$tbUnChange,tbSubmitChange|(fstatus=='B' and fchangestatus=='1') or fpublishstatus=='publish_status_02'"
    // },
    // 允许在协同情况下进行变更（当【变更状态】=”正常”或”变更完成” 且 【协同状态】=”协同完成” 且 订单未作废 且 没有下游单据(不包含收支记录)  且 对应协同的K3《订购意向书》已审核 时, 允许点<变更>）
    {
      "id": "unlock_change_in_sync",
      "expression": "menu:$tbChange|(fchangestatus=='0' or fchangestatus=='2') and fbizstatus=='business_status_09' and fcancelstatus==false"
    },
    // 允许在协同情况下, 变更中时取消变更 （当【变更状态】=”变更中” 且 【协同状态】=”协同完成”时, 允许点<取消变更>）
    {
      "id": "unlock_unchange_in_sync",
      "expression": "menu:$tbUnChange|fchangestatus=='1' and fbizstatus=='business_status_09'"
    },
    // 允许在协同情况下, 提交变更 (增加当【变更状态】=”变更中” 且 【协同状态】=”协同完成” 且 对应协同的K3《订购意向书》已审核 时, 允许点<提交变更>)
    {
      "id": "unlock_submitchange_in_sync",
      "expression": "menu:$tbSubmitChange|fchangestatus=='1' and fbizstatus=='business_status_09'"
    },
    // 在协同变更的情况下, 不允许用户自行点击<审核>并执行变更 (当【变更状态】=”变更已提交” 且 【协同状态】=”协同完成”时, 锁定不允许点击<审核>)
    {
      "id": "lock_auditflow_in_sync",
      "expression": "menu:tbAudit$|fchangestatus=='3' and fbizstatus=='business_status_09'"
    },
    // 如果订单的【变更状态】=“变更中”或“变更已提交”时：则 <作废><反作废><提交总部> 按钮需灰色，禁止点击
    {
      "id": "lock_menu_bychangestatus",
      "expression": "menu:tbCancel,tbUncancel$|fchangestatus=='1' or fchangestatus=='3'"
    },
    //变更中且总部合同状态不为空 则锁定 提交变更 按钮，去除后台校验。避免中台反写采购变更单时触发“当前订单为向总部采购的订单, 变更保存后请点击提交至总部”校验
    {
      "id": "lock_menu_submitchange",
      "expression": "menu:tbSubmitChange$|fchangestatus=='1' and fhqderstatus!=''"
    },

    //此规则表示：该商品对应的商品商品属性“允许定制”=true时放开，为false时，锁定
    {
      "id": "lock_fcustomdesc",
      "expression": "field:fcustomdes_e$|fcustom!=true"
    },
    {
      "id": "unlock_fcustomdesc",
      "expression": "field:$fcustomdes_e|fcustom==true"
    },
    ////此规则表示：该商品对应的商品商品属性“允许选配”=true时，辅助属性放开，为false时，锁定
    //{
    //  "id": "lock_fsel",
    //  "expression": "field:fattrinfo$|fispresetprop!=true"
    //},
    //{
    //  "id": "unlock_fsel",
    //  "expression": "field:$fattrinfo|fispresetprop==true"
    //},
    //下推时零售单价>0时自动锁定
    {
      "id": "lock_saleprice",
      "expression": "field:fsalprice$|fsourceentryid_e!='' and fsalprice!=0 and fstatus!='E'"
    },

    //订单增补按钮的锁定与解锁
    {
      "id": "lock_tbordersup",
      "expression": "menu:tbordersup$|fstatus!='E' or ftype!='order_type_01'"
    },
    {
      "id": "unlock_tbordersup",
      "expression": "menu:$tbordersup|fstatus=='E' and ftype=='order_type_01'"
    },
    //业务类型字段的锁定与解锁
    {
      "id": "lock_ftype",
      "expression": "field:ftype$|fsourcetype=='ydj_purchaseorder' and ftype=='order_type_02'"
    },
    {
      "id": "unlock_ftype",
      "expression": "field:$ftype|fsourcetype!='ydj_purchaseorder' or ftype!='order_type_02'"
    },
    ////如果协同状态=已协同，则锁定变更、提交变更、变更记录
    //{
    //  "id": "lock_ChangeMenu",
    //  "expression": "menu:tbChange,tbSubmitChange,tbQueryChange$|fsupplierstatus=='已协同'"
    //},
    //{
    //  "id": "lock_tbSubmitAgain",
    //  "expression": "menu:tbSubmitAgain$|fchargebackstatus=='0' and fstatus!='E'"
    //},
    //{
    //  "id": "unlock_tbSubmitAgain",
    //  "expression": "menu:$tbSubmitAgain|fchargebackstatus=='1' and fstatus=='E'"
    //},
    {
      "id": "lock_fownerid",
      "expression": "field:fownerid$|fownertype=='' or fstatus=='D' or fstatus=='E'"
    },
    {
      "id": "unlock_fownerid",
      "expression": "field:$fownerid|fownertype!='' and fstatus!='D' and fstatus!='E'"
    },
    {
      "id": "fstatus_",
      "expression": "field:$*;menu:*$tbNew,tbSave,tbSaveSubmit,tbSaveAudit,tbPull,tbQueryInventory,btnUnStandardCustom,btnStandardCustom,btnSuitCustom,tbUnstdTypeAudit,btnQYG,btnZCG,btnCXDB,btnbindcombnumber,btnunbindcombnumber,btnresetsort,btnShowSelection,btnUnbindContract,btnquerybarcode,btnaddpromotion,tbQueryFirstInventory|fstatus==''"
    },
    //保存并提交锁定锁定&解锁
    {
      "id": "lock_tbSaveSubmit",
      "expression": "menu:tbSaveSubmit$|fstatus=='D' or fstatus=='E'"
    },
    {
      "id": "unlock_tbSaveSubmit",
      "expression": "menu:$tbSaveSubmit|fstatus!='D' and fstatus!='E'"
    },
    //如果 【总部变更状态】="提交至总部"时, 不允许点击<保存>按钮, 锁定该按钮
    //{
    //  "id": "lock_tbSave",
    //  "expression": "menu:tbSave,tbSaveSubmit,tbSaveAudit$|fhqderchgstatus=='01' and fbilltypeid!='ydj_purchaseorder_zb'"
    //},
    //{
    //  "id": "unlock_tbSave",
    //  "expression": "menu:$tbSave|fhqderchgstatus != '01'"
    //},
    //条码按钮锁定
    {
      "id": "lock_tbBarcode",
      "expression": "menu:tbBarcode,tbAddCode$|fstatus!='E'"
    },
    //打码按钮的锁定与解锁(只有在【数据状态】=”已审核”)
    {
      "id": "unlock_AddCode",
      "expression": "menu:$tbAddCode|fstatus=='E'"
    },
    //生成收货任务按钮的锁定与解锁(只有在【数据状态】=”已审核”时才能够允许点击)
    {
      "id": "lock_ReceiptTask",
      "expression": "menu:tbReceiptTask$|fstatus!='E'"
    },
    {
      "id": "unlock_ReceiptTask",
      "expression": "menu:$tbReceiptTask|fstatus=='E'"
    },
    //保存并审核锁定锁定&解锁
    {
      "id": "lock_tbSaveAudit",
      "expression": "menu:tbSaveAudit$|fstatus=='E'"
    },
    {
      "id": "unlock_tbSaveAudit",
      "expression": "menu:$tbSaveAudit|fstatus!='E'"
    },
    {
      "id": "lock_btnShowSelection",
      "expression": "menu:btnShowSelection$|fstatus=='E'"
    },
    //{
    //  "id": "unlock_btnShowSelection",
    //  "expression": "menu:$btnShowSelection|fstatus!='E'"
    //}
    //开票金额=成交金额，则锁定开票操作
    {
      "id": "lock_tbOpenInvoice",
      "expression": "menu:tbOpenInvoice$|ffbillamount==fpinvoicemount"
    },
    {
      "id": "unlock_tbOpenInvoice",
      "expression": "menu:$tbOpenInvoice|ffbillamount!=fpinvoicemount"
    },
    //绑定沙发组合号锁定
    {
      "id": "lock_btnbindcombnumber",
      "expression": "menu:btnCXDB,btnZCG,btnQYG,btnUnStandardCustom,btnStandardCustom,btnSuitCustom,btnbindcombnumber,btnunbindcombnumber,btnUnbindContract$|fstatus=='D' or fstatus=='E' or fcancelstatus==true"
    },
    {
      "id": "unlock_btnbindcombnumber",
      "expression": "menu:$btnCXDB,btnZCG,btnQYG,btnUnStandardCustom,btnStandardCustom,btnSuitCustom,btnbindcombnumber,btnunbindcombnumber,btnUnbindContract|fstatus!='D' and fstatus!='E' and fcancelstatus==false"
    },
    //非标审批锁定
    {
      "id": "lock_tbUnstdTypeAudit",
      "expression": "menu:tbUnstdTypeAudit$|fstatus=='D' or fstatus=='E' or fcancelstatus==true"
    },
    {
      "id": "unlock_tbUnstdTypeAudit",
      "expression": "menu:$tbUnstdTypeAudit|fstatus!='D' and fstatus!='E' and fcancelstatus==false"
    },
    //单据类型==期初采购订单时，单据类型框锁定
    {
      "id": "lock_fbilltypeid",
      "expression": "field:fbilltypeid$|fsourcetype=='QCXSHT_SYS_01'"
    },
    {
      "id": "unlock_fbilltypeid",
      "expression": "field:$fbilltypeid|fsourcetype!='QCXSHT_SYS_01'"
    },

    ////总部手工单 只允许编辑采购员，改由前端js控制
    //{
    //  "id": "lock_fbilltype_zb",
    //  "expression": "field:*$fpostaffid,fpodeptid|fbilltypeid=='ydj_purchaseorder_zb'"
    //},
    //总部手工单审核状态下
    {
      "id": "lock_fbilltype_zb_menu",
      "expression": "field:fdescription;menu:*$tbSave,tbShowRecord,tbSaveSubmit,tbSaveAudit,tbAudit,tbSubmit,tbPayment,tbRefund,tbIncomeDisburse,tbPrintTmpl,tbPrint,tbExportExcelByPrintTmpl,tbOperateLogs,tbSharelayout,tbShareBill,tbResetlayout,tbLinkFormSearch,tbListAttachment,tbStockIn,tbReturnGood,tbBarcode,tbAddCode,tbReceiptTask,btnquerybarcode|fbilltypeid=='ydj_purchaseorder_zb' and fstatus=='E'"
    },
    //总部手工单非审核状态下
    {
      "id": "lock_fbilltype_zb_menu2",
      "expression": "field:fdescription;menu:*$tbSave,tbShowRecord,tbSaveSubmit,tbSaveAudit,tbAudit,tbSubmit,tbIncomeDisburse,tbPrintTmpl,tbPrint,tbExportExcelByPrintTmpl,tbOperateLogs,tbSharelayout,tbShareBill,tbResetlayout,tbLinkFormSearch,tbListAttachment|fbilltypeid=='ydj_purchaseorder_zb' and fstatus!='E'"
    },
    {
      "id": "lock_swjv6info",
      "expression": "field:fsendtarget,fthirdsource,fthirdbillno,fconsumersaddress$|fthirdsource=='三维家'"
    },
    //变更时锁定提交一级
    {
      "id": "lock_tbSubmitAgent",
      "expression": "menu:tbSubmitAgent$|fchangestatus=='1'"
    },
    //锁定业绩品牌
    {
      "id": "lock_fresultbrandid",
      "expression": "field:fresultbrandid$|funstdtypestatus=='02' or funstdtypestatus=='03' or funstdtypestatus=='05' or funstdtypestatus=='06'"
    },
    //解锁业绩品牌
    {
      "id": "unlock_fresultbrandid",
      "expression": "field:$fresultbrandid|funstdtypestatus=='' or funstdtypestatus=='01' or funstdtypestatus=='04'"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

    //暂无操作按钮的隐藏与显示
    // {
    //   "id": "hide_noop",
    //   "expression": "other:.y-pur-noop|fbizstatus=='business_status_01' or fbizstatus=='business_status_02' or fbizstatus=='business_status_05' or fbizstatus=='business_status_09'"
    // },
    // {
    //   "id": "show_noop",
    //   "expression": "other:$.y-pur-noop|fbizstatus!='business_status_01' and fbizstatus!='business_status_02' and fbizstatus!='business_status_05' and fbizstatus!='business_status_09'"
    // },
    //发送按钮的隐藏与显示
    {
      "id": "hide_submitsynergy",
      "expression": "other:[menu=submitsynergy]|fbizstatus!='business_status_01' or fstatus!='E'"
    },
    {
      "id": "show_submitsynergy",
      "expression": "other:$[menu=submitsynergy]|fbizstatus=='business_status_01' and fstatus=='E'"
    },

    //撤销发送按钮的隐藏与显示
    {
      "id": "hide_repealsynergy",
      "expression": "other:[menu=repealsynergy]|fbizstatus!='business_status_02' or fstatus!='E'"
    },
    {
      "id": "show_repealsynergy",
      "expression": "other:$[menu=repealsynergy]|fbizstatus=='business_status_02' and fstatus=='E'"
    },

    //确认按钮的隐藏与显示
    {
      "id": "hide_pricefirm",
      "expression": "other:[menu=pricefirm]|fbizstatus!='business_status_05' or fstatus!='E' or fchargebackstatus=='1'"
    },
    {
      "id": "show_pricefirm",
      "expression": "other:$[menu=pricefirm]|fbizstatus=='business_status_05' and fstatus=='E' and fchargebackstatus!='1'"
    },

    //取消确认按钮的隐藏与显示
    {
      "id": "hide_cancelconfirm",
      "expression": "other:[menu=cancelconfirm]|fbizstatus!='business_status_09' or fstatus!='E' or fchargebackstatus=='1'"
    },
    {
      "id": "show_cancelconfirm",
      "expression": "other:$[menu=cancelconfirm]|fbizstatus=='business_status_09' and fstatus=='E' and fchargebackstatus!='1'"
    },

    //新增状态下所有操作隐藏
    {
      "id": "hide_allop",
      "expression": "other:[menu=submitsynergy],[menu=repealsynergy],[menu=pricefirm],[menu=cancelconfirm],[menu=payment]$|id==''"
    },

    ////重新发送按钮的隐藏与显示
    //{
    //  "id": "hide_tbSubmitAgain",
    //  "expression": "other:[menu=submitagain],.fchargebackreason|fchargebackstatus!='1' or fstatus!='E'"
    //},
    //{
    //  "id": "show_tbSubmitAgain",
    //  "expression": "other:$[menu=submitagain],.fchargebackreason|fchargebackstatus=='1' and fstatus=='E'"
    //}
    {
      "id": "hide_fchargebackreason",
      "expression": "other:.fchargebackreason|fchargebackreason==''"
    },
    {
      "id": "show_fchargebackreason",
      "expression": "other:$.fchargebackreason|fchargebackreason!=''"
    },
    // 采购订单已作废时, 则隐藏该按钮
    {
      "id": "hide_tbSubmitHQ",
      "expression": "other:[menu=submithq]$|fcancelstatus==true"
    },
    // 采购订单已作废时, 则隐藏该按钮
    {
      "id": "hide_tbSubmitHQ",
      "expression": "other:$[menu=submithq]|fcancelstatus==false"
    },
    // 三维家控制  v6-swj-info
    //{
    //  "id": "show_tbSWJField",
    //  "expression": "other:$[menu=submithq]|fthirdsource=='三维家'"
    //},
    //// 采购订单已作废时, 则隐藏该按钮
    //{
    //  "id": "hide_tbSWJField",
    //  "expression": "field:fsendtarget,fthirdbillno,fconsumersaddress$|fthirdsource!='三维家'"
    //},
    {
      "id": "hide_swjv6info",
      "expression": "other:.v6-swj-info$|fthirdsource!='三维家'"
    },
    {
      "id": "show_swjv6info",
      "expression": "other:$.v6-swj-info|fthirdsource=='三维家'"
    },
    {
      "id": "hide_type",
      "expression": "other:.ftype$.v6-swj-info-indenttype|fthirdsource!=''"
    },
    {
      "id": "show_type",
      "expression": "other:.v6-swj-info-indenttype$.ftype|fthirdsource==''"
    }
  ],

  //定义表单计算规则
  "calcRules": [
    { "expression": "fsupplieraddr=fsupplierid__faddress" },
    //客户基础资料值变化时，携带客户属性字段到页面指定的字段上面
    { "expression": "fprovince=fcustomerid__fprovince|fcustomerid=='' or 1==1" },
    { "expression": "fcity=fcustomerid__fcity|fcustomerid=='' or 1==1" },
    { "expression": "fregion=fcustomerid__fregion|fcustomerid=='' or 1==1" },
    { "expression": "faddress=fcustomerid__faddress|fcustomerid=='' or 1==1" },
    { "expression": "fphone=fcustomerid__fphone|fcustomerid=='' or 1==1" },
    { "expression": "fsource=fcustomerid__fsource|fcustomerid=='' or 1==1" },
    //采购订单  待付金额=订单金额-已付金额
    { "expression": "fpayamount=ffbillamount-fsettleamount" },
    //选择商品，携带单位，品牌，系列
    { "expression": "funitid=fmaterialid__funitid|fmaterialid=='' or 1==1" },
    { "expression": "fbizunitid=fmaterialid__fpurunitid|fmaterialid=='' or 1==1" },
    //选择商品，携带规格型号
    { "expression": "fmtrlmodel=fmaterialid__fspecifica|fmaterialid=='' or 1==1" },

    //带出商品是否非标
    { "expression": "funstdtype=fmaterialid__funstdtype|fmaterialid!='' or 1==1" },

    { "expression": "fbizqty=1|fmaterialid!='' and fbizqty<=0" },
    { "expression": "fdistrate=10|fmaterialid!='' and fdistrate<=0" },
    { "expression": "famount=fprice*fbizqty" },
    { "expression": "fdealamount_e=fdealprice*fbizqty" },
    { "expression": "fdealamount=sum(famount)" },
    { "expression": "fdistamount=sum(fdistamount_e)" },
    { "expression": "fdealprice=fprice*fdistrate/10" },
    { "expression": "fdistamount_e=famount-fdealamount_e" },
    { "expression": "fdistrate=fdealprice*10/fprice|fprice!=0 and fprice!=''" },
    { "expression": "fdealprice=fdealamount_e/fbizqty|fbizqty!=0" },
    //选择商品时，携带出默认辅助属性
    { "expression": "fattrinfo=getAuxPropValue(fmaterialid)" },
    { "expression": "fresultbrandid=fmaterialid__fseriesid|fmaterialid!='' and fpodeptid=='' " },
    //{ "expression": "fpodeptid=fpostaffid__fdeptid" },
    { "expression": "fmtrlimage=getImages(fmaterialid,fattrinfo,fcustomdes_e)" },
    //选择采购员信息，自动带出门店
    { "expression": "fpodeptid=fpostaffid__fdeptid|fstaffid=='' or 1==1" },
    //选择采购员信息，自动带出门店
    { "expression": "fgoodschanneltype=fchannel__ftype" },
    { "expression": "fstoreid=fpodeptid__fstore" }

  ]
}