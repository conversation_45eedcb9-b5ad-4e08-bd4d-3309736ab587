{
  "Id": "ydj_order2ste_registfee",
  "Number": "ydj_order2ste_registfee",
  "Name": "销售合同下推费用申请",
  "SourceFormId": "ydj_order",
  "TargetFormId": "ste_registfee",
  "ConvertWay": 0,
  "ActiveEntityKey": "fbillhead",
  //"FilterString": "fstatus='E'",
  //"Message": "销售合同必须是已审核状态！",
  "FieldMappings": [
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_order'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "frelatetype",
      "Name": "单位类型",
      "MapType": 1,
      "SrcFieldId": "'ste_channel'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "frelatecusid",
      "Name": "单位名称",
      "MapType": 0,
      "SrcFieldId": "fchannel",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftrainingdept",
      "Name": "承担部门",
      "MapType": 0,
      "SrcFieldId": "fdeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "frelatemanid",
      "Name": "关联人员",
      "MapType": 0,
      "SrcFieldId": "fstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fregistdate",
      "Name": "发生日期",
      "MapType": 0,
      "SrcFieldId": "forderdate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fplanbrokerage",
      "Name": "应付佣金",
      "MapType": 0,
      "SrcFieldId": "fplanbrokerage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }
  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [

  ]
}