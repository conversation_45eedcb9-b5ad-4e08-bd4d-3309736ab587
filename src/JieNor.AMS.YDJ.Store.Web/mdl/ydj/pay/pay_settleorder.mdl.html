<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title></title>
	<meta charset="utf-8" />
</head>
<body id="pay_settleorder" el="1" basemodel="bill_basetmpl" cn="支付结算单" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_pay_settleorder" pn="fbillhead" cn="支付结算单">
        <!--基本信息-->
        <input group="基本信息" type="datetime" el="113" ek="fbillhead" id="fpaytime" fn="fpaytime" pn="fpaytime" cn="支付时间" lock="-1" visible="-1" />
        <select group="基本信息" el="122" ek="fbillhead" id="fpaystatus" fn="fpaystatus" pn="fpaystatus" cn="支付状态" cg="支付状态" refid="bd_enum" dfld="fenumitem" defval="'fpaystatus_01'" lock="-1" visible="-1"></select>
        <input group="基本信息" type="text" el="100" ek="fbillhead" id="fproductid" fn="fproductid" pn="fproductid" cn="产品id" lock="-1" visible="62" />

        <!--系统内置逻辑依赖字段-->
        <input type="checkbox" id="fissponsor" el="116" ek="FBillHead" fn="fissponsor" visible="0" cn="发起方" />

        <!--用途信息-->
        <select group="用途信息" el="122" ek="fbillhead" id="fpurpose" fn="fpurpose" pn="fpurpose" cn="用途" cg="用途" refid="bd_enum" dfld="fenumitem" lock="-1" visible="-1"></select>
        <select group="用途信息" el="122" ek="fbillhead" id="fdirection" fn="fdirection" pn="fdirection" cn="金额方向" cg="金额方向" refid="bd_enum" dfld="fenumitem" lock="-1" visible="-1"></select>
        <input group="用途信息" el="100" type="text" ek="fbillhead" id="foldtranid" fn="foldtranid" pn="foldtranid" cn="原流水号" lock="-1" visible="-1" />
        <input group="用途信息" el="116" type="checkbox" ek="fbillhead" id="fiscash" fn="fiscash" pn="fiscash" cn="现金业务" lock="-1" visible="-1" />
        <input group="用途信息" el="100" type="text" ek="fbillhead" id="fbiztype" fn="fbiztype" pn="fbiztype" cn="业务类型" lock="-1" visible="0" />
        <input group="用途信息" el="100" type="text" ek="fbillhead" id="fbiznumber" fn="fbiznumber" pn="fbiznumber" cn="业务单号" lock="-1" visible="-1" />
        <input group="用途信息" el="100" type="text" ek="fbillhead" id="fplatformsumm" fn="fplatformsumm" pn="fplatformsumm" cn="平台摘要" lock="-1" visible="-1" />
        <input group="用途信息" el="116" type="checkbox" ek="fbillhead" id="fisshareprofit" fn="fisshareprofit" pn="fisshareprofit" cn="是否分润" lock="-1" visible="-1" />
        <select group="用途信息" el="122" ek="fbillhead" id="fbizpurpose" fn="fbizpurpose" pn="fbizpurpose" cn="业务用途" cg="业务用途" refid="bd_enum" dfld="fenumitem" lock="-1" visible="-1"></select>

        <!--金额信息-->
        <select group="金额信息" el="122" ek="fbillhead" id="fcurrency" fn="fcurrency" pn="fcurrency" cn="币别" cg="币别" refid="bd_enum" dfld="fenumitem" lock="-1" visible="-1"></select>
        <input group="金额信息" el="105" type="text" ek="fbillhead" id="fmoney" fn="fmoney" pn="fmoney" cn="金额" lock="-1" visible="-1" />

        <!--交易主体信息-->
        <input group="交易主体信息" el="100" type="text" ek="fbillhead" id="fmainname" fn="fmainname" pn="fmainname" cn="主体名称" lock="-1" visible="-1" />
        <input group="交易主体信息" el="100" type="text" ek="fbillhead" id="fmainid" fn="fmainid" pn="fmainid" cn="交易主体id" lock="-1" visible="-1" />
        <input group="交易主体信息" type="text" el="100" ek="fbillhead" id="fmaincnlid" fn="fmaincnlid" pn="fmaincnlid" cn="支付渠道" lock="-1" visible="0" />
        <input group="交易主体信息" el="122" type="text" ek="fbillhead" id="fmaincnlmode" fn="fmaincnlmode" pn="fmaincnlmode" cn="渠道方式" cg="渠道方式" refid="bd_enum" dfld="fenumitem" lock="-1" visible="-1" />
        <input group="交易主体信息" el="100" type="text" ek="fbillhead" id="fmainaccountid" fn="fmainaccountid" pn="fmainaccountid" cn="账户" lock="-1" visible="-1" />

        <!--交易对手信息-->
        <input group="交易对手信息" el="100" type="text" ek="fbillhead" id="foppname" fn="foppname" pn="foppname" cn="对手名称" lock="-1" visible="-1" />
        <input group="交易对手信息" el="100" type="text" ek="fbillhead" id="foppid" fn="foppid" pn="foppid" cn="交易对手id" lock="-1" visible="-1" />
        <input group="交易主体信息" type="text" el="100" ek="fbillhead" id="foppcnlid" fn="foppcnlid" pn="foppcnlid" cn="支付渠道" lock="-1" visible="0" />
        <input group="交易对手信息" el="122" type="text" ek="fbillhead" id="foppcnlmode" fn="foppcnlmode" pn="foppcnlmode" cn="渠道方式" cg="渠道方式" refid="bd_enum" dfld="fenumitem" lock="-1" visible="-1" />
        <input group="交易对手信息" el="100" type="text" ek="fbillhead" id="foppaccountid" fn="foppaccountid" pn="foppaccountid" cn="账户" lock="-1" visible="-1" />

        <!-- 线下回款凭据-->
        <input group="线下汇款凭据" el="100" type="text" ek="fbillhead" id="fremitbank" fn="fremitbank" pn="fremitbank" cn="汇款银行名称" lock="-1" visible="-1" />
        <input group="线下汇款凭据" el="100" type="text" ek="fbillhead" id="fbankaccount" fn="fbankaccount" pn="fbankaccount" cn="汇款银行账号" lock="-1" visible="-1" />
        <input group="线下汇款凭据" el="100" type="text" ek="fbillhead" id="fremitusername" fn="fremitusername" pn="fremitusername" cn="汇款户名" lock="-1" visible="-1" />
        <input group="线下汇款凭据" el="113" type="datetime" ek="fbillhead" id="fremittime" fn="fremittime" pn="fremittime" cn="汇款时间" lock="-1" visible="-1" />
        <input group="线下汇款凭据" el="100" type="text" ek="fbillhead" id="fcontactphone" fn="fcontactphone" pn="fcontactphone" cn="联系人手机" lock="-1" visible="-1" />
        <input group="线下汇款凭据" el="135" type="text" ek="fbillhead" id="fremitimage" fn="fremitimage" pn="fremitimage" cn="汇款底单扫描" lix="10" lock="-1" visible="-1" />
    </div>

    <div id="opList">

        <ul el="10" id="confirm" op="setstatus" opn="确认" permid="payorder_confirmbill" desc="出纳线下核对账务无误后，在系统中对此记录进行确认操作！"
            data="{'parameter':{'statusFieldKey':'fpaystatus','statusValue':'fpaystatus_02'},
                    'condition':{'expr':'fpaystatus==\'fpaystatus_01\'','message':'支付状态只有为待确认时才允许确认！'}}"></ul>

        <ul el="10" id="unconfirm" op="setstatus" opn="取消确认" permid="payorder_unconfirmbill" desc="出纳线下核对账务出错后，在系统中对此记录进行取消确认操作！"
            data="{'parameter':{'statusFieldKey':'fpaystatus','statusValue':'fpaystatus_01'},
                    'condition':{'expr':'fpaystatus==\'fpaystatus_02\'','message':'支付状态只有为已确认时才允许取消确认！'}}"></ul>
        
    </div>
    <div id="permList">
        <ul el="12" id="payorder_confirmbill" cn="确认"></ul>
        <ul el="12" id="payorder_unconfirmbill" cn="取消确认"></ul>
    </div>
</body>
</html>
