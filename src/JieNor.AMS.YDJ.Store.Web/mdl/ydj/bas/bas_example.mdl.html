<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
</head>
<!--表单模型-->
<!--业务领域类型范围 0 至 50-->
<!--具体请参考元素类型定义文件 HtmlElementType.cs -->
<body id="bas_example" basemodel="bd_basetmpl" el="3" cn="表单模型示例">

    <!--单据头-->
    <!--实体类型范围 51 至 99 -->
    <div id="fbillhead" el="51" pk="fid" tn="t_bas_example" pn="fbillhead" cn="表单模型示例">

        <!--字段定义-->
        <!--字段类型范围 100 至 500 -->
        <!--其中有部分字段已经在基类模型中定义，业务表单无需重复定义，请查看基类模型-->
        <input el="100" ek="fbillhead" visible="-1" id="ftext" fn="ftext" pn="ftext" cn="文本字段" />
        <input el="101" ek="fbillhead" visible="-1" id="finteger" fn="finteger" pn="finteger" cn="整数字段" />
        <input el="102" ek="fbillhead" visible="-1" id="fdecimal" fn="fdecimal" pn="fdecimal" cn="小数字段" />
        <input el="103" ek="fbillhead" visible="-1" id="fqty" fn="fqty" pn="fqty" cn="数量字段" />
        <input el="104" ek="fbillhead" visible="-1" id="fprice" fn="fprice" pn="fprice" cn="单价字段" />
        <input el="105" ek="fbillhead" visible="-1" id="famount" fn="famount" pn="famount" cn="金额字段" />
        <input el="106" ek="fbillhead" visible="-1" id="fbasedata" fn="fbasedata" pn="fbasedata" cn="基础资料字段" refid="sec_user" />
        <input el="107" ek="fbillhead" visible="-1" id="fbasedataprop" fn="" pn="fbasedataprop" cn="基础资料属性字段" ctlfk="fbasedata" dispfk="fnumber" desc="该字段不存储到数据库，所以 fn 属性设为空" />
        <input el="108" ek="fbillhead" visible="-1" id="fnumber" fn="fnumber" pn="fnumber" cn="编码字段" />
        <input el="109" ek="fbillhead" visible="-1" id="funitid" fn="funitid" pn="funitid" cn="计量单位字段" refid="ydj_unit" ctlfk="fbasedata" />
        <input el="110" ek="fbillhead" visible="-1" id="ffile" fn="ffile" pn="ffile" cn="单文件字段" />
        <input el="111" ek="fbillhead" visible="-1" id="fimage" fn="fimage" pn="fimage" cn="单图片字段" />
        <input el="112" ek="fbillhead" visible="-1" id="fdate" fn="fdate" pn="fdate" cn="日期字段" />
        <input el="113" ek="fbillhead" visible="-1" id="fdatetime" fn="fdatetime" pn="fdatetime" cn="日期时间字段" />
        <input el="114" ek="fbillhead" visible="-1" id="fradiogroup" fn="fradiogroup" pn="fradiogroup" cn="单选按钮组字段" /> <!--暂时没用到-->
        <input el="115" ek="fbillhead" visible="-1" id="fradio" fn="fradio" pn="fradio" cn="单选按钮字段" /> <!--暂时没用到-->
        <input el="116" ek="fbillhead" visible="-1" id="fcheckbox" fn="fcheckbox" pn="fcheckbox" cn="复选框字段" />
        <input el="117" ek="fbillhead" visible="-1" id="fcombo" fn="fcombo" pn="fcombo" cn="辅助资料字段" cg="记录类型" refid="bd_enum" />

        <!--<input el="118" ek="fbillhead" visible="-1" id="fcreater" fn="fcreater" pn="fcreater" cn="创建人字段" refid="sec_user" />-->
        <!--<input el="119" ek="fbillhead" visible="-1" id="fcreatedate" fn="fcreatedate" pn="fcreatedate" cn="创建时间字段" />-->
        <!--<input el="120" ek="fbillhead" visible="-1" id="fmodifier" fn="fmodifier" pn="fmodifier" cn="修改人字段" refid="sec_user" />-->
        <!--<input el="121" ek="fbillhead" visible="-1" id="fmodifydate" fn="fmodifydate" pn="fmodifydate" cn="修改时间字段" />-->

        <input el="122" ek="fbillhead" visible="-1" id="fbillstatus" fn="fbillstatus" pn="fbillstatus" cn="单据状态字段" cg="数据状态" refid="bd_enum" />
        <input el="123" ek="fbillhead" visible="-1" id="fbilltype" fn="fbilltype" pn="fbilltype" cn="单据类型字段" refid="bd_billtype" />

        <!--<input el="124" ek="fbillhead" visible="-1" id="fuser" fn="fuser" pn="fuser" cn="用户字段" refid="sec_user" />-->

        <input el="125" ek="fbillhead" visible="-1" id="fmulcombo" fn="fmulcombo" pn="fmulcombo" cn="多选辅助资料字段" cg="户型" refid="bd_enum" />

        <!--<input el="126" ek="fbillhead" visible="-1" id="fmultilangtext" fn="fmultilangtext" pn="fmultilangtext" cn="多语言文本字段" />--> <!--暂时没用到-->

        <input el="127" ek="fbillhead" visible="-1" id="fjson" fn="fjson" pn="fjson" cn="json字段" />
        <input el="128" ek="fbillhead" visible="-1" id="fprintcount" fn="fprintcount" pn="fprintcount" cn="打印次数字段" />
        <input el="129" ek="fbillhead" visible="-1" id="fattchcoun" fn="fattchcoun" pn="fattchcoun" cn="附件数字段" />
        <input el="130" ek="fbillhead" visible="-1" id="fnotescount" fn="fnotescount" pn="fnotescount" cn="批注数字段" />
        <input el="131" ek="fbillhead" visible="-1" id="fmulbasedata" fn="fmulbasedata" pn="fmulbasedata" cn="多选基础资料字段" refid="ydj_product" />
        <input el="132" ek="fbillhead" visible="-1" id="fauxproperty" fn="fauxproperty" pn="fauxproperty" cn="辅助属性字段" ctlfk="fbasedata" /> <!--需要关联商品字段-->

        <!--<input el="133" ek="fbillhead" visible="-1" id="fsize" fn="fsize" pn="fsize" cn="尺寸字段" />-->

        <input el="134" ek="fbillhead" visible="-1" id="fmulfile" fn="fmulfile" pn="fmulfile" cn="多文件字段" />
        <input el="135" ek="fbillhead" visible="-1" id="fmulimage" fn="fmulimage" pn="fmulimage" cn="多图片字段" />
        <input el="136" ek="fbillhead" visible="-1" id="fexpr" fn="fexpr" pn="fexpr" cn="表达式字段" />
        <input el="137" ek="fbillhead" visible="-1" id="fsnapshot" fn="fsnapshot" pn="fsnapshot" cn="快照字段" />
        <input el="138" ek="fbillhead" visible="-1" id="frichtext" fn="frichtext" pn="frichtext" cn="富文本字段" />
        <input el="139" ek="fbillhead" visible="-1" id="ffieldmodel" fn="ffieldmodel" pn="ffieldmodel" cn="字段模型字段" ctlfk="fbizobject" />
        <input el="140" ek="fbillhead" visible="-1" id="fsourcetype" fn="fsourcetype" pn="fsourcetype" cn="源单类型字段" refid="sys_bizobject" />
        <input el="141" ek="fbillhead" visible="-1" id="fsourcenumber" fn="fsourcenumber" pn="fsourcenumber" cn="源单编号字段" ctlfk="fsourcetype" />
        <input el="142" ek="fbillhead" visible="-1" id="fmulfieldmodel" fn="fmulfieldmodel" pn="fmulfieldmodel" cn="多选字段模型字段" ctlfk="fbizobject" />

        <!--该字段目前仅在表格中使用，具体示例请参考：\mdl\sys\sys_opserconfig.mdl.html-->
        <input el="143" ek="fbillhead" visible="-1" id="fjsonconfig" fn="fjsonconfig" pn="fjsonconfig" cn="json配置字段" />

        <!--该字段目前只在表格中使用-->
        <!--<input el="144" ek="fbillhead" visible="-1" id="fbutton" fn="" pn="fbutton" cn="按钮字段" desc="该字段不存储到数据库，所以 fn 属性设为空" />-->

        <input el="145" ek="fbillhead" visible="-1" id="fdynbasedata" fn="fdynbasedata" pn="fdynbasedata" cn="动态基础资料字段"
               dataUrl="/designtime/bpm_consoleview?operationno=listbizobject" /> <!--很少用到-->
        <input el="145" ek="fbillhead" visible="-1" id="fmuldynbasedata" fn="fmuldynbasedata" pn="fmuldynbasedata" cn="多选动态基础资料字段"
               dataUrl="/designtime/bpm_consoleview?operationno=listbizobject" /> <!--很少用到-->

        <!--<input el="146" ek="fbillhead" visible="-1" id="fexprpanel" fn="fexprpanel" pn="fexprpanel" cn="表达式面板字段" />--> <!--很少用到-->
        <!--<input el="147" ek="fbillhead" visible="-1" id="fjsonimage" fn="fjsonimage" pn="fjsonimage" cn="只读图片字段" />--> <!--很少用到-->
        <!--<input el="148" ek="fbillhead" visible="-1" id="fcompany" fn="fcompany" pn="fcompany" cn="企业字段" />--> <!--后台隐藏字段-->

        <!--多类别基础资料类型字段、多类别基础资料字段 配套使用-->
        <div el="149" ek="fbillhead" visible="-1" id="fownertype" fn="fownertype" pn="fownertype" cn="货主类型" dataviewname="v_bd_ownerdata">
            <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
            <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
        </div>
        <input el="150" ek="fbillhead" visible="-1" id="fownerid" fn="fownerid" pn="fownerid" cn="货主" ctlfk="fownertype" />

        <!--<input el="151" ek="fbillhead" visible="-1" id="ftablebasedata" fn="ftablebasedata" pn="ftablebasedata" cn="选单基础资料字段" refid="ydj_product" />--> <!--暂时没用到-->

        <input el="152" ek="fbillhead" visible="-1" id="fsimpleselect" fn="fsimpleselect" pn="fsimpleselect" cn="简单下拉框字段" vals="'1':'测试A','2':'测试B'" />

        <!--基础资料字段、基础资料分录字段 配套使用-->
        <input el="106" ek="fbillhead" visible="-1" id="fhouseid" fn="fhouseid" pn="fhouseid" cn="仓库" refid="ydj_storehouse" />
        <input el="153" ek="fbillhead" visible="-1" id="flocationid" fn="flocationid" pn="flocationid" cn="仓库"
               ctlfk="fhouseid" luek="fentity" lunbfk="flocnumber" lunmfk="flocname" />

        <input el="154" ek="fbillhead" visible="-1" id="fqrcode" fn="fqrcode" pn="fqrcode" cn="二维码字段" bct="QR" url="" />
        <input el="154" ek="fbillhead" visible="-1" id="fbarcode" fn="fbarcode" pn="fbarcode" cn="条形码字段" bct="BR" url="" />
        <input el="155" ek="fbillhead" visible="-1" id="ftag" fn="ftag" pn="ftag" cn="标签字段" />
        <input el="156" ek="fbillhead" visible="-1" id="ffilterpanel" fn="ffilterpanel" pn="ffilterpanel" cn="条件面板字段" ctlfk="fbizobject" /> <!--依赖业务对象字段-->
        <input el="157" ek="fbillhead" visible="-1" id="fperson" fn="fperson" pn="fperson" cn="人员字段" ctlfk="fbizobject" />
        <input el="158" ek="fbillhead" visible="-1" id="ftime" fn="ftime" pn="ftime" cn="时间字段" />
        <input el="159" ek="fbillhead" visible="-1" id="fdaterange" fn="fdaterange" pn="fdaterange" cn="日期区间字段" />

        <!--'0':'正常','1':'整单关闭','2':'部分关闭','3':'自动关闭','4':'手动关闭'-->
        <input el="160" ek="fbillhead" visible="-1" id="fclosestatus" fn="fclosestatus" pn="fclosestatus" cn="关闭状态字段" />

        <input el="161" ek="fbillhead" visible="-1" id="fmtrlimage" fn="fmtrlimage" pn="fmtrlimage" cn="商品图片字段" />
        <input el="162" ek="fbillhead" visible="-1" id="fhyperlink" fn="fhyperlink" pn="fhyperlink" cn="超链接字段" /> <!--暂时没用到-->
        <input el="163" ek="fbillhead" visible="-1" id="fusersign" fn="fusersign" pn="fusersign" cn="用户签名字段" />
        <input el="164" ek="fbillhead" visible="-1" id="fflowinstance" fn="fflowinstance" pn="fflowinstance" cn="流程实例字段" /> <!--后台隐藏字段-->
        <input el="165" ek="fbillhead" visible="-1" id="fsourceid" fn="fsourceid" pn="fsourceid" cn="源单id字段" /> <!--后台隐藏字段-->

        <input el="168" ek="fbillhead" visible="-1" id="fcolor" fn="fcolor" pn="fcolor" cn="颜色字段" />

        <!--业务对象字段-->
        <input el="106" ek="fbillhead" visible="-1" id="fbizobject" fn="fbizobject" pn="fbizobject" cn="业务对象字段" refid="sys_bizobject" />

        <!--省市区字段特殊，是由三个辅助资料字段组成-->
        <input el="122" ek="fbillhead" visible="-1" id="fprovince" fn="fprovince" pn="fprovince" cn="省" cg="省" refid="bd_enum" />
        <input el="122" ek="fbillhead" visible="-1" id="fcity" fn="fcity" pn="fcity" cn="市" cg="市" refid="bd_enum" />
        <input el="122" ek="fbillhead" visible="-1" id="fregion" fn="fregion" pn="fregion" cn="区" cg="区" refid="bd_enum" />

        <!--户型字段特殊，是由四个辅助资料字段组成-->
        <input el="122" ek="fbillhead" visible="-1" id="froom" fn="froom" pn="froom" cn="室" cg="户型枚举" refid="bd_enum" />
        <input el="122" ek="fbillhead" visible="-1" id="fhall" fn="fhall" pn="fhall" cn="厅" cg="户型枚举" refid="bd_enum" />
        <input el="122" ek="fbillhead" visible="-1" id="ftoilet" fn="ftoilet" pn="ftoilet" cn="卫" cg="户型枚举" refid="bd_enum" />
        <input el="122" ek="fbillhead" visible="-1" id="fbalcony" fn="fbalcony" pn="fbalcony" cn="阳台" cg="户型枚举" refid="bd_enum" />

    </div>

    <!--单据体-->
    <table id="ftestentity" el="52" pk="fentryid" tn="t_bas_exampletest" pn="ftestentity" cn="单据体">
        <tr>
            <!--字段定义-->
            <th el="152" ek="ftestentity" visible="1150" id="fbizfieldsrc" fn="fbizfieldsrc" pn="fbizfieldsrc" cn="业务字段来源" 
                vals="'basedata':'基础资料','enumdata':'辅助资料','text':'文本'" width="130" lock="-1"></th>
            <th el="166" ek="ftestentity" visible="1150" id="fbizfieldtype" fn="fbizfieldtype" pn="fbizfieldtype" cn="业务字段类型" 
                refid="bd_bizfieldtype" dfld="fsrc" sformid="" width="240"></th>
            <th el="167" ek="ftestentity" visible="1150" id="fbizfieldvalue" fn="fbizfieldvalue" pn="fbizfieldvalue" cn="业务字段值" 
                ctlfk="fbizfieldsrc,fbizfieldtype" width="240" desc="注意：ctlfk属性值顺序不能变，必须是先来源后类型"></th>
        </tr>
    </table>

    <!--单据体-->
    <table id="fentity" el="52" pk="fentryid" tn="t_bas_exampleentry" pn="fentity" cn="单据体">
        <tr>
            <!--字段定义-->
            <th el="100" ek="fentity" visible="1150" id="fftext_e" fn="ftext" pn="ftext" cn="文本字段"></th>
            <th el="101" ek="fentity" visible="1150" id="finteger_e" fn="finteger" pn="finteger" cn="整数字段"></th>
            <th el="102" ek="fentity" visible="1150" id="fdecimal_e" fn="fdecimal" pn="fdecimal" cn="小数字段"></th>
            <th el="103" ek="fentity" visible="1150" id="fqty_e" fn="fqty" pn="fqty" cn="基本单位数量" ctlfk="funitid_e"></th>
            <th el="103" ek="fentity" visible="1150" id="fbizqty_e" fn="fbizqty" pn="fbizqty" cn="业务单位数量" ctlfk="fbizunitid_e" basqtyfk="fqty_e"></th>
            <th el="104" ek="fentity" visible="1150" id="fprice_e" fn="fprice" pn="fprice" cn="单价字段"></th>
            <th el="105" ek="fentity" visible="1150" id="famount_e" fn="famount" pn="famount" cn="金额字段"></th>
            <th el="106" ek="fentity" visible="1150" id="fbasedata_e" fn="fbasedata" pn="fbasedata" cn="基础资料字段" refid="ydj_product"></th>
            <th el="107" ek="fentity" visible="1150" id="fbasedataprop_e" fn="" pn="fbasedataprop" cn="基础资料属性字段" ctlfk="fbasedata_e" dispfk="fnumber"></th>
            <th el="109" ek="fentity" visible="1150" id="funitid_e" fn="funitid" pn="funitid" cn="基本单位" refid="ydj_unit" sformid="" ctlfk="fbasedata_e" lock="-1" filter="fisbaseunit='1'"></th>
            <th el="109" ek="fentity" visible="1150" id="fbizunitid_e" fn="fbizunitid" pn="fbizunitid" cn="业务单位" refid="ydj_unit" sformid="" ctlfk="fbasedata_e"></th>
            <th el="110" ek="fentity" visible="1150" id="ffile_e" fn="ffile" pn="ffile" cn="单文件字段"></th>
            <th el="111" ek="fentity" visible="1150" id="fimage_e" fn="fimage" pn="fimage" cn="单图片字段"></th>
            <th el="112" ek="fentity" visible="1150" id="fdate_e" fn="fdate" pn="fdate" cn="日期字段"></th>
            <th el="113" ek="fentity" visible="1150" id="fdatetime_e" fn="fdatetime" pn="fdatetime" cn="日期时间字段"></th>
            <th el="116" ek="fentity" visible="1150" id="fcheckbox_e" fn="fcheckbox" pn="fcheckbox" cn="复选框字段"></th>
            <th el="117" ek="fentity" visible="1150" id="fcombo_e" fn="fcombo" pn="fcombo" cn="辅助资料字段" cg="户型" refid="bd_enum"></th>
            <th el="134" ek="fentity" visible="1150" id="fmulfile_e" fn="fmulfile" pn="fmulfile" cn="多文件字段"></th>
            <th el="135" ek="fentity" visible="1150" id="fmulimage_e" fn="fmulimage" pn="fmulimage" cn="多图片字段"></th>
            <th el="161" ek="fentity" visible="1150" id="fmtrlimage_e" fn="fmtrlimage" pn="fmtrlimage" cn="商品图片字段" />
            <th el="152" ek="fentity" visible="1150" id="fsimpleselect_e" fn="fsimpleselect" pn="fsimpleselect" cn="简单下拉框字段" vals="'1':'测试A','2':'测试B'" />

            <!--多类别基础资料类型字段、多类别基础资料字段 配套使用-->
            <th el="149" ek="fentity" visible="1150" id="fownertype_e" fn="fownertype" pn="fownertype" cn="货主类型" dataviewname="v_bd_ownerdata">
                <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
            </th>
            <th el="150" ek="fentity" visible="1150" id="fownerid_e" fn="fownerid" pn="fownerid" cn="货主" ctlfk="fownertype_e"></th>

            <!--基础资料字段、基础资料分录字段 配套使用-->
            <th el="106" ek="fentity" visible="1150" id="fhouseid_e" fn="fhouseid" pn="fhouseid" cn="仓库" refid="ydj_storehouse"></th>
            <th el="153" ek="fentity" visible="1150" id="flocationid_e" fn="flocationid" pn="flocationid" cn="仓库"
                ctlfk="fhouseid_e" luek="fentity" lunbfk="flocnumber" lunmfk="flocname"></th>
        </tr>
    </table>

    <!--子单据体（目前很少用到）-->
    <table id="fdetail" el="53" pek="fentity" pk="fdetailid" tn="t_bas_exampledetail" pn="fdetail" cn="子单据体">
        <tr>
            <th el="100" ek="fdetail" visible="1150" id="fftext_d" fn="ftext" pn="ftext" cn="文本字段"></th>
            <th el="101" ek="fdetail" visible="1150" id="finteger_d" fn="finteger" pn="finteger" cn="整数字段"></th>
            <th el="102" ek="fdetail" visible="1150" id="fdecimal_d" fn="fdecimal" pn="fdecimal" cn="小数字段"></th>
            <th el="103" ek="fdetail" visible="1150" id="fqty_d" fn="fqty" pn="fqty" cn="数量字段"></th>
            <th el="104" ek="fdetail" visible="1150" id="fprice_d" fn="fprice" pn="fprice" cn="单价字段"></th>
            <th el="105" ek="fdetail" visible="1150" id="famount_d" fn="famount" pn="famount" cn="金额字段"></th>
            <th el="106" ek="fdetail" visible="1150" id="fbasedata_d" fn="fbasedata" pn="fbasedata" cn="基础资料字段" refid="ydj_product"></th>
            <th el="107" ek="fdetail" visible="1150" id="fbasedataprop_d" fn="" pn="fbasedataprop" cn="基础资料属性字段" ctlfk="fbasedata_d" dispfk="fnumber"></th>
        </tr>
    </table>

    <!--树形单据体（目前很少用到）-->
    <table id="ftreeentity" el="54" pk="fentryid" tn="t_bas_exampletreeentry" pn="ftreeentity" cn="树形单据体"
           lvlfk="flevel" pfk="fparentid" expfk="fexpanded" leffk="fisleaf" frtfk="ffrontid">
        <tr>
            <th el="100" ek="ftreeentity" visible="1150" id="fftext_t" fn="ftext" pn="ftext" cn="文本字段"></th>
            <th el="101" ek="ftreeentity" visible="1150" id="finteger_t" fn="finteger" pn="finteger" cn="整数字段"></th>
            <th el="102" ek="ftreeentity" visible="1150" id="fdecimal_t" fn="fdecimal" pn="fdecimal" cn="小数字段"></th>
            <th el="103" ek="ftreeentity" visible="1150" id="fqty_t" fn="fqty" pn="fqty" cn="数量字段"></th>
            <th el="104" ek="ftreeentity" visible="1150" id="fprice_t" fn="fprice" pn="fprice" cn="单价字段"></th>
            <th el="105" ek="ftreeentity" visible="1150" id="famount_t" fn="famount" pn="famount" cn="金额字段"></th>
            <th el="106" ek="ftreeentity" visible="1150" id="fbasedata_t" fn="fbasedata" pn="fbasedata" cn="基础资料字段" refid="ydj_product"></th>
            <th el="107" ek="ftreeentity" visible="1150" id="fbasedataprop_t" fn="" pn="fbasedataprop" cn="基础资料属性字段" ctlfk="fbasedata_t" dispfk="fnumber"></th>
        </tr>
    </table>

    <!--表单操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="mytest" op="mytest" opn="我的测试" data="" permid="biz_mytest">

            <!--定义校验规则-->
            <li el="11" vid="510" cn="文本字段不允许为空" data="{'expr':'ftext!=\'\' and ftext!=\' \'','message':'文本字段不允许为空！'}"></li>
        </ul>
    </div>

    <!--表单操作关联的权限项列表-->
    <div id="permList">
        <ul el="12" id="biz_mytest" cn="我的测试" order="1"></ul>
    </div>

</body>
</html>