{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [
    //此项规则表示：单据状态=D或E时，保存操作不可用, 其他操作可用
    {
      "id": "fstatus_DE",
      "expression": "menu:tbSave|fstatus=='D' or fstatus=='E'"
    },
    {
      "id": "fstatus_Audit",
      "expression": "menu:tbAudit|fstatus=='E'"
    },
    {
      "id": "fstatus_Submit",
      "expression": "menu:tbSubmit|fstatus=='D' or fstatus=='E'"
    },
    // 审核状态 = 已提交/已审核DE 时，所有字段不可编辑
    {
      "id": "fstatus_None",
      "expression": "field:*|fstatus=='D' or fstatus=='E'"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [
    {
      //隐藏生效和失效日期
      "id": "hide_date",
      "expression": "other:.y-date|fadjustmode=='price_adjust_01'"
    },
    {
      //显示生效和失效日期
      "id": "show_date",
      "expression": "other:$.y-date|fadjustmode=='price_adjust_02'"
    }
  ],
  //定义表单计算规则
  "calcRules": [
    //选择商品时，携带出默认辅助属性
    { "expression": "fattrinfo=getAuxPropValue(fproductid)" },
    { "expression": "funitid=fproductid__fsalunitid|fproductid=='' or 1==1" }
  ]
}