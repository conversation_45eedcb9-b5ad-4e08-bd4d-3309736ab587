<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="bas_storesysparam" basemodel="param_basetmpl" el="5" cn="销售管理参数">
    <div id="fbillhead" el="51" pk="fid" pn="fbillhead" cn="销售管理参数">

        <input el="116" type="checkbox" id="fischannelnullable" ek="fbillhead" defval="true" cn="销售意向合作渠道可为空" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fissaleusable" ek="fbillhead" defval="false" cn="销售意向零售价可编辑" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fisorderusable" ek="fbillhead" defval="false" cn="二级分销合同零售价可编辑" checkmode="checkbox" />

        <input el="116" type="checkbox" id="fcanreceipt" ek="fbillhead" defval="true" cn="销售单据（意向及合同）不审核可收款" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fcanrefund" ek="fbillhead" defval="false" cn="销售单据（意向及合同）不审核可退款" checkmode="checkbox" />
        <input el="116" ek="fbillhead" id="foutspotnopur" fn="foutspotnopur" pn="foutspotnopur" cn="出现货商品无需采购" visible="-1" defval="false" checkmode="checkbox" />
        <input el="116" ek="fbillhead" id="foutspotautores" fn="foutspotautores" pn="foutspotautores" cn="出现货时自动预留" visible="-1" defval="false" checkmode="checkbox" />
        <!-- <input el="116" ek="fbillhead" id="frescarrystorehouse" fn="frescarrystorehouse" pn="frescarrystorehouse" cn="可预留到仓库仓位" visible="-1" defval="true" checkmode="checkbox"  /> -->

        <input el="116" type="checkbox" id="fenablesyncgallery" ek="fbillhead" defval="false" cn="单据商品图同步至图库" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fenableproductimg" ek="fbillhead" defval="false" cn="先取图库后取主图" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fonlyproductimg" ek="fbillhead" defval="false" cn="只取主图" checkmode="checkbox" />

        <input group="基本信息" el="116" ek="FBillHead" id="fhasvoucherwhenorderchange" fn="fhasvoucherwhenorderchange" pn="fhasvoucherwhenorderchange" visible="-1" cn="合同变更必须上传凭证"
               lock="0" copy="0" lix="0" notrace="true" ts="" checkmode="checkbox" />
        <input group="基本信息" el="116" ek="FBillHead" id="fhasvoucherwhenreceivable" fn="fhasvoucherwhenreceivable" pn="fhasvoucherwhenreceivable" visible="-1" cn="收款必须上传凭证"
               lock="0" copy="0" lix="0" notrace="true" ts="" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fcanexcess" ek="fbillhead" defval="false" cn="销售合同允许超额收款" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fistransfer" ek="fbillhead" defval="false" cn="订单超额收款自动转账户余额" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fdontreflect" ek="fbillhead" defval="false" cn="销售合同退货退款影响未收款" checkmode="checkbox" />

        <input el="116" type="checkbox" id="fincomeconfirmaudit" ek="fbillhead" defval="false" cn="收支记录确认前需审核" checkmode="checkbox" />
        <input el="116" type="checkbox" id="foutspotupdatemtono" ek="fbillhead" defval="false" cn="出现货商品自动更新物流跟踪号" checkmode="checkbox" />

        <input el="100" type="checkbox" id="fcustomerarea" ek="fbillhead" cn="客户地区必录信息" />
        <input el="100" type="checkbox" id="fcustomerunique" ek="fbillhead" defval="'fphone,fstore'" cn="客户报备唯一性控制规则" />
        <input el="100" type="checkbox" id="fcustomerrecordunique" ek="fbillhead" defval="'fphone,fwechat,fcustomername'" cn="商机报备唯一性控制规则" />
        <input el="102" type="text" id="fauditminpercentage" ek="fbillhead" cn="销售合同允许审核的最低金额比例" />

        <input el="116" type="checkbox" id="ffirstratioctrl" ek="fbillhead" defval="false" cn="首款比例控制" checkmode="checkbox" />
        <input el="102" type="text" id="fnotoutspotratio" ek="fbillhead" cn="非现货首款比例" />
        <input el="102" type="text" id="foutspotratio" ek="fbillhead" cn="现货首款比例" />

        <input el="101" type="text" id="freserveday" ek="fbillhead" cn="销售意向预留日期控制" desc="销售意向预留X天后，如未成交则自动释放" />

        <input el="116" type="checkbox" id="fenablebrokerageratio" ek="fbillhead" defval="false" cn="合作渠道按比例计算佣金" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fenableprohibitionchanges" ek="fbillhead" defval="false" cn="合同存在下游未审核单据禁止变更" checkmode="checkbox" />

        <input group="基本信息" el="152" ek="fbillhead" id="fgetpricemode" fn="fgetpricemode" pn="fgetpricemode" visible="0" cn="销售取价模式"
               lock="0" copy="1" lix="0" notrace="true" ts="" vals="'1':'取本地销售价目','2':'取总部销售价目','3':'先本地再总部'" defval="'1'" />

        <input el="116" type="checkbox" id="fcanwholedis" ek="fbillhead" defval="true" cn="销售合同支持整单折" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fcanfixeprice" ek="fbillhead" defval="true" cn="销售合同支持一口价" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fcanremovetail" ek="fbillhead" defval="true" cn="销售合同支持抹尾差" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fremovedecimal" ek="fbillhead" defval="true" cn="抹小数位" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fremoveunidigit" ek="fbillhead" defval="true" cn="抹个位" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fremovetens" ek="fbillhead" defval="true" cn="抹十位" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fremovehundred" ek="fbillhead" defval="true" cn="抹百位" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fremovethousand" ek="fbillhead" defval="true" cn="抹千位" checkmode="checkbox" />

        <input el="116" type="checkbox" id="fenablecollectamount" ek="fbillhead" defval="false" cn="销售意向定金启用应收控制" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fenablephonelengthctrl" ek="fbillhead" defval="false" cn="启用手机号位数控制" checkmode="checkbox" />
        <input el="116" type="checkbox" id="femptysaletentionentry" ek="fbillhead" defval="true" cn="允许销售意向商品明细为空" checkmode="checkbox" />

        <input el="116" type="checkbox" id="fenablemustinputbankid" ek="fbillhead" defval="true" cn="刷卡方式银行卡必填" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fenableprotectecusinfo" ek="fbillhead" defval="true" cn="CRM客户隐私保护" checkmode="checkbox" />

        <input el="116" type="checkbox" id="fenablelotinventoryctl" ek="fbillhead" defval="false" cn="开启可销库存控制" checkmode="checkbox" />

        <input el="116" type="checkbox" id="fpaymentmustbeget" ek="fbillhead" defval="false" cn="销售合同收款审核前, 必须收到全款" visible="0" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fafterautomaticaudit" ek="fbillhead" defval="false" cn="销售合同收到全款后自动审核" visible="0" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fafteraudittocloud" ek="fbillhead" defval="false" cn="销售合同审核后自动生成Cloud订单(订购意向书与销售订单)" visible="0" checkmode="checkbox" />

        <input el="116" type="checkbox" id="fnocapitalcoordination" ek="fbillhead" defval="false" cn="销售合同结算时不做资金协同" visible="0" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fautosetduty" ek="fbillhead" defval="true" cn="创建客户自动设置负责人及所属门店" visible="0" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fexcludezeroorder" ek="fbillhead" defval="true" cn="仪表盘合同金额统计包含零收款金额" visible="0" checkmode="checkbox" />
        <input el="101" type="text" id="fnotfollowrecycleday" ek="fbillhead" defval="3" cn="商机领用不跟进，被回收后___天内不能领用该商机" checkmode="checkbox" />
        <input el="116" type="checkbox" id="foutspotnotice" ek="fbillhead" defval="false" cn="销售合同保存时，如商品行属于非出现货且有库存的给出提示" visible="0" checkmode="checkbox" />


        <!--该参数移到库存管理参数下-->
        <!--<input el="116" type="checkbox" id="fallowsalescontrol" ek="fbillhead" defval="false" cn="可销范围控制" checkmode="checkbox"  />-->

        <input el="116" type="checkbox" id="fnoorders" ek="fbillhead" defval="true" cn="无统一零售价商品禁止下单" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fnotallowprice" ek="fbillhead" defval="true" cn="禁止零售价小于总部零售价" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fnodealprice" ek="fbillhead" defval="false" cn="无经销价商品默认价格显示为0（前提是当前经销商启用经销价）" checkmode="checkbox" />
        <!--JN_YLW_R023-->
        <input el="116" type="checkbox" id="fisupdatesuitprice" ek="fbillhead" defval="false" cn="调价后更新套件组合价格与子件价格合计" checkmode="checkbox" />

        <!--JN_XCX_R199【优丽文】小程序新增本人负责/全部数据筛选按钮-->
        <input el="116" type="checkbox" id="fdealcustomervisible" ek="fbillhead" defval="false" cn="成交客户所有人可见" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fphonestandardcontrol" ek="fbillhead" defval="false" cn="启用手机号按国内电信号段规范控制" checkmode="checkbox" />

        <!--R-BL-BG035收款协同-已扣佣金/费用明细-->
        <input el="116" type="checkbox" id="fallowreducebrokerage" ek="fbillhead" defval="false" cn="合同收款时允许扣减佣金" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fallowaddexpense" ek="fbillhead" defval="false" cn="合同收款时允许添加费用项目" checkmode="checkbox" />
        <input el="101" type="text" id="fproportionoratioamount" ek="fbillhead" defval="100" cn="销售合同允许出库的最低金额比例" />
        <input el="101" type="text" id="fproportionbuyeramount" ek="fbillhead" defval="0" cn="销售合同允许采购的最低金额比例" />

        <!--商品非标选配属性值默认个数显示-->
        <input el="101" type="text" id="foptionalattrnumber" ek="fbillhead" defval="8" cn="商品非标选配属性值默认个数显示" />
        <!--合作渠道地址必录-->
        <input el="116" type="checkbox" id="fchanneladdress" ek="fbillhead" defval="true" cn="合作渠道地址必录" checkmode="checkbox" />

        <input el="116" type="checkbox" id="fopenomsservice" ek="fbillhead" defval="true" cn="启用定制OMS业务" checkmode="checkbox" visible="0" />

        <input el="116" type="checkbox" id="fswjcustomeraddress" ek="fbillhead" defval="false" cn="定制柜客户地址必录" checkmode="checkbox" />

        <input el="100" type="checkbox" id="fporeserveprops" ek="fbillhead" defval="'fcustom,funstdtype'" cn="采购入库审核时触发关联的销售合同自动预留的商品属性" />

        <input el="116" type="checkbox" id="foutcontrolbyincome" ek="fbillhead" defval="false" cn="销售合同按收款金额控制出库" checkmode="checkbox" />

        <input el="116" type="checkbox" id="fautostockout" ek="fbillhead" defval="false" cn="销售合同提货方式为立即提货时审核后自动生成出库单" checkmode="checkbox" />

        <input el="116" type="checkbox" id="fisgiftdiscount" ek="fbillhead" defval="false" cn="销售合同赠品参与折扣计算" tips="勾选后，赠品行的零售价会汇总到货品原值，参与整体折扣计算" checkmode="checkbox" />

        <input el="116" type="checkbox" id="fnopromotiondiscount" ek="fbillhead" defval="false" cn="促销商品不参与折扣计算" checkmode="checkbox" />

        <input el="116" type="checkbox" id="frefundcusacount" ek="fbillhead" defval="false" cn="退款时客户收款账号必录" checkmode="checkbox" />

        <input el="116" ek="FBillHead" id="fmustinvoicenumber" fn="fmustinvoicenumber" pn="fmustinvoicenumber" visible="-1" cn="收款必填收款小票号"
               lock="0" copy="0" lix="0" notrace="true" ts="" checkmode="checkbox" />

        <input el="116" type="checkbox" id="fowneditprice" ek="fbillhead" defval="false" cn="自建商品零售价可编辑" checkmode="checkbox" />

        <input el="116" type="checkbox" id="forderchangestaff" ek="fbillhead" defval="false" cn="合同变更允许切换收货人信息" checkmode="checkbox" />

        <input el="116" type="checkbox" id="freturnmodifyorder" ek="fbillhead" defval="false" cn="销售退换货中允许变更合同" checkmode="checkbox" />

        <input el="116" type="checkbox" id="ftransferallowrefund" ek="fbillhead" defval="false" cn="允许需转单的销售合同整单关闭后发起退款" checkmode="checkbox" />
        <input el="116" type="checkbox" id="fordercounteraudit" ek="fbillhead" defval="false" cn="合同有出库或采购且已确认收款允许反审核" checkmode="checkbox" tips="勾选则表示允许销售合同下游关联销售出库单或采购订单，当前合同的收款单已确认收款能反审核收款单" />
        <input el="116" type="checkbox" id="forderoverpaymentdisout" ek="fbillhead" defval="false" cn="销售合同超额收款禁止出库" checkmode="checkbox" />
        <!--大家居参数-->
        <input el="100" type="text" id="fhomestaysd" ek="fbillhead" cn="散单提示" />
        <input el="100" type="text" id="fhomestayzdzp" ek="fbillhead" cn="整家宅配提示" />
        <input el="100" type="text" id="fhomestaydjj" ek="fbillhead" cn="大家居提示" />
        

    </div>
</body>
</html>