<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="sel_propselectionsingle" el="0" cn="属性选配">

    <div id="fbillhead" el="51" pk="fid" pn="fbillhead" cn="基本信息">
        <input el="106" ek="fbillhead" id="fproductid" pn="fproductid" cn="商品" refid="ydj_product" visible="-1" lock="-1" />
        <input el="107" ek="fbillhead" id="fmtrlnumber" pn="fmtrlnumber" cn="商品编码" ctlfk="fproductid" dispfk="fnumber" visible="-1" lock="-1" />
        <input el="106" ek="fbillhead" id="fselcategoryid" pn="fselcategoryid" cn="选配类别" refid="sel_category" visible="-1" lock="-1" />
        <input el="100" ek="fbillhead" id="forderentryid" pn="forderentryid" cn="订单明细行ID" visible="0" lock="-1" />
        <input el="100" ek="fbillhead" id="fauxpropvalsetid" pn="fauxpropvalsetid" cn="辅助属性ID" visible="0" lock="-1" />
        <input el="116" ek="fbillhead" id="fisnonstandard" pn="fisnonstandard" cn="是否非标产品" visible="0" lock="-1" />
    </div>

    <table id="fentry" el="52" pk="fentryid" pn="fentry" cn="属性明细">
        <tr>
            <th el="106" ek="fentry" id="fselpropid" pn="fselpropid" cn="属性名" refid="sel_prop" width="200" visible="-1" lock="-1"></th>

            <!--动态列字段定义开始-->
            <th el="152" ek="fentry" visible="0" id="fpropvaluesrc" pn="fpropvaluesrc" cn="属性值来源"
                vals="'basedata':'基础资料','enumdata':'辅助资料','text':'文本'" width="130" lock="-1"></th>
            <th el="166" ek="fentry" visible="0" id="fpropvaluetype" pn="fpropvaluetype" cn="属性值类型"
                refid="bd_bizfieldtype" dfld="fsrc" sformid="" width="240"></th>
            <th el="167" ek="fentry" visible="1150" id="fpropvalue" pn="fpropvalue" cn="属性值"
                ctlfk="fpropvaluesrc,fpropvaluetype" width="300" desc="注意：ctlfk属性值顺序不能变，必须是先来源后类型" esp="false" ffks="fnumber,fname"></th>
            <!--动态列字段定义结束-->

            <th el="100" ek="fentry" id="fdatatype" pn="fdatatype" cn="数据类型" width="90" visible="0" lock="-1"></th>
            <th el="116" ek="fentry" id="fiscontrolmust" pn="fiscontrolmust" cn="是否必录" width="90" visible="0" lock="-1"></th>
            <th el="111" ek="fentry" id="fimage" pn="fimage" cn="属性值图片" width="140" visible="0" lock="-1"></th>
            <th lix="25" el="102" ek="fentry" id="fmin" fn="fmin" pn="fmin" cn="起" width="60" visible="0" format="0,000.000000" dformat="0,000.00"></th>
            <th lix="25" el="102" ek="fentry" id="fmax" fn="fmax" pn="fmax" cn="止" width="60" visible="0" format="0,000.000000" dformat="0,000.00"></th>
            <th el="100" ek="fentry" id="frange" pn="frange" cn="限定范围" width="240" visible="-1" len="200"></th>
            <th el="116" ek="fentry" id="fallowcustom" pn="fallowcustom" cn="支持非标录入" width="110" visible="-1" lock="-1"></th>
            <th el="100" ek="fentry" id="fnonstandard" pn="fnonstandard" cn="非标值" width="240" visible="-1" len="200"></th>
            <th el="116" ek="fentry" id="fenableconstraint" pn="fenableconstraint" cn="属性值是否启用选配约束" width="240" visible="0"
                desc="根据此标记来决定是否要启用选配约束条件逻辑"></th>
        </tr>
    </table>

</body>
</html>