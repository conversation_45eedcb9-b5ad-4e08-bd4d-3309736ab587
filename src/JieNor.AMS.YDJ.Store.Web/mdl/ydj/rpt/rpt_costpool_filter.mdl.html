<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="rpt_costpool_filter" el="0" basemodel="" cn="总部费用池查询报表过滤" isolate="0">
    <div id="fbillhead" el="51" pk="fid" pn="fbillhead" cn="总部费用池查询报表过滤">
        <input group="基本信息" el="106" ek="fbillhead" id="agent" fn="agent" pn="agent" visible="53" cn="售达方" refid="bas_agent"
               lock="0" copy="0" lix="50" notrace="true" ts="" filter="" reflvt="0" dfld="" />
        <input group="基本信息" el="131" ek="fbillhead" id="org" fn="org" pn="org" visible="53" cn="销售组织" refid="bas_organization"
               lock="0" copy="0" lix="50" notrace="true" ts="" filter="" reflvt="0" dfld="" />
        <input group="基本信息" el="112" ek="fbillhead" id="fdatefrom" fn="fdatefrom" pn="fdatefrom" visible="-1" cn="开始日期从"
               lock="0" copy="1" lix="0" notrace="true" ts="" format="yyyy-MM-dd" />
        <input group="基本信息" el="112" ek="fbillhead" id="fdateto" fn="fdateto" pn="fdateto" visible="-1" cn="结束日期至"
               lock="0" copy="1" lix="0" notrace="true" ts="" format="yyyy-MM-dd" />
        <input group="基本信息" el="152" ek="fbillhead" id="type" fn="type" pn="type" visible="-1" cn="类型" vals="'1':'所有','2':'已返利','3':'未返利或部分返利'"
               lock="0" copy="0" defval="1" lix="50" />
    </div>
</body>
</html>