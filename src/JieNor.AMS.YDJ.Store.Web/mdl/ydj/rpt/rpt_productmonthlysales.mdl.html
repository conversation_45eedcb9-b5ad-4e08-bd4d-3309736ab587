<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
</head>
<body id="rpt_productmonthlysales" el="1" basemodel="" cn="月均销售数量表" rac="true" isolate="1">
    <div id="fbillhead" el="51" pk="fid" pn="fbillhead" tn="t_ydj_productmonthlysales" cn="月均销售数量表">
        <input group="基本信息" el="148" ek="fbillhead" id="fmainorgid" fn="fmainorgid" pn="fmainorgid" cn="企业主体" xlsin="0" visible="0" copy="0" />
        <input group="基本信息" el="106" ek="fbillhead" id="fagentid" fn="fagentid" pn="fagentid" visible="-1" cn="经销商名称" lock="-1" lix="1" refid="bas_agent" dfld="fnumber" />
        <input group="基本信息" el="107" ek="fbillhead" visible="-1" id="fagentnumber" fn="fagentnumber" pn="fagentnumber" cn="经销商编码" lix="2" width="120" ctlfk="fagentid" dispfk="fnumber" />
        <!-- 商品（销售合同.单据体.商品） -->
        <input group="基本信息" el="106" ek="fbillhead" id="fproductid" fn="fproductid" pn="fproductid" visible="-1" cn="商品" lock="-1" lix="1" refid="ydj_product" dfld="fnumber,fbrandid,fseriesid,fseltypeid" />
        <input group="基本信息" el="107" ek="fbillhead" visible="-1" id="fproductnumber" fn="fproductnumber" pn="fproductnumber" cn="商品编码" lix="2" width="120" ctlfk="fproductid" dispfk="fnumber" />
        <input group="基本信息" el="107" ek="fbillhead" visible="1086" id="fbrandid" fn="fbrandid" pn="fbrandid" cn="品牌" lix="3" width="120" ctlfk="fproductid" dispfk="fbrandid" />
        <input group="基本信息" el="107" ek="fbillhead" visible="1086" id="fseriesid" fn="fseriesid" pn="fseriesid" cn="系列" lix="4" width="120" ctlfk="fproductid" dispfk="fseriesid" />
        <input group="基本信息" el="107" ek="fbillhead" id="fseltypeid" fn="fseltypeid" ctlfk="fproductid" dispfk="fseltypeid" ts="" cn="型号" visible="-1" lix="5" />
        <input lix="15" group="基本信息" el="132" ek="fbillhead" id="fattrinfo" fn="fattrinfo" pn="fattrinfo" visible="0" cn="辅助属性" lock="-1" copy="0" notrace="true" ts="" ctlfk="fmaterialid" pricefk="" />
        <input group="基本信息" el="106" ek="fbillhead" id="fattrinfo_e" fn="fattrinfo_e" pn="fattrinfo_e" refid="bd_auxpropvalue_ext" visible="-1" cn="辅助属性"
               lock="-1" copy="0" lix="6" notrace="true" ts="" width="200" />

        <input group="基本信息" el="100" ek="fbillhead" len="2000" id="fcustomdes" fn="fcustomdes" pn="fcustomdes" cn="定制说明" width="160" visible="-1" lix="7" />
        <input group="基本信息" el="103" ek="fbillhead" visible="-1" id="fsumqty" fn="fsumqty" pn="fsumqty" cn="总销售数量" lix="8" width="130"  tips="按【商品编码+辅助属性+定制说明】维度汇总统计销售合同商品行的【销售数量】"/>
        <input group="基本信息" el="103" ek="fbillhead" visible="-1" id="fmonthqty" fn="fmonthqty" pn="fmonthqty" cn="统计月份数" lix="9" width="130" tips="取查询报表日期的年度1月1日-查询日期上月最后一天的数据作为统计月份数，例如是2025.7.1查询，取值2025.1.1-2025.6.30数据；如果是2025年1月1日查询，取值2024.1.1-2024.12.31数据；" />
        <input group="基本信息" el="103" ek="fbillhead" visible="-1" id="favgsaleqty" fn="favgsaleqty" pn="favgsaleqty" cn="月均销售数量" lix="10" width="130"  tips="月均销售数量=总销售数量/统计月份数，结果保留2位小数"/>
        <input group="基本信息" el="113" ek="fbillhead" id="fupdatetime" fn="fupdatetime" pn="fupdatetime" visible="-1" cn="更新时间" lock="-1" lix="11" />
        <input group="基本信息" el="120" ek="fbillhead" id="fmodifierid" fn="fmodifierid" pn="fmodifierid" cn="更新人" refId="Sec_User" dfld="fname" visible="-1" copy="0" lix="85" />
    </div>
    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="query" op="query" opn="查看" data="" permid="fw_view"></ul>
        <ul el="10" ek="fbillhead" id="print" op="print" opn="打印" data="" permid="fw_print"></ul>
        <ul el="10" ek="fbillhead" id="listdatatopdf" op="listdatatopdf" opn="导出PDF" data="" permid="fw_export"></ul>
        <ul el="10" ek="fbillhead" id="listdatatoexcel" op="listdatatoexcel" opn="导出Excel" data="" permid="fw_export"></ul>
    </div>

    <div id="permList">
        <ul el="12" id="fw_view" cn="查看"></ul>
        <ul el="12" id="fw_print" cn="打印"></ul>
        <ul el="12" id="fw_export" cn="导出"></ul>
        <ul el="12" id="fw_updatePriceData" cn="手动更新"></ul>
    </div>

</body>
</html>