<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="coo_chargemoney" basemodel="bill_basetmpl" el="1" cn="扣款单">
    <div id="fbillhead" el="51" pk="fid" tn="t_coo_chargemoney" pn="fbillhead" cn="扣款单">
        <!--基本信息-->
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="ftranid" fn="ftranid" cn="流水号" lix="1" lock="-1" copy="0" />
        <input group="基本信息" el="100" type="text" id="fordercompanyname" ek="fbillhead" fn="fordercompanyname" ts="" cn="采购方" visible="-1" lock="-1" />
        <input group="基本信息" el="100" type="text" id="fordercompanyid" ek="fbillhead" fn="fordercompanyid" pn="fordercompanyid" ts="" cn="采购方企业id" visible="0" lock="-1" />
        <input group="基本信息" el="122" type="text" id="fbusinessstatus" ek="fbillhead" fn="fbusinessstatus" pn="fbusinessstatus" cg="(充值单)业务状态" cn="业务状态" refid="bd_enum" lock="-1" dfld="fenumitem" defval="'inpour_status_01'" visible="-1" desc="两种：未确认，已确认" />
        <input group="基本信息" el="112" type="text" id="fdate" ek="fbillhead" fn="fdate" pn="fdate" ts="" cn="扣款日期" defval="@currentshortdate" visible="-1" lock="-1" />
        <input group="基本信息" el="100" type="text" id="fsellercompany" ek="fbillhead" fn="fsellercompany" ts="" cn="销售方" visible="-1" lock="-1" />
        <input group="结算信息" el="100" type="text" id="fsellercompanyid" ek="fbillhead" fn="fsellercompanyid" pn="fsellercompanyid" ts="" cn="销售方企业id" visible="0" lock="-1" />
        <input group="基本信息" el="100" type="text" id="fcreatecompany" ek="fbillhead" fn="fcreatecompany" ts="" cn="创建方" visible="-1" lock="-1" />
        <input group="结算信息" el="100" id="fcreatecompanyid" ek="fbillhead" fn="fcreatecompanyid" pn="fcreatecompanyid" ts="" cn="创建方企业id" visible="0" lock="-1" />
		<input group="基本信息" el="116" ek="fbillhead" id="fissyn" fn="fissyn" pn="fissyn" cn="是否为对方企业" visible="32" lock="-1" />
        
        <input group="基本信息" el="100" type="text" id="fsourceform" ek="fbillhead" fn="fsourceform" ts="" cn="充值来源表单" visible="0" lock="-1" />
        <input group="基本信息" el="100" type="text" id="fsourceid" ek="fbillhead" fn="fsourceid" ts="" cn="来源id" visible="0" lock="-1" />

        <input group="基本信息" el="100" type="text" id="fcoocompanyid" ek="fbillhead" fn="fcoocompanyid" pn="fcoocompanyid" ts="" cn="对方企业id" visible="0" lock="-1" />
        <input group="基本信息" el="100" type="text" id="fcustomerid" ek="fbillhead" fn="fcustomerid" pn="fcustomerid" ts="" cn="客户" refid="ydj_customer" visible="0" lock="-1" />
        <input group="基本信息" el="100" type="text" id="fsupplierid" ek="fbillhead" fn="fsupplierid" pn="fsupplierid" ts="" cn="供应商" refid="ydj_supplier" visible="0" lock="-1" />
        <!--结算明细-->
        <input group="结算明细" el="122" id="fusagetype" ek="fbillhead" fn="fusagetype" pn="fusagetype" refId="bd_enum" dfld="fenumitem" ts="" lock="-1" defval="'settleaccount_type_01'" visible="-1" cg="结算单账户类型" cn="扣款用途 " desc="三种：货款，返利，保证金额" />
        <input group="结算明细" el="100" type="text" id="fdescription" ek="fbillhead" fn="fdescription" pn="fdescription" ts="" cn="备注" visible="-1" lock="-1" />
        <input group="结算明细" el="105" type="text" id="fmoney" ek="fbillhead" fn="fmoney" pn="fmoney" ts="" cn="金额" visible="-1" lock="-1" />
        
        <input group="结算明细" el="135" type="text" id="fimage" ek="fbillhead" fn="fimage" pn="fimage" cn="附件" visible="-1" lix="10" lock="-1"/>

        <input el="144" ek="fbillhead" visible="-1" id="foperate" fn="foperate" pn="foperate" cn="操作" width="100" lix="1000" />
    </div>
</body>
</html>