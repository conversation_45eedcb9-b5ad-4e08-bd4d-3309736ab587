{
  //规则引擎基类
  "base": "",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "fstatus_ABC",
      "expression": ""
    },
    {
      "id": "fstatus_D",
      "expression": ""
    },
    {
      "id": "fstatus_E",
      "expression": ""
    },
    {
      "id": "fstatus_",
      "expression": ""
    },

    ////如果支付方式不是银行，则锁定银行账号字段
    //{
    //  "id": "lock_bankcard",
    //  "expression": "field:fmybankid,fsynbankid|fway!='payway_06' and fway!='payway_11' and fway!='payway_07' and fway!='payway_08'"
    //},
    //{
    //  "id": "unlock_bankcard",
    //  "expression": "field:$fmybankid,fsynbankid|fway=='payway_06' or fway=='payway_11' or fway=='payway_07' or fway=='payway_08'"
    //}
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [

  ]
}