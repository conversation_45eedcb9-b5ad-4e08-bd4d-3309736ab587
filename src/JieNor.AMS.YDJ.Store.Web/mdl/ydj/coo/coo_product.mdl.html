<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="coo_product" basemodel="bd_basetmpl" el="3" cn="协同商品">
    <div id="fbillhead" el="51" pk="fid" tn="t_coo_product" pn="fbillhead" cn="协同商品">
		<input group="基本信息" el="147" id="fimage" ek="fbillhead" fn="fimage" pn="fimage" cn="商品主图" visible="-1" lix="1" lock="-1" />
        <input group="基本信息" el="100" type="text" id="fname" ek="fbillhead" fn="fname" ts="" cn="名称" visible="-1" lix="2" lock="-1" />
        <input group="基本信息" el="100" type="text" id="fcategory" ek="fbillhead" fn="fcategory" ts="" cn="分类" visible="-1" lix="3" lock="-1" />
        <input group="基本信息" el="100" type="text" id="funit" ek="fbillhead" fn="funit" ts="" visible="-1" cn="单位" lix="4" lock="-1" />
        <input group="基本信息" el="100" type="text" id="fbrand" ek="fbillhead" fn="fbrand" ts="" visible="-1" cn="品牌" lix="5" lock="-1" />
        <input group="基本信息" el="100" type="text" id="fpublishcompany" ek="fbillhead" fn="fpublishcompany" ts="" visible="-1" cn="发布企业" lix="6" lock="-1" />
        <input group="基本信息" el="100" type="text" id="fpublishcompanyid" ek="fbillhead" fn="fpublishcompanyid" ts="" visible="0" cn="发布企业id" lix="6" lock="-1" />
        <input group="基本信息" el="100" type="text" id="fcompanytype" ek="fbillhead" fn="fcompanytype" pn="fcompanytype" visible="0" cn="企业性质" lix="7" lock="-1" />
        <input group="基本信息" el="113" type="date" id="fpublishdate" ek="fbillhead" fn="fpublishdate" pn="fpublishdate" cn="发布日期" visible="-1" defval="@currentshortdate" lix="8" lock="-1" />
        <input group="基本信息" el="100" type="text" id="fpublishstatus" ek="fbillhead" fn="fpublishstatus" ts="" visible="-1" cn="发布状态" lix="9" lock="-1" />
        <input group="基本信息" el="100" type="text" id="flocalproduct" ek="fbillhead" fn="flocalproduct" ts="" visible="-1" cn="本地商品" lix="10" lock="-1" />        
        <input group="基本信息" el="100" type="text" id="flocalproductid" ek="fbillhead" fn="flocalproductid" ts="" visible="0" cn="本地商品id" lock="-1" />
        <input group="基本信息" el="100" type="text" id="fremoteproductid" ek="fbillhead" fn="fremoteproductid" ts="" visible="0" cn="远程商品id" lock="-1" />
        <input group="基本信息" el="127" type="text" id="fcontent" ek="fbillhead" fn="fcontent" ts="" cn="详细" lix="11" visible="0" lock="-1" />
        <input el="100" ek="fbillhead" type="text" id="fnumber" fn="fnumber" pn="fnumber" visible="0" lock="-1" />
		<input el="100" ek="fbillhead" type="text" id="fdescription" fn="fdescription" pn="fdescription" visible="0" lock="-1" />
		<input type="text" id="fcreatorid" el="118" ek="fbillhead" refId="Sec_User" dfld="FName" fn="fcreatorid" pn="fcreatorid" visible="0" lock="-1" />
        <input type="datetime" id="fcreatedate" el="119" ek="fbillhead" fn="fcreatedate" pn="fcreatedate" visible="0" lock="-1" />
        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="数据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="数据状态" visible="0" lock="-1" ></select>
        
    </div>
</body>
</html>