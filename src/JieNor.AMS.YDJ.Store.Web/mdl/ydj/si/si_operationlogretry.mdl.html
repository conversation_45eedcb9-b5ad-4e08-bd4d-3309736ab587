<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
</head>
<body id="si_operationlogretry" el="3" basemodel="" mn="" cn="集成操作日志-重试" rac="true" vom="3" nssd="true" isolate="1" IsAsynLstDesc="true">

    <div id="fbillhead" el="51" pk="fid" tn="t_si_operationlogretry" pn="fbillhead" cn="集成操作日志">

        <input type="datetime" id="fcreatedate" el="119" ek="fbillhead" fn="fcreatedate" pn="fcreatedate" cn="创建日期" width="130" visible="-1" copy="0" xlsin="0" lix="251" />

        <input group="基本信息" el="106" ek="FBillHead" id="fmyobjectid" fn="fmyobjectid" pn="fmyobjectid" visible="-1" cn="业务对象"
               lock="-1" copy="1" lix="0" notrace="false" ts="" refid="sys_bizobject" filter="" reflvt="0" dfld="" width="140" />

        <input group="基本信息" el="100" ek="FBillHead" id="fopapi" fn="fopapi" pn="fopapi" visible="-1" cn="操作接口"
               lock="-1" copy="1" lix="0" notrace="false" ts="" width="200" />

        <input group="基本信息" el="100" ek="FBillHead" id="fopname" fn="fopname" pn="fopname" visible="-1" cn="操作名称"
               lock="-1" copy="1" lix="0" notrace="false" ts="" width="200" />

        <!--操作状态-->
        <input group="基本信息" el="152" ek="FBillHead" id="fopstatus" fn="fopstatus" pn="fopstatus" visible="-1" cn="操作状态"
               lock="-1" copy="1" lix="0" notrace="false" ts="" vals="1:'处理中',2:'成功',3:'出错'" width="75" />

        <!--错误来源-->
        <input group="基本信息" el="152" ek="FBillHead" id="ferrorsource" fn="ferrorsource" pn="ferrorsource" visible="-1" cn="错误来源"
               lock="-1" copy="1" lix="0" notrace="false" ts="" vals="1:'我方',2:'对方'" width="75" />

        <input group="基本信息" el="100" ek="FBillHead" id="fdescription" fn="fdescription" pn="fdescription" visible="-1" cn="备注"
               lock="-1" copy="1" lix="0" notrace="false" ts="" width="200" len="800" />

        <input group="基本信息" el="113" ek="FBillHead" id="foptime" fn="foptime" pn="foptime" visible="-1" cn="操作时间"
               lock="-1" copy="1" lix="0" notrace="false" ts="" width="105" />

        <input group="基本信息" el="152" ek="FBillHead" id="foplogsource" fn="foplogsource" pn="foplogsource" visible="-1" cn="操作日志来源"
               lock="-1" copy="1" lix="0" notrace="false" ts="" width="75" vals="1:'我方',2:'对方'"></th>

        <!--增加企业隔离-->
        <input type="text" id="fmainorgid" el="148" ek="fbillhead" fn="fmainorgid" pn="fmainorgid" cn="企业主体" xlsin="0" visible="0" copy="0" />

        <input group="基本信息" el="100" ek="FBillHead" id="fsuccessnumbers" fn="fsuccessnumbers" pn="fsuccessnumbers" visible="-1" cn="成功对象编码"
               lock="-1" copy="1" lix="0" notrace="false" ts="" width="140" len="2000" />
        <input group="基本信息" el="100" ek="FBillHead" id="ffailnumbers" fn="ffailnumbers" pn="ffailnumbers" visible="-1" cn="失败对象编码"
               lock="-1" copy="1" lix="0" notrace="false" ts="" width="140" len="2000" />

        <input type="text" id="frequestdata" el="127" ek="fbillhead" fn="frequestdata" pn="frequestdata" cn="请求数据包" desc="仅记录出错情况，用于重复提交" width="120" visible="0" lix="12" />
        <input type="text" id="fcanretry" el="116" ek="fbillhead" fn="fcanretry" pn="fcanretry" cn="能否重试" width="120" visible="-1" lix="12" defval="false" />
        <input type="text" id="fisretry" el="116" ek="fbillhead" fn="fisretry" pn="fisretry" cn="是否已重试" width="120" visible="0" lix="12" />
    </div>

    <table id="fentity" el="52" pk="FEntryId" tn="t_si_operationlogentryretry" pn="fentity" cn="日志明细">
        <tr> 
            <th el="113" ek="fentity" id="flogtime" fn="flogtime" pn="flogtime" visible="454" cn="日志时间"
                lock="-1" copy="1" lix="0" notrace="false" ts="" width="105"></th> 

            <th el="127" ek="fentity" id="flogcontent" fn="flogcontent" pn="flogcontent" visible="454" cn="日志信息" xsslv="2" 
                lock="-1" copy="1" lix="0" notrace="false" ts="" width="800"></th>

            <th ek="fentity" el="144" id="foplist" fn="foplist" pn="foplist" cn="操作" btnid="detail" width="60" btntxt="详情" visible="454" lock="-1" align="center"></th>
        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="querydata" op="querydata" opn="查看" data="" permid="fw_view" useslave="true"></ul>
        <!-- <ul el="10" ek="fbillhead" id="query" op="query" opn="查看" data="" permid="fw_view" useslave="true"></ul> -->
        <ul el="10" ek="fbillhead" id="modify" op="modify" opn="修改" data="" permid="fw_modify" useslave="true"></ul>
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存" data="" permid="" useslave="true"></ul>
        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除" data="" permid="fw_delete" useslave="true">
            <li el="11" vid="510" ek="fbillhead" cn="同步状态为处理中无法删除"
                data="{'expr':'fopstatus!=\'1\'','message':'同步状态为处理中，无法删除！'}"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="retry" op="retry" opn="错误重试" data="" permid="fw_retry" useslave="true"></ul>
        <ul el="10" ek="fbillhead" id="listdatatoexcel" op="listdatatoexcel" opn="导出Excel" data="" permid="fw_export" useslave="true"></ul>
        <ul el="10" ek="fbillhead" id="operatelog" op="operatelog" opn="查看操作日志" data="" permid="fw_operatelog" useslave="true"></ul>
    </div>

    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <ul el="12" id="fw_view" cn="查看"></ul>
        <ul el="12" id="fw_delete" cn="删除"></ul>
        <ul el="12" id="fw_export" cn="导出"></ul>
        <ul el="12" id="fw_retry" cn="错误重试"></ul>
        <ul el="12" id="fw_operatelog" cn="查看操作日志"></ul>
    </div>

</body>
</html>