<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ydj_service" basemodel="bill_basetmpl" el="1" cn="服务单" ubl="1" cff="ydj_service_filter" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_service" pn="fbillhead" cn="服务单">
        <input group="基本信息" el="123" ek="fbillhead" visible="-1" id="fbilltypeid" fn="fbilltypeid" pn="fbilltypeid" refid="bd_billtype" cn="单据类型" lix="2" must="1" width="90" />
        <input group="基本信息" el="100" ek="fbillhead" id="oppid" fn="oppid" pn="oppid" visible="0" cn="中台ID" />
        <!--此处服务类型不加默认值，会影响选单后值变动，默认放到js init绑定-->
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fservicetype" fn="fservicetype" pn="fservicetype" cn="服务类型" cg="服务类型" defval="'fres_type_02'" refid="bd_enum" dfld="fenumitem" must="1" lix="5"></select>
        <input group="基本信息" el="113" type="datetime" id="fservicedate" ek="fbillhead" fn="fservicedate" pn="fservicedate" cn="预约时间" visible="-1" must="1" lix="10" width="160" />
        <input group="基本信息" el="104" type="text" id="fexpectamount" ek="fbillhead" fn="fexpectamount" ts="" cn="服务金额" visible="-1" lock="-1" lix="30" />
        <input group="基本信息" el="106" ek="fbillhead" id="fagentid" fn="fagentid" pn="fagentid" cn="招商经销商" cg="招商经销商" dfld="fcityid" refid="ms_crmdistributor" filter="fstate='0'" visible="-1" must="1" lix="15" />
        <!--<input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fauthcity" fn="fauthcity" pn="fauthcity" cn="授权城市" refid="ydj_city" dfld="fname" lix="16" must="1" />-->
        <input group="基本信息" el="107" ek="fbillhead" visible="1150" id="fcustomernumber" fn="fcustomernumber" pn="fcustomernumber" cn="客户编码" ctlfk="fcustomerid" dispfk="fnumber" lix="14" />
        <input group="基本信息" el="106" ek="fbillhead" id="fcustomerid" fn="fcustomerid" pn="fcustomerid" cn="客户" cg="客户" dfld="fcusnature" refid="ydj_customer" visible="-1" must="1" lix="15" />
        <input group="基本信息" el="116" type="checkbox" id="fisdonecus" ek="fbillhead" fn="fisdonecus" ts="" defval="'0'" visible="-1" cn="成交客户" lix="15" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fmasterid" fn="fmasterid" pn="fmasterid" cn="服务人员" refid="ydj_staff" filter="(fbiztype='6' or fbiztype='0')" dfld="fname,fphone" lix="12" copy="0" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fpromisorid" fn="fpromisorid" pn="fpromisorid" cn="代约人" refid="ydj_staff" dfld="fname,fphone" lix="12" copy="0" lock="-1" />
        <input group="基本信息" el="100" type="text" id="fphone" ek="fbillhead" fn="fphone" pn="fphone" ctlfk="fstaffid" dispfk="fphone" ts="" visible="-1" cn="服务人员电话" lix="4" width="85" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fteamid" fn="fteamid" pn="fteamid" cn="服务团队" cg="服务团队" refid="ydj_dept" lix="20" />
        <input group="基本信息" el="107" ek="fbillhead" visible="1150" id="fdeptnumber" fn="fdeptnumber" pn="fdeptnumber" cn="部门编码" ctlfk="fdeptid" dispfk="fnumber" lix="24" />
        <input group="基本信息" el="106" ek="fbillhead" visible="1150" id="fdeptid" fn="fdeptid" pn="fdeptid" cn="销售部门" ctlfk="forderno" refid="ydj_dept" lix="25" />
        <input group="基本信息" el="106" ek="fbillhead" visible="1150" id="forderno" fn="forderno" pn="forderno" refid="ydj_order" cn="合同编号" lix="35" />
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fserstatus" fn="fserstatus" pn="fserstatus" copy="0" cn="服务状态" cg="服务状态" ts="" refid="bd_enum" dfld="fenumitem" defval="'sersta01'" lix="8" copy="0" lock="-1"></select>
        <input group="基本信息" el="100" type="text" id="fattention" fn="fattention" pn="fattention" cn="注意事项" visible="-1" len="500" width="250" lix="261" />
        <input group="基本信息" el="100" type="text" id="fcancelreason" fn="fcancelreason" pn="fcancelreason" cn="取消原因" visible="-1" len="200" width="250" lix="262" />

        <input group="基本信息" el="165" ek="fbillhead" visible="0" copy="0" id="fsourceinterid_h" fn="fsourceinterid" pn="fsourceinterid" cn="源单id(上游为出库单时对应的出库单上游合同id)" lix="60" />
        <input group="基本信息" el="100" type="text" id="fcusnature" ek="fbillhead" fn="fcusnature" ts="" visible="0" cn="客户性质" lix="40" />
        <input group="物流信息" el="100" type="text" id="fcollectrel" ek="fbillhead" fn="fcollectrel" ts="" visible="1150" cn="收货人" must="1" lix="40" />
        <input group="物流信息" el="100" type="text" id="fcollectpho" ek="fbillhead" fn="fcollectpho" ts="" visible="-1" cn="联系电话" must="1" width="100" lix="45" />
        <input group="基本信息" el="100" type="text" id="fcusphone" ek="fbillhead" fn="fcusphone" ts="" visible="0" cn="客户手机号" width="100" lix="45" />
        <input group="基本信息" el="100" type="text" id="fcustomername" ek="fbillhead" fn="fcustomername" ts="" visible="0" cn="客户名称" lix="40" />
        <select group="物流信息" el="122" ek="fbillhead" visible="36" id="fprovince" fn="fprovince" pn="fprovince" cn="省" cg="省" refid="bd_enum" dfld="fenumitem"></select>
        <select group="物流信息" el="122" ek="fbillhead" visible="36" id="fcity" fn="fcity" pn="fcity" cn="市" cg="市" refid="bd_enum" dfld="fenumitem"></select>
        <select group="物流信息" el="122" ek="fbillhead" visible="36" id="fregion" fn="fregion" pn="fregion" cn="区" cg="区" refid="bd_enum" dfld="fenumitem"></select>
        <input group="物流信息" el="100" type="text" id="fcollectadd" ek="fbillhead" fn="fcollectadd" ts="" visible="-1" cn="详细地址" lix="50" />
        <input group="后台字段" el="100" type="text" id="fzbcollectadd" ek="fbillhead" fn="fzbcollectadd" copy="1" ts="" visible="0" cn="总部详细省市区地址" lix="50" />

        <input group="图片附件" el="135" id="fimage" ek="fbillhead" fn="fimage" pn="fimage" cn="图片附件" visible="96" copy="0" />
        <input group="基本信息" el="100" id="fztimage" type="text" ek="fbillhead" fn="fztimage" pn="fztimage" cn="中台图片" lock="-1" visible="0" copy="0" len="4000" />
        <input group="基本信息" el="113" type="datetime" id="fclosedate" ek="fbillhead" fn="fclosedate" pn="fclosedate" copy="0" cn="关闭时间" visible="0" />
        <select group="基本信息" el="152" ek="fbillhead" id="fsourcecancl" fn="fsourcecancl" pn="fsourcecancl" visible="-1" copy="0" cn="来源渠道" vals="'0':'总部下发','1':'自建'" defval="'1'" lix="8" lock="-1"></select>
        <select group="基本信息" el="152" ek="fbillhead" id="freservtype" fn="freservtype" pn="freservtype" visible="-1" copy="0" cn="预约类型" vals="'0':'自约','1':'代约'" lix="8" lock="-1"></select>
        <select group="基本信息" el="152" ek="fbillhead" id="flogisticsstatus" fn="flogisticsstatus" pn="flogisticsstatus" visible="-1" copy="0" cn="物流状态" vals="'0':'总部已发货','1':'已到货'" defval="'0'" lix="8" lock="-1"></select>


        <!-- 售后问题 -->
        <select group="售后问题" el="122" ek="fbillhead" visible="-1" id="fquestiontype" fn="fquestiontype" pn="fquestiontype" cn="问题类别" cg="售后问题类别" refid="bd_enum" dfld="fenumitem"></select>
        <input group="售后问题" el="100" type="text" id="fquestiondesc" fn="fquestiondesc" pn="fquestiondesc" cn="问题描述" visible="-1" len="500" />
        <input group="售后问题" el="135" id="fquestionimage" ek="fbillhead" fn="fquestionimage" pn="fquestionimage" copy="0" cn="问题图片" visible="96" />

        <!-- 内部处理结论 -->
        <select group="处理结论" el="122" ek="fbillhead" visible="-1" id="finstitutiontype" fn="finstitutiontype" pn="finstitutiontype" cn="责任单位类型" cg="责任单位类型" refid="bd_enum" dfld="fenumitem"></select>
        <input group="处理结论" id="fdutysupplierid" el="106" ek="fbillhead" fn="fdutysupplierid" ts="" visible="96" cn="责任单位(供应商)" filter="ftype='suppliertype_01'" refid="ydj_supplier" lix="100" />
        <input group="处理结论" id="fdutycustomerid" el="106" ek="fbillhead" fn="fdutycustomerid" ts="" visible="96" cn="责任单位(客户)" refid="ydj_customer" lix="105" />
        <input group="处理结论" id="fdutystaffid" el="106" ek="fbillhead" fn="fdutystaffid" ts="" visible="96" cn="责任单位(员工)" refid="ydj_staff" lix="110" />
        <input group="处理结论" id="fdutydeptid" el="106" ek="fbillhead" fn="fdutydeptid" ts="" visible="96" cn="责任单位(部门)" refid="ydj_dept" />
        <select group="处理结论" el="122" id="fhandleconclusion" fn="fhandleconclusion" pn="fhandleconclusion" ek="fbillhead" visible="-1" cn="内部处理结论" lix="38" cg="内部处理结论" refid="bd_enum" dfld="fenumitem"></select>
        <input group="处理结论" el="100" type="text" id="fscheme" ek="fbillhead" fn="fscheme" cn="内部处理方式" visible="96" len="500" />

        <!--完工信息-->
        <input group="完工信息" el="113" type="datetime" id="ffinishdate" ek="fbillhead" fn="ffinishdate" pn="ffinishdate" copy="0" cn="完工时间" visible="1150" lix="55" />
        <input group="完工信息" el="100" type="text" id="ffinishremark" fn="ffinishremark" pn="ffinishremark" cn="完工说明" copy="0" visible="1150" len="500" lix="60" />
        <input group="完工信息" el="135" id="fsignconfirmimg" ek="fbillhead" fn="fsignconfirmimg" pn="fsignconfirmimg" copy="0" cn="签字确认单" visible="96" />
        <input group="完工信息" el="135" type="text" id="fdoneimage" ek="fbillhead" width="200" fn="fdoneimage" copy="0" cn="现场图片图" visible="96" />
        <input group="完工信息" el="116" type="checkbox" id="fisrepurchase" ek="fbillhead" fn="fisrepurchase" copy="0" cn="是否有复购商机" visible="-1" />

        <input group="基本信息" el="100" type="text" id="fdescription" fn="fdescription" cn="备注" visible="0" len="500" width="200" />

        <!--总部创建固定信息-->
        <input group="基本信息" el="100" type="text" id="fzbcreatephone" fn="fzbcreatephone" cn="总部创建人电话" visible="0" width="200" />
        <input group="基本信息" el="100" type="text" id="fzbcreatename" fn="fzbcreatename" cn="总部创建人" visible="0" width="200" />
        <input group="基本信息" el="113" type="text" id="fzbcreatedate" fn="fzbcreatedate" cn="总部创建时间" visible="0" width="200" />

        <!-- 是否退回，主要作用，避免点击退回按钮时，退回请求协同到中台速度和定时任务同步请求并发情况下，先发送退回而导致后续的定时任务发送false标记再给到中台 -->
        <input group="基本信息" el="116" type="checkbox" id="fisreturn" ek="fbillhead" fn="fisreturn" ts="" defval="'0'" visible="0" cn="是否退回" lix="15" />

        <input group="协同信息" el="152" ek="fbillhead" id="fmusisyncstatus" fn="fmusisyncstatus" pn="fmusisyncstatus" visible="0" cn="慕思协同状态" copy="0" ts="" vals=" '01':'待协同','02':'协同成功','03':'协同失败'" defval="'01'" lix="8" />
        <input group="协同信息" el="113" ek="fbillhead" type="datetime" id="fmusisyncdate" fn="fmusisyncdate" pn="fmusisyncdate" ts="" visible="0" cn="慕思协同日期" lix="6" copy="0" />

        <input group="基本信息" el="116" type="checkbox" id="fisgoldstewardcard" ek="fbillhead" fn="fisgoldstewardcard" pn="fisgoldstewardcard"
               defval="'0'" visible="-1" cn="是否金管家尊享卡" width="120" lix="800" lock="-1" />
        <!-- 一件代发 -->
        <input group="基本信息" el="116" ek="fbillhead" id="fpiecesendtag" fn="fpiecesendtag" pn="fpiecesendtag" visible="1150" cn="一件代发标记" copy="0" lix="500" notrace="false" ts="" defval="false" canchange="false" lock="-1" />
        <input group="服务信息" el="106" ek="fbillhead" visible="-1" id="fstoreid" fn="fstoreid" pn="fstoreid" cn="门店名称" cg="门店名称" refid="ser_store"  lock="-1" copy="0"/>
        <!--<input group="完工信息" el="100" type="text" id="freporter" ek="fbillhead" fn="freporter" ts="" cn="操作人" visible="32" lock="-1" copy="0" />-->
        <!--<input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fdeptid_link" fn="fdeptid_link" pn="fdeptid_link" cn="关联部门" cg="关联部门" refid="ydj_dept" lix="25" />-->
        <!--<input group="基本信息" type="number" id="fpreaccount" el="102" ek="FBillHead" fn="fpreaccount" ts="" cn="新加结算金额" visible="0" lix="35" />-->
        <!--<input group="基本信息" el="113" type="datetime" id="fservicetime" ek="fbillhead" fn="fservicetime" ts="" cn="服务时间" defval="@currentDate" visible="-1" lix="8" width="160" />-->
        <!--<input group="基本信息" el="113" type="datetime" id="fsettletime" ek="fbillhead" fn="fsettletime" ts="" cn="结算时间" visible="-1" lix="8" width="160" />-->
        <!-- 隐藏商户、商户单号 -->
        <!--<input group="基本信息" el="106" ek="fbillhead" visible="0" id="fdealerid" fn="fdealerid" pn="fdealerid" cn="商户" cg="商户" refid="ydj_customer" lix="5" width="120" />
    <input group="基本信息" el="100" type="text" id="fmerbill" ek="fbillhead" fn="fmerbill" ts="" visible="0" cn="商户单号" />-->
        <!-- 新增销售部门、客户字段展示 -->
        <!--<select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fsettlestatus" fn="fsettlestatus" pn="fsettlestatus" cn="结算状态" cg="结算状态" lock="-1" ts="" refid="bd_enum" dfld="fenumitem" defval="'settle_status01'" lix="50" copy="0"></select>-->
        <!--<input group="基本信息" el="106" type="text" id="fcaptainid" ek="fbillhead" fn="fcaptainid" ts="" refid="ydj_master" cn="队长" visible="32" lock="-1" copy="0" />-->
        <!--<input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fstaffid_link" fn="fstaffid_link" pn="fstaffid_link" cn="关联账号" cg="关联账号" refid="sec_user" lix="25" />-->
        <!--<input group="基本信息" el="100" type="text" id="fevaluate" ek="fbillhead" fn="fevaluate" len="500" ts="" visible="44" cn="商户评价" />-->
        <!--<input group="服务信息" el="106" ek="fbillhead" visible="0" id="fbrandid" fn="fbrandid" pn="fbrandid" cn="品牌" cg="品牌" refid="ydj_brand" />-->
        <!--<select group="服务信息" el="122" ek="fbillhead" visible="36" id="fprovince" fn="fprovince" pn="fprovince" cn="省" cg="省" refid="bd_enum" dfld="fenumitem"></select>
    <select group="服务信息" el="122" ek="fbillhead" visible="36" id="fcity" fn="fcity" pn="fcity" cn="市" cg="市" refid="bd_enum" dfld="fenumitem"></select>
    <select group="服务信息" el="122" ek="fbillhead" visible="36" id="fregion" fn="fregion" pn="fregion" cn="区" cg="区" refid="bd_enum" dfld="fenumitem"></select>-->
        <!--<input group="服务信息" el="100" type="text" id="fdescription" fn="fdescription" cn="备注" visible="32" len="1000" width="200" />-->
        <!--<input group="服务信息" el="100" type="text" len="200" id="fwechatcode" fn="fwechatcode" cn="微信二维码" visible="0" copy="0" />
    <input group="服务信息" el="116" type="text" id="fisschedule" fn="fisschedule" cn="添加日程" visible="0" copy="0" />-->
        <!--<input group="服务信息" el="106" ek="fbillhead" visible="-1" id="fstoreid" fn="fstoreid" pn="fstoreid" cn="门店" cg="门店" refid="ser_store" />-->
        <!--<input group="基本信息" el="100" type="text" id="fsourcebillnum" fn="fsourcebillnum" pn="fsourcebillnum" visible="-1" cn="源单编号" lock="-1" copy="0" />-->
        <!--<input group="服务信息" el="122" type="text" id="fstatus" visible="32" />-->
        <!--师傅信息-->
        <!--<input group="师傅信息" el="104" type="text" id="fcashamount" ek="fbillhead" fn="fcashamount" pn="fcashamount" ts="" cn="已结算金额" visible="32" lock="-1" copy="0" />-->
        <!--<input group="师傅信息" el="104" type="text" id="frealamount" ek="fbillhead" fn="frealamount" ts="" cn="实际结算金额" visible="32" lock="-1" />-->
        <!--<input group="师傅信息" el="104" type="text" id="fnotamount" ek="fbillhead" fn="fnotamount" ts="" cn="未结算金额" visible="32" lock="-1" />-->
        <!--<input group="师傅信息" el="113" type="datetime" id="fbespeakdate" ek="fbillhead" fn="fbespeakdate" ts="" cn="确认预约时间" visible="32" />-->
        <!--商品详情-->
        <!--<input group="商品详情" el="135" type="text" id="fproimage" width="200" fn="fproimage" cn="商品图片" visible="0" len="2000" />-->
        <!--完工信息-->
        <!--<input group="完工信息" el="113" type="datetime" id="freportdate" ek="fbillhead" fn="freportdate" ts="" cn="完工时间" visible="32" lock="-1" copy="0" />
    <input group="完工信息" el="135" type="text" id="fdoneimage" ek="fbillhead" width="200" fn="fdoneimage" cn="现场图片图" visible="0" copy="0" lock="-1" />
    <input group="完工信息" el="135" type="text" id="ftectonicimage" ek="fbillhead" width="200" fn="ftectonicimage" cn="内部构造图" visible="0" copy="0" lock="-1" />
    <input group="完工信息" el="135" type="text" id="fgroupimage" ek="fbillhead" width="200" fn="fgroupimage" cn="安装合影图" visible="0" copy="0" lock="-1" />
    <input group="完工信息" el="100" type="text" id="freporter" ek="fbillhead" fn="freporter" ts="" cn="操作人" visible="32" lock="-1" copy="0" />
    <input group="完工信息" el="100" id="fnote" fn="fnote" ek="fbillhead" pn="fnote" cn="完工说明" visible="32" copy="0" lock="-1" len="1000" />-->
        <!--客户评价-->
        <!--<select group="评价信息" el="122" ek="fbillhead" visible="32" id="fqualstar" fn="fqualstar" pn="fqualstar" cn="评分" cg="评分" refid="bd_enum" dfld="fenumitem" copy="0" lock="-1"></select>
    <select group="评价信息" el="125" len="500" ek="fbillhead" visible="-1" id="fqual" fn="fqual" pn="fqual" cn="评级项目" cg="评级项目" refid="bd_enum" dfld="fenumitem" copy="0" lock="-1"></select>
    <input group="评价信息" el="100" pn="fevaludesc" id="fevaludesc" fn="fevaludesc" ek="fbillhead" cn="评价说明" visible="32" copy="0" lock="-1" />-->
        <!--排程信息-->
        <!--<input group="排程信息" el="100" ek="fbillhead" id="fschedulebillno" fn="fschedulebillno" pn="fschedulebillno" visible="-1" cn="排程编号"
       lock="-1" copy="0" lix="10" notrace="true" ts="" />-->
        <!-- 总部处理结论 -->
        <!-- 该单据体数据以根据服务单单据编号从下游售后反馈单查询为准 -->
        <table id="fhqhandleentry" el="52" pk="fentryid" tn="t_ydj_hqhandleconclusion" pn="fhqhandleentry" kfks="fbillno_e,fmaterialid_e" cn="总部处理结论">
            <tr>
                <th lix="100" el="106" ek="fhqhandleentry" id="fbillno_e" fn="fbillno_e" pn="fbillno_e" width="160" visible="1150" cn="单据编号" refid="ste_afterfeedback" dfld="fbillno" lock="-1"></th>
                <th lix="100" el="107" ek="fhqhandleentry" id="fdate_e" fn="fdate_e" pn="fdate_e" visible="1150" cn="创建时间" lock="-1" copy="0" notrace="true" ts="" ctlfk="fbillno_e" dispfk="fcreatedate" refvt="0" len="150"></th>
                <th lix="100" el="107" ek="fhqhandleentry" id="fmtrlnumber_e" fn="fmtrlnumber_e" pn="fmtrlnumber_e" visible="1150" cn="商品编码" lock="-1" copy="0" notrace="true" ts="" ctlfk="fmaterialid_e" dispfk="fnumber" refvt="0"></th>
                <th lix="100" el="106" ek="fhqhandleentry" id="fmaterialid_e" fn="fmaterialid_e" pn="fmaterialid_e" cn="商品" refid="ydj_product" width="160" visible="1150" lock="-1"></th>
                <th lix="100" el="107" ek="fhqhandleentry" id="fquestiontype_e" fn="fquestiontype_e" pn="fquestiontype_e" visible="1150" cn="问题类别" lock="-1" copy="0" notrace="true" ts="" ctlfk="fbillno_e" dispfk="fquestiontype" refvt="0"></th>
                <th lix="100" el="107" ek="fhqhandleentry" id="fquestiondesc_e" fn="fquestiondesc_e" pn="fquestiondesc_e" visible="1150" cn="问题描述" lock="-1" copy="0" notrace="true" ts="" ctlfk="fbillno_e" dispfk="fquestiondesc" refvt="0"></th>
                <th lix="100" el="107" ek="fhqhandleentry" id="ffeedstatus_e" fn="ffeedstatus_e" pn="ffeedstatus_e" visible="1150" cn="售后状态" lock="-1" copy="0" notrace="true" ts="" ctlfk="fbillno_e" dispfk="ffeedstatus" refvt="0"></th>
                <th lix="100" el="107" ek="fhqhandleentry" id="fhqhandleconclusion_e" fn="fhqhandleconclusion_e" pn="fhqhandleconclusion_e" visible="1150" cn="总部处理结论" lock="-1" copy="0" notrace="true" ts="" ctlfk="fbillno_e" dispfk="fhqhandleconclusion" refvt="0"></th>
                <th lix="100" el="107" ek="fhqhandleentry" id="fhqscheme_e" fn="fhqscheme_e" pn="fhqscheme_e" visible="1150" cn="总部处理方式" lock="-1" copy="0" notrace="true" ts="" ctlfk="fbillno_e" dispfk="fhqscheme" refvt="0"></th>

                <!--<th el="140" ek="fhqhandleentry" id="fsourceformid_e" fn="fsourceformid_e" ts="" cn="来源单类型" visible="0" copy="0" lix="150"></th>
            <th el="141" ek="fhqhandleentry" id="fsourcebillno_e" fn="fsourcebillno_e" ts="" cn="来源单编号" visible="0" copy="0" lix="153"></th>
            <th el="100" ek="fhqhandleentry" id="fsourceinterid_e" fn="fsourceinterid_e" ts="" cn="来源单内码" visible="0" copy="0" lix="155"></th>
            <th el="100" ek="fhqhandleentry" id="fsourceentryid_e" fn="fsourceentryid_e" ts="" cn="来源单分录内码" visible="0" copy="0" lix="157"></th>-->
            </tr>
        </table>

        <!--服务（明细）-->
        <table id="fserviceentry" el="52" pk="fentryid" tn="t_ydj_servicedetail" pn="fserviceentry" kfks="fseritemid" cn="服务信息">
            <tr>
                <th lix="100" el="108" ek="fserviceentry" id="fnumber_e" fn="fnumber" visible="0" cn="行编码" lock="-1" copy="0"></th>
                <th el="106" ek="fserviceentry" id="fseritemid" fn="fseritemid" pn="fseritemid" cn="服务项目" refid="ydj_serviceitem" sformid="" visible="1150" width="200"></th>
                <th el="106" ek="fserviceentry" id="funitid" fn="funitid" pn="funitid" cn="单位" refid="ydj_unit" ctlfk="fseritemid" sformid="" visible="1150" width="100" lock="-1"></th>
                <th el="104" ek="fserviceentry" id="fprice" fn="fprice" pn="fprice" ts="" cn="单价" visible="1150" width="130" format="0,000.00"></th>
                <th el="102" ek="fserviceentry" id="fqty" fn="fqty" pn="fqty" cn="数量" visible="1150" width="90" defVal="1" format="0,000.00"></th>
                <th el="105" ek="fserviceentry" id="famount" fn="famount" pn="famount" ts="" cn="金额" visible="1150" width="140" format="0,000.00" lock="-1"></th>
                <!--<th el="100" ek="fserviceentry" id="frequire" fn="frequire" pn="frequire" cn="要求" visible="0" width="200"></th>-->
                <!--补价时携带到变更单上，系统内部使用-->
                <!--<th el="104" ek="fserviceentry" id="forderprice" fn="forderprice" pn="forderprice" visible="0" cn="商户下单价"
                lock="-1" copy="1" lix="0" notrace="true" ts="" roundType="0" format="0,000.00"></th>
            <th el="140" ek="fproductentry" id="fsourceformid" fn="fsourceformid" ts="" cn="来源单类型" visible="1062" copy="0" lix="150"></th>
            <th el="141" ek="fproductentry" id="fsourcebillno" fn="fsourcebillno" ts="" cn="来源单编号" visible="1062" copy="0" lix="153"></th>
            <th el="100" ek="fproductentry" id="fsourceinterid" fn="fsourceinterid" ts="" cn="来源单内码" visible="0" copy="0" lix="155"></th>
            <th el="100" ek="fproductentry" id="fsourceentryid" fn="fsourceentryid" ts="" cn="来源单分录内码" visible="0" copy="0" lix="157"></th>-->
            </tr>
        </table>
        <!--商品（明细）-->
        <table id="fproductentry" el="52" pk="fentryid" tn="t_ydj_serviceproduct" pn="fproductentry" kfks="fmaterialid,fqty_e" cn="商品信息">
            <tr>
                <th lix="100" el="108" ek="fproductentry" id="fpnumber_e" fn="fnumber" visible="0" cn="行编码" lock="-1" copy="0"></th>
                <th lix="100" el="107" ek="fproductentry" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="1150" cn="商品编码" lock="-1" copy="0" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fnumber" refvt="0"></th>
                <th lix="105" el="106" ek="fproductentry" id="fmaterialid" fn="fmaterialid" pn="fmaterialid" cn="商品" refid="ydj_product" width="160" visible="1150"></th>
                <th lix="110" el="107" ek="fproductentry" visible="1150" id="fcategoryid" fn="fcategoryid" pn="fcategoryid" cn="商品类别" ctlfk="fmaterialid" dispfk="fcategoryid" lock="-1"></th>
                <th lix="115" el="107" ek="fproductentry" id="fbrandid" fn="fbrandid" cn="品牌" visible="1150" ctlfk="fmaterialid" dispfk="fbrandid" sformid="" width="100" lock="-1"></th>
                <th lix="120" el="107" ek="fproductentry" id="fseriesid" fn="fseriesid" cn="系列" visible="1150" ctlfk="fmaterialid" dispfk="fseriesid" sformid="" width="100" lock="-1"></th>
                <th lix="122" el="107" ek="fproductentry" id="fsubseriesid" fn="fsubseriesid" cn="子系列" visible="1086" ctlfk="fmaterialid" dispfk="fsubseriesid" sformid="" width="100" lock="-1"></th>
                <th lix="125" el="100" ek="fproductentry" id="fattrinfo" fn="fattrinfo" cn="辅助属性" width="160" visible="1150" lock="-1" len="1000"></th>
                <th el="100" ek="fproductentry" len="2000" id="fcustomdesc" fn="fcustomdesc" cn="定制说明" lix="130" width="160" visible="1150" lock="-1"></th>
                <th lix="135" el="109" ek="fproductentry" id="funitid_e" fn="funitid" cn="基本单位" ctlfk="fmaterialid" refid="ydj_unit" sformid="" filter="fisbaseunit='1'" visible="1150" width="80" lock="-1"></th>
                <th lix="140" el="103" ek="fproductentry" id="fqty_e" fn="fqty" cn="数量" visible="1150" width="130"></th>
                <th el="105" ek="fproductentry" id="famount_e" fn="famount" cn="金额" lix="141" width="90" visible="1150" format="0,000.00"></th>
                <th lix="142" el="107" ek="fproductentry" id="fspecifica" fn="fspecifica" cn="规格型号" visible="1150" ctlfk="fmaterialid" dispfk="fspecifica" width="100" lock="-1"></th>
                <!-- 如果上游单据为销售出库单，那么此处以下内码将为出库单对应上游单据内码，因需反写操作流程，故此处已此为配置，而不记录实际上游内码 -->
                <th el="140" ek="fproductentry" id="fsourceformid" fn="fsourceformid" ts="" cn="来源单类型" visible="0" copy="0" lix="150"></th>
                <th el="141" ek="fproductentry" id="fsourcebillno" fn="fsourcebillno" ts="" cn="来源单编号" visible="0" copy="0" lix="153"></th>
                <th el="100" ek="fproductentry" id="fsourceinterid" fn="fsourceinterid" ts="" cn="来源单内码" visible="0" copy="0" lix="155"></th>
                <th el="100" ek="fproductentry" id="fsourceentryid" fn="fsourceentryid" ts="" cn="来源单分录内码" visible="0" copy="0" lix="157"></th>

                <th id="fseltypeid" el="107" ek="fproductentry" fn="fseltypeid" ctlfk="fmaterialid" dispfk="fseltypeid" ts="" cn="型号" visible="1086" lix="460"></th>

                <!--E3相关商品信息字段-->
                <th lix="99" el="100" ek="fproductentry" id="fecmtrlnumber" fn="fecmtrlnumber" pn="fecmtrlnumber" visible="1150" cn="电商商品编码" width="120" lock="-1" copy="0" ts=""></th>
                <th lix="99" el="100" ek="fproductentry" id="fecmtrlname" fn="fecmtrlname" pn="fecmtrlname" visible="1150" cn="电商商品名称" width="120" lock="-1" copy="0" ts=""></th>
                <th lix="99" el="100" ek="fproductentry" id="fecmtrlspecifica" fn="fecmtrlspecifica" pn="fecmtrlspecifica" visible="1150" cn="电商商品规格型号" width="120" lock="-1" copy="0" ts=""></th>
                <th lix="99" el="116" ek="fproductentry" id="fecisreturn" fn="fecisreturn" pn="fecisreturn" cn="是否退货" visible="1150" lock="-1" copy="0" canchange="true"></th>
                <th lix="99" el="112" ek="fproductentry" id="fecreturndate" fn="fecreturndate" pn="fecreturndate" cn="退货日期" visible="1150" lock="-1" copy="0"></th>
                <th lix="99" el="100" ek="fproductentry" id="fecreturnreason" fn="fecreturnreason" pn="fecreturnreason" cn="退货原因" visible="1150" lock="-1" len="1000"></th>
            </tr>
        </table>
        <!--<input group="物流信息" el="106" ek="fbillhead" visible="-1" id="fcarid" fn="fcarid" pn="fcarid" cn="车辆类型" cg="车辆类型" refid="ser_truckinfo" />
    <input group="物流信息" type="number" id="fceper" el="102" ek="FBillHead" fn="fceper" ts="" cn="物流公里" visible="-1" lix="35" />
    <input group="物流信息" el="100" type="text" id="fcollectadd" ek="fbillhead" fn="fcollectadd" ts="" visible="-1" cn="提货点" />
    <input group="物流信息" el="100" type="text" id="fcollectrel" ek="fbillhead" fn="fcollectrel" ts="" visible="-1" cn="提货联系人" />
    <input group="物流信息" el="100" type="text" id="fcollectpho" ek="fbillhead" fn="fcollectpho" ts="" visible="-1" cn="提货电话" />
    <input group="物流信息" el="116" type="checkbox" ek="FBillHead" id="fisupstairs" fn="fisupstairs" pn="fisupstairs" cn="需抬楼" visible="96" />
    <input group="物流信息" el="116" type="checkbox" ek="FBillHead" id="fistransport" fn="fistransport" pn="fistransport" cn="需搬运" visible="96" />-->
        <!--<input group="服务信息" el="100" ek="fbillhead" type="text" id="fname" fn="fname" pn="fname" ts="" visible="-1" cn="业主名称" lix="6" />
    <input group="服务信息" el="100" type="text" id="fphone" ek="fbillhead" fn="fphone" ts="" visible="-1" cn="联系电话" lix="7" width="110" />
    <input group="服务信息" el="100" ek="fbillhead" visible="32" id="faddress" fn="faddress" pn="faddress" cn="详细地址" />-->
        <!--<input group="物流信息" el="100" type="text" id="fname" ek="fbillhead" fn="fname" ts="" visible="-1" cn="业主名称" />
    <input group="物流信息" el="100" type="text" id="fphone" ek="fbillhead" fn="fphone" ts="" visible="-1" cn="业主电话" />
    <input group="物流信息" el="100" type="text" id="faddress" ek="fbillhead" fn="faddress" ts="" visible="-1" cn="目的地" />-->
        <!--历史预约（明细）-->
        <table id="fhistoryentry" el="52" pk="fentryid" tn="t_ydj_historyorder" pn="fhistoryentry" copy="0" cn="历史预约">
            <tr>
                <!--<th el="116" ek="fhistoryentry" id="fissuccess" fn="fissuccess" pn="fissuccess" cn="预约成功" width="65" visible="96" copy="0"></th>-->
                <th el="113" ek="fhistoryentry" id="fappointdate" fn="fappointdate" pn="fappointdate" ts="" cn="预约时间" visible="1150" copy="0" lock="-1"></th>
                <th el="100" ek="fhistoryentry" id="fappointdesc" fn="fappointdesc" pn="fappointdesc" cn="预约备注" width="200" len="500" visible="1150" copy="0" lock="-1"></th>
                <th el="100" ek="fhistoryentry" id="faddress" fn="faddress" pn="faddress" cn="预约详细地址" width="200" len="200" visible="1150" copy="0" lock="-1"></th>
                <th el="100" ek="fhistoryentry" id="foperationid" fn="foperationid" pn="foperationid" cn="操作人" visible="1150" copy="0" lock="-1"></th>
                <th el="113" ek="fhistoryentry" id="foperationdate" fn="foperationdate" pn="foperationdate" ts="" cn="操作时间" visible="1150" copy="0" lock="-1"></th>
            </tr>
        </table>
        <!--<table id="fcashentry" el="52" pk="fentryid" tn="t_ydj_writeoffentry" pn="fcashentry" cn="核销明细">
    <tr>
        <th el="102" ek="fcashentry" id="fwriteoffamount" fn="fwriteoffamount" pn="fwriteoffamount" visible="-1" cn="核销金额" width="150"
            lock="-1" copy="0" lix="200" notrace="true" ts="" roundType="0" format="0,000.00"></th>
        <th el="100" ek="fcashentry" id="fwriteoffbillno" fn="fwriteoffbillno" pn="fwriteoffbillno" visible="-1" cn="提现交易单号" width="200"
            lock="-1" copy="0" lix="0" notrace="true" ts=""></th>
    </tr>
    </table>-->
        <!--【服务类型】=”增值“显示-->
        <table id="fvistzzentry" el="52" pk="fentryid" tn="t_ydj_vistzzentry" pn="fvistzzentry" copy="0" cn="回访详情-增值">
            <tr>
                <th lix="1" el="100" ek="fvistzzentry" id="fbillnozz" fn="fbillno" pn="fbillnozz" visible="-1" cn="单据编码" lock="-1" copy="0"></th>
                <th lix="5" el="113" ek="fvistzzentry" id="fvistdatezz" fn="fvistdate" pn="fvistdatezz" cn="回访日期" visible="-1" lock="-1" copy="0"></th>
                <th lix="10" el="122" ek="fvistzzentry" id="fstatuszz" fn="fstatus" pn="fstatuszz" cn="数据状态" visible="-1" lock="-1" copy="0"></th>
                <th lix="15" el="122" ek="fvistzzentry" id="fvistmodezz" fn="fvistmode" pn="fvistmodezz" cn="回访方式" cg="回访方式" refid="bd_enum" dfld="fenumitem" visible="-1" lock="-1" copy="0"></th>
                <th lix="20" el="106" ek="fvistzzentry" id="fstaffidzz" fn="fstaffid" pn="fstaffidzz" cn="回访人员" refid="ydj_staff" dfld="fname" visible="-1" lock="-1" copy="0"></th>
                <th lix="25" el="106" ek="fvistzzentry" id="fdeptidzz" fn="fdeptid" pn="fdeptidzz" cn="回访部门" refid="ydj_dept" dfld="fname" visible="-1" lock="-1" copy="0"></th>
                <th lix="30" el="122" ek="fvistzzentry" id="fservicescorezz" fn="fservicescore" pn="fservicescorezz" cn="服务评价" cg="整体评分" refid="bd_enum" dfld="fenumitem" visible="-1" lock="-1" copy="0"></th>
                <th lix="35" el="100" ek="fvistzzentry" id="fvistresultzz" fn="fvistresult" pn="fvistresultzz" cn="评价内容" visible="-1" lock="-1" copy="0" len="500"></th>
                <th lix="40" el="100" ek="fvistzzentry" id="fcustomercomplaintzz" fn="fcustomercomplaint" pn="fcustomercomplaintzz" cn="投诉建议" visible="-1" lock="-1" copy="0" len="500"></th>
                <th lix="45" el="152" ek="fvistzzentry" vals="'0':'否','1':'是'" id="fisrecommendzz" fn="fisrecommend" pn="fisrecommendzz" cn="是否愿意推荐我们的产品" width="200" visible="-1" lock="-1" copy="0"></th>
                <th lix="50" el="100" ek="fvistzzentry" id="fdescriptionzz" fn="fdescription" pn="fdescriptionzz" cn="备注" visible="-1" lock="-1" copy="0" len="500"></th>
                <th lix="55" el="100" ek="fvistzzentry" id="fevalphonezz" fn="fevalphone" pn="fevalphonezz" ts="" cn="评价人手机号" visible="1086"></th>
                <th lix="60" el="113" ek="fvistzzentry" id="fevaldatezz" fn="fevaldate" pn="fevaldatezz" ts="" cn="评价时间" visible="1086"></th>
            </tr>
        </table>
        <!--【服务类型】=”送装“显示-->
        <table id="fvistszentry" el="52" pk="fentryid" tn="t_ydj_vistszentry" pn="fvistszentry" copy="0" cn="回访详情-送装">
            <tr>
                <th lix="1" el="100" ek="fvistszentry" id="fbillnosz" fn="fbillno" pn="fbillnosz" visible="-1" cn="单据编码" lock="-1" copy="0"></th>
                <th lix="5" el="113" ek="fvistszentry" id="fvistdatesz" fn="fvistdate" pn="fvistdatesz" cn="回访日期" visible="-1" lock="-1" copy="0"></th>
                <th lix="10" el="122" ek="fvistszentry" id="fstatussz" fn="fstatus" pn="fstatussz" cn="数据状态" visible="-1" lock="-1" copy="0"></th>
                <th lix="15" el="122" ek="fvistszentry" id="fvistmodesz" fn="fvistmode" pn="fvistmodesz" cn="回访方式" cg="回访方式" refid="bd_enum" dfld="fenumitem" visible="-1" lock="-1" copy="0"></th>
                <th lix="20" el="106" ek="fvistszentry" id="fstaffidsz" fn="fstaffid" pn="fstaffidsz" cn="回访人员" refid="ydj_staff" dfld="fname" visible="-1" lock="-1" copy="0"></th>
                <th lix="25" el="106" ek="fvistszentry" id="fdeptidsz" fn="fdeptid" pn="fdeptidsz" cn="回访部门" refid="ydj_dept" dfld="fname" visible="-1" lock="-1" copy="0"></th>
                <th lix="30" el="122" ek="fvistszentry" id="fscoresz" fn="fscore" pn="fscoresz" cn="产品质量评价" width="150" cg="整体评分" refid="bd_enum" dfld="fenumitem" visible="-1" lock="-1" copy="0"></th>
                <th lix="35" el="122" ek="fvistszentry" id="fsalescoresz" fn="fsalescore" pn="fsalescoresz" cn="销售服务评价" width="150" cg="整体评分" refid="bd_enum" dfld="fenumitem" visible="-1" lock="-1" copy="0"></th>
                <th lix="40" el="122" ek="fvistszentry" id="finstallscoresz" fn="finstallscore" pn="finstallscoresz" cn="送装服务评价" width="150" cg="整体评分" refid="bd_enum" dfld="fenumitem" visible="-1" lock="-1" copy="0"></th>
                <th lix="45" el="100" ek="fvistszentry" id="fvistresultsz" fn="fvistresult" pn="fvistresultsz" cn="评价内容" visible="-1" lock="-1" copy="0" len="500"></th>
                <th lix="50" el="100" ek="fvistszentry" id="fcustomercomplaintsz" fn="fcustomercomplaint" pn="fcustomercomplaintsz" cn="投诉建议" visible="-1" lock="-1" copy="0" len="500"></th>
                <th lix="55" el="152" ek="fvistszentry" vals="'0':'否','1':'是'" id="fisrecommendsz" fn="fisrecommend" pn="fisrecommendsz" cn="是否愿意推荐我们的产品" width="200" visible="-1" lock="-1" copy="0"></th>
                <th lix="60" el="100" ek="fvistszentry" id="fdescriptionsz" fn="fdescription" pn="fdescriptionsz" cn="备注" visible="-1" lock="-1" copy="0" len="500"></th>
                <th lix="65" el="100" ek="fvistszentry" id="fevalphonesz" fn="fevalphone" pn="fevalphonesz" ts="" cn="评价人手机号" visible="1086"></th>
                <th lix="70" el="113" ek="fvistszentry" id="fevaldatesz" fn="fevaldate" pn="fevaldatesz" ts="" cn="评价时间" visible="1086"></th>
            </tr>
        </table>
        <!--【服务类型】=”售后“显示-->
        <table id="fvistshentry" el="52" pk="fentryid" tn="t_ydj_vistshentry" pn="fvistshentry" copy="0" cn="回访详情-售后">
            <tr>
                <th lix="1" el="100" ek="fvistshentry" id="fbillnosh" fn="fbillno" pn="fbillnosh" visible="-1" cn="单据编码" lock="-1" copy="0"></th>
                <th lix="5" el="113" ek="fvistshentry" id="fvistdatesh" fn="fvistdate" pn="fvistdatesh" cn="回访日期" visible="-1" lock="-1" copy="0"></th>
                <th lix="10" el="122" ek="fvistshentry" id="fstatussh" fn="fstatus" pn="fstatussh" cn="数据状态" visible="-1" lock="-1" copy="0"></th>
                <th lix="15" el="122" ek="fvistshentry" id="fvistmodesh" fn="fvistmode" pn="fvistmodesh" cn="回访方式" cg="回访方式" refid="bd_enum" dfld="fenumitem" visible="-1" lock="-1" copy="0"></th>
                <th lix="20" el="106" ek="fvistshentry" id="fstaffidsh" fn="fstaffid" pn="fstaffidsh" cn="回访人员" refid="ydj_staff" dfld="fname" visible="-1" lock="-1" copy="0"></th>
                <th lix="25" el="106" ek="fvistshentry" id="fdeptidsh" fn="fdeptid" pn="fdeptidsh" cn="回访部门" refid="ydj_dept" dfld="fname" visible="-1" lock="-1" copy="0"></th>
                <th lix="30" el="100" ek="fvistshentry" id="fvistresultsh" fn="fvistresult" pn="fvistresultsh" cn="评价内容" visible="-1" lock="-1" copy="0" len="500"></th>
                <th lix="35" el="100" ek="fvistshentry" id="fcustomercomplaintsh" fn="fcustomercomplaint" pn="fcustomercomplaintsh" cn="投诉建议" visible="-1" lock="-1" copy="0" len="500"></th>
                <th lix="40" el="152" ek="fvistshentry" vals="'0':'否','1':'是'" id="fintimesh" fn="fintime" pn="fintimesh" cn="售后是否有及时联系" width="200" visible="-1" lock="-1" copy="0"></th>
                <th lix="45" el="152" ek="fvistshentry" vals="'0':'否','1':'是'" id="fissatisfysh" fn="fissatisfy" pn="fissatisfysh" cn="售后师傅处理服务是否满意" width="200" visible="-1" lock="-1" copy="0"></th>
                <th lix="50" el="100" ek="fvistshentry" id="fdescriptionsh" fn="fdescription" pn="fdescriptionsh" cn="备注" visible="-1" lock="-1" copy="0" len="500"></th>
                <th lix="55" el="100" ek="fvistshentry" id="fevalphonesh" fn="fevalphone" pn="fevalphonesh" ts="" cn="评价人手机号" visible="1086"></th>
                <th lix="60" el="113" ek="fvistshentry" id="fevaldatesh" fn="fevaldate" pn="fevaldatesh" ts="" cn="评价时间" visible="1086"></th>
            </tr>
        </table>
    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。'fprovince','fcity','fregion','fcollectadd',-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="MSSaveSync" op="MSSaveSync" opn="慕思协同保存" data="{'syncFieldIds':['fcancelreason','fserstatus','fprovince','fcity','fregion','fattrinfo','fcustomdesc','fcancelstatus','fbillno','fservicetype','fservicedate','fagentid','forderno','fattention','fcustomerid','fzbcollectadd','fcollectrel','fcollectpho','fisdonecus','fseritemid','fnumber_e','fprice','fqty','famount','fmaterialid','fqty_e','famount_e','fpnumber_e','fimage','fztimage','fpromisorid','freservtype']}" permid=""></ul>
        <ul el="10" id="setstatus01" op="setstatus" opn="派单" data="" permid="ydj_setstatus01"></ul>
        <ul el="10" id="setstatus02" op="setstatus" opn="取消派单" data="" permid="ydj_setstatus02"></ul>
        <ul el="10" id="setstatus03" op="setstatus" opn="立即预约" data="" permid="ydj_setstatus03"></ul>
        <ul el="10" id="setstatus04" op="setstatus" opn="完工汇报" data="" permid="ydj_setstatus04">
            <!--<li el="17" sid="2004" cn="反写关联流程" data="{
            'executeType':'add'
            }"></li>-->
        </ul>
        <ul el="10" id="pushaftermanager" op="pushaftermanager" opn="转总部" data="{'parameter':{'ruleId':'ydj_service2ste_afterfeedback'}}" permid="ydj_push" ubl="1"></ul>
        <ul el="10" id="serviceclose" op="serviceclose" opn="服务关闭" data="" permid="ydj_serviceclose" ubl="1"></ul>
        <ul el="10" id="cancelservice" op="cancelservice" opn="取消服务" data="" permid="ydj_cancelservice"></ul>
        <ul el="10" id="servicevist" op="servicevist" opn="回访" data="" permid="ydj_servicevist"></ul>
        <ul el="10" id="transfer" op="transfer" opn="转单" data="" permid="fw_transfer"></ul>
        <ul el="10" id="servisit" op="servisit" opn="邀请评价" data="" permid="fw_servisit"></ul>
        <ul el="10" id="followerrecord" op="followerrecord" opn="跟进" data="" permid="fw_followerrecord"></ul>
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存">
            <li el="17" sid="1004" cn="变更生效执行"></li>
            <li el="11" vid="517" id="save_valid_sourcebill" cn="保存时校验源单明细行是否存在于源单中"
                data="{'sourceTypeFieldKey':'fsourceformid','sourceNoFieldKey':'fsourcebillno','sourceEntryIdFieldKey':'fsourceentryid'}" precon=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="savesubmit" op="savesubmit" opn="保存并提交" data="" permid="fw_savesubmit"></ul>
        <ul el="10" ek="fbillhead" id="saveaudit" op="saveaudit" opn="保存并审核" data="" permid="fw_saveaudit"></ul>
        <!--如果此处做了处理记得作废问下需求是否需要已完工或已关闭才能作废<ul el="10" ek="fbillhead" id="cancel" op="cancel" opn="作废">
            <li el="17" sid="2004" cn="反写关联流程" data="{
                'executeType':'remove'
                }"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="uncancel" opn="反作废">
            <li el="17" sid="2004" cn="反写关联流程" data="{
                'executeType':'add'
                }"></li>
        </ul>-->
        <ul el="10" ek="fbillhead" id="aftersalesprocessing" op="aftersalesprocessing" opn="售后处理" data="" permid="fw_aftersalesprocessing"></ul>
    </div>

   

    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <ul el="12" id="ydj_setstatus01" cn="派单"></ul>
        <ul el="12" id="ydj_setstatus02" cn="取消派单"></ul>
        <ul el="12" id="ydj_setstatus03" cn="立即预约"></ul>
        <ul el="12" id="ydj_setstatus04" cn="完工汇报"></ul>
        <ul el="12" id="ydj_push" cn="转总部"></ul>
        <ul el="12" id="ydj_serviceclose" cn="服务关闭"></ul>
        <ul el="12" id="ydj_cancelservice" cn="取消服务"></ul>
        <ul el="12" id="ydj_servicevist" cn="回访"></ul>
        <ul el="12" id="fw_transfer" cn="转单"></ul>
        <ul el="12" id="fw_servisit" cn="邀请评价"></ul>
        <ul el="12" id="fw_followerrecord" cn="跟进"></ul>
        <ul el="12" id="fw_savesubmit" cn="保存并提交"></ul>
        <ul el="12" id="fw_saveaudit" cn="保存并审核"></ul>
        <ul el="12" id="fw_aftersalesprocessing" cn="售后处理"></ul>
    </div>

</body>
</html>