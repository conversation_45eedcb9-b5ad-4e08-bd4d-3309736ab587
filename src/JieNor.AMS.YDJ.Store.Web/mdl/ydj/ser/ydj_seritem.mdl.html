<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ydj_seritem" basemodel="bd_basetmpl" el="3" cn="服务项目" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_seritem" pn="fbillhead" cn="服务项目">

        <!--基本信息-->
        <input group="基本信息" el="100" id="fname" ek="fbillhead" cn="服务名称" lix="3" />
        <input group="基本信息" el="122" ek="fbillhead" visible="0" id="fservicecate" fn="fservicecate" pn="fservicecate" cn="服务类目" cg="服务类目" refid="bd_enum" sformid="" dfld="fenumitem" lix="10"/>
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fservicetype" fn="fservicetype" pn="fservicetype" cn="服务类目" cg="服务类目" refid="ydj_service_item" lix="10" width="120"  />
        <!--自动生成的标记，通常用于关联至车辆类型，用于计算运输费，正常录单时不应该被选择到-->
        <input type="checkbox" id="fautogen" el="116" ek="FBillHead" fn="fautogen" ts="" defvalue="'0'" visible="-1" cn="物流专用" lix="15"/>


        <input group="基本信息" el="109" ek="fbillhead" visible="-1" id="funitid" fn="funitid" pn="funitid" cn="单位" refid="ydj_unit" lix="2" />
        <input group="基本信息" type="number" id="fsellprice" el="102" ek="FBillHead" fn="fsellprice" ts="" cn="售价" lix="30" />
        <input group="基本信息" type="number" id="fcostprice" el="102" ek="FBillHead" fn="fcostprice" ts="" cn="成本价"  lix="30" />
    	
    </div>
</body>
</html>