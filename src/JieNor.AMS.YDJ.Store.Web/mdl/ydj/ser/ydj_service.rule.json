{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [
    ////服务状态 != (待派单,拒单待审核,申请重新派单) 时，所有字段不可编辑
    //{
    //  "id": "fserstatus_2",
    //  "expression": "field:*|fserstatus!='sersta01' and fserstatus!='sersta03' and fserstatus!='sersta10'"
    //},
    //服务状态 = 待评价 时，完工时间不可以编辑
    {
      "id": "fserstatus_04",
      "expression": "field:ffinishdate|fserstatus=='sersta04'"
    },
    //服务状态 = 已关闭 时，完工时间不可以编辑
    {
      "id": "fserstatus_05",
      "expression": "field:ffinishdate|fserstatus=='sersta05'"
    },
    //服务状态 = (已取消/已关闭) 时，所有字段不可以编辑
    {
      "id": "fserstatus_06",
      "expression": "field:*|fserstatus=='sersta0' or fserstatus=='sersta06'"
    },
    //此项规则表示：单据状态=A 或 B 或 C（关联暂存，创建，重新审核）时，所有字段可用，审核 反审核 撤销 操作不可用，其他操作可用
    {
      "id": "fstatus_ABC",
      "expression": "field:$*;menu:tbAudit,tbUnaudit,tbUnsubmit$*|fstatus=='A' or fstatus=='B' or fstatus=='C'"
    },
    //此项规则表示：单据状态=D（已提交）时，所有字段不可用，提交 选单 操作不可用，其他操作可用
    {
      "id": "fstatus_D",
      "expression": "field:$*;menu:tbSubmit,tbPull$*|fstatus=='D'"
    },
    //此项规则表示：单据状态=E（已审核）时，所有字段不可用，审核 提交 选单 操作不可用，其他操作可用
    {
      "id": "fstatus_E",
      "expression": "field:$*;menu:tbSubmit,tbAudit,tbPull,tbSaveSubmit,tbSaveAudit$*|fstatus=='E'"
    },
    //保存并提交锁定锁定&解锁
    {
      "id": "lock_tbSaveSubmit",
      "expression": "menu:tbSaveSubmit|fstatus=='D' or fstatus=='E'"
    },
    {
      "id": "unlock_tbSaveSubmit",
      "expression": "menu:$tbSaveSubmit|fstatus!='D' and fstatus!='E'"
    },
    //此项规则表示：单据状态='' 时(新增的时候)，所有字段可用，只有 新增 保存 新增负责人 替换负责人 移除负责人 操作可用，其他操作都不可用
    {
      "id": "fstatus_",
      "expression": "menu:*$tbNew,tbSave,btnAddDuty,btnReplaceDuty,btnRemoveDuty,tbSaveSubmit,tbSaveAudit,tbPull|fstatus==''"
    }

    //{ //服务单.服务状态=待师傅确认 的订单允许修改单价、图片、注意事项
    //  "id": "fserstatus_3",
    //  "expression": "field:$fprice,fproimage,fattention|fserstatus=='sersta02'"
    //},
    ////源单编号不为空 锁定 商户和订单号
    //{
    //  "id": "lockdealer",
    //  "expression": "field:fservicetype,fmerbill,fname,fphone,fseritemid,fprice,fqty,fdealernumber,fdealerid|fsourcebillnum!='' and fsourcebillnum!=' '"
    //},
    ////锁定关联店员
    //{
    //  "id": "lockfstaffid_link",
    //  "expression": "field:fstaffid_link|1==1"
    //}
  ],

  //定义表单可见性规则
  "visibleRules": [

    //服务状态 ==（待派单，待确认，拒单待审核，待预约，待完工）时，隐藏完工信息
    { "expression": "other:.y-completework|fserstatus=='sersta01' or fserstatus=='sersta02'" },
    //服务状态 !=（待派单，待确认，拒单待审核，待预约，待完工）时，显示完工信息
    { "expression": "other:$.y-completework|fserstatus!='sersta01' and fserstatus!='sersta02'" },

    //服务状态非（待派单且来源渠道为总部下发），那么隐藏退回按钮
    { "expression": "other:[menu='returnback']$|fserstatus!='sersta01' or fsourcecancl!='0'" },
    //服务状态为待派单且来源渠道为总部下发，那么显示退回按钮
    { "expression": "other:$[menu='returnback']|fserstatus=='sersta01' and fsourcecancl=='0'" },

    //取消原因为空则不显示
    { "expression": "other:.cancelreason$|fcancelreason==''" },
    //取消原因不为空则显示
    { "expression": "other:$.cancelreason|fcancelreason!=''" },

    //服务类型 ==（增值或送装）时，隐藏 售后问题 和 内部处理结论 两个区块
    { "expression": "other:.y-afrersaleproblem,.y-internalhandleconclusion|fservicetype=='fres_type_01' or fservicetype=='fres_type_02'" },
    //服务类型 ==（售后）时，显示 售后问题 和 内部处理结论 两个区块
    { "expression": "other:$.y-afrersaleproblem,.y-internalhandleconclusion|fservicetype=='fres_type_03'" },

    //仅当【服务类型】=“售后”，才显示<关闭>按钮。
    { "expression": "other:[menu='serviceclose']$|fservicetype!='fres_type_03'" },
    { "expression": "other:$[menu='serviceclose']|fservicetype=='fres_type_03'" },

    //控制去js了
    //《总部处理结论》表体有数据显示（即服务单已转售后反馈单）,否则隐藏
    //{ "expression": "other:.y-hqhandleconclusion|fbillno_e==''" },
    //{ "expression": "other:$.y-hqhandleconclusion|fbillno_e!=''" },

    //选择供应商隐藏规则
    {
      "id": "show_dutysupplier",
      "expression": "other:$.y-dutysupplier| finstitutiontype == 'dutyunit_type_01'"
    },
    {
      "id": "hide_dutysupplier",
      "expression": "other:.y-dutysupplier| finstitutiontype != 'dutyunit_type_01'"
    },
    //选择客户隐藏规则
    {
      "id": "show_dutycustomer",
      "expression": "other:$.y-dutycustomer| finstitutiontype == 'dutyunit_type_02'"
    },
    {
      "id": "hide_dutycustomer",
      "expression": "other:.y-dutycustomer| finstitutiontype != 'dutyunit_type_02'"
    },
    //选择员工隐藏规则
    {
      "id": "show_dutystaff",
      "expression": "other:$.y-dutystaff| finstitutiontype == 'dutyunit_type_03'"
    },
    {
      "id": "hide_dutystaff",
      "expression": "other:.y-dutystaff| finstitutiontype != 'dutyunit_type_03'"
    },
    //选择部门隐藏规则
    {
      "id": "show_dutydept",
      "expression": "other:$.y-dutydept| finstitutiontype == 'dutyunit_type_04'"
    },
    {
      "id": "hide_dutydept",
      "expression": "other:.y-dutydept| finstitutiontype != 'dutyunit_type_04'"
    }

    ////服务状态 ==（待评价）时，隐藏评价信息
    //{ "expression": "other:.y-evaluate|fserstatus=='sersta05'" },
    ////服务状态 ==（待回访，待结算，关闭）时，显示评价信息
    //{ "expression": "other:$.y-evaluate|fserstatus=='sersta07' or fserstatus=='sersta08' or fserstatus=='sersta11'" }

    //控制去js了
    //{ "expression": "other:.y-vistsh,.y-vistsz,.y-vistzz|id=='' or id==' ' "},
    ////回访详情显示--增值
    //{ "expression": "other:.y-vistsh,.y-vistsz$.y-vistzz|id!='' and id!=' ' and fservicetype=='fres_type_02'" },
    ////回访详情显示--送装
    //{ "expression": "other:.y-vistsh,.y-vistzz$.y-vistsz|id!='' and id!=' ' and fservicetype=='fres_type_01'" },
    ////回访详情显示--售后
    //{ "expression": "other:.y-vistzz,.y-vistsz$.y-vistsh|id!='' and id!=' ' and fservicetype=='fres_type_03'" }
  ],

  //定义表单计算规则
  "calcRules": [
    { "expression": "funitid_e=fmaterialid__funitid|1==1" },
    { "expression": "funitid=fseritemid__funit|1==1" },
    { "expression": "famount=fqty*fprice| 1==1" },
    //{ "expression": "fcollectrel=fcustomerid__fcontacts| 1==1" },
    //{ "expression": "fcollectpho=fcustomerid__fphone| 1==1" },
    //{ "expression": "fprovince=fcustomerid__fprovince| 1==1" },
    //{ "expression": "fcity=fcustomerid__fcity| 1==1" },
    //{ "expression": "fregion=fcustomerid__fregion| 1==1" },
    //{ "expression": "fcollectadd=fcustomerid__faddress| 1==1" },
    { "expression": "fcusnature=fcustomerid__fcusnature| 1==1" },
    { "expression": "fdeptid=forderno__fdeptid| 1==1" },
    //{ "expression": "fstaffid_link=fmasterid__fstaffid_link|fmasterid!='' or 1==1" }
    { "expression": "fteamid=fmasterid__fdeptid|fmasterid!='' or 1==1" },

    { "expression": "fphone=fmasterid__fphone" }
    
    //控制去了js
    //,

    //{ "expression": "fbilltypeid ='ydj_service_billtype_02'|fservicetype=='fres_type_01'" },
    //{ "expression": "fbilltypeid ='ydj_service_billtype_03'|fservicetype=='fres_type_02'" },
    //{ "expression": "fbilltypeid ='ydj_service_billtype_04'|fservicetype=='fres_type_03'" },

    //{ "expression": "fservicetype='fres_type_01'|fbilltypeid =='ydj_service_billtype_02'" },
    //{ "expression": "fservicetype='fres_type_02'|fbilltypeid =='ydj_service_billtype_03'" },
    //{ "expression": "fservicetype='fres_type_03'|fbilltypeid =='ydj_service_billtype_04'" }
  ]
}