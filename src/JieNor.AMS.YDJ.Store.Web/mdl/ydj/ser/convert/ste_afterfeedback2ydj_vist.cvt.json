{
  "Id": "ste_afterfeedback2ydj_vist",
  "Number": "ste_afterfeedback2ydj_vist",
  "Name": "售后反馈单下推回访单",
  "SourceFormId": "ste_afterfeedback",
  "TargetFormId": "ydj_vist",
  "ActiveEntityKey": "fproductentry",
  "Visible": false,
  "FilterString": "ffeedstatus='aft_service_05' or ffeedstatus='aft_service_06'",
  "Message": "回访失败：【单据/上游单据】售后反馈单售后状态必须是已完成或已关闭状态！",
  "FieldMappings": [
    {
      "Id": "fagentid",
      "Name": "招商经销商",
      "MapType": 0,
      "SrcFieldId": "fagentid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //{
    //  "Id": "fauthcity",
    //  "Name": "授权城市",
    //  "MapType": 0,
    //  "SrcFieldId": "fauthcity",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    {
      "Id": "fmainorgid",
      "Name": "组织",
      "MapType": 0,
      "SrcFieldId": "fmainorgid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fservicetype",
      "Name": "服务类型",
      "MapType": 1,
      "SrcFieldId": "'fres_type_03'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forderdeptid",
      "Name": "销售部门",
      "MapType": 0,
      "SrcFieldId": "fdeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forderno",
      "Name": "合同编号",
      "MapType": 0,
      "SrcFieldId": "forderno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fvistmode",
      "Name": "回访方式",
      "MapType": 1,
      "SrcFieldId": "'vist_canal_01'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomerid",
      "Name": "客户",
      "MapType": 0,
      "SrcFieldId": "fcustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcontacts",
      "Name": "联系人",
      "MapType": 0,
      "SrcFieldId": "flinkstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },

    {
      "Id": "fphone",
      "Name": "联系电话",
      "MapType": 0,
      "SrcFieldId": "flinkmobile",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprovince",
      "Name": "省",
      "MapType": 0,
      "SrcFieldId": "fprovince",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcity",
      "Name": "市",
      "MapType": 0,
      "SrcFieldId": "fcity",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fregion",
      "Name": "区",
      "MapType": 0,
      "SrcFieldId": "fregion",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "faddress",
      "Name": "联系地址",
      "MapType": 0,
      "SrcFieldId": "flinkaddress",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fzbaddress",
      "Name": "总部联系地址",
      "MapType": 0,
      "SrcFieldId": "fzblinkaddress",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ste_afterfeedback'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },

    //商品信息字段
    {
      "Id": "fmaterialid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 15
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 16
    },
    {
      "Id": "fcustomdesc",
      "Name": "定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdesc",
      "MapActionWhenGrouping": 0,
      "Order": 17
    },
    {
      "Id": "funitid_e",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid_e",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fqty_e",
      "Name": "基本单位实发数量",
      "MapType": 0,
      "SrcFieldId": "fqty_e",
      "MapActionWhenGrouping": 0,
      "Order": 19
    },
    {
      "Id": "fsourceformid",
      "Name": "来源单类型",
      "MapType": 1,
      "SrcFieldId": "'ste_afterfeedback'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno",
      "Name": "来源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fproductentry.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }

  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [

  ]
}