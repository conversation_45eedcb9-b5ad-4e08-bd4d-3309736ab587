{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "lock_fownerid",
      "expression": "field:fownerid$|fownertype=='' or fstatus=='D' or fstatus=='E'"
    },
    {
      "id": "unlock_fownerid",
      "expression": "field:$fownerid|fownertype!='' and fstatus!='D' and fstatus!='E'"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    //库存状态=仓库.默认库存状态
    { "expression": "fstockstatus=fstorehouseid__fstockid" },
    //选择商品，携带单位与计价单位
    { "expression": "funitid=fmaterialid__funitid|fmaterialid=='' or 1==1" },
    //携带库存单位=基本单位
    { "expression": "fstockunitid=fmaterialid__fstockunitid|fmaterialid=='' or 1==1" },
    //设置数量默认为1
    { "expression": "fstockqty=1|fmaterialid!='' and fstockqty<=0" },
    //计价数量变化时计算金额
    { "expression": "famount=fqty*fprice|fprice!='' or fqty!='' or 1==1" },
    { "expression": "fprice=famount/fqty|fqty!=0 and fqty!=''" },
    //选择商品时，携带出默认辅助属性
    { "expression": "fattrinfo=getAuxPropValue(fmaterialid)" },
    { "expression": "fmtrlimage=getImages(fmaterialid,fattrinfo,fcustomdesc)" }
  ]
}