<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）

    库存关账
-->
<html lang="en">
<head>
</head>
<body id="stk_invcloseaccount" el="0" cn="关账/反关账" rac="true" isolate="1" nfsa="true">
    <div id="fbillhead" el="51" pk="fid" tn="" pn="fbillhead" cn="单据头">
        <input group="基本信息" el="112" ek="FBillHead" id="fclosedate" fn="fclosedate" pn="fclosedate" visible="-1" cn="关账日期"
               lock="0" copy="0" lix="0" notrace="true" ts="" />
        <input group="基本信息" el="112" ek="FBillHead" id="flastclosedate" fn="flastclosedate" pn="flastclosedate" visible="-1" cn="上一次关账日期"
               lock="-1" copy="0" lix="0" notrace="true" ts="" />

        <input group="基本信息" el="100" id="flastcalc" ek="fbillhead" ts="" cn="最近成本计算" lock="-1" visible="-1" lix="12" width="95" />

    </div>

    <div id="opList">
        <ul el="10" ek="fbillhead" id="new" op="new" opn="关账/反关账" data="" permid="fw_new"></ul>
    </div>
    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <ul el="12" id="fw_new" cn="关账/反关账"></ul>
    </div>
</body>
</html>