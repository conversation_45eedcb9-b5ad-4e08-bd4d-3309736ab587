{
  "Id": "ydj_leads2ydj_customerrecord",
  "Number": "ydj_leads2ydj_customerrecord",
  "Name": "销售线索转换成销售机会",
  "SourceFormId": "ydj_leads",
  "TargetFormId": "ydj_customerrecord",
  "ActiveEntityKey": "fbillhead",
  "FilterString": "fleadsstatus='leads_status_01' or fleadsstatus='leads_status_02'",
  "Message": "线索转商机失败：<br>1、业务状态必须为【未分配】或【已分配】！",
  "VisibleEx": 1,
  "FieldMappings": [

    //表头字段
    {
      "Id": "fcustomername",
      "Name": "客户名称",
      "MapType": 0,
      "SrcFieldId": "fname",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fphone",
      "Name": "联系方式",
      "MapType": 0,
      "SrcFieldId": "fphone",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fwechat",
      "Name": "微信",
      "MapType": 0,
      "SrcFieldId": "fwechat",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fprovince",
      "Name": "省",
      "MapType": 0,
      "SrcFieldId": "fprovince",
      "MapActionWhenGrouping": 0,
      "Order": 2
    },
    {
      "Id": "fcity",
      "Name": "市",
      "MapType": 0,
      "SrcFieldId": "fcity",
      "MapActionWhenGrouping": 0,
      "Order": 3
    },
    {
      "Id": "fregion",
      "Name": "区",
      "MapType": 0,
      "SrcFieldId": "fregion",
      "MapActionWhenGrouping": 0,
      "Order": 4
    },
    {
      "Id": "faddress",
      "Name": "详细地址",
      "MapType": 0,
      "SrcFieldId": "faddress",
      "MapActionWhenGrouping": 0,
      "Order": 5
    },
    {
      "Id": "fbuildingid",
      "Name": "楼盘",
      "MapType": 0,
      "SrcFieldId": "fbuildingid",
      "MapActionWhenGrouping": 0,
      "Order": 5
    },
    {
      "Id": "fdeptid",
      "Name": "所属门店",
      "MapType": 0,
      "SrcFieldId": "fdeptid",
      "MapActionWhenGrouping": 0,
      "Order": 6
    },
    {
      "Id": "fdutyid",
      "Name": "负责人",
      "MapType": 0,
      "SrcFieldId": "fdutyid",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fcustomersource",
      "Name": "客户来源",
      "MapType": 1,
      "SrcFieldId": "fcustomersource",
      "MapActionWhenGrouping": 0,
      "Order": 8
    },
    {
      "Id": "fleadssource",
      "Name": "源线索",
      "MapType": 1,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 9
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_leads'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fchannelid",
      "Name": "外部渠道",
      "MapType": 1,
      "SrcFieldId": "fchannelid",
      "MapActionWhenGrouping": 0,
      "Order": 10
    },
    {
      "Id": "fleadssourceid",
      "Name": "源线索id",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 9
    },
    {
      "Id": "fchancestatus",
      "Name": "业务状态",
      "MapType": 1,
      "SrcFieldId": "'chance_status_02'",
      "MapActionWhenGrouping": 0,
      "Order": 10
    }
  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [

  ]
}