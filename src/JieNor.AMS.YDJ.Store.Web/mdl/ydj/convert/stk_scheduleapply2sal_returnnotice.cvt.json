{
  "Id": "stk_scheduleapply2sal_returnnotice",
  "Number": "stk_scheduleapply2sal_returnnotice",
  "Name": "排单申请单转销售退货通知单",
  "SourceFormId": "stk_scheduleapply",
  "TargetFormId": "sal_returnnotice",
  "ActiveEntityKey": "fentity",
  "relationFieldKey": "fsourceentryid",
  "FilterString": "fqty>fscheduleqty and fstatus='E'",
  "Message": "排单失败：<br>1、排单申请单必须是已审核状态！<br>2、至少要有一行商品明细没有完全排单(实排数量-已排数量>0)！",
  "FieldMappings": [
    {
      "Id": "fbilltype",
      "Name": "单据类型",
      "MapType": 1,
      "SrcFieldId": "'soreturnnotice_type_01'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsostaffid",
      "Name": "销售员",
      "MapType": 0,
      "SrcFieldId": "fsostaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsodeptid",
      "Name": "销售部门",
      "MapType": 0,
      "SrcFieldId": "fsodeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "freturntype",
      "Name": "退货类型",
      "MapType": 1,
      "SrcFieldId": "'sostockreturn_biztype_01'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdate",
      "Name": "申请日期",
      "MapType": 0,
      "SrcFieldId": "fstockdateto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstaffid",
      "Name": "收货人",
      "MapType": 0,
      "SrcFieldId": "fstockstaffidto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockdeptid",
      "Name": "收货部门",
      "MapType": 0,
      "SrcFieldId": "fstockdeptidto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstaffphone",
      "Name": "收货人电话",
      "MapType": 1,
      "SrcFieldId": "fstockstaffidto.fphone",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockaddress",
      "Name": "收货人地址",
      "MapType": 0,
      "SrcFieldId": "fstockaddressto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomerid",
      "Name": "客户",
      "MapType": 0,
      "SrcFieldId": "fcustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkstaffid",
      "Name": "发货人",
      "MapType": 1,
      "SrcFieldId": "fstockstaffid.fname",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkmobile",
      "Name": "发货人电话",
      "MapType": 0,
      "SrcFieldId": "fstockstaffid.fphone",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkaddress",
      "Name": "发货人地址",
      "MapType": 0,
      "SrcFieldId": "fstockaddress",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },

    //明细字段
    {
      "Id": "fmaterialid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfoto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomdesc",
      "Name": "定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdescto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizunitid",
      "Name": "销售单位",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fqty",
      "Name": "实退数量",
      "MapType": 0,
      "SrcFieldId": "fqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fplanqty",
      "Name": "应退数量",
      "MapType": 0,
      "SrcFieldId": "fqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprice",
      "Name": "单价",
      "MapType": 0,
      "SrcFieldId": "fprice",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },

    {
      "Id": "famount",
      "Name": "金额",
      "MapType": 0,
      "SrcFieldId": "famount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorehouseid",
      "Name": "仓库",
      "MapType": 0,
      "SrcFieldId": "fstorehouseidto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockunitid",
      "Name": "库存单位",
      "MapType": 0,
      "SrcFieldId": "fstockunitid",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fstockstatus",
      "Name": "库存状态",
      "MapType": 0,
      "SrcFieldId": "fstockstatusto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtono",
      "Name": "物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtonoto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownertype",
      "Name": "货主类型",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownerid",
      "Name": "货主",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fentrynote",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fentrynote",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'stk_scheduleapply'",
      "MapActionWhenGrouping": 0,
      "Order": 6
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编码",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fsourceformid",
      "Name": "来源单类型",
      "MapType": 1,
      "SrcFieldId": "'stk_scheduleapply'",
      "MapActionWhenGrouping": 0,
      "Order": 6
    },
    {
      "Id": "fsourcebillno",
      "Name": "来源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fsourceinterid",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fsourceinterid_h",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fsourceentryid",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtrlimage",
      "Name": "图片",
      "MapType": 0,
      "SrcFieldId": "fmtrlimage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdescription",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fdescription",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }

  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [

  ]
}