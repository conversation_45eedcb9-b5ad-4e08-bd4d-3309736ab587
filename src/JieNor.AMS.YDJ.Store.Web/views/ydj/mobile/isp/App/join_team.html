<html>
<head>
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, user-scalable=no" />
    <title>加入团队</title>
    <link href="/fw/css/ydj/mobile/isp/App/ionic.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/isp/App/override.css" rel="stylesheet" />
</head>
<body>
    <div class="bar bar-header bar-white">
        <i class="bar-title-left icon ion-ios-arrow-back blue button button-icon" onclick="Andoggy.finishPage()"></i>
        <div class="title">加入团队</div>
    </div>
    <div class="content has-header marginTop44">
        <div class="team-icon">
            <img src="/fw/css/ydj/mobile/isp/App/images/userhead.png" width="90" />
            <p><span id="inviter"></span>邀请您加入队伍</p>
        </div>

        <div class="team-member">
            <div class="col50">
                <p>队伍</p>
                <p class="blue" id="tname"></p>
            </div>
            <div class="col50">
                <p>队伍人数</p>
                <p class="blue" id="tnum"></p>
            </div>
        </div>

        <div class="text-center submit-wrrap">
            <button class="btn-bg-blue" id="join">立即加入</button>
        </div>

    </div>
    
    <script src="/fw/include/jquery/jquery-1.11.3.min.js"></script>
    <script src="/fw/js/ydj/mobile/isp/App/iscroll.js"></script>
    <!--Reference the SignalR library.广播通信插件 -->
    <script src="/fw/include/signalr/jquery.signalR-2.2.2.js"></script>
    <!--Reference the autogenerated SignalR hub script. -->
    <script src="/signalr/hubs"></script>
    <script src="/fw/js/ydj/mobile/isp/App/Comm.js"></script>

    <script>
        var sender = GetQueryString('sender');
        var teamid = GetQueryString('teamid');
        if (teamid) {
            DomTag('inviter').innerText = sender;
            Ajax({
                url: "/bill/ydj_team?operationno=getdataByTid&format=json",
                data: {
                    fromId: "ydj_team",
                    operationNo: "getdataByTid",
                    simpleData: { Id: teamid }
                },
                callback: function () {
                    var Json = arguments[0];
                    if (String(Json.operationResult.isSuccess).Boolean()) {
                        var result = Json.operationResult.srvData;
                        if (result)
                        {
                            result = result.uidata;
                            DomTag('tname').innerText = result.fname;
                            DomTag('tnum').innerText = result.fstaffentry.length;
                        }
                        else
                        {
                            Message.Alert("该队伍已解散");
                        }
                    }
                }
            });
        }

        //连接消息通道并获取消息
        // Start the connection.
        $.connection.hub.start().done(function (r) {

        })
        .fail(function (e) {
            console.info(e);
        });

        var loghub = $.connection.log;
        var imhub = $.connection.ydjIMHub;
        var sql = new WebSql();
        var that;
        if (DomTag('join').getAttribute('class').indexOf('btn-bg-blue')>-1) {
            DomTag('join').onclick = function () {

                that = this;

                RemoveClass(that, 'btn-bg-blue');
                AddClass(that, 'btn-bg-grey');
                that.setAttribute('disabled', 'disabled');

                sql.Select("UserInfo", ["*"], null, function () {
                    var Data = arguments[0];
                    if (Data.length == 0) {
                        return;
                    }
                    Data = Data[0];
                    if (String(JSON.parse(Data["fapprovestatus"])["id"]).isNullOrEmpty() || JSON.parse(Data["fapprovestatus"])["id"] == 'auth1' || JSON.parse(Data["fapprovestatus"])["id"] == 'auth3') {
                        Message.Box('尚未通过实名认证请通过后重试', function () {
                            Redirect('/views/ydj/mobile/isp/App/certification.html');
                        }, '确定');
                    } else {

                        imhub.server.joinInTeam(teamid, true, localStorage.getItem('Id'));

                    }
                }, function () { alert(arguments[0]) });
            }
        } 

        imhub.client.onRecvError = function (errCode, error) {
            if (!error) {
                return;
            }

            setTimeout(function () {
                AddClass(that, 'btn-bg-blue');
                RemoveClass(that, 'btn-bg-grey');
                that.removeAttribute('disabled');
            }, 50000)

            Message.Box(error.Message, null, '确定');
        }

        imhub.client.onRecvSimpleMessage = function (type, messages) {
            if (!messages) {
                return;
            }
            Message.Box(messages, null, '确定');

            setTimeout(function () {
                AddClass(that, 'btn-bg-blue');
                RemoveClass(that, 'btn-bg-grey');
                that.removeAttribute('disabled');
            }, 50000)
        }

    </script>
</body>
</html>