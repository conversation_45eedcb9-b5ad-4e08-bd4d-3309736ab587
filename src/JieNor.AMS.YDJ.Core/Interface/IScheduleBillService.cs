using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 排单服务接口
    /// </summary>
    public interface IScheduleBillService
    {
        /// <summary>
        /// 检查是否已有排单记录，并把无排单记录的表单返回
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="dataEntities"></param>
        /// <param name="form"></param>
        /// <param name="schedulePlanBillEntityKey"></param>
        /// <param name="errorMessages"></param>
        /// <returns>无排单记录的表单</returns>
        DynamicObject[] CheckHasScheduleBill(UserContext userContext, DynamicObject[] dataEntities, HtmlForm form, string schedulePlanBillEntityKey, List<string> errorMessages);
    }
}
