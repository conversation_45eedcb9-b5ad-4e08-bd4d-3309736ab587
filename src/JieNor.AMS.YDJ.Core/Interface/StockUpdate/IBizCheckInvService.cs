using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Reserve;
using JieNor.AMS.YDJ.DataTransferObject.Reserve;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Interface.StockUpdate
{
    /// <summary>
    /// 业务单据操作的库存校验服务
    /// </summary>
    public interface IBizCheckInvService
    {
        /// <summary>
        /// 库存校验服务：业务单据操作时，进行库存校验
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="inventoryData"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        List<BizInvCheckResult> BizCheckInventory(UserContext userCtx,HtmlForm invFormMeta, IEnumerable<DynamicObject > inventoryData, BizInvCheckPara checkPara, OperateOption option);
    }
}
