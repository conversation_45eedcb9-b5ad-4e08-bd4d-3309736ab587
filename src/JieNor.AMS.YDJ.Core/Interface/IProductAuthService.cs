using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 商品授权清单服务接口
    /// </summary>
    public interface IProductAuthService
    {
        /// <summary>
        /// 添加默认授权行（经销商授权、送达方授权、门店授权接口固定增加"通配品牌"与"慕思助眠"授权）
        /// 需要手动保存
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productAuths"></param>
        void AddDefault(UserContext userCtx, IEnumerable<DynamicObject> productAuths);


        /// <summary>
        /// 添加【专供标记】商品到单据体-例外商品
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productAuths"></param>
        /// <param name="autoSave">是否自动保存</param>
        void AddSpecial(UserContext userCtx, IEnumerable<DynamicObject> productAuths, bool autoSave);

        /// <summary>
        /// 添加【专供标记】商品到单据体-例外商品
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productAuths"></param>
        /// <param name="autoSave">是否自动保存</param>
        /// <param name="specialProductIds">指定专供标记商品ids</param>
        void AddSpecial(UserContext userCtx, IEnumerable<DynamicObject> productAuths, bool autoSave, IEnumerable<string> specialProductIds);
        void AddSpecialBySql(UserContext userCtx, IEnumerable<string> specialProductIds);
        /// <summary>
        /// 所有【授权组织类型】= "经销商"的《商品授权清单》将【专供标记】商品到单据体-例外商品删除
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="specialProductIds"></param>
        void RemoveSpecial(UserContext userCtx, IEnumerable<string> specialProductIds);

        /// <summary>
        /// 禁用
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="orgIds">组织ids</param>
        /// <returns></returns>
        IOperationResult Forbid(UserContext userCtx, HtmlForm formMate, IEnumerable<string> orgIds, OperateOption option = null);

        /// <summary>
        /// 反禁用
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="orgIds">组织ids</param>
        /// <returns></returns>
        IOperationResult Unforbid(UserContext userCtx, HtmlForm formMate, IEnumerable<string> orgIds, OperateOption option = null);

        /// <summary>
        /// 清空商品授权缓存数据
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productAuths"></param>
        void ClearPrdAuthCache(UserContext userCtx, IEnumerable<DynamicObject> productAuths, HtmlForm htmlForm = null,
            string operationNo = null);
        /// <summary>
        /// 处清除二级分销商商品授权清单数据
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productAuths">此次操作的一级商品授权清单</param>
        /// <param name="addexcludeProdIds">添加专供的商品</param>
        /// <param name="removeexcludeProdIds">移除专供商品</param>
        /// <param name="htmlForm"></param>
        /// <param name="operationNo"></param>
        void ClearSecondAgentAuth(UserContext userCtx, List<DynamicObject> productAuths, List<string> addexcludeProdIds, List<string> removeexcludeProdIds,
            List<string> removeAuthProdIds,
            HtmlForm htmlForm = null, string operationNo = null, string operationName = null);
    }
}
