using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 跟进记录接口
    /// </summary>
    public interface IFollowerRecordService
    {
        /// <summary>
        /// 根据表单更新跟进记录的客户
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntities"></param>
        void UpdateFollowerRecord(UserContext userContext, HtmlForm htmlForm, List<DynamicObject> dataEntities);

        /// <summary>
        /// 处理上下查查询条件
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntities"></param>
        /// <param name="linkFormDatas"></param>
        /// <param name="customerPropertyId"></param>
        void DealLinkForm(UserContext userContext,
                          HtmlForm htmlForm,
                          DynamicObject[] dataEntities,
                          List<Dictionary<string, object>> linkFormDatas,
                          string customerPropertyId);
    }
}
