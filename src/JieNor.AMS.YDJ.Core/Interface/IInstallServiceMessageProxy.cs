using JieNor.Framework;
using JieNor.Framework.DataTransferObject.IM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 安装服务单相关的消息代理实现类
    /// </summary>
    public interface IInstallServiceMessageProxy
    {
        /// <summary>
        /// 初始化消息总线
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="hub"></param>
        void InitHubContext(UserContext userCtx, object hub);

        /// <summary>
        /// 获取所有服务单相关的消息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        IEnumerable<IMMessageHead> GetAllServiceBillMessage(UserContext userCtx);

        /// <summary>
        /// 获取所有公告消息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        IEnumerable<IMMessageHead> GetAllNoticeMessage(UserContext userCtx);
        /// <summary>
        /// 获取消息条数和时间倒序首条
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        Dictionary<string, string> GetNoticeMessageCountAndFirst(UserContext userCtx);

        /// <summary>
        /// 获取服务单相关条数和时间倒序首条
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        Dictionary<string, string> GetServiceMessageCountAndFirst(UserContext userCtx);
        /// <summary>
        /// 获取提醒类消息条数和时间倒序首条
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        Dictionary<string, string> GetAlarmMessageCountAndFirst(UserContext userCtx);
        /// <summary>
        /// 获取所有提醒消息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        IEnumerable<IMMessageHead> GetAllRemindMessage(UserContext userCtx);

        /// <summary>
        /// 获得消息列表
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="qid"></param>
        /// <param name="Read"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="PageCount"></param>
        /// <returns></returns>
        IEnumerable<IMMessageHead> GetMessageListByPage(UserContext userCtx, string qid, bool Read, int pageIndex, int pageSize, ref int PageCount);
        /// <summary>
        /// 发出邀请消息，邀请某师傅加入某团队
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="masterId"></param>
        /// <param name="joinTeamId"></param>
        void InviteMaster(UserContext userCtx, string masterId, string joinTeamId);

        /// <summary>
        /// 表示当前消息处理人是否同意加入某个团队
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="joinTeamId"></param>
        /// <param name="agree"></param>
        /// <param name="masterId">为空时表示当前登录人所对应的师傅</param>
        object JoinInTeam(UserContext userCtx, string joinTeamId, bool agree, string masterId = null);

        /// <summary>
        /// 认证成功发送提醒消息到APP
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="masterId"></param>
        void SendAuthSucessMessage(UserContext userCtx, string masterId);
        /// <summary>
        /// 认证失败发送提醒消息到APP
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="masterId"></param>
        void SendAuthErrorMessage(UserContext userCtx, string masterId);
        
    }
}
