using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormModel.List;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.DataEntity.Inventory
{
    /// <summary>
    /// 库存计算中用的查询对象实体
    /// </summary>
    public class StockComputeQueryObject
    {
        /// <summary>
        /// 查询参数对象 
        /// </summary>
        public SqlBuilderParameter SqlBuilderParameter { get; set; }

        /// <summary>
        /// 查询结果对象
        /// </summary>
        public QueryObject QueryObject { get; set; }

        /// <summary>
        /// 查询字段列表
        /// </summary>
        public IEnumerable<string> SqlSelectFieldList { get; set; }
    }
}
