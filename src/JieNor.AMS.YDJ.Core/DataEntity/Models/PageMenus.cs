using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.DataEntity.Models
{
    /// <summary>
    /// 移动端通用详情按钮列表
    /// </summary>
    public class PageMenus
    {
        /// <summary>
        /// 菜单标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 菜单按钮列表
        /// </summary>
        public IEnumerable<Menus> Menus { get; set; }
    }

    /// <summary>
    /// 移动端通用详情按钮
    /// </summary>
    public class Menus
    {
        /// <summary>
        /// 按钮Id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 按钮名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 按钮图标
        /// </summary>
        public string Icon { get; set; }

        /// <summary>
        /// 按钮表单Id
        /// </summary>
        public string FormId { get; set; }

        /// <summary>
        /// 按钮操作码
        /// </summary>
        public string OpCode { get; set; }
    }
}
