using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.DataEntity.Barcode
{
    /// <summary>
    /// 更新条码选项对象
    /// </summary>
    public class UpdateBarcodeTraceBill
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="rollback"></param>
        /// <param name="updateStatusOnly"></param>
        public UpdateBarcodeTraceBill(bool rollback, bool updateStatusOnly)
        {
            this.Rollback = rollback;
            this.UpdateStatusOnly = updateStatusOnly;
        }

        /// <summary>
        /// 仅更新条码状态
        /// </summary>
        public bool UpdateStatusOnly { get; private set; }

        /// <summary>
        /// 回滚操作
        /// </summary>
        public bool Rollback { get; private set; }

        /// <summary>
        /// 关联更新单据标识
        /// </summary>
        public string FormId { get; set; }

        /// <summary>
        /// 关联更新单据编号
        /// </summary>
        public string BillNo { get; set; }

        /// <summary>
        /// 关联更新单据主键
        /// </summary>
        public string BillId { get; set; }

        /// <summary>
        /// 关联更新单据日期
        /// </summary>
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 操作名称 
        /// </summary>
        public string OperationName { get; set; }

    }
}
