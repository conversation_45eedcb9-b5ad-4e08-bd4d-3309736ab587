using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using ServiceStack;
using ServiceStack.VirtualPath;

namespace JieNor.AMS.YDJ.Core.Utils
{
    /// <summary>
    /// 调试工具
    /// </summary>
    public class DebugUtil
    {
        public static StringBuilder AppendLine(StringBuilder builder, string title, string message)
        {
            builder.AppendLine($"{title}:{message}");
            return builder;
        }

        public static StringBuilder AppendLine(StringBuilder builder, string title, object obj)
        {
            try
            {
                builder.AppendLine($"{title}:{obj?.ToJson()}");
            }
            catch (Exception)
            {

            }

            return builder;
        }

        public static void WriteLogToFile(string message, string fileName)
        {
            try
            {
                var files = new FileSystemVirtualPathProvider(HostContext.AppHost, HostContext.Config.WebHostPhysicalPath);
                string path = "app_data/debuglog/" + DateTime.Now.ToString("yyyy-MM-dd") + "/" + fileName + ".txt";

                StringBuilder content = new StringBuilder();
                content.AppendLine("时间:" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                content.AppendLine("内容:" + message);
                content.AppendLine();

                if (!files.FileExists(path))
                {
                    files.WriteFile(path, content.ToString());
                }
                else
                {
                    files.AppendFile(path, content.ToString());
                }
            }
            catch (Exception)
            {
            }
        }

    }
}
