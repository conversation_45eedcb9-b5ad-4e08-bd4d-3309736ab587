using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using JieNor.Framework.Interface.Log;
using ServiceStack;
using System.Diagnostics;
using System.Runtime.Serialization;
using System.Text;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.Framework.Interface.Cache;
using System.Data;

namespace JieNor.AMS.YDJ.Core
{


    /// <summary>
    /// 【商品】数据权限隔离辅助类
    /// </summary>  
    public partial  class ProductDataIsolateHelper  
    {

         




        /// <summary>
        /// 获取供应商对应的销售组织
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="brandIds"></param>
        /// <param name="seriIds"></param>
        /// <returns></returns>
        private static List<BaseDataSummary> GetSupplierSalOrgInfo(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            var result = new List<BaseDataSummary>();
            result.Add(new BaseDataSummary() { Id = "@#$", Name = "", Number = "" });//避免未取到对应组织时后续的查询错误

            var supplyId = rulePara.SrcPara["supplierid"];
            var dbService = ctx.Container.GetService<IDBService>();

            var sql = @"select a.fid,a.forgid, b.fnumber as forgno,b.fname as forgname 
                        from t_ydj_supplier a  with(nolock) 
                        left join T_BAS_ORGANIZATION b  with(nolock) on b.fid =a.forgid 
                        where a.fid=@fid ";
            var param = new SqlParam("@fid", System.Data.DbType.String, supplyId);
            using (var reader = dbService.ExecuteReader(ctx, sql, param))
            {
                while (reader.Read())
                {
                    result.Add(new BaseDataSummary()
                    {
                        Id = reader.GetString("forgid"),
                        Name = reader.GetString("forgname"),
                        Number = reader.GetString("forgno")
                    }
                    );
                }
            }

            return result;
        }





        private static string GetAuthSql(UserContext ctx, List<string> ids, string fldName)
        {
            var dbService = ctx.Container.GetService<IDBService>();
            var sql = "";
            if (ids.Count > 30)
            {
                var tempTbl = dbService.CreateTempTableWithDataList(ctx, ids, false);

                dbService.DeleteTempTableByName(ctx, tempTbl, 5);

                switch (fldName)
                {
                    case "fseriesid"://系列
                        sql = @"select a.fmainorgid,a.fid,a.fnumber as fmatno,a.fname as fmatname, a.fseriesid,c.fnumber as fseriesno,c.fname as fseriesname,a.fauxseriesid 
                        from T_BD_MATERIAL a WITH (NOLOCK)                         
                        left join t_ydj_series c  with(nolock) on c.fid =a.fseriesid 
                        inner join {0} b  with(nolock) on c.fid=b.fid 
                        where exists (select 1 from T_BAS_ORGANIZATION x with(nolock)  where a.fbizorgid =x.fid and x.forgtype in ('1','4') ) ".Fmt(tempTbl);
                        break;
                    case "fauxseriesid"://附属品牌
                        sql = @"select a.fmainorgid,a.fid,a.fnumber as fmatno,a.fname as fmatname, a.fseriesid,c.fnumber as fseriesno,c.fname as fseriesname,a.fauxseriesid 
                        from T_BD_MATERIAL a WITH (NOLOCK)                         
                        left join t_ydj_series c  with(nolock) on c.fid =a.fauxseriesid or a.fauxseriesid like '%' + c.fid + '%'
                        inner join {0} b  with(nolock) on c.fid=b.fid 
                        where exists (select 1 from T_BAS_ORGANIZATION x with(nolock)  where a.fbizorgid =x.fid and x.forgtype in ('1','4') ) ".Fmt(tempTbl);
                        break;
                    case "fbrandid"://品牌
                        sql = @"select a.fmainorgid,a.fid,a.fnumber as fmatno,a.fname as fmatname, a.fseriesid,c.fnumber as fseriesno,c.fname as fseriesname,a.fauxseriesid 
                        from T_BD_MATERIAL a WITH (NOLOCK)                         
                        left join t_ydj_series c  with(nolock) on c.fid =a.fseriesid 
                        inner join {0} b  with(nolock) on a.fbrandid=b.fid 
                        where exists (select 1 from T_BAS_ORGANIZATION x with(nolock)  where a.fbizorgid =x.fid and x.forgtype in ('1','4') ) ".Fmt(tempTbl);
                        break;
                    case "fid"://商品id
                        sql = @"select a.fmainorgid,a.fid,a.fnumber as fmatno,a.fname as fmatname, a.fseriesid,c.fnumber as fseriesno,c.fname as fseriesname,a.fauxseriesid 
                        from T_BD_MATERIAL a WITH (NOLOCK)                         
                        left join t_ydj_series c  with(nolock) on c.fid =a.fseriesid 
                        inner join {0} b  with(nolock) on a.fid=b.fid 
                        where exists (select 1 from T_BAS_ORGANIZATION x with(nolock)  where a.fbizorgid =x.fid and x.forgtype in ('1','4') ) ".Fmt(tempTbl);
                        break;
                    default:
                        break;
                }
            }
            else
            {
                switch (fldName)
                {
                    case "fseriesid"://系列
                        sql = @"select a.fmainorgid,a.fid,a.fnumber as fmatno,a.fname as fmatname,a.fseriesid,c.fnumber as fseriesno,c.fname as fseriesname,a.fauxseriesid 
                        from T_BD_MATERIAL a WITH (NOLOCK) 
                        left join t_ydj_series c  with(nolock) on c.fid =a.fseriesid  
                        where c.fid in ('{0}') 
                        and exists (select 1 from T_BAS_ORGANIZATION x with(nolock)  where a.fbizorgid =x.fid and x.forgtype in ('1','4')  ) ".Fmt(string.Join("','", ids));
                        break;
                    case "fauxseriesid"://附属品牌
                        sql = @"select a.fmainorgid,a.fid,a.fnumber as fmatno,a.fname as fmatname,a.fseriesid,c.fnumber as fseriesno,c.fname as fseriesname,a.fauxseriesid 
                        from T_BD_MATERIAL a WITH (NOLOCK) 
                        left join t_ydj_series c  with(nolock) on c.fid =a.fauxseriesid or a.fauxseriesid like '%' + c.fid + '%'
                        where c.fid in ('{0}') 
                        and exists (select 1 from T_BAS_ORGANIZATION x with(nolock)  where a.fbizorgid =x.fid and x.forgtype in ('1','4')  ) ".Fmt(string.Join("','", ids));
                        break;
                    case "fbrandid"://品牌
                        sql = @"select a.fmainorgid,a.fid,a.fnumber as fmatno,a.fname as fmatname,a.fseriesid,c.fnumber as fseriesno,c.fname as fseriesname,a.fauxseriesid 
                        from T_BD_MATERIAL a WITH (NOLOCK) 
                        left join t_ydj_series c  with(nolock) on c.fid =a.fseriesid  
                        where a.fbrandid in ('{0}') 
                        and exists (select 1 from T_BAS_ORGANIZATION x with(nolock)  where a.fbizorgid =x.fid and x.forgtype in ('1','4')  ) ".Fmt(string.Join("','", ids));
                        break;
                    case "fid"://商品id
                        sql = @"select a.fmainorgid,a.fid,a.fnumber as fmatno,a.fname as fmatname,a.fseriesid,c.fnumber as fseriesno,c.fname as fseriesname,a.fauxseriesid 
                        from T_BD_MATERIAL a WITH (NOLOCK) 
                        left join t_ydj_series c  with(nolock) on c.fid =a.fseriesid  
                        where a.fid in ('{0}') 
                        and exists (select 1 from T_BAS_ORGANIZATION x with(nolock)  where a.fbizorgid =x.fid and x.forgtype in ('1','4')  ) ".Fmt(string.Join("','", ids));
                        break;
                    default:
                        break;
                }
            }

            return sql + System.Environment.NewLine;
        }



        /// <summary>
        /// 获取按品牌授权及自建的品牌信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="brandIds"></param>
        /// <param name="seriIds"></param>
        /// <returns></returns>
        private static HashSet<string> GetAutBrandInfo(UserContext ctx, List<UserOrgInfo> orgInfos, HashSet<string> brandIds, HashSet<string> seriIds)
        {
            var result = new HashSet<string>(brandIds);

            var allSql = new List<string>();
            //系列对应的品牌
            var sql = "select a.fbrandid  from t_ydj_series a  with(nolock) where a.fid in ('{0}') ".Fmt(seriIds.JoinEx("','", false));
            allSql.Add(sql);
            var orgIds = orgInfos.Select(f => "'{0}'".Fmt(f.OrgId)).ToList();
            sql = "select a.fid as fbrandid from t_Ydj_Brand a  with(nolock) where a.fmainorgid in ({0}) ".Fmt(orgIds.JoinEx(",", false));//自建的品牌
            allSql.Add(sql);

            sql = string.Join(System.Environment.NewLine + "union all" + System.Environment.NewLine, allSql);
            var dbService = ctx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(ctx, sql))
            {
                while (reader.Read())
                {
                    var id = reader.GetString("fbrandid");
                    result.Add(id);
                }
            }

            return result;
        }




        /// <summary>
        /// 在同一个主经销商配置表当中的，商品授权共用
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="agentInfo"></param>
        /// <param name="result"></param>
        private void GetMainSubAgentMapInfo(UserContext ctx, List<AgentInfo> agentInfo, List<AgentInfo> currentAgent, List<UserAgentInfo> result)
        {
            var sql = @" select b.fid, b.fmainagentid,a.fsubagentid  
                         from t_bas_macentry a with(nolock) 
                         inner join t_bas_mac b with(nolock)  on a.fid=b.fid
                         where b.fforbidstatus='0' and ( b.fmainagentid  in ({0}) or a.fsubagentid in ({0}) ) 
                        ".Fmt(currentAgent.Select(f => f.OrgId).JoinEx(",", true));
            var dbService = ctx.Container.GetService<IDBService>();
            var mainSubOrgInfo = dbService.ExecuteDynamicObject(ctx, sql);

            var others = new HashSet<string>();
            foreach (var agent in result)
            {
                var exists = GetSubAgentInfo(mainSubOrgInfo, agent.OrgId);
                foreach (var item in exists)
                {
                    others.Add(item);
                }
            }

            foreach (var agent in others)
            {
                //如果主经销A城市，子经销AB城市,这里只取到了一个agents，商品授权清单只取到了A城市的授权，因为配置了主子经销商所以B的授权也需要
                //if (result.Any(f => f.OrgId == agent))
                //{
                //    continue;
                //}

                //bug:28282，这里因为分销商是多城市，但是这里强制拿First条的话可能匹配不到实际对应城市的，这里暂时把所有满足的都加进去
                //var otherAgent = agentInfo.FirstOrDefault(f => f.OrgId == agent);
                var otherAgents = agentInfo.Where(f => f.OrgId == agent)?.ToList();
                if (otherAgents != null)
                {
                    foreach (var otherAgent in otherAgents)
                    {
                        //已经添加过经销商、实控人、城市同维度的不再添加，上面的判断提到走完主子配置之后，以防主经销A城市，子经销AB城市配置主子后取不到B的商品授权清单。
                        if (result.Any(o => o.OrgId == otherAgent.OrgId && o.ActualOwnerNo == otherAgent.ActualOwnerNo && o.City == otherAgent.City)) continue;

                        result.Add(new UserAgentInfo()
                        {
                            Id = otherAgent.Id,
                            OrgId = otherAgent.OrgId,
                            OrgName = otherAgent.OrgName,
                            OrgNo = otherAgent.OrgNo,
                            OrgType = otherAgent.OrgType,
                            TopCompanyId = otherAgent.TopCompanyId,
                            ActualOwnerName = otherAgent.ActualOwnerName,
                            ActualOwnerNo = otherAgent.ActualOwnerNo,
                            City = otherAgent.City,
                        });
                    }
                }

                otherAgents?.Clear();
            }

            mainSubOrgInfo?.Clear();
            mainSubOrgInfo = null;
            others?.Clear();
            others = null;
        }

        /// <summary>
        /// 在同一个主经销商配置表当中的，商品授权共用
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="agentInfo"></param>
        /// <param name="result"></param>
        private static void GetMainSubAgentMapInfo(UserContext ctx, List<AgentInfo> agentInfo, List<UserOrgInfo> result)
        {
            var sql = @" select b.fid, b.fmainagentid,a.fsubagentid  
                         from t_bas_macentry a with(nolock) 
                         inner join t_bas_mac b with(nolock)  on a.fid=b.fid
                         where b.fforbidstatus='0' and ( b.fmainagentid  in ({0}) or a.fsubagentid in ({0}) ) 
                        ".Fmt(result.Select(f => f.OrgId).JoinEx(",", true));
            var dbService = ctx.Container.GetService<IDBService>();
            var mainSubOrgInfo = dbService.ExecuteDynamicObject(ctx, sql);

            var others = new HashSet<string>();
            foreach (var agent in result)
            {
                var exists = GetMSubAgentInfo(mainSubOrgInfo, agent.OrgId);
                foreach (var item in exists)
                {
                    others.Add(item);
                }
            }
            string bizOrgId = result?[0].BizOrgId;
            string bizOrgType = result?[0].BizOrgType;
            foreach (var agent in others)
            {
                if (result.Any(f => f.OrgId == agent))
                {
                    continue;
                }

                //bug:28282，这里因为分销商是多城市，但是这里强制拿First条的话可能匹配不到实际对应城市的，这里暂时把所有满足的都加进去
                //var otherAgent = agentInfo.FirstOrDefault(f => f.OrgId == agent);
                var otherAgents = agentInfo.Where(f => f.OrgId == agent).ToList();
                if (otherAgents != null)
                {
                    foreach (var otherAgent in otherAgents)
                    {
                        result.Add(new UserOrgInfo()
                        {
                            BizOrgId = bizOrgId,
                            OrgId = otherAgent.OrgId,
                            OrgName = otherAgent.OrgName,
                            OrgNo = otherAgent.OrgNo,
                            OrgType = otherAgent.OrgType,
                            BizOrgType = bizOrgType,
                            TopCompanyId = otherAgent.TopCompanyId,
                        });
                    }
                }
                otherAgents?.Clear();
            }
        }


        /// <summary>
        /// 在同一个主经销商配置表当中的组织信息
        /// </summary>
        /// <param name="mainSubOrgInfo"></param>
        /// <param name="agentOrgId"></param>
        /// <returns></returns>
        private HashSet<string> GetSubAgentInfo(DynamicObjectCollection mainSubOrgInfo, string agentOrgId)
        {
            var result = new HashSet<string>();
            var exists = mainSubOrgInfo.Where(f => Convert.ToString(f["fmainagentid"]) == agentOrgId || Convert.ToString(f["fsubagentid"]) == agentOrgId).ToList();
            var grp = exists.GroupBy(f => Convert.ToString(f["fid"])).ToList();
            foreach (var item in grp)
            {
                var mainSubData = mainSubOrgInfo.Where(f => Convert.ToString(f["fid"]) == item.Key).ToList();
                foreach (var map in mainSubData)
                {
                    result.Add(Convert.ToString(map["fmainagentid"]));
                    result.Add(Convert.ToString(map["fsubagentid"]));
                }
            }

            grp?.Clear();
            grp = null;
            exists?.Clear();

            return result;
        }

        /// <summary>
        /// 在同一个主经销商配置表当中的组织信息
        /// </summary>
        /// <param name="mainSubOrgInfo"></param>
        /// <param name="agentOrgId"></param>
        /// <returns></returns>
        private static HashSet<string> GetMSubAgentInfo(DynamicObjectCollection mainSubOrgInfo, string agentOrgId)
        {
            var result = new HashSet<string>();
            var exists = mainSubOrgInfo.Where(f => Convert.ToString(f["fmainagentid"]) == agentOrgId || Convert.ToString(f["fsubagentid"]) == agentOrgId).ToList();
            var grp = exists.GroupBy(f => Convert.ToString(f["fid"])).ToList();
            foreach (var item in grp)
            {
                var mainSubData = mainSubOrgInfo.Where(f => Convert.ToString(f["fid"]) == item.Key).ToList();
                foreach (var map in mainSubData)
                {
                    result.Add(Convert.ToString(map["fmainagentid"]));
                    result.Add(Convert.ToString(map["fsubagentid"]));
                }
            }

            grp?.Clear();
            grp = null;
            exists?.Clear();
            exists = null;

            return result;
        }



















    }

}