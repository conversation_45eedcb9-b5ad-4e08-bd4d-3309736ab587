using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn.CustomEventArgs;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Metadata
{
    [InjectService]
    [FormId("ydj_staff")]
    public class StaffFieldController : IBaseDataFilterController
    {
        /// <summary>
        /// 默认员工
        /// </summary>
        public const int StaffType_Default = 0;
        /// <summary>
        /// 销售员
        /// </summary>
        public const int StaffType_Sale = 1;
        /// <summary>
        /// 采购员
        /// </summary>
        public const int StaffType_Purchase = 2;
        /// <summary>
        /// 仓管员
        /// </summary>
        public const int StaffType_Inventory = 4;

        /// <summary>
        /// 构建员工资料的额外过滤条件
        /// </summary>
        /// <param name="e"></param>
        public void BuildListFilterString(BuildQueryListFilterStringEventArgs e)
        {            
            if (e.HtmlField == null) return;
            int bizType = 0;
            if(e.HtmlField is HtmlStaffField)
            {
                bizType = (e.HtmlField as HtmlStaffField).BizType;
            }
            else
            {
                var dataOption = e.HtmlField.Parameter.FromJson<JObject>(true);
                if (dataOption != null)
                {
                    bizType = dataOption.GetJsonValue("bizType", 0);
                }
            }
            if (bizType == 0) return;

            List<int> lstBizTypes = new List<int>();
            if ((bizType & StaffType_Sale) == StaffType_Sale)
            {
                lstBizTypes.Add(StaffType_Sale);
            }
            if ((bizType & StaffType_Purchase) == StaffType_Purchase)
            {
                lstBizTypes.Add(StaffType_Purchase);
            }
            if ((bizType & StaffType_Inventory) == StaffType_Inventory)
            {
                lstBizTypes.Add(StaffType_Inventory);
            }
            e.AppendFilterString($" exists(select 1 from t_bd_staffentry pe with(nolock) where fid=pe.fid and pe.fbiztype in ({string.Join(",", lstBizTypes)}) ) ");
        }
    }
}
