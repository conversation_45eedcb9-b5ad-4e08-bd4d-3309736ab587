using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.IM
{
    /// <summary>
    /// 极光消息推送
    /// </summary>
    public interface IJPushService
    {
        /// <summary>
        /// 消息推送
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="UserId">Userid集合</param>
        /// <param name="Title">标题</param>
        /// <param name="Content">内容</param>
        /// <param name="Url">url</param>
        void SendMessage(UserContext ctx, string[] UserId, string Title, string Content = null, string Url = "");

        /// <summary>
        /// 根据标签发送消息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="tags"></param>
        /// <param name="title"></param>
        /// <param name="content"></param>
        /// <param name="extras"></param>
        void SendMessageByTags(UserContext ctx, string[] tags, string title, string content, Dictionary<string, string> extras);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="alias"></param>
        /// <param name="title"></param>
        /// <param name="content"></param>
        /// <param name="extras"></param>
        void SendMessageByAlias(UserContext ctx, string[] alias, string title, string content, Dictionary<string, string> extras);
    }
}
