using JieNor.Framework.DataTransferObject.IM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.IM
{



    /// <summary>
    /// IM客户端监听服务接口：提供客户端监听事件的统一接口
    /// </summary>
    public interface IIMClientSubscribe
    {
         
        /// <summary>
        /// 监听IM登录
        /// </summary>
        event Action<bool, string> onLogInIMServer;

        /// <summary>
        /// 注销登录的监听函数
        /// </summary>
        event Action<bool, string> onLogOutIMServer;


      


         

        /// <summary>
        /// 创建聊天群组监听函数
        /// </summary>
        event Action<string> onCreatIMGroup;

        /// <summary>
        /// 创建单聊组监听函数
        /// </summary>
        event Action<bool, string> onCreateSignGroup;
        

        /// <summary>
        /// 监听用户在聊天群组里面的昵称修改
        /// </summary>  
        event Action<string,string ,string > onSetNickInIMGroup;



        /// <summary>
        /// 将某人踢出聊天群组 的监听函数
        /// </summary>
        event Action<string, string, string> onKickFromIMGroup ;

        /// <summary>
        /// 退出聊天群组(自己主动退出)的监听函数
        /// </summary>  
        event Action<string, string> onLeaveFromIMGroup;


        /// <summary>
        /// 监听聊天群的主题（公告）更改
        /// </summary>
        event Action<string, string> onSetIMGroupTopic ;

        /// <summary>
        /// 监听聊天群的欢迎词更改
        /// </summary>
        event Action<string, string> onSetIMGroupWelcome ;


        /// <summary>
        /// 监听群成员的群管理员属性更改
        /// </summary>
        event Action<string, string,bool> onSetIMGroupAdmin;






        /// <summary>
        /// 允许某人加入聊天群（通常是管理员允许）的监听函数
        /// </summary>  
        event Action<string, string,bool > onAllowJoinIMGroup;


        /// <summary>
        /// 是否禁止某些神仙在群里发言的监听函数
        /// </summary> 
        event Action<string, string, bool, string> onSetBanInIMGroup;



        /// <summary>
        /// 设置聊天群的锁定状态的监听函数
        /// </summary>
        event Action<string, bool, string> onSetIMGroupLockStatus;



        /// <summary>
        /// 解散聊天群的监听函数
        /// </summary>
        event Action<string,  string> onDissolveIMGroup ;


        /// <summary>
        /// 获取当前登录用户的所属聊天群组信息 
        /// </summary>
        event Action<string> onGetCurrUserIMGroupList;


        /// <summary>
        /// 获取当前登录用户的所属聊天群组信息 
        /// </summary>
        event Action<string> onGetAllIMGroupList;







        /// <summary>
        /// 监听用户的创建
        /// </summary>
        event Action<string> onCreatIMUser;



        /// <summary>
        /// 监听用户头像的更改
        /// </summary>
        event Action<string,string > onChangeIMUserGravatar;


        /// <summary>
        /// 监听用户禁用状态的更改
        /// </summary>
        event Action<string,bool, string> onSetIMUserForbitSatus;




        /// <summary>
        /// 监听用户的状态
        /// </summary>
        event Action<string, IMUserStatus> onSetIMUserStatus ;






        /// <summary>
        /// 监听广播消息
        /// </summary>    
        event Action<string, string > onReceiveBaroadCast;

        /// <summary>
        /// 监听消息
        /// </summary>    
        event Action<string, string> onReceiveMessage;

        /// <summary>
        /// 监听@消息
        /// </summary>    
        event Action<string, string> onReceiveMention;



        /// <summary>
        /// 设置消息已读状态
        /// </summary> 
        event Action<string, bool > onSetMessageReadStatus;

        /// <summary>
        /// 设置消息的处理状态
        /// </summary> 
        event Action<string, IMMsgHandleStatus> onSetMessageProcessStatus;





        /// <summary>
        /// 监听未读信息 
        /// </summary> 
        event Action<string> onGetUnReadMessage;



        /// <summary>
        /// 监听推送给客户端的消息
        /// </summary> 
        event Action<string> onGetAllMessage;



        /// <summary>
        /// 监听系统广播消息
        /// </summary> 
        event Action<string> onGetSysBaroadCastMsg;



        /// <summary>
        /// 监听是否同意加好友
        /// </summary>    
        event Action<string, string,bool> onAgreeAddFriend;

        /// <summary>
        /// 监听分组的增加
        /// </summary>    
        event Action<string,string> onCreateIMCategory;

        




    }






}
