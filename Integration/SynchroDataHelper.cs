
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.BPM;
using JieNor.Framework.DataTransferObject.Integration;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.Integration
{

    /// <summary>
    /// 数据同步工具类
    /// </summary>
    public partial class SynchroDataHelper
    {

        static ConcurrentDictionary<string , BPMBizObjectInfo> BPMBizObjectInfo = new ConcurrentDictionary<string , BPMBizObjectInfo>();


        public static BPMBizObjectInfo GetBPMBizObjectInfo(UserContext ctx, string formId)
        {
            GetAllBPMBizObjectInfo(ctx);

            formId = formId.ToLowerInvariant();
            if (BPMBizObjectInfo.ContainsKey(formId))
            {
                return BPMBizObjectInfo[formId];
            }
            return null;
        }



        public static List<BPMBizObjectInfo> GetAllBPMBizObjectInfo(UserContext ctx,string modelId="")
        {
            if(BPMBizObjectInfo!=null && BPMBizObjectInfo.Count >0)
            {
                return BPMBizObjectInfo.Values.ToList ();
            }

            BPMBizObjectInfo = new ConcurrentDictionary<string, BPMBizObjectInfo>();
            string strSql = $@"select distinct t0.fbillformid,t0.fname
                                                ,t2.fname fmodulename
				                                ,t2.fmoduleid
				                                ,t2.forder as fmdlorder
				                                ,t1.fgroupid
				                                ,t1.forder as fgrporder
				                                ,t0.fmenuid
				                                ,t0.forder
                                from T_SYS_MENUITEM t0 
                                inner join T_SYS_MENUGROUP t1 on t0.fgroupid=t1.fgroupid 
                                inner join T_SYS_BIZMODULE t2 on t1.fmoduleid=t2.fmoduleid  ";
            if (!modelId.IsNullOrEmptyOrWhiteSpace())
            {
                strSql += $@" where t2.fmoduleid = '" + modelId + "' ";
            }
            strSql += $@" order by t2.forder, t2.fmoduleid, t1.forder,t1.fgroupid,t0.forder,t0.fmenuid ";

            var svc = ctx.Container.GetService<IDBService>();
            var metaService = ctx.Container.GetService<IMetaModelService>();
            using (var reader = svc.ExecuteReader(ctx, strSql))
            {
                while (reader.Read())
                {
                    var bizObjId = reader.GetValue<string>("fbillformid");
                    if (bizObjId.IsNullOrEmptyOrWhiteSpace()) continue;
                    try
                    {
                        BPMBizObjectInfo tmpl = new BPMBizObjectInfo();
                        tmpl.ModelId = reader.GetString("fmoduleid");
                        tmpl.ModelCaption = reader.GetString("fmodulename");
                        tmpl.FormId = bizObjId.ToLowerInvariant ();
                        tmpl.FormCaption = reader.GetString("fname");
                        tmpl.FormUrl = "";

                        if (!BPMBizObjectInfo.ContainsKey(tmpl.FormId))
                        {
                            BPMBizObjectInfo.TryAdd(tmpl.FormId, tmpl);
                        }
                    }
                    catch
                    {
                        //吃掉异常，
                    }
                }
            }

            return BPMBizObjectInfo.Values.ToList();
        }


        public static IEnumerable<DynamicObject> GetSCRMData(UserContext ctx, string formId)
        {
            var meta = HtmlParser.LoadFormMetaFromCache(formId, ctx);
            
            var pkFld = meta.BillPKFldName;
            var table = meta.BillHeadTableName;
            var sqlIds = string.Format("select {0} from {1} ", pkFld, table);
            if(meta.Isolate=="1" && meta.GetField ("fmainorgid")!=null)
            {
                sqlIds += " where fmainorgid='{0}' ".Fmt (ctx.Company) ;
            }
            return GetSCRMData(ctx, formId, sqlIds);             
        }


        /// <summary>
        /// 获取scrm单据的内码信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        public static  DynamicObjectCollection  GetSCRMDataIDS(UserContext ctx, string formId)
        {
            var meta = HtmlParser.LoadFormMetaFromCache(formId, ctx);

            var pkFld = meta.BillPKFldName;
            var table = meta.BillHeadTableName;
            var sqlIds = string.Format("select {0} as fid from {1} ", pkFld, table);
            if (meta.Isolate == "1" && meta.GetField("fmainorgid") != null)
            {
                sqlIds += " where fmainorgid='{0}' ".Fmt(ctx.Company);
            }

            var svc = ctx.Container.GetService<IDBService>();
            var ids= svc.ExecuteDynamicObject(ctx, sqlIds);

            return ids;
        }


        public static IEnumerable<DynamicObject> GetSCRMData(UserContext ctx, string formId, string sql4Ids)
        {
            List<object> ids = new List<object>();
            var svc = ctx.Container.GetService<IDBService>();
            using (var reader = svc.ExecuteReader(ctx, sql4Ids))
            {
                while (reader.Read())
                {
                    if (reader[0] is DBNull)
                    {
                        continue;
                    }
                    ids.Add(reader[0]);
                }
            }
            return GetSCRMData(ctx, formId, ids);
        }


        public static IEnumerable<DynamicObject> GetSCRMData(UserContext ctx, string formId, IEnumerable<object> ids)
        {
            if(ids==null || ids.Count ()==0)
            {
                return new List<DynamicObject>();
            }

            var meta = HtmlParser.LoadFormMetaFromCache(formId, ctx);
            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));             
            var datas = dm.Select(ids).OfType<DynamicObject>();

            return datas;
        }






        
      



        public static string GetSystemProfile(UserContext ctx,string category, string key)
        {
            var svc = ctx.Container.GetService<ISystemProfile>();
            if(svc !=null )
            {
                return svc.GetProfile(ctx, category, key);
            }
               
            return "";
        }





        #region SyncLog  同步日志




        /// <summary>
        /// 转换日志格式
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="log"></param>
        /// <returns></returns>
        public static DynamicObject LogToDynamicObject(UserContext ctx, SynchroLogInfo log)
        {
            var meta = HtmlParser.LoadFormMetaFromCache("sys_synclog", ctx);
            var dyLog = meta.GetDynamicObjectType(ctx).CreateInstance() as DynamicObject;
            dyLog["Id"] = log.logId.IsNullOrEmptyOrWhiteSpace ()? Guid.NewGuid().ToString() : log.logId;
            dyLog["FGroupId"] = log.GroupId;
            dyLog["FBeginTime"] = log.BeginTime;
            dyLog["fsynctype"] = log.SyncType;
            dyLog["FDirectDesc"] = log.DirectDesc;
            dyLog["FTargetSystem"] = log.TargetSystem;
            dyLog["FSourceSystem"] = log.SourceSystem;
            dyLog["FEndTime"] = log.EndTime < Convert.ToDateTime("1997-01-01 00:00:00") ? DateTime.Now : log.EndTime;
            dyLog["FContent"] = log.Content ;
            dyLog["foperatorid"] = ctx.UserId;

            var logEn = dyLog["FEntity"] as DynamicObjectCollection;
            foreach (var item in log.LogEntry)
            {
                var en = logEn.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                en["Id"]= item.logEntryId.IsNullOrEmptyOrWhiteSpace() ? Guid.NewGuid().ToString() : item.logEntryId ;
                en["FOperateTime"] = item.OperateTime;
                en["FEnContent"] = item.Content;

                logEn.Add(en);
            }

            return dyLog;
        }

        /// <summary>
        /// 记录同步日志
        /// </summary>
        /// <param name="ctx">k3cloud登录上下文</param>
        /// <param name="log">同步结果</param>
        public static void WriteSynchroLog(UserContext ctx, SynchroLogInfo log)
        {
            var meta = HtmlParser.LoadFormMetaFromCache("sys_synclog", ctx);
            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));
            var dyLog = LogToDynamicObject(ctx, log);
            
            var prepareService = ctx.Container.GetService<IPrepareSaveDataService>();
            prepareService?.PrepareDataEntity(ctx, meta,new DynamicObject[] { dyLog }, OperateOption.Create());

            dm.Save(dyLog,null, OperateOption.InstanceBulkCopyAndNoCache );
        }

       


        #endregion   SyncLog   同步日志
















    }


}
