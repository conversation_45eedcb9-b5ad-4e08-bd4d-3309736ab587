using JieNor.Framework.DataTransferObject.Poco;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.Integration
{

    /// <summary>
    /// 数据同步到第三方系统
    /// </summary>
    public interface ISynDataTo3Sys
    {
        /// <summary>
        /// 总入口
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="result"></param>
        /// <param name="ids"></param>
        void DoSynchroData(UserContext ctx, ref List<string> result, IEnumerable<object> ids);


    }




}
