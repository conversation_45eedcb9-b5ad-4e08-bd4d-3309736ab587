using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService;
using System.Linq;
using System;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.Framework.AppService.SystemPlugIn.Company
{
    /// <summary>
    /// 我的企业：保存
    /// </summary>
    [InjectService]
    [FormId("sys_company")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            //e.Rules.Add(this.RuleFor("fbillhead", data => data["fdescription"]).NotEmpty().WithMessage("企业介绍不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (newData["fprovince"].IsNullOrEmptyOrWhiteSpace() 
                    && newData["fcity"].IsNullOrEmptyOrWhiteSpace() 
                    && newData["fregion"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("区域不能为空！"));
            //e.Rules.Add(this.RuleFor("fbillhead", data => data["faddress"]).NotEmpty().WithMessage("详细地址不能为空！"));
        }

        /// <summary>
        /// 执行操作事务前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var dataEntity = e.DataEntitys[0];

            //我的令牌
            var tokenId = "";
            var tokenResult = this.Gateway.InvokeLocal<object>(this.Context, new CommonFormDTO() { FormId = "sys_mainfw", OperationNo = "getmytoken" });
            if (tokenResult is DynamicDTOResponse)
            {
                tokenId = Convert.ToString((tokenResult as DynamicDTOResponse).OperationResult.SrvData);
            }

            //加入云链
            var result = this.Gateway.Invoke(this.Context,
                TargetSEP.EisService,
                new CommonFormDTO()
                {
                    FormId = "eis_gateway",
                    OperationNo = "join",
                    SimpleData = new Dictionary<string, string>()
                    {
                        { "companyid", this.Context.Company },
                        { "companyno", "" },
                        { "companyname", this.Context?.Companys?.FirstOrDefault(t => t.CompanyId.EqualsIgnoreCase(this.Context.Company))?.CompanyName },
                        { "tokenid", tokenId },
                        { "tokenType", "jwt" },
                        { "productid", this.ProductId() },
                        { "appserver", this.GetCurrentAppServer().ToString() }
                    }
                });
            if (result is DynamicDTOResponse)
            {
                var simpleData = (result as DynamicDTOResponse)?.OperationResult?.SimpleData ?? new Dictionary<string, string>();
                if (simpleData.ContainsKey("companyId") && !simpleData["companyId"].IsNullOrEmptyOrWhiteSpace())
                {
                    dataEntity["fcoocompanyid"] = simpleData["companyId"];

                    //更新企业产品明细
                    var htmlEntry = this.HtmlForm.GetEntryEntity("fproductentry");
                    var productEntrys = dataEntity["fproductentry"] as DynamicObjectCollection;
                    if (productEntrys != null)
                    {
                        var product = this.GetProductInfo();
                        var productEntry = productEntrys.LastOrDefault(t => Convert.ToString(t["fproductid"]).EqualsIgnoreCase(product.Id));
                        if (productEntry == null)
                        {
                            productEntry = new DynamicObject(htmlEntry.DynamicObjectType);
                            productEntrys.Add(productEntry);
                        }
                        productEntry["fseq"] = 1;
                        productEntry["fproductid"] = product.Id;
                        productEntry["fproductname"] = product.Name;
                        productEntry["fproductalias"] = product.Alias;
                    }
                }
            }
        }

        /// <summary>
        /// 执行操作事务后事件
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var dataEntity = e.DataEntitys[0];

            //更新EIS站点中的企业信息
            var responseResult = this.Gateway.Invoke(
                this.Context,
                TargetSEP.EisService,
                new CommonBillDTO()
                {
                    FormId = "eis_company",
                    OperationNo = "BizUpdateCompany",
                    BillData = "",
                    ExecInAsync = false,
                    AsyncMode = (int)Enu_AsyncMode.Background,
                    SelectedRows = new List<SelectedRow>()
                    {
                        new SelectedRow()
                        {
                            PkValue = dataEntity["id"] as string,
                            BillNo = dataEntity["fnumber"] as string,
                            EntityKey = "",
                            EntryPkValue = ""
                        }
                    },
                    SimpleData = new Dictionary<string, string>
                    {
                        { "flogomax", dataEntity["flogomax"] as string },
                        { "fdescription", dataEntity["fdescription"] as string },
                        { "fenterprisescale", dataEntity["fenterprisescale"] as string },
                        { "fprovince", dataEntity["fprovince"] as string },
                        { "fcity", dataEntity["fcity"] as string },
                        { "fregion", dataEntity["fregion"] as string },
                        { "faddress", dataEntity["faddress"] as string },
                        { "fcontactid", dataEntity["fcontactid"] as string },
                        { "fcontactphone", dataEntity["fcontactphone"] as string },
                        { "fcontactposition", dataEntity["fcontactposition"] as string },
                        { "fcontactmail", dataEntity["fcontactmail"] as string },
                        { "fcompanyurl", dataEntity["fcompanyurl"] as string }
                    }
                }) as CommonBillDTOResponse;
        }

        /// <summary>
        /// 准备操作关联服务
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareBusinessServices(PrepareBusinessServiceEventArgs e)
        {
            base.PrepareBusinessServices(e);
            e.Services.Add(new FormServiceDesc()
            {
                ServiceAlias = "filesyn",
                Condition = "",
                ParamString = new
                {
                    companyFieldKey = "#gw#"
                }.ToJson()
            });
        }
    }
}