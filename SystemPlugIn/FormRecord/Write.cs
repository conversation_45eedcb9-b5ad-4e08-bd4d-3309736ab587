using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.Framework.AppService.SystemPlugIn.FormRecord
{
    /// <summary>
    /// 写入表单动态
    /// </summary>
    [InjectService]
    [FormId("bd_record")]
    [OperationNo("write")]
    public class Write : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 执行操作事务后事件
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            this.Result.IsSuccess = false;

            var bizTipParam = this.GetQueryOrSimpleParam<string>("records").FromJson<JArray>();
            if (bizTipParam.IsNullOrEmpty())
            {
                this.Result.SimpleMessage = "写入表单动态失败：没有有效的动态内容！";
                return;
            }

            Dictionary<string, Tuple<string, string>> dctPkIdMap;
            using (var tran = this.Context.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                dctPkIdMap = this.GetPkIdMap(bizTipParam);

                tran.Complete();

                if (dctPkIdMap.Count <= 0) return;
            }
            List<string> lstSuccessLog = new List<string>();

            //打包日志数据包            
            Dictionary<string, List<LogEntry>> dctLogEntryByOperator = new Dictionary<string, List<LogEntry>>(StringComparer.OrdinalIgnoreCase);
            foreach (JObject kvpItem in bizTipParam)
            {
                var tranId = kvpItem.GetJsonValue<string>("tranId");
                Tuple<string, string> pkId = null;

                if (!dctPkIdMap.TryGetValue(tranId, out pkId)) continue;

                if (pkId.Item1.IsEmptyPrimaryKey()) continue;

                var formId = kvpItem.GetJsonValue<string>("formId");
                var opCode = kvpItem.GetJsonValue<string>("opcode");
                var opName = kvpItem.GetJsonValue<string>("opname");
                var opContent = kvpItem.GetJsonValue<string>("opcontent");

                //调用方操作者名称，不传时直接取 CallerContext.UserName
                var operatorName = kvpItem.GetJsonValue<string>("operator");
                if (operatorName.IsNullOrEmptyOrWhiteSpace())
                {
                    operatorName = this.Context?.CallerContext?.DisplayName;
                    if (operatorName.IsNullOrEmptyOrWhiteSpace())
                    {
                        operatorName = this.Context?.CallerContext?.UserName;
                        if (operatorName.IsNullOrEmptyOrWhiteSpace())
                        {
                            operatorName = this.Context?.DisplayName;
                            if (operatorName.IsNullOrEmptyOrWhiteSpace())
                            {
                                operatorName = this.Context?.UserName;
                            }
                        }
                    }
                }
                operatorName = operatorName ?? "";

                 DateTime opDate = DateTime.Now;
                string opDateStr = kvpItem.GetJsonValue<string>("opdate");
                if (!DateTime.TryParse(opDateStr, out opDate))
                {
                    opDate = DateTime.Now;
                }

                List<AttachEntry> attachEntries = new List<AttachEntry>();
                var attachArray = kvpItem.GetJsonValue<JArray>("attachEntries");
                if (attachArray != null && attachArray.Count > 0)
                {
                    foreach(var attachObject in attachArray)
                    {
                        AttachEntry attachEntry = new AttachEntry();
                        attachEntries.Add(attachEntry);

                        attachEntry.AttachId = attachObject.GetJsonValue<string>("attachId");
                        attachEntry.AttachName = attachObject.GetJsonValue<string>("attachName");
                        attachEntry.AttachExt = attachObject.GetJsonValue<string>("attachExt");
                    }
                }

                List<LogEntry> lstLogObjs = new List<LogEntry>();
                if (!dctLogEntryByOperator.TryGetValue(operatorName, out lstLogObjs))
                {
                    lstLogObjs = new List<LogEntry>();
                    dctLogEntryByOperator[operatorName] = lstLogObjs;
                }

                var logEntry = new LogEntry()
                {
                    BillFormId = formId,
                    LogType = Enu_LogType.RecordType_03,
                    Level = Enu_LogLevel.Info.ToString(),
                    Category = (long)Enu_LogCategory.BizOp | (long)Enu_LogCategory.BizOp_Complete,
                    BillIds = pkId.Item1,
                    BillNos = pkId.Item2,
                    OpCode = opCode,
                    OpName = opName,
                    Content = opContent,
                    OpDate = opDate,
                    AttachEntries = attachEntries
                };
                lstLogObjs.Add(logEntry);
                lstSuccessLog.Add(tranId);
            }

            //保存日志数据
            foreach (var kvpLog in dctLogEntryByOperator)
            {
                CallerContext caller = new CallerContext();
                if (!kvpLog.Key.IsNullOrEmptyOrWhiteSpace())
                {
                    caller.UserName = caller.DisplayName = kvpLog.Key;
                }
                this.Logger.BatchWriteLog(this.Context, kvpLog.Value, caller);
            }
            this.Result.IsSuccess = true;
            this.Result.SrvData = new
            {
                SuccessLogs = lstSuccessLog
            };
        }

        /// <summary>
        /// 根据交易流水号获取对应的主键Id
        /// </summary>
        /// <param name="bizTipParam"></param>
        /// <returns></returns>
        private Dictionary<string, Tuple<string,string>> GetPkIdMap(JArray bizTipParam)
        {
            Dictionary<string, Tuple<string, string>> dctPkIdMap = new Dictionary<string, Tuple<string, string>>(StringComparer.OrdinalIgnoreCase);

            var lstTempTbl = new List<string>();
            //根据表单分组
            var groupList = bizTipParam.GroupBy(o=> o.GetJsonValue<string>("formId"));
            foreach (var group in groupList)
            {
                var formId = group.Key;
                if (formId.IsNullOrEmptyOrWhiteSpace()) continue;

                var allTranIds = group.Select(o => o.GetJsonValue<string>("tranId"));
                if (allTranIds.Any() == false) continue;

                var bizForm = this.MetaModelService.LoadFormModel(this.Context, formId);
                var numberField = bizForm.GetNumberField();
                List<SqlParam> lstParams = new List<SqlParam>();
                var pkIdSql = $"select t0.fid,t0.ftranid,{(numberField != null ? ("" + numberField.FieldName) : "''")} as fnumber from {bizForm.BillHeadTableName} t0 ";
                if (allTranIds.IsGreaterThan(50))
                {
                    var tempTableName = this.DBService.CreateTempTableWithDataList(this.Context, allTranIds);
                    pkIdSql += $"\r\n inner join {tempTableName} temp on temp.fid=t0.ftranid ";

                    lstTempTbl.Add(tempTableName);
                }
                else
                {
                    pkIdSql += "\r\n where 1=1 ";
                    var strFilter = "";
                    var pIndex = 1;
                    foreach (var tranId in allTranIds)
                    {
                        if (strFilter.IsNullOrEmptyOrWhiteSpace())
                        {
                            strFilter += $" t0.ftranid=@tranId{pIndex}";
                        }
                        else
                        {
                            strFilter += $" or t0.ftranid=@tranId{pIndex}";
                        }
                        lstParams.Add(new SqlParam($"tranId{pIndex}", System.Data.DbType.String, tranId));
                        pIndex++;
                    }
                    pkIdSql += $" and ({strFilter})";
                }

                using (var pkIdReader = this.DBService.ExecuteReader(this.Context, pkIdSql, lstParams))
                {
                    while (pkIdReader.Read())
                    {
                        dctPkIdMap[pkIdReader[1] as string] = Tuple.Create(pkIdReader[0] as string, pkIdReader[2] as string);
                    }
                }
            }

            if (lstTempTbl.Count > 0)
            {
                DBService.DeleteTempTableByName(Context, lstTempTbl, true);
            }

            return dctPkIdMap;
        }
    }
}