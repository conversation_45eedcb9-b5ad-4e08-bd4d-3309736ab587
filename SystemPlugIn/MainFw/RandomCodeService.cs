using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.DataTransferObject;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Drawing;
using JieNor.Framework.Interface.Cache;
using ServiceStack;
using System.IO;
using JieNor.Framework.IoC;
using JieNor.Framework.DataTransferObject.DynamicImage;

namespace JieNor.Framework.AppService.SystemPlugIn.MainFw
{




    /// <summary>
    /// 验证码服务
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.ExternalOwned)]
    public class RandomCodeService : IRandomCode
    {
        const  string noChar = "123456789";
        const string lChar = "abcdefghigklmnpqrstuvwxyz";
        const string uChar = "ABCDEFGHIGKLMNPQRSTUVWXYZ";

        const string cacheKey = "RandomCodeImg:{0}";

        Random rnd = new Random();
        
        /// <summary>
        /// 生成随机码
        /// </summary>
        /// <param name="param">验证码构建参数</param> 
        /// <returns></returns>
        public   List<string> CreateRandomCode(RandomCodeImgParam param)
        {
            if(param==null )
            {
                param = new RandomCodeImgParam();
            }
            List<string> ret = new List<string>();
            for (int i = 0; i < param.Count ; i++)
            {
                ret.Add(BuildRandomCode(param));
            }

            return ret;
        }


        /// <summary>
        /// 构建随机码字符
        /// </summary>
        /// <param name="param"></param>
        private string BuildRandomCode(RandomCodeImgParam param)
        {
            string allChar = noChar;
            if (param.AddLowerLetter)
            {
                allChar += lChar;
            }
            if (param.AddUpperLetter)
            {
                allChar += uChar;
            }

            string randomCode = ""; 
            for (int i = 0; i < param.Length; i++)
            {
                int index = rnd.Next(0, allChar.Length);
                randomCode += allChar[index];

                allChar.Remove(index, 1);
            }
            return randomCode;
        }

        /// <summary>
        /// 创建随机码图片信息
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public List<RandomCodeInfo> CreateRandomCodeImg(RandomCodeImgParam param)
        {             
            List<string> randomCode = CreateRandomCode(param);
            List<RandomCodeInfo> imgs = new List<RandomCodeInfo>();
            foreach (var rnd in randomCode)
            {
                imgs.Add(CreateImage(param, rnd));
            }


            //记录随机码信息，以便后续的校验
            CacheRandCodeImgInfo(imgs);

            return imgs;
        }


        /// <summary>
        /// 创建随机码图片
        /// </summary>
        /// <param name="randomCode">随机码</param>
        private RandomCodeInfo CreateImage(RandomCodeImgParam param,string randomCode)
        { 
            //创建绘图
            Bitmap img = new Bitmap(randomCode.Length * 16, 25);
            using (Graphics objGraphics = Graphics.FromImage(img))
            {
                objGraphics.SmoothingMode = SmoothingMode.HighQuality;

                //清除整个绘图面并以指定背景色填充
                objGraphics.Clear(param.BackgroundColor);

                //创建画笔
                using (SolidBrush objSolidBrush = new SolidBrush(param.FontColor))
                {
                    AddForeNoisePoint(img,param);
                    AddBackgroundNoisePoint(img, objGraphics);

                    //文字居中
                    StringFormat objStringFormat = new StringFormat(StringFormatFlags.NoClip);
                    objStringFormat.Alignment = StringAlignment.Center;
                    objStringFormat.LineAlignment = StringAlignment.Center;
                     
                    Font objFont = new Font(param.FontFamily, rnd.Next(param.FontSize - 3, param.FontSize), FontStyle.Bold);
                    
                    //验证码旋转，防止机器识别：转转更健康
                    char[] chars = randomCode.ToCharArray();
                    for (int i = 0; i < chars.Length; i++)
                    { 
                        float angle = rnd.Next(-param.RandomAngle, param.RandomAngle);

                        objGraphics.TranslateTransform(12, 12);
                        objGraphics.RotateTransform(angle);
                        objGraphics.DrawString(chars[i].ToString(), objFont, objSolidBrush, -2, 2, objStringFormat);
                        objGraphics.RotateTransform(-angle);
                        objGraphics.TranslateTransform(2, -12);
                    }
                }
            }

            RandomCodeInfo imgInfo = new RandomCodeInfo();
            imgInfo.ImageId = Guid.NewGuid().ToString().Replace ("-","");
            using (var ms = new MemoryStream())
            {
                img.Save(ms, ImageFormat.Bmp);
                ms.Flush();
                ms.Position = 0;
                imgInfo.Image = ms.GetBuffer();
            } 
            imgInfo.Code = randomCode;

            if(System.Diagnostics.Debugger.IsAttached)
            {
                SaveImageWhenDebug(img, imgInfo);
            }

            return imgInfo;
        }

        private static void SaveImageWhenDebug(Bitmap img, RandomCodeInfo imgInfo)
        {
            try
            {
                var path = PathUtils.GetStartupPath();
                var dir = Path.Combine(path, @"app_data\randcodeimage");
                if (!Directory.Exists(dir))
                {
                    Directory.CreateDirectory(dir);
                }
                var file = Path.Combine(dir, @"{0}.Bmp".Fmt(imgInfo.ImageId));
                img.Save(file);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.Assert(false, ex.Message);
            }           
        }


        /// <summary>
        /// 添加前景噪点
        /// </summary>
        /// <param name="objBitmap"></param>
        private    void AddForeNoisePoint( Bitmap objBitmap, RandomCodeImgParam param)
        { 
            for (int i = 0; i < objBitmap.Width * param.ForeNoisePointCount; i++)
            {
                objBitmap.SetPixel(rnd.Next(objBitmap.Width), rnd.Next(objBitmap.Height), param.FontColor);
            }
        }

        /// <summary>
        /// 背景噪点
        /// </summary>
        /// <param name="objBitmap"></param>
        /// <param name="objGraphics"></param>
        private    void AddBackgroundNoisePoint(Bitmap objBitmap, Graphics objGraphics)
        { 
            using (Pen objPen = new Pen(Color.Azure, 0))
            {
                for (int i = 0; i < objBitmap.Width * 2; i++)
                {
                    objGraphics.DrawRectangle(objPen, rnd.Next(objBitmap.Width), rnd.Next(objBitmap.Height), 1, 1);
                }
            }
        }


        /// <summary>
        /// 记录随机码信息，以便后续的校验
        /// </summary>
        /// <param name="imgs"></param>
        private static void CacheRandCodeImgInfo(List<RandomCodeInfo> imgs)
        {
            IRedisCache cache = HostContext.TryResolve<IServiceContainer>()?.BeginNewLifetimeScope()?.GetService<IRedisCache>();
            if (cache != null)
            {
                foreach (var img in imgs)
                {                    
                    cache.Set (cacheKey.Fmt(img.ImageId), img.Code,TimeSpan.FromHours (5));
                    //清空
                    img.Code = "";
                } 
            }
        }


        /// <summary>
        /// 验证码图片校验
        /// </summary>
        /// <param name="imgId"></param>
        /// <param name="randomCode"></param>
        /// <returns></returns>
        public bool ValidateRandomCodeImg(string imgId,string  randomCode)
        {
            IRedisCache cache = HostContext.TryResolve<IServiceContainer>()?.BeginNewLifetimeScope().GetService<IRedisCache>();
            if (cache != null)
            {
                var key = cacheKey.Fmt(imgId);
                var value = cache.Get<string>(key);

                return value.EqualsIgnoreCase(randomCode);
            }

            return false;
        }


    }







 

}
