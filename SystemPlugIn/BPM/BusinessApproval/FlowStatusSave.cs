using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using JieNor.Framework.AppService.OperationService.ApprovalFlow;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.BPM;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.Framework.AppService.SystemPlugIn.BPM.BusinessApproval
{
    /// <summary>
    /// 流程信息：启用，停用，编辑
    /// </summary>
    [InjectService]
    [FormId("bpm_businessapproval")]
    [OperationNo("optype")]
    public class FlowStatusSave : AbstractOperationServicePlugIn
    {
        /*
        * 启用：后台服务根据传过来的行流程id，将表体状态更新为启用，并且将子表体关联的行置为启用（目的就是进入写保护状态）。
        * 停用：仅仅是将父表体状态更新为停用，但子表体关联实际行，不能更新继续处于写保护状态。
        * 修改：根据点击按钮传过来的参数，决定返回一个行流程id，以及流程数据，如果此行已进入写保护状态，则只返回流程图数据，不返回id，否则返回当前子行的id给前端传入设计器业务，将来设计器页面在调用保存时，将此行id传回服务端，以方便服务端决定是新增子行，还是更新子行。
        */
        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            var flowId = this.GetQueryOrSimpleParam<string>("rowid");
            if (flowId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("参数 rowid 为空，请检查！");

            //根据流程Id获取对应的业务审批动态对象
            string strSql = $"select fid from t_bpm_businessflow where fentryid=@fentryid";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fentryid", System.Data.DbType.String, flowId)
            };
            string approvalId = string.Empty;
            using (var reader = this.DBService.ExecuteReader(this.Context, strSql, sqlParam))
            {
                if (reader.Read())
                {
                    approvalId = Convert.ToString(reader["fid"]);
                }
            }
            if (approvalId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"此流程信息关联的业务审批对象不存在或已被删除！");
            }

            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "bpm_businessapproval");
            var dm = this.Context.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            DynamicObject approval = dm.Select(approvalId) as DynamicObject;
            if (approval == null) throw new BusinessException($"此流程信息关联的业务审批对象【{approvalId}】不存在或已被删除！");

            var flowEntrys = approval["fflowentry"] as DynamicObjectCollection;
            var flowEntry = flowEntrys.FirstOrDefault(f => Convert.ToString(f["id"]).EqualsIgnoreCase(flowId));
            if (flowEntry == null) throw new BusinessException($"此流程信息【{flowId}】不存在或已被删除！");

            var versionEntrys = flowEntry["fversionentry"] as DynamicObjectCollection;

            var srccessMessage = "操作成功！";
            var flowStatus = string.Empty;
            var optype = this.GetQueryOrSimpleParam<string>("optype", "").ToLower();
            switch (optype)
            {
                case "on":
                    //启用
                    flowEntry["fflowstatus"] = flowStatus = "flow_sta02";
                    this.Enabled(htmlForm, flowEntry, versionEntrys);
                    if (this.Result.IsSuccess == false) return;
                    srccessMessage = "启用成功！";
                    break;
                case "off":
                    //停用
                    flowEntry["fflowstatus"] = flowStatus = "flow_sta03";
                    srccessMessage = "停用成功！";
                    break;
                case "edit":
                    //修改
                    this.Edit(htmlForm, flowEntry, versionEntrys);
                    return;
                default:
                    break;
            }

            //保存业务审批
            dm.Save(approval);

            //启用 或 停用
            if (optype.EqualsIgnoreCase("on") || optype.EqualsIgnoreCase("off"))
            {
                this.AddSetValueAction("fflowstatus", flowStatus, flowId);
            }

            this.Result.SrvData = approval;
            this.Result.IsSuccess = true;
            this.Result.ComplexMessage.SuccessMessages.Add(srccessMessage);
        }

        /// <summary>
        /// 启用
        /// </summary>
        /// <param name="htmlForm"></param>
        /// <param name="flowEntry"></param>
        /// <param name="versionEntrys"></param>
        private void Enabled(HtmlForm htmlForm, DynamicObject flowEntry, DynamicObjectCollection versionEntrys)
        {
            var approvalFlowService = this.Container.GetService<IApprovalFlowService>();

            if (flowEntry["fverno"].IsNullOrEmptyOrWhiteSpace())
            {
                flowEntry["fverno"] = approvalFlowService.GetNewFlowVersionNo(this.Context, flowEntry["id"] as string);
            }
            flowEntry["fdistributor"] = this.Context.UserId;
            flowEntry["fdistributedate"] = DateTime.Now;

            //将子表体关联的行置为启用（目的就是进入写保护状态）。
            var currentMaxVerNo = "";
            foreach (var verEntry in versionEntrys)
            {
                if (!Convert.ToString(verEntry["fverstatus"]).EqualsIgnoreCase("flowver_sta02"))
                {
                    verEntry["fverstatus"] = "flowver_sta02";
                    verEntry["fdistributor"] = this.Context.UserId;
                    verEntry["fdistributedate"] = DateTime.Now;
                }
                if (verEntry["fvername"].IsNullOrEmptyOrWhiteSpace())
                {
                    currentMaxVerNo = approvalFlowService.GetNewFlowVersionNo(this.Context, flowEntry["id"] as string, currentMaxVerNo);
                    verEntry["fvername"] = currentMaxVerNo;
                }
            }
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="htmlForm"></param>
        /// <param name="flowEntry"></param>
        /// <param name="versionEntrys"></param>
        private void Edit(HtmlForm htmlForm, DynamicObject flowEntry, DynamicObjectCollection versionEntrys)
        {
            var flowEntity = htmlForm.GetEntryEntity("fflowentry");
            var versionEntity = htmlForm.GetSubEntryEntity("fversionentry");

            DynamicObject editVerEntry = null;

            //根据流程版本号匹配流程版本记录
            var _verEntry = versionEntrys.FirstOrDefault(f => Convert.ToString(f["fvername"]).EqualsIgnoreCase(Convert.ToString(flowEntry["fverno"])));
            if (_verEntry == null) throw new BusinessException($"此流程信息没有关联的版本记录，请检查！");

            //如果流程版本记录已启用，则将此流程版本记录复制一份作为新版本
            if (Convert.ToString(_verEntry["fverstatus"]).EqualsIgnoreCase("flowver_sta02"))
            {
                editVerEntry = versionEntity.DynamicObjectType.CreateInstance() as DynamicObject;
                editVerEntry["id"] = "";
                editVerEntry["fflowname"] = _verEntry["fflowname"];
                editVerEntry["fvername"] = "";
                editVerEntry["fismain"] = false;
                editVerEntry["fverstatus"] = "flowver_sta01";
                editVerEntry["fdistributor"] = "";
                editVerEntry["fdistributornumber"] = "";
                editVerEntry["fdistributedate"] = null;
                editVerEntry["foperationid"] = this.Context.UserId;
                editVerEntry["fcreatedate"] = DateTime.Now;
                editVerEntry["fdesc"] = _verEntry["fdesc"];
                editVerEntry["fdetails"] = _verEntry["fdetails"];
            }
            else
            {
                editVerEntry = _verEntry;
            }

            FlowVersionEdit.SetLatestMultiField(this.Context, editVerEntry);

            var uiConverter = this.Container.GetService<IUiDataConverter>();
            var uiFlowData = uiConverter.PackageEntityData(this.Context, htmlForm, flowEntity, flowEntry);
            var uiVersionData = uiConverter.PackageEntityData(this.Context, htmlForm, versionEntity, editVerEntry);

            this.Result.IsSuccess = true;
            this.Result.SrvData = new
            {
                flowEntry = uiFlowData,
                verEntry = uiVersionData,
                verId = editVerEntry["id"],
                rowid = flowEntry["id"],
                fflowentry = new
                {
                    id = flowEntry["id"],
                    fflowtitle = flowEntry["fflowtitle"],
                    fdescription_e = flowEntry["fdescription"]
                }
            };
        }
    }
}