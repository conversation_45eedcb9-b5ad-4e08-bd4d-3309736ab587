using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Newtonsoft.Json;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.DataTransferObject.Report;

namespace JieNor.Framework.AppService.SystemPlugIn.Bas.BizWarnScheme
{
    /// <summary>
    /// 业务预警方案：保存
    /// </summary>
    [InjectService]
    [FormId("bas_bizwarnscheme")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */

            //校验预警频率
            var jobScheduleService = this.Container.GetService<IJobScheduleService>();
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var hertz = Convert.ToString(newData["fhertz"]);
                var isOk = jobScheduleService.CheckCronExpr(this.Context, hertz, true);
                return isOk;
            }).WithMessage("预警频率格式错误！"));

            //校验预警条件
            string errorMsg = null;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var condition = Convert.ToString(newData["fcondition"]);
                if (!condition.IsNullOrEmptyOrWhiteSpace())
                {
                    var filterList = JsonConvert.DeserializeObject<List<FilterRowObject>>(condition);
                    if (filterList != null)
                    {
                        //左括号数必须等于右括号数
                        var leftBrackets = string.Join("", filterList.Select(o => o.LeftBracket));
                        var rightBrackets = string.Join("", filterList.Select(o => o.RightBracket));
                        if (leftBrackets.Length != rightBrackets.Length)
                        {
                            errorMsg = $"预警条件中的左括号总数必须等于右括号总数！";
                            return false;
                        }

                        foreach (var filter in filterList)
                        {
                            if(filter.Id.IsNullOrEmptyOrWhiteSpace() || filter.Operator.IsNullOrEmptyOrWhiteSpace())
                            {
                                errorMsg = $"预警条件中的字段、比较符不能为空！";
                                return false;
                            }
                        }
                    }
                }
                return true;
            }).WithMessage("{0}", (n, o) => errorMsg));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var bizOp = Convert.ToString(newData["fbizop"]);
                var msgType = Convert.ToString(newData["fmsgtype"]);
                if (bizOp.IsNullOrEmptyOrWhiteSpace() && msgType.IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("预警操作为空时，消息类型不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var bizOp = Convert.ToString(newData["fbizop"]);
                var msgTitle = Convert.ToString(newData["fmsgtitle"]);
                if (bizOp.IsNullOrEmptyOrWhiteSpace() && msgTitle.IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("预警操作为空时，消息标题不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var bizOp = Convert.ToString(newData["fbizop"]);
                var sendObjYg = Convert.ToString(newData["fsendobjyg"]);
                var sendObjJs = Convert.ToString(newData["fsendobjjs"]);
                var sendObjBl = Convert.ToString(newData["fsendobjbl"]);
                if (bizOp.IsNullOrEmptyOrWhiteSpace()
                    && sendObjYg.IsNullOrEmptyOrWhiteSpace()
                    && sendObjJs.IsNullOrEmptyOrWhiteSpace()
                    && sendObjBl.IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("预警操作为空时，发送对象(员工)、发送对象(角色)、发送对象(变量)不能同时为空！"));

            string errorMsg2 = null;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var msgTitle = Convert.ToString(newData["fmsgtitle"]);
                return CheckTemplateText(msgTitle, out errorMsg2);
            }).WithMessage("消息标题中不允许出现 {0}", (n, o) => errorMsg2));

            string errorMsg3 = null;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var msgContent = Convert.ToString(newData["fmsgcontent"]);
                return CheckTemplateText(msgContent, out errorMsg3);
            }).WithMessage("消息正文中不允许出现 {0}", (n, o) => errorMsg3));
        }

        /// <summary>
        /// 检查模版字符串
        /// </summary>
        /// <param name="text"></param>
        /// <param name="errorMsg"></param>
        /// <returns></returns>
        private bool CheckTemplateText(string text, out string errorMsg)
        {
            errorMsg = "";

            var strs = new string[] { "{{", "}}", "{}", "{{}", "{}}" };
            if (!text.IsNullOrEmptyOrWhiteSpace())
            {
                if (text.ContainsAny(strs))
                {
                    errorMsg = string.Join(" 、", strs);
                    return false;
                }
            }
            return true;
        }
    }
}