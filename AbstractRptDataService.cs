using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.Interface.Report;
using JieNor.Framework.DataTransferObject.Print;
using JieNor.Framework.MetaCore.FormModel.Office;

namespace JieNor.Framework.Interface
{

    /// <summary>
    /// Office端： 报表数据查询服务
    /// </summary> 
    public abstract class OfficeRptDataService : AbstractOperationService, IReportDataService
    {



        #region 禁止重载的方法，避免干扰派生类
        protected sealed override void AfterExecute(ref DynamicObject[] dataEntities)
        {
            base.AfterExecute(ref dataEntities);
        }

        protected sealed override bool AutoSaveData
        {
            get
            {
                return base.AutoSaveData;
            }
        }


        protected sealed override void BeforeExecute(ref DynamicObject[] dataEntities)
        {
            base.BeforeExecute(ref dataEntities);
        }

        protected sealed override bool CanDoWriteback
        {
            get
            {
                return base.CanDoWriteback;
            }
        }

        //protected sealed override void DoWriteback(DynamicObject[] dataEntities)
        //{
        //    base.DoWriteback(dataEntities);
        //}


        protected sealed override void InitializeOperationDataEntities(ref DynamicObject[] dataEntities)
        {
            base.InitializeOperationDataEntities(ref dataEntities);
        }

        protected sealed override List<IDataValidRule> PrepareValidationRules()
        {
            return base.PrepareValidationRules();
        }

        protected sealed override void SaveDataEntities(ref DynamicObject[] dataEntities)
        {
            base.SaveDataEntities(ref dataEntities);
        }

        protected sealed override bool IgnoreOpMessage
        {
            get
            {
                return base.IgnoreOpMessage;
            }
        }
        #endregion

        protected override string OperationName
        {
            get
            {
                return "报表查询";
            }
        }


        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_View;
            }
        }

        /// <summary>
        /// 列表操作上下文
        /// </summary>
        protected ReportOperationContext ListOperationContext
        {
            get
            {
                return this.OperationContext as ReportOperationContext;
            }
        }

        /// <summary>
        /// 是否office端查询
        /// </summary>
        protected virtual bool IsOffice
        {
            get
            {
                return this.GetQueryOrSimpleParam("office", "false").EqualsIgnoreCase("true");
            }
        }


        FilterSchemeObject filter = null;
        /// <summary>
        /// 报表过滤条件（过滤条件、排序、显示隐藏列等）：由客户端传过来
        /// </summary>
        protected virtual FilterSchemeObject FilterObject
        {
            get
            {
                if (filter == null)
                {
                    var filterString = this.GetQueryOrSimpleParam<string>("filter", "");
                    if (filterString.IsNullOrEmptyOrWhiteSpace())
                    {
                        return new FilterSchemeObject();
                    }

                    filter = JsonConvert.DeserializeObject<FilterSchemeObject>(filterString);
                }
                if (filter == null)
                {
                    filter = new FilterSchemeObject();
                }
                filter.DataTitle = this.DataTitle;
                filter.FilterTitle = "";
                foreach (var item in this.FilterTitle)
                {
                    filter.FilterTitle += item.Value + Environment.NewLine;
                }

                if (this.ListOperationContext != null)
                {
                    filter.PageIndex = this.ListOperationContext.PageIndex;
                    filter.PageCount = this.ListOperationContext.PageCount;
                }
                else
                {
                    filter.PageIndex = 1;
                    filter.PageCount = this.GetQueryOrSimpleParam<int>("count", 1000000); ;
                }
                return filter;
            }
        }

        /// <summary>
        /// 优先级
        /// </summary>
        public virtual int Priority
        {
            get
            {
                return 0;
            }
        }

        /// <summary>
        /// 设置数据的标题：报表数据 -- 设置为报表名称，列表数据--设置为列表名称
        /// </summary>
        protected virtual string DataTitle
        {
            get;
            set;
        }


        /// <summary>
        /// 设置返回的过滤条件信息：一般来说，报表查询时，在报表查询出来的列表上方，显示一些相应的过滤条件信息
        /// </summary>
        public virtual Dictionary<string, string> FilterTitle
        {
            get
            {
                return new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            }
        }




        /// <summary>
        /// 报表列头结构对象
        /// </summary>
        protected ColumnHeadObject ColumnHead
        {
            get;
            private set;
        }

        /// <summary>
        /// 构建报表列头信息：动态生成列的报表通常通过重载此方法实现动态构建报表列头信息
        /// </summary>
        /// <returns></returns>
        protected virtual ColumnHeadObject BuildColumnHeads()
        {
            ColumnHeadObject head = new ColumnHeadObject();
            head.Caption = this.DataTitle;
            var cols = GetRptColObjectFromModel();
            foreach (var item in cols)
            {
                head.AddSubHead(item);
            }
            return head;
        }




        /// <summary>
        /// 从文件获取报表模型里面定义的字段列表
        /// </summary>
        /// <returns></returns>
        protected virtual RptTemplateInfo GetRptTemplateInfo()
        {
            if (this.OperationContext is ReportOperationContext)
            {
                return (this.OperationContext as ReportOperationContext).ReportTemplate;
            }

            RptTemplateInfo tmpl = null;
            string strFileFilter = FilterObject.BillFormId + "*.rpt.json";
            var sqlPath = System.IO.Path.Combine(PathUtils.GetStartupPath(), "mdl");
            var allfiles = System.IO.Directory.GetFiles(sqlPath, strFileFilter, System.IO.SearchOption.AllDirectories).ToList();
            if (allfiles == null || allfiles.Count == 0)
            {
                throw new Exception("找不到报表模型文件 " + strFileFilter);
            }

            foreach (var file in allfiles)
            {
                var txt = System.IO.File.ReadAllLines(file, System.Text.Encoding.Default);
                if (txt.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                var line = txt.Where(f => !f.StartsWith("//"));
                var json = string.Join(Environment.NewLine, line);
                if (json.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                tmpl = json.FromJson<RptTemplateInfo>();
                if (tmpl == null)
                {
                    throw new Exception("报表模型文件设置错误 " + strFileFilter);
                }

                break;
            }

            return tmpl;
        }


        /// <summary>
        /// 从文件获取报表模型里面定义的字段列表
        /// </summary>
        /// <returns></returns>
        protected List<ColumnObject> GetRptColObjectFromModel()
        {
            List<ColumnObject> cols = new List<ColumnObject>();
            var tmpl = GetRptTemplateInfo();
            foreach (var item in tmpl.col)
            {
                cols.Add(item.ConvertTo());
            }
            this.DataTitle = tmpl.title;

            cols.ForEach(f => f.DBFieldName = f.Id);

            return cols;
        }

        /// <summary>
        /// 返回查询数据 
        /// </summary>
        /// <param name="dataEntities"></param>
        protected sealed override void DoExecute(ref DynamicObject[] dataEntities)
        {
            this.ColumnHead = BuildColumnHeads();
            ListDesc desc;
            using (var reader = BuildQueryData(out desc))
            {
                var datas = PackQueryData(reader, desc, ColumnHead.AllColumns);
                datas.ColHeads = this.ColumnHead;
                datas.RptTmplInfo = GetRptTemplateInfo();

                //设置报表查询返回的数据
                this.OperationContext.Result.SrvData = datas.ToJson();

                this.OperationContext.Result.IsSuccess = true;
            }
        }


        protected QueryDataInfo PackQueryData(IDataReader reader, ListDesc desc, List<ColumnObject> cols)
        {
            QueryDataInfo datas = new QueryDataInfo();

            if (cols != null && cols.Count > 0)
            {
                if (IsOffice)
                {
                    datas.OfficeDatas = PackExcelData(reader, cols);
                }
                else
                {
                    datas.Datas = PackReportData(reader, cols);
                }
            }
            datas.DatasDesc = desc;
            datas.DataTitle = this.DataTitle;
            datas.FilterTitle = this.FilterTitle;

            return datas;
        }

        private Dictionary<string, List<object>> PackExcelData(IDataReader reader, List<ColumnObject> cols)
        {
            Dictionary<string, List<object>> lstListData = new Dictionary<string, List<object>>(StringComparer.OrdinalIgnoreCase);
            for (int colIndex = 0; colIndex < cols.Count; colIndex++)
            {
                if (!lstListData.ContainsKey(cols[colIndex].Id))
                {
                    lstListData.Add(cols[colIndex].Id, new List<object>());
                }
            }
            while (reader.Read())
            {
                for (int colIndex = 0; colIndex < cols.Count; colIndex++)
                {
                    var key = cols[colIndex].Id;
                    var value = reader[cols[colIndex].DBFieldName];
                    if (value is DBNull)
                    {
                        value = "";
                    }
                    if (cols[colIndex].ColType == QueryColTypeEnum.Text)
                    {
                        lstListData[key].Add(string.Format("'{0}", value));
                    }
                    else
                    {
                        lstListData[key].Add(value);
                    }
                }
            }

            return lstListData;
        }

        /// <summary>
        /// 提供一个工具方法，可以针对dataReader与列信息进行数据打包
        /// </summary>
        /// <param name="reader"></param>
        /// <param name="cols"></param>
        /// <returns></returns>
        protected List<Dictionary<string, object>> PackReportData(IDataReader reader, List<ColumnObject> cols)
        {
            List<Dictionary<string, object>> lstListData = new List<Dictionary<string, object>>();
            while (reader.Read())
            {
                Dictionary<string, object> dctRowObj = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
                for (int colIndex = 0; colIndex < cols.Count; colIndex++)
                {
                    var objVal = reader[cols[colIndex].DBFieldName];
                    dctRowObj[cols[colIndex].DBFieldName] = objVal is DBNull ? "" : objVal;
                }
                lstListData.Add(dctRowObj);
            }

            return lstListData;
        }

        /// <summary>
        /// 生成报表明细数据的sql
        /// </summary>
        /// <param name="desc"></param>
        /// <returns></returns>
        protected abstract IDataReader BuildQueryData(out ListDesc desc);

        #region Implement Interface IReportDataService：此接口供报表领域模型使用
        /// <summary>
        /// 触发报表数据打包后的行为
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="rptOperCtx"></param>
        /// <param name="rptRowObjs"></param>
        protected virtual void AfterPackReportData(UserContext userCtx, ReportOperationContext rptOperCtx, IEnumerable<Dictionary<string, object>> rptRowObjs)
        {

        }

        /// <summary>
        /// 构建报表图表数据源
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="rptOperCtx"></param>
        /// <param name="rptDataSource"></param>
        protected virtual void BuildRptChartDataSource(UserContext userCtx, ReportOperationContext rptOperCtx, ReportDataSource rptDataSource)
        {

        }



        /// <summary>
        /// 构建报表图表数据源
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="rptOperCtx"></param>
        /// <param name="rptModelDesc"></param>
        protected virtual void BuildRptChartDataDesc(UserContext userCtx, ReportOperationContext rptOperCtx, ReportModelDesc rptModelDesc)
        {

        }

        /// <summary>
        /// 获取报表表格结构信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="rptOperCtx"></param>
        /// <returns></returns>
        public ReportModelDesc GetRptGridModel(UserContext userCtx, ReportOperationContext rptOperCtx)
        {
            PlugInProxy<IOperationServicePlugIn> plugInProxy;
            rptOperCtx.Option.TryGetVariableValue<PlugInProxy<IOperationServicePlugIn>>("_plugInProxy_", out plugInProxy);
            this.Initialize(plugInProxy, rptOperCtx, this.PerfDataView);

            ReportModelDesc model = new ReportModelDesc();
            model.RptGridModel = this.BuildColumnHeads();

            this.BuildRptChartDataDesc(userCtx, rptOperCtx, model);
            return model;
        }

        /// <summary>
        /// 获取报表明细数据
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="rptOperCtx"></param>
        /// <returns></returns>
        public ReportDataSource GetRptDetailData(UserContext userCtx, ReportOperationContext rptOperCtx)
        {
            PlugInProxy<IOperationServicePlugIn> plugInProxy;
            rptOperCtx.Option.TryGetVariableValue<PlugInProxy<IOperationServicePlugIn>>("_plugInProxy_", out plugInProxy);
            this.Initialize(plugInProxy, rptOperCtx, this.PerfDataView);

            ReportDataSource rptData = new ReportDataSource();
            List<Dictionary<string, object>> lstRptRowObjs = new List<Dictionary<string, object>>();
            this.ColumnHead = BuildColumnHeads();
            ListDesc desc;
            var reader = BuildQueryData(out desc);
            if (reader != null)
            {
                using (reader)
                {
                    var datas = PackQueryData(reader, desc, ColumnHead.AllColumns);
                    if (datas != null)
                    {
                        lstRptRowObjs = datas.Datas ?? new List<Dictionary<string, object>>();
                    }
                }
            }

            //给实现类干预数据的机会。
            this.AfterPackReportData(userCtx, rptOperCtx, lstRptRowObjs);

            rptData.RptGridDataSource = lstRptRowObjs;
            desc.CurrentRows = lstRptRowObjs.Count;
            desc.Bills = desc.Rows;
            rptData.ListDesc = desc;

            //生成图表配置信息
            this.BuildRptChartDataSource(userCtx, rptOperCtx, rptData);

            return rptData;
        }




        public string PrintRptData(UserContext userCtx, ReportOperationContext rptOperCtx)
        {
            PlugInProxy<IOperationServicePlugIn> plugInProxy;
            rptOperCtx.Option.TryGetVariableValue<PlugInProxy<IOperationServicePlugIn>>("_plugInProxy_", out plugInProxy);
            this.Initialize(plugInProxy, rptOperCtx, this.PerfDataView);

            var data = this.OperationContext.HtmlForm.GetDynamicObjectType(this.UserCtx).CreateInstance() as DynamicObject;
            var dataRows = data["freportlist"] as DynamicObjectCollection;
            List<DynamicObject> dataEntities = new List<DynamicObject>() { data };
            this.ColumnHead = BuildColumnHeads();
            ListDesc desc;
            var reader = BuildQueryData(out desc);
            if (reader != null)
            {
                using (reader)
                {
                    while (reader.Read())
                    {
                        var row = dataRows.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                        dataRows.Add(row);
                        for (int colIndex = 0; colIndex < ColumnHead.AllColumns.Count; colIndex++)
                        {
                            var fld = this.OperationContext.HtmlForm.GetFieldIgnoreCase(ColumnHead.AllColumns[colIndex].DBFieldName);
                            if (fld != null)
                            {
                                var objVal = reader[ColumnHead.AllColumns[colIndex].DBFieldName];
                                row[fld.PropertyName] = objVal is DBNull ? "" : objVal;
                            }
                        }
                    }
                }
            }

            var strSql = $@"
            select fid,fname from t_bas_officetmpl 
            where ftmpltype='print' and (fmainorgid='{this.UserCtx.Company}' or fmainorgid='0')
            and fsrcformid ='{this.OperationContext.HtmlForm.Id}' 
            order by fmainorgid desc, fdefault desc";
            var tmplDatas = this.DBService.ExecuteDynamicObject(this.UserCtx, strSql);

            var tmplId = tmplDatas.FirstOrDefault()?["fid"]?.ToString();
            var svc = this.Container.GetService<IPrintService>();
            PrintOption templete = svc.GetPrintTmpl(UserCtx, this.OperationContext.HtmlForm.Id, tmplId);
            if (templete == null)
            {
                throw new Exception(string.Format("{0} 未定义套打模板，无法进行预览及打印", this.OperationContext.HtmlForm.Caption));
            }
            templete.IsExport = this.GetQueryOrSimpleParam<bool>("exportExcel", false);

            //允许业务插件自行设置打印的数据及mdl
            var printDatas = from p in dataEntities
                             select new PrintData() { BizData = p };
            var printContext = new PrintContext(this.OperationContext.UserContext)
            {
                BizDatas = printDatas.ToList(),
                FormMeta = this.OperationContext.HtmlForm,
                PrintOption = templete
            };
            var ae = new OnCustomServiceEventArgs()
            {
                EventName = OnCustomServiceEventArgs.BeforPrintParameter,
                EventData = printContext,
                DataEntities = dataEntities.ToArray(),
            };
            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", ae);

            var offCtx = svc.BuildPrintContext(this.UserCtx, printContext.FormMeta, printContext.BizDatas, printContext.PrintOption);
            var url = svc.PrintWithTemplete(offCtx);
            url = url.Replace("\\", "/");
            url = url.Substring(url.LastIndexOf("/prints"));

            return url;
        }



        #endregion
    }


}