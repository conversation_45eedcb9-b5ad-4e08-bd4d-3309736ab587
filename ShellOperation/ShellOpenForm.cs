using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.OpData;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.ShellOperation
{
    /// <summary>
    /// 外壳服务：打开表单
    /// </summary>
    [InjectService("shellopenform")]
    public class ShellOpenForm : AbstractShellOperationService
    {
        private const string CST_KEY_FormShowParameter = "_FormShowParameter_";

        /// <summary>
        /// 共享单据时的参数信息
        /// </summary>
        protected ShareBillParam ShareBillPara { get; set; }

        /// <summary>
        /// 执行外壳服务
        /// </summary>
        protected override void ExecuteService()
        {
            if (this.ShareBillPara.IsNullOrEmpty()
                || this.ShareBillPara.FormId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("无效的链接参数！");
            }
            var formMeta = this.MetaModelService.LoadFormModel(this.Context, this.ShareBillPara.FormId);


            string opCode = "";

            switch (this.ShareBillPara.Status)
            {
                case Enu_BillStatus.New:
                    opCode = "new";
                    break;
                case Enu_BillStatus.View:
                    opCode = "view";
                    break;
                case Enu_BillStatus.Modify:
                    opCode = "modify";
                    break;
                case Enu_BillStatus.Query:
                    opCode = "query";
                    break;
            }
            if (this.ShareBillPara.PkId.IsEmptyPrimaryKey()
                && this.ShareBillPara.DomainType != Enu_DomainType.List)
            {
                opCode = "new";
            }

            var gateWay = this.Context.Container.GetService<IHttpServiceInvoker>();
            IOperationResult result = null;
            var option = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
            option["pageId"] = Guid.NewGuid().ToString();
            option[CST_KEY_FormShowParameter] = this.ShareBillPara.CustomParameter;
            switch (this.ShareBillPara.DomainType)
            {
                case Enu_DomainType.Bill:
                    if (!this.ShareBillPara.PkId.IsEmptyPrimaryKey() && !opCode.EqualsIgnoreCase("new"))
                    {
                        option["id"] = this.ShareBillPara.PkId;
                    }
                    result = gateWay.InvokeBillOperation(this.Context, this.ShareBillPara.FormId, null, opCode, option);
                    break;
                case Enu_DomainType.Dynamic:
                case Enu_DomainType.Parameter:
                    result = gateWay.InvokeBillOperation(this.Context, this.ShareBillPara.FormId, null, opCode, option);
                    break;
                case Enu_DomainType.List:
                    result = gateWay.InvokeListOperation(this.Context, this.ShareBillPara.FormId, null, opCode, option);
                    break;
                default:
                    throw new BusinessException("不支持此操作！");
            }

            if (result != null)
                this.Result.MergeResult(result);
        }

        /// <summary>
        /// 初始化服务参数
        /// </summary>
        /// <param name="actionPara"></param>
        protected override void InitializeService(string actionPara)
        {
            base.InitializeService(actionPara);
            this.ShareBillPara = actionPara?.FromJson<ShareBillParam>();
        }
    }
}
