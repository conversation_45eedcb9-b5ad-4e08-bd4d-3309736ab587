using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.IoC;
using JieNor.Framework.CustomException;
using Newtonsoft.Json.Linq;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.Interface.SystemIntegration;
using Autofac.Features.Metadata;
using JieNor.Framework.Meta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn.SystemIntegration;
using System.Text.RegularExpressions;
using JieNor.Framework.DataEntity.Integration;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Interface.QueryBuilder;
using Newtonsoft.Json;

namespace JieNor.Framework.AppService.ServiceInst
{
    /// <summary>
    /// 传输数据至第三方系统
    /// </summary>
    [InjectService("to3sys")]
    [ServiceMetaAttribute("name", "数据集成服务")]
    [ServiceMetaAttribute("serviceid", HtmlElementType.HtmlBizService_TransferTo3Sys)]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class TransferTo3SysService : AbstractBaseService
    {
        /// <summary>
        /// 系统集成插件代理
        /// </summary>
        protected List<ISyncDataTo3SysPlugIn> PlugIns { get; set; }

        /// <summary>
        /// 协同操作码
        /// </summary>
        protected string OpCode { get; set; }

        /// <summary>
        /// 关闭事务
        /// </summary>
        public override int TransactionMode => _tranMode;

        /// <summary>
        /// 外部应用标识
        /// </summary>
        protected string ExtAppId { get; set; }

        /// <summary>
        /// 字段映射对象主键
        /// </summary>
        protected string BillMapId { get; set; }

        /// <summary>
        /// 服务前置条件
        /// </summary>
        protected string Condition { get; set; }

        /// <summary>
        /// 日志服务
        /// </summary>
        protected ILogServiceEx LogService { get; set; }

        private int _tranMode = 1;


        private Dictionary<string, Dictionary<string, string>> AllRefDataMapping = new Dictionary<string, Dictionary<string, string>>();

        private ISystemIntegrationService systemIntegrationService;

        private HtmlForm syncLogMeta;

        /// <summary>
        /// 服务初始化参数
        /// </summary>
        /// <param name="servicePara"></param>
        protected override void OnServiceInitialized(string servicePara)
        {
            base.OnServiceInitialized(servicePara);

            var jsonPara = JObject.Parse(servicePara);
            var dctPara = jsonPara.GetJsonValue<string>("serConfig", "")?.FromJson<Dictionary<string, object>>(true);
            this.Condition = jsonPara.GetJsonValue("condition", "");
            //只支持两种行为
            this.OpCode = dctPara.GetValue("opcode", "")?.ToString() ?? this.OperationNo;
            if (this.OpCode.IsNullOrEmptyOrWhiteSpace())
            {
                this.OpCode = "save";
            }

            //允许配置决定事务模式：默认关闭
            var tranMode = dctPara.GetValue("tranMode", "0")?.ToString() ?? "0";
            int.TryParse(tranMode, out _tranMode);

            object extAppId = "";
            dctPara.TryGetValue("extAppId", out extAppId);
            this.ExtAppId = Convert.ToString(extAppId);

            if (this.ExtAppId.IsNullOrEmptyOrWhiteSpace())
            {
                this.ExtAppId = this.GetQueryOrSimpleParam<string>("extAppId");
            }

            object billMapId = "";
            dctPara.TryGetValue("billMapId", out billMapId);
            this.BillMapId = Convert.ToString(billMapId);

            if (this.BillMapId.IsNullOrEmptyOrWhiteSpace())
            {
                this.BillMapId = this.GetQueryOrSimpleParam<string>("billMapId");
            }

            //加载系统所有引用映射数据
            this.LoadAllRefDataMapping(this.Context, this.ExtAppId);

            systemIntegrationService = this.Container.GetService<ISystemIntegrationService>();
            syncLogMeta = this.MetaModelService.LoadFormModel(this.Context, "si_datasyncresult");

            this.LogService = this.Container.GetService<ILogServiceEx>();
        }

        /// <summary>
        /// 处理业务数据同步逻辑
        /// </summary>
        /// <param name="dataEntities"></param>
        public override void ExecuteService(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null) return;

            // 对话框提示
            this.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;

            var lstValidObjs = FilterValidDataEntities(dataEntities);
            if (lstValidObjs.Any() == false) return;

            this.Container.GetService<LoadReferenceObjectManager>()?
                .Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), lstValidObjs, true);

            var fieldMapObj = this.GetQueryOrSimpleParam<DynamicObject>("__fieldMapObj__");
            var dm = this.GetDataManager();
            if (fieldMapObj == null)
            {
                if (this.BillMapId.IsNullOrEmptyOrWhiteSpace())
                {
                    return;
                }
                var siFieldMapMeta = this.MetaModelService.LoadFormModel(this.Context, "si_bizobjectfieldmap");

                dm.InitDbContext(this.Context, siFieldMapMeta.GetDynamicObjectType(this.Context));
                fieldMapObj = dm.Select(this.BillMapId)?.Clone(false, false) as DynamicObject;
                fieldMapObj["fextappid"] = this.ExtAppId;
            }
            if (fieldMapObj == null) return;
            var extAppObj = this.systemIntegrationService.GetExternalAppObject(this.Context, fieldMapObj["fextappid"] as string, this.Option);
            if (fieldMapObj == null
                || extAppObj == null)
            {
                throw new BusinessException("同步失败，找不到待同步到的目标系统信息！");
            }

            var fissyncpre = Convert.ToBoolean(fieldMapObj["fissyncpre"]);
            if (fissyncpre)
            {
                var ignoreBaseDataFormIds = Convert.ToString(fieldMapObj["fsyncignorebizobid"]).SplitKey();// new[] { "ydj_unit", "ydj_stockstatus", "bd_lookupbaseentry" };

                // 前置同步基础资料
                if (!SyncMapedBaseData(fieldMapObj, lstValidObjs, ignoreBaseDataFormIds))
                {
                    return;
                }
            }

            var extAppServiceUrl = extAppObj["fserverurl"] as string;
            var extAppToken = extAppObj["fappkey"] as string;

            var targetServer = new TargetServer()
            {
                Host = extAppServiceUrl,
                TokenId = extAppToken?.EncodeFromBase64String() ?? "",
                TokenType = "X-AccessToken",
            };
            //同步到的目标系统
            var syncSysId = fieldMapObj["fsyncmode"] as string;
            this.PlugIns = this.Container.GetService<IEnumerable<Meta<Lazy<ISyncDataTo3SysPlugIn>>>>()
                .Where(o => o.Metadata.GetString(ServiceMetaKeyConsts.ThirdSystemId).EqualsIgnoreCase(syncSysId)
                    && (o.Metadata.GetString(ServiceMetaKeyConsts.FormId).EqualsIgnoreCase(this.HtmlForm.Id)
                            || Regex.IsMatch(this.HtmlForm.Id, $"^{o.Metadata.GetString(ServiceMetaKeyConsts.FormId)}$")))
                .Select(o => o.Value.Value)
                .ToList();

            //触发插件初始化事件
            this.PlugIns.OfType<AbstractSyncDataTo3SysPlugIn>()
                .ToList()
                .ForEach(o => o.InitializePlugIn(this.Context, this.HtmlForm, fieldMapObj, targetServer));

            //触发同步操作执行前事件
            var sendBeforeArgs = new BeforeSendDataToTargetEventArgs(lstValidObjs.ToArray());
            this.PlugIns.ForEach(o => o.BeforeSendDataToTarget(sendBeforeArgs));
            lstValidObjs = sendBeforeArgs.DataEntities.ToList();

            var syncDataList = this.PackBillData(lstValidObjs.ToArray(), this.HtmlForm, fieldMapObj);
            if (syncDataList.Any() == false) return;

            //将配置映射信息发给对方
            Dictionary<string, object> dctBillMapping = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
            dctBillMapping["SrcFormId"] = fieldMapObj["fmyobjectid"] as string;
            dctBillMapping["DstFormId"] = fieldMapObj["fextobjectid"] as string;
            List<Dictionary<string, object>> lstFieldMappings = new List<Dictionary<string, object>>();
            foreach (var mapObj in fieldMapObj["fentity"] as DynamicObjectCollection)
            {
                var myFieldId = mapObj["fmyfieldid"] as string;
                var myFieldObj = this.HtmlForm.GetField(myFieldId);
                if (myFieldObj != null)
                {
                    lstFieldMappings.Add(new Dictionary<string, object>()
                    {
                        { "SrcFieldId", myFieldObj.ApiPropertyName },
                        { "SrcEntityKey", myFieldObj.Entity.ApiPropertyName },
                        { "DstFieldId", mapObj["fextfieldid"] as string },
                        { "DstEntityKey", mapObj["fextentityid"] as string },
                        { "IsPrimaryKey", (bool)mapObj["fispk"]}
                    });
                }
                else
                {
                    //映射字段不存在时，默认为目标系统字段
                    lstFieldMappings.Add(new Dictionary<string, object>()
                    {
                        { "SrcFieldId", mapObj["fextfieldid"] as string },
                        { "SrcEntityKey", "" },
                        { "DstFieldId", mapObj["fextfieldid"] as string },
                        { "DstEntityKey", mapObj["fextentityid"] as string },
                        { "IsPrimaryKey", Convert.ToBoolean(mapObj["fispk"])}
                    });
                }
            }
            dctBillMapping["FieldMappings"] = lstFieldMappings;

            //触发构建映射信息后事件
            var eventArgs = new AfterBuildBillMappingEventArgs(lstValidObjs.ToArray(), dctBillMapping, syncDataList.Select(o => o.Item2).ToArray());
            this.PlugIns.ForEach(o => o.AfterBuildBillMapping(eventArgs));

            //来源表单标识
            //var srcFormId = Convert.ToString(dctBillMapping["SrcFormId"]);
            dctBillMapping.TryGetValue("SrcFormId", out var srcFormId);
            if (srcFormId.IsNullOrEmptyOrWhiteSpace())
            {
                srcFormId = this.HtmlForm.Id;
            }
            var extendParam = fieldMapObj["fextendparam"] as string;
            Dictionary<string, object> dctExtParam = new Dictionary<string, object>();
            try
            {
                dctExtParam = extendParam?.FromJson<Dictionary<string, object>>() ?? new Dictionary<string, object>();
            }
            catch
            {
                dctExtParam = new Dictionary<string, object>();
            }

            syncDataList.ToList().ForEach(o => this.systemIntegrationService.WriteDataSyncResult(this.Context, o.Item2, "字段映射：" + dctBillMapping.ToJson()));

            syncDataList.ToList().ForEach(o => this.systemIntegrationService.WriteDataSyncResult(this.Context, o.Item2, "获取字段映射完成，正在发送数据……"));

            DynamicDTOWrapper targetDto = null;
            if (this.OpCode.EqualsIgnoreCase("save"))
            {
                targetDto = new CommonBillDTO()
                    .SetFormId(srcFormId.ToString())
                    .SetOperationNo(this.OpCode)
                    .SetBillData(syncDataList.Select(o => o.Item1))
                    .SetOption(dctExtParam)
                    .SetOption("__mappingMeta", dctBillMapping.ToJson())
                    .SetOptionFlag((long)Enu_OpFlags.TPSRequest);
            }
            else
            {
                var billPkIdMap = syncDataList.Select(o => Tuple.Create(Convert.ToString(o.Item1["id"]), Convert.ToString(o.Item1["_SrcPkId_"])))
                        .ToDictionary(o => o.Item1, o => o.Item2);
                targetDto = new CommonListDTO()
                    .SetFormId(srcFormId.ToString())
                    .SetOperationNo(this.OpCode)
                    .SetSelectedRows(syncDataList.Select(o => new SelectedRow()
                    {
                        PkValue = Convert.ToString(o.Item1["id"])
                    }))
                    .SetOption(dctExtParam)
                    .SetOption("__mappingMeta", dctBillMapping.ToJson())
                    .SetOption("__billPkIdMap", billPkIdMap.ToJson())
                    .SetOptionFlag((long)Enu_OpFlags.TPSRequest);
            }

            foreach (var item in sendBeforeArgs.Option)
            {
                targetDto.SetOption(item.Key, item.Value);
            }

            //调用数据同步跨站服务

            try
            {
                var result = this.Gateway.Invoke(this.Context, targetServer, targetDto);

                var resp = result as DynamicDTOResponse;
                this.Result.MergeResult(resp?.OperationResult);

                var ae = new AfterSendDataToTargetEventArgs(lstValidObjs.ToArray(), resp.OperationResult,
                    syncDataList.Select(o => o.Item2).ToArray());
                this.PlugIns.ForEach(o => o.AfterSendDataToTarget(ae));

                var k3Resps = resp?.OperationResult?.SrvData?.ToJson()?.FromJson<List<K3IntegrationResponse>>();

                foreach (var logObj in syncDataList)
                {
                    var id = logObj.Item1.GetJsonValue("_SrcPkId_", "");
                    var number = logObj.Item1.GetJsonValue("_SrcNo_", "");
                    if (id.IsNullOrEmptyOrWhiteSpace())
                    {
                        logObj.Item2["fsyncstatus"] = "3";
                        this.systemIntegrationService.WriteDataSyncResult(this.Context, logObj.Item2,
                            resp?.OperationResult.SimpleMessage);
                        this.systemIntegrationService.WriteDataSyncResult(this.Context, logObj.Item2, "数据同步失败!");
                        continue;
                    }

                    if (this.OpCode.EqualsIgnoreCase("save"))
                    {
                        var k3Resp = k3Resps?.FirstOrDefault(s =>
                            s.DataInfo != null && s.DataInfo._SrcPkId_.EqualsIgnoreCase(id));
                        bool isSuccess = Convert.ToBoolean(resp?.OperationResult?.IsSuccess ?? false);//是否发送成功
                        if (k3Resp == null && !isSuccess)
                        {
                            logObj.Item2["fsyncstatus"] = "3";
                            this.systemIntegrationService.WriteDataSyncResult(this.Context, logObj.Item2,
                                resp?.OperationResult.SimpleMessage);
                            this.systemIntegrationService.WriteDataSyncResult(this.Context, logObj.Item2, "数据同步失败!");
                            continue;
                        }

                        logObj.Item2["fsyncstatus"] = "2";
                        logObj.Item2["ftargetbillid"] = k3Resp.PrimaryKeyValue;
                        logObj.Item2["ftargetbillnumber"] = k3Resp.BillNo;
                        if (k3Resp.Messages != null)
                        {
                            foreach (var message in k3Resp.Messages)
                            {
                                this.systemIntegrationService.WriteDataSyncResult(this.Context, logObj.Item2,
                                    message.Value);
                            }
                        }

                        //数据同步状态设置
                        this.systemIntegrationService.WriteDataSyncResult(this.Context, logObj.Item2, "数据同步成功!");
                    }
                    else
                    {
                        var targetBillId = logObj.Item1.GetJsonValue("id", "");
                        var targetBillNo = logObj.Item1.GetJsonValue("no", "");

                        var opName = this.OpCode.EqualsIgnoreCase("delete") ? "删除" : "反审核";

                        if (resp.OperationResult.ComplexMessage.SuccessMessages.Any(s => s.Contains(targetBillNo)))
                        {
                            logObj.Item2["fsyncstatus"] = "2";
                            logObj.Item2["ftargetbillid"] = targetBillId;
                            logObj.Item2["ftargetbillnumber"] = targetBillNo;
                            this.systemIntegrationService.WriteDataSyncResult(this.Context, logObj.Item2,
                                $"数据{opName}成功!");
                        }
                        else
                        {
                            logObj.Item2["fsyncstatus"] = "3";
                            logObj.Item2["ftargetbillid"] = targetBillId;
                            logObj.Item2["ftargetbillnumber"] = targetBillNo;
                            this.systemIntegrationService.WriteDataSyncResult(this.Context, logObj.Item2,
                                $"数据{opName}失败!");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                foreach (var logObj in syncDataList)
                {
                    //数据同步状态设置
                    this.systemIntegrationService.WriteDataSyncResult(this.Context, logObj.Item2,
                        ex.Message + "\r\n" + ex.StackTrace, true);
                }

                throw;
            }
            finally
            {
                this.systemIntegrationService.SaveDataSyncResultAsync(this.Context, this.syncLogMeta,
                    syncDataList.Select(o => o.Item2).ToArray(), this.Option);
            }

        }

        /// <summary>
        /// 过滤符合条件的数据
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private List<DynamicObject> FilterValidDataEntities(DynamicObject[] dataEntities)
        {
            var evaluator = this.Container.GetService<IBizExpressionEvaluator>();
            var bizExpCtx = this.Container.GetService<IBizExpressionContext>();
            bizExpCtx.HtmlForm = this.HtmlForm;
            bizExpCtx.Context = this.Context;

            BizDynamicDataRow dcRow = new BizDynamicDataRow(this.HtmlForm, this.Context, "fbillhead");
            bizExpCtx.BindSetField(new TrySetValueHandler(dcRow.TrySetMember));
            bizExpCtx.BindGetField(new TryGetValueHandler(dcRow.TryGetMember));

            List<DynamicObject> lstValidObjs = new List<DynamicObject>();
            foreach (var dataEntity in dataEntities)
            {
                //如果存在前置条件
                if (!this.Condition.IsNullOrEmptyOrWhiteSpace())
                {
                    //执行表达式条件，如果条件不成立，则不执行后续操作
                    bizExpCtx.BizData = dataEntity;
                    dcRow.ActiveDataObject = dataEntity;
                    var checkResult = evaluator.CheckCondition(this.Condition, bizExpCtx);
                    if (!checkResult)
                    {
                        continue;
                    }
                }

                lstValidObjs.Add(dataEntity);
            }

            return lstValidObjs;
        }

        /// <summary>
        /// 前置同步匹配了映射的基础资料
        /// </summary>
        /// <returns></returns>
        private bool SyncMapedBaseData(DynamicObject fieldMapObj, IEnumerable<DynamicObject> sourceDataEntities, List<string> ignoreBaseDataFormIds)
        {
            // 只同步单据引用的基础资料
            if (this.HtmlForm.ElementType != (int)HtmlElementType.HtmlForm_BillForm)
            {
                return true;
            }

            var baseDataFlds = new List<HtmlBaseDataField>();

            #region 1.判断映射字段里是否有基础资料字段
            if (fieldMapObj == null)
            {
                this.Result.ComplexMessage.ErrorMessages.Add("业务对象映射不存在！");
                return false;
            }

            var fieldEntrys = fieldMapObj["fentity"] as DynamicObjectCollection;
            var productFields = new List<HtmlField>();
            foreach (var fieldEntry in fieldEntrys)
            {
                var fldId = Convert.ToString(fieldEntry["fmyfieldid"]);

                var fld = this.HtmlForm.GetField(fldId);
                if (fld is HtmlBaseDataField)
                {
                    var bdFld = (HtmlBaseDataField)fld;
                    baseDataFlds.Add(bdFld);
                }
                else if (fld is HtmlBasePropertyField)
                {
                    var bpFld = (HtmlBasePropertyField)fld;
                    var controlFld = (HtmlBaseDataField)this.HtmlForm.GetField(bpFld.ControlFieldKey);

                    baseDataFlds.Add(controlFld);
                }
            }

            if (baseDataFlds.Count == 0) return true;

            #endregion

            //// 排除单位、库存状态、关联表单
            //var ignoreBaseDataFormIds = new[] { "ydj_unit", "ydj_stockstatus", "bd_lookupbaseentry" };

            var baseDataFormIds = baseDataFlds
                .Select(s => s.RefFormId?.ToLower())
                .Where(s => !ignoreBaseDataFormIds.Contains(s))
                .Distinct()
                .ToList();

            // 找业务映射
            string sql = $@"select fid as id from t_si_bizobjectfieldmap where fmainorgid='{this.Context.Company}' and fextappid='{this.ExtAppId}' and fforbidstatus='0' and fmyobjectid in ({baseDataFormIds.JoinEx(",", true)})";
            var baseDataFieldMapObjIds = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>()).Select(s => Convert.ToString(s["id"]));
            var baseDataFieldMapObjs = this.Context.LoadBizDataById("si_bizobjectfieldmap", baseDataFieldMapObjIds);

            if (baseDataFieldMapObjs.IsNullOrEmpty()) return true;

            var dataEntitySet = new ExtendedDataEntitySet();
            dataEntitySet.Parse(this.Context, sourceDataEntities, this.HtmlForm);

            foreach (var baseDataFormId in baseDataFormIds)
            {
                var baseDataForm = this.MetaModelService.LoadFormModel(this.Context, baseDataFormId);

                var matchBaseDataFieldMapObjs =
                    baseDataFieldMapObjs.Where(s => Convert.ToString(s["fmyobjectid"]).EqualsIgnoreCase(baseDataFormId));
                var matchBaseDataFlds = baseDataFlds.Where(s => s.RefFormId.EqualsIgnoreCase(baseDataFormId));

                if (!SyncBaseData(baseDataForm, matchBaseDataFieldMapObjs, matchBaseDataFlds, dataEntitySet))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 前置同步基础资料
        /// </summary>
        /// <param name="baseDataForm">基础资料</param>
        /// <param name="fieldMapObjs"></param>
        /// <param name="dataEntitySet">来源数据包</param>
        private bool SyncBaseData(HtmlForm baseDataForm, IEnumerable<DynamicObject> fieldMapObjs, IEnumerable<HtmlBaseDataField> baseDataFlds, ExtendedDataEntitySet dataEntitySet)
        {
            #region 1.获取数据包里的所有基础资料id

            var baseDataIds = new HashSet<string>();

            foreach (var baseDataFld in baseDataFlds)
            {
                var setEntities = dataEntitySet.FindByEntityKey(baseDataFld.EntityKey);
                if (setEntities.IsNullOrEmpty()) continue;

                foreach (var entity in setEntities)
                {
                    var baseDataId = baseDataFld.DynamicProperty.GetValue<string>(entity.DataEntity);
                    if (baseDataId.IsNullOrEmptyOrWhiteSpace())
                    {
                        continue;
                    }

                    if (baseDataFld is HtmlMulBaseDataField)
                    {
                        foreach (var item in baseDataId.SplitKey(","))
                        {
                            baseDataIds.Add(item);
                        }
                    }
                    else
                    {
                        baseDataIds.Add(baseDataId);
                    }
                }
            }

            if (baseDataIds.Count == 0) return true;

            #endregion

            #region 2.根据《数据同步结果》筛选出数据包里未同步的基础资料

            // 非同步基础资料
            List<string> noSyncBaseDataIds = new List<string>();

            var dbService = this.Container.GetService<IDBService>();
            string tempTbl = null;
            string filter = "";

            // 使用从库
            var slaveCtx = this.Context.CreateSlaveDBContext();
            using (var tran = slaveCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                if (baseDataIds.Count >= 20)
                {
                    tempTbl = dbService.CreateTempTableWithDataList(slaveCtx, baseDataIds);

                    filter += $" fid in (select fid from {tempTbl}) ";
                }
                else
                {
                    filter += $" fid in ('{string.Join("','", baseDataIds)}') ";
                }

                var sqlBuilderParameter = BuildNoSyncSqlBuilderParameter(slaveCtx, baseDataForm, filter, null);
                var queryObj = BuildNoSyncQueryObject(slaveCtx, baseDataForm, sqlBuilderParameter, this.ExtAppId);

                #region 记录SQL

                StringBuilder log = new StringBuilder();
                log.Append($"[同步数据(K/3Cloud)]非同步基础资料执行语句：");

                foreach (var sqlParam in sqlBuilderParameter.DynamicParams)
                {
                    log.AppendLine();

                    string name = sqlParam.Name.StartsWith("@") ? sqlParam.Name : "@" + sqlParam.Name;

                    log.Append($"declare {name} nvarchar(100);");
                    log.Append($"set {name}='{sqlParam.Value}'");
                }

                log.AppendLine();
                log.Append(queryObj.SqlNoPage);

                this.LogService.Info(log);

                #endregion

                using (var reader =
                    this.DBService.ExecuteReader(slaveCtx, queryObj.SqlNoPage, sqlBuilderParameter.DynamicParams))
                {
                    while (reader.Read())
                    {
                        var pkId = reader["fbillhead_id"] as string;

                        noSyncBaseDataIds.Add(pkId);
                    }
                }

                tran.Complete();

                if (!tempTbl.IsNullOrEmptyOrWhiteSpace())
                {
                    dbService.DeleteTempTableByName(slaveCtx, tempTbl, true);
                }
            }

            if (noSyncBaseDataIds.IsNullOrEmpty())
            {
                return true;
            }

            #endregion

            #region 3.同步基础资料
            var result = new OperationResult { IsSuccess = true };

            var lstBatchData = noSyncBaseDataIds.Select(s => new SelectedRow { PkValue = s });

            this.LogService.Info($"前置同步基础资料[{baseDataForm.Id}]：" + string.Join(",", noSyncBaseDataIds));

            var gateway = this.Container.GetService<IHttpServiceInvoker>();
            foreach (var fieldMapObj in fieldMapObjs)
            {
                CommonListDTO listDto = new CommonListDTO()
                    .SetSelectedRows(lstBatchData)
                    .SetFormId(baseDataForm.Id)
                    .SetOperationNo("to3sys")
                    .SetTaskId(this.TaskId)
                    .SetOption("__fieldMapObj__", fieldMapObj)
                    .SetOption("extAppId", fieldMapObj["fextappid"]);
                Dictionary<string, object> dctHeader = new Dictionary<string, object>();
                dctHeader["X-AppId"] = this.Context.AppId;
                dctHeader["X-CompanyId"] = this.Context.Company;

                //交给本地操作行为执行，目的是具体同步的代码与按钮操作代码进行复用
                var objResult = gateway.InvokeLocal<object>(this.Context, listDto, Enu_HttpMethod.Post, dctHeader);

                if (objResult is DynamicDTOResponse)
                {
                    var procResult = (objResult as DynamicDTOResponse).OperationResult;

                    result.MergeResult(procResult);
                }
            }

            this.Result.MergeResult(result);

            #endregion

            this.Result.SimpleMessage = $"前置同步基础资料[{baseDataForm.Caption}]：\r\n" + this.Result.SimpleMessage;

            return result.IsSuccess;
        }

        /// <summary>
        /// 打包当前待传输的数据对象
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="hForm"></param>
        /// <param name="fieldMapObject"></param>
        /// <returns></returns>
        private IEnumerable<Tuple<JObject, DynamicObject>> PackBillData(IEnumerable<DynamicObject> dataEntities, HtmlForm hForm, DynamicObject fieldMapObject)
        {
            //var syncLogMeta = this.MetaModelService.LoadFormModel(this.Context, "si_datasyncresult");
            var allMySyncFieldKeys = (fieldMapObject["fentity"] as DynamicObjectCollection)?
                .Select(o => o["fmyfieldid"] as string)
                .Distinct(StringComparer.OrdinalIgnoreCase)
                .ToList();
            string extAppId = Convert.ToString(fieldMapObject["fextappid"]);

            //从全局的引用数据映射表里找到与当前表单要同步字段相关的值映射表
            var allLinkRefDataMapping = new Dictionary<string, Dictionary<string, string>>();
            var allSyncFieldItems = hForm.GetFieldList().Where(o => (o is HtmlBaseDataField || o is HtmlComboField) && allMySyncFieldKeys.Contains(o.Id, StringComparer.OrdinalIgnoreCase));
            foreach (var syncFieldItem in allSyncFieldItems)
            {
                Dictionary<string, string> dctFieldValuesMap = new Dictionary<string, string>();
                if (syncFieldItem is HtmlComboField)
                {
                    if (!this.AllRefDataMapping.TryGetValue((syncFieldItem as HtmlComboField).CategoryFilter, out dctFieldValuesMap))
                    {
                        dctFieldValuesMap = new Dictionary<string, string>();
                    }
                }
                else
                {
                    if (!this.AllRefDataMapping.TryGetValue((syncFieldItem as HtmlBaseDataField).RefFormId, out dctFieldValuesMap))
                    {
                        dctFieldValuesMap = new Dictionary<string, string>();
                    }
                }
                allLinkRefDataMapping[syncFieldItem.Id] = dctFieldValuesMap;
            }

            var packOption = OperateOption.Create();
            packOption.SetOptionFlag(Enu_OpFlags.TPSRequest);
            packOption.SetSyncTargetSEP(new DataTransferObject.Poco.TargetSEP("", ""));
            packOption.SetSyncFieldKeys(allMySyncFieldKeys);
            packOption.SetAuxPropSyncFieldKeys(new List<string>());
            packOption.SetSyncRefDataMapping(allLinkRefDataMapping);
            //packOption.SetNotPackEntityPkId(notPackEntityPkId);

            Dictionary<string, string> dctTargetPkIds = new Dictionary<string, string>();
            Dictionary<string, string> dctTargetNos = new Dictionary<string, string>();
            if (!this.OpCode.EqualsIgnoreCase("save"))
            {
                var strSql = $@"
select t0.fbillid,t0.ftargetbillid,t0.ftargetbillnumber from T_SI_DATASYNCRESULT t0
inner join (
    select fbillid, max(fid) fid 
    from t_si_datasyncresult 
    where fmainorgid='{this.Context.Company}' and fextappid='{extAppId}' and ftargetbillid<>'' and ftargetbillid is not null 
    group by fbillid
) t1 on t0.fid=t1.fid 
where t0.fbillid in ({string.Join(",", dataEntities.Select(o => $"'{Convert.ToString(o["id"])}'"))})
";
                // 使用从库
                var slaveCtx = this.Context.CreateSlaveDBContext();

                using (var reader = this.DBService.ExecuteReader(slaveCtx, strSql))
                {
                    while (reader.Read())
                    {
                        dctTargetPkIds[reader["fbillid"] as string] = reader["ftargetbillid"] as string;
                        dctTargetNos[reader["fbillid"] as string] = reader["ftargetbillnumber"] as string;
                    }
                }
            }

            List<Tuple<JObject, DynamicObject>> lstTargetBillObjs = new List<Tuple<JObject, DynamicObject>>();
            //需要协同的数据包
            var uiConverter = this.Container.GetService<IUiDataConverter>();
            //var numberField = hForm.GetNumberField();
            //var modifyDateField = hForm.GetField("fmodifydate") as HtmlDateTimeField;

            //获得常量字段映射
            var allConstFieldItems = (fieldMapObject["fentity"] as DynamicObjectCollection)?
                .Where(o => o["fmyconstval"]?.IsNullOrEmptyOrWhiteSpace() == false)
                .ToList();

            //获得表达式字段映射
            var allExpressionFieldItems = (fieldMapObject["fentity"] as DynamicObjectCollection)?
                .Where(o => o["fmyfieldexpression"]?.IsNullOrEmptyOrWhiteSpace() == false)
                .ToList();

            IMuSiExpressionParser expressionParser = this.Container.GetService<IMuSiExpressionParser>();

            foreach (var dataEntity in dataEntities)
            {
                var synLogObj = this.systemIntegrationService.CreateDataSyncResult(this.Context, this.syncLogMeta, hForm,
                    dataEntity, extAppId, this.Option);
                this.systemIntegrationService.WriteDataSyncResult(this.Context, synLogObj, "正在打包数据……");

                var be = new BeforePackTargetBillEventArgs(dataEntity, synLogObj);
                this.PlugIns.ForEach(o => o.BeforePackTargetBill(be));
                if (be.Cancel)
                {
                    continue;
                }
                JObject targetBillData = new JObject();
                if (this.OpCode.EqualsIgnoreCase("save"))
                {
                    JObject joSyncData = uiConverter.CreateUIDataObject(this.Context, hForm, dataEntity, packOption);
                    targetBillData = joSyncData.GetJsonValue<JObject>("uiData");

                    //打包常量字段
                    foreach (var mapObj in allConstFieldItems)
                    {
                        var constVal = mapObj["fmyconstval"];
                        var dstFldId = mapObj["fextfieldid"] as string;
                        if (dstFldId?.IsNullOrEmptyOrWhiteSpace() == false)
                        {
                            targetBillData[dstFldId] = JToken.FromObject(constVal ?? "");
                        }
                    }

                    //打包表达式字段
                    foreach (var mapObj in allExpressionFieldItems)
                    {
                        var expression = Convert.ToString(mapObj["fmyfieldexpression"]);
                        var dstFldId = mapObj["fextfieldid"] as string;
                        if (dstFldId?.IsNullOrEmptyOrWhiteSpace() == false)
                        {
                            targetBillData[dstFldId] = JToken.FromObject(expressionParser.Evaluate(this.Context, this.HtmlForm, dataEntity, expression) ?? "");
                        }
                    }
                }
                else
                {
                    var billId = dataEntity["id"] as string;
                    dctTargetPkIds.TryGetValue(billId, out var targetBillId);
                    dctTargetNos.TryGetValue(billId, out var targetBillNo);
                    targetBillData["id"] = targetBillId;
                    targetBillData["no"] = targetBillNo;
                    targetBillData["_SrcPkId_"] = billId;

                    var numberFld = hForm.GetNumberField();
                    // 增加编码
                    targetBillData["_SrcNo_"] =
                        numberFld == null ? "" : Convert.ToString(dataEntity[numberFld.PropertyName]);

                }
                var ae = new AfterPackTargetBillEventArgs(dataEntity, targetBillData, synLogObj, fieldMapObject);
                this.PlugIns.ForEach(o => o.AfterPackTargetBill(ae));

                var targetPkId = Convert.ToString(targetBillData["id"]);

                //如果目标主键不存在，且为非保存操作时，则不打包数据
                if (targetPkId.IsEmptyPrimaryKey()
                    && !this.OpCode.EqualsIgnoreCase("save"))
                {
                    this.systemIntegrationService.WriteDataSyncResult(this.Context, synLogObj, "打包时发现目标主键为空，不进行协同！");
                    continue;
                }

                this.systemIntegrationService.WriteDataSyncResult(this.Context, synLogObj, $"数据打包完成：{targetBillData}");
                this.WriteLog($"打包完成：{targetBillData}");

                var tplObj = Tuple.Create(ae.TargetBillData, ae.LogObject);

                lstTargetBillObjs.Add(tplObj);
            }
            return lstTargetBillObjs;
        }

        /// <summary>
        /// 加载所有引用数据映射
        /// </summary>
        /// <param name="context"></param>
        /// <param name="extAppId"></param>
        private void LoadAllRefDataMapping(UserContext context, string extAppId = "")
        {
            var dataService = this.Container.GetService<IBillDataService>();
            var allRefDataMapObjs = dataService.LoadBySql(this.Context,
                "si_bizobjectfieldvaluemap",
                "fextappid=@extappid",
                new SqlParam("extappid", System.Data.DbType.String, extAppId));

            foreach (var refDataObj in allRefDataMapObjs)
            {
                var myObjectType = refDataObj["fmyobjecttype"] as string;
                var myAuxFormId = refDataObj["fmyauxformid"] as string;
                var myAuxFormId_Txt = refDataObj["fmyauxformid_txt"] as string;
                var myBaseFormId = refDataObj["fmybaseformid"] as string;
                var dctValueMap = new Dictionary<string, string>();
                // 辅助资料
                if (myObjectType == "1" && !myAuxFormId.IsNullOrEmptyOrWhiteSpace())
                {
                    if (!this.AllRefDataMapping.TryGetValue(myAuxFormId_Txt, out dctValueMap))
                    {
                        dctValueMap = new Dictionary<string, string>();
                        this.AllRefDataMapping[myAuxFormId_Txt] = dctValueMap;
                    }

                    var entryValueObjs = refDataObj["fentity"] as DynamicObjectCollection;
                    if (entryValueObjs == null) continue;
                    foreach (var entryObj in entryValueObjs)
                    {
                        var myFieldValue = entryObj["fmyvalueid"] as string;
                        var extFieldValue = entryObj["fextvalueid"] as string;
                        if (myFieldValue.IsNullOrEmptyOrWhiteSpace()) continue;
                        dctValueMap[myFieldValue] = extFieldValue;
                    }
                }
                // 基础资料
                else if (myObjectType == "2" && !myBaseFormId.IsNullOrEmptyOrWhiteSpace())
                {
                    if (!this.AllRefDataMapping.TryGetValue(myBaseFormId, out dctValueMap))
                    {
                        dctValueMap = new Dictionary<string, string>();
                        this.AllRefDataMapping[myBaseFormId] = dctValueMap;
                    }

                    var entryValueObjs = refDataObj["fentity"] as DynamicObjectCollection;
                    if (entryValueObjs == null) continue;
                    foreach (var entryObj in entryValueObjs)
                    {
                        var myFieldValue = entryObj["fmybdid"] as string;
                        var extFieldValue = entryObj["fextvalueid"] as string;
                        if (myFieldValue.IsNullOrEmptyOrWhiteSpace()) continue;
                        dctValueMap[myFieldValue] = extFieldValue;
                    }
                }
            }
        }

        /// <summary>
        /// 构建非同步查询对象
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formMeta"></param>
        /// <param name="sqlBuilderParameter"></param>
        /// <param name="extAppId"></param>
        /// <returns></returns>
        public static QueryObject BuildNoSyncQueryObject(UserContext userCtx, HtmlForm formMeta, SqlBuilderParameter sqlBuilderParameter, string extAppId)
        {
            var querObj = QueryService.BuilQueryObject(sqlBuilderParameter);
            var syncLogSubQuery = @"
            select m1.fbillid,m2.fsyncstatus,m2.fsynctime
            from (
                select fbillid, max(fid) fid 
                from t_si_datasyncresult with(nolock) where fmainorgid=@fmainorgid and fextappid=@fextappid and fmyobjectid=@formId
                group by fbillid
            ) m1
            inner join t_si_datasyncresult m2 with(nolock) on m1.fid=m2.fid";
            var entityTable = QueryService.GetEntityTable(userCtx, formMeta.Id, "fbillhead");
            querObj.SqlFrom += $"\r\n left join ({syncLogSubQuery}) sync on sync.fbillid= {entityTable.FullPkFldName}";

            sqlBuilderParameter.AddParameter(new SqlParam("@formId", System.Data.DbType.String, formMeta.Id));
            // 增加过滤条件：数据同步结果的外部应用为当前外部应用
            sqlBuilderParameter.AddParameter(new SqlParam("@fextappid", System.Data.DbType.String, extAppId));
            sqlBuilderParameter.AddParameter(new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company));

            return querObj;
        }

        /// <summary>
        /// 构建非同步sqlbuilder参数对象
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formMeta"></param>
        /// <param name="filter"></param>
        /// <param name="sqlParams"></param>
        /// <returns></returns>
        public static SqlBuilderParameter BuildNoSyncSqlBuilderParameter(UserContext userCtx, HtmlForm formMeta, string filter, IEnumerable<SqlParam> sqlParams)
        {
            var numberField = formMeta.GetNumberField();
            if (numberField == null)
            {
                throw new BusinessException($"业务对象（{formMeta.Caption}）同步终止：没有指定编码字段！");
            }
            SqlBuilderParameter sqlBuilderParameter = new SqlBuilderParameter(userCtx, formMeta);
            sqlBuilderParameter.AddSelectField(numberField.Id);
            sqlBuilderParameter.PageCount = -1;
            sqlBuilderParameter.PageIndex = -1;
            sqlBuilderParameter.QueryUserFieldOnly = false;
            sqlBuilderParameter.IsDistinct = true;
            sqlBuilderParameter.NoIsolation = false;
            sqlBuilderParameter.NoColorSetting = true;
            sqlBuilderParameter.ReadDirty = true;
            sqlBuilderParameter.EnableDataRowACL = false;
            sqlBuilderParameter.EnableDataQueryRule = false;

            //修改时间
            var modifyDateFld = formMeta.GetField("fmodifydate");
            if (modifyDateFld != null)
            {
                sqlBuilderParameter.FilterString = $"({modifyDateFld.FieldName}>sync.fsynctime or sync.fsynctime is null or sync.fsyncstatus='3')";
            }
            else
            {
                sqlBuilderParameter.FilterString = "1=1";
            }

            if (!filter.IsNullOrEmptyOrWhiteSpace())
            {
                sqlBuilderParameter.FilterString += $" and ({filter})";
            }

            if (!sqlParams.IsNullOrEmpty())
            {
                foreach (var sqlParam in sqlParams)
                {
                    sqlBuilderParameter.AddParameter(sqlParam);
                }
            }

            return sqlBuilderParameter;
        }
    }
}
