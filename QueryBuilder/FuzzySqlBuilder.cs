using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using JieNor.Framework.MetaCore.FormOp;
using System.Collections.Concurrent;
using JieNor.Framework.MetaCore.FormMeta;
using System.Text.RegularExpressions;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.IoC;

using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Consts;
using JieNor.Framework.MetaCore;
using JieNor.Framework.DataTransferObject;

namespace JieNor.Framework.AppService.QueryBuilder
{

    /// <summary>
    /// 编辑界面的基础资料的模糊查询sql构建器
    /// </summary>
    [InjectService]
    public class FuzzySqlBuilder : IFuzzySqlBuilder
    {
         

        
        //[PerfMonitor]
        public List<Dictionary<string, object>> GetQueryData(UserContext ctx, SqlBuilderParameter param )
        {
            //param.EnableDataRowACL = false;
            QueryObject queryObject = QueryService.BuilQueryObject(param);

            var evaluator = ctx.Container.GetService<IBizExpressionEvaluator>();
            var exprCtx = ctx.Container.GetService<IBizExpressionContext>();
            exprCtx.Context = ctx;
            List<Dictionary<string, object>> lstListData = new List<Dictionary<string, object>>();
            var dbSvc = ctx.Container.GetService<IDBService>();             
            using (var reader = dbSvc.ExecuteReader(ctx, queryObject.Sql, param.DynamicParams))
            { 
                while (reader.Read())
                {
                    Dictionary<string, object> dctRowObj = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
                    for (int iCol = 0; iCol < reader.FieldCount; iCol++)
                    {
                        var objValue = reader[iCol];
                        var colName = reader.GetName(iCol);
                        dctRowObj[colName] = objValue.IsNullOrEmptyOrWhiteSpace() ? "" : objValue;
                    }

                    //设置字段显示颜色
                    queryObject.SetFldCellColor(param.HtmlForm, dctRowObj, param.MergeBillHeadField);
                    queryObject.SetRowBGColor(param.HtmlForm, dctRowObj, evaluator, exprCtx);

                    lstListData.Add(dctRowObj);
                }
            }

            return lstListData;
        }
         



    }
}
