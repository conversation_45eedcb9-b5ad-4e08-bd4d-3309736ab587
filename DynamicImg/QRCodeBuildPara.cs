using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 二维码构建参数
    /// </summary>
    public class QRCodeBuildPara
    {
        public string Content
        {
            get;
            set;
        }
        public bool RetLocalDir
        {
            get;
            set;
        }
        public bool Rotate90Flip
        {
            get;
            set;
        }

        public int Width
        {
            get;
            set;
        }

        public int Height
        {
            get;
            set;
        }

        public int FontSize
        {
            get;
            set;
        }
        public bool FontBold
        {
            get;
            set;
        }

        /// <summary>
        /// 纯条码
        /// </summary>
        public bool PureBarcode { get; set; }
    }
}
