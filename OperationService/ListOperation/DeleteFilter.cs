using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.Framework.AppService.OperationService.ListOperation
{
    [InjectService("DeleteFilter")]
    public class DeleteFilter : AbstractOperationService
    {
        protected override string OperationName
        {
            get
            {
                return "删除";
            }
        }

        protected override string PermItem
        {
            get
            {
                return null;
            }
        }

        protected override bool IgnoreOpMessage
        {
            get
            {
                return true;
            }
        }
        /// <summary>
        /// 删除过滤方案
        /// </summary>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            this.OperationContext.Result.IsSuccess = false;
            this.OperationContext.Result.SimpleMessage = "方案删除失败！";
            string id = this.GetQueryOrSimpleParam<string>("id");
            if (id.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            var schemeService = this.OperationContext.Container.GetService<IFilterSchemeService>();
            schemeService.DeleteFilterScheme(this.OperationContext.UserContext, this.OperationContext.HtmlForm.Id, id);
            this.OperationContext.Result.IsSuccess = true;
            this.OperationContext.Result.SimpleMessage = "方案删除成功！";

        }
    }
}
