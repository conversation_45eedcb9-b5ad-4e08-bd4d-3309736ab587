using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormOp;
using Newtonsoft.Json.Linq;
using JieNor.Framework.CustomException;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 设置表单状态
    /// </summary>
    [InjectService("setstatus")]
    public class SetStatus : AbstractOperationService
    {
        /// <summary>
        /// 操作后状态值
        /// </summary>
        protected string StatusValue
        {
            get;
            set;
        }

        /// <summary>
        /// 状态字段标识
        /// </summary>
        protected string StatusFieldKey { get; set; }

        /// <summary>
        /// 自动保存
        /// </summary>
        protected override bool AutoSaveData
        {
            get
            {
                return false;
            }
        }

        protected override void InitializeOperationDataEntities(ref DynamicObject[] dataEntities)
        {
            base.InitializeOperationDataEntities(ref dataEntities);

            var prepareService = this.Container.GetService<IPrepareSaveDataService>();
            prepareService?.PrepareDataEntity(this.UserCtx, this.OperationContext.HtmlForm, dataEntities, this.OperationContext.Option);

            this.StatusFieldKey = this.OpConfig.Parameter.GetJsonValue<string>("statusFieldKey","");
            this.StatusValue = this.OpConfig.Parameter.GetJsonValue<string>("statusValue", "");
            var statusField = this.OperationContext.HtmlForm.GetField(this.StatusFieldKey);
            if (statusField.IsNullOrEmpty())
            {
                //兼容之前老的设置状态操作
                //throw new BusinessException($"{this.OperationName}操作必须关联状态字段！");
            }
        }

        /// <summary>
        /// 执行状态变化逻辑
        /// </summary>
        /// <param name="dataEntities"></param>
        protected sealed override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null) return;


            //设置状态字段值
            var statusField = this.OperationContext.HtmlForm?.GetField(this.StatusFieldKey);
            if (statusField != null)
            {
                var dataEntitySet = new ExtendedDataEntitySet();
                dataEntitySet.Parse(this.OperationContext, dataEntities, this.OperationContext.HtmlForm);
                var entryRowObjs = dataEntitySet.FindByEntityKey(statusField.EntityKey);
                foreach (var dataRow in entryRowObjs)
                {
                    statusField.DynamicProperty.SetValue(dataRow.DataEntity, this.StatusValue);
                }
            }

            //只有表单与列表才有存储模型，才需要先保存
            if (this.OperationContext is BillOperationContext
                || this.OperationContext is ListOperationContext)
            {
                var dm = this.GetDataManager();
                dm.InitDbContext(this.UserCtx, this.OperationContext.HtmlForm.GetDynamicObjectType(this.UserCtx));
                dm.Save(dataEntities);
            }
        }

        /// <summary>
        /// 操作后将状态变化行为返回前端
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void AfterExecute(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null) return;
            if ((this.OperationContext is BillOperationContext
                || this.OperationContext.GetType() == typeof(OperationContext)) && dataEntities.Length == 1)
            {
                var statusField = this.OperationContext.HtmlForm?.GetField(this.StatusFieldKey);
                if (statusField != null)
                {
                    var dataEntitySet = new ExtendedDataEntitySet();
                    dataEntitySet.Parse(this.OperationContext, dataEntities, this.OperationContext.HtmlForm);
                    var entryRowObjs = dataEntitySet.FindByEntityKey(statusField.EntityKey);
                    foreach (var dataRow in entryRowObjs)
                    {
                        var row = dataRow.RowIndex;
                        if (statusField.Entity is HtmlHeadEntity)
                        {
                            row = -1;
                        }
                        this.OperationContext.SetValue(this.StatusFieldKey, this.StatusValue, row.ToString());
                    }
                }
            }

            base.AfterExecute(ref dataEntities);
        }
    }
}
