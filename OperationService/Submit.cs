using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections;
using System.Linq;
using JieNor.Framework.DataTransferObject;
using System.Collections.Generic;
using JieNor.Framework.Interface.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.Framework.AppService.OperationService
{

    /// <summary>
    /// 提交操作:1、如果未定义审批信息，则直接设置为已审核
    ///          2、如果已经提交，则忽略
    ///          3、设置单据状态为提交状态，同时预插入审批路线（审批日志）
    /// </summary>
    [InjectService("Submit")]
    public class Submit : AbstractSetStatus
    {
        protected override string OperationName
        {
            get
            {
                return "提交";
            }
        }
        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_Submit;
            }
        }

        protected override bool AutoSaveData
        {
            get
            {
                return true;
            }
        }

        /// <summary>
        /// 状态值
        /// </summary>
        protected override string StatusValue
        {
            get
            {
                return Enum.GetName(typeof(BillStatus), BillStatus.D);
            }
        }

        bool ExistCheckLogEntry
        {
            get;
            set;
        }

        private IList CheckSetting
        {
            get
            {
                return Audit.GetAuditSetting(this.OperationContext);
            }
        }


        protected override void AfterExecute(ref DynamicObject[] dataEntities)
        {
            base.AfterExecute(ref dataEntities);
            if (CheckSetting == null || CheckSetting.Count == 0)
            {
                //如果未定义审批流程，则自动调用审核操作
                //AutoAudit(dataEntities);
            }
        }


        private void AutoAudit(IEnumerable <DynamicObject> dataEntities)
        {
            var statusField = this.OperationContext.HtmlForm.GetField(this.OperationContext.HtmlForm.BizStatusFldKey);
            if (statusField == null)
            {
                return;
            }
            
            var statusName1 = Enum.GetName(typeof(BillStatus), BillStatus.B);
            var statusName2 = Enum.GetName(typeof(BillStatus), BillStatus.C);
            var validDataObjs = dataEntities.Where(o => statusField.DynamicProperty.GetValue<string>(o).EqualsIgnoreCase(statusName1)
                || statusField.DynamicProperty.GetValue<string>(o).EqualsIgnoreCase(statusName2))
            .ToArray();
            if (validDataObjs.Any() == false)
            {
                return;
            }

            var result = this.Gateway.InvokeBillOperation(this.UserCtx, this.OperationContext.HtmlForm.Id, validDataObjs, "audit", null);
            if (result != null)
            {
                this.OperationContext.Result.MergeResult(result);
            }
        }

        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Count() == 0)
            {
                return;
            }

            HtmlComplexMessage msg = new HtmlComplexMessage();

            if (CheckSetting == null || CheckSetting.Count == 0)
            {
                //没有设置审批信息，直接设置为已提交状态
                foreach (var item in dataEntities)
                {
                    SetSubmitInfo(item);
                }                

                msg.SuccessMessages.Add("操作成功：数据已提交审核");

                // 这里提示信息不能直接覆盖，而是要做合并，否则会导致业务插件中的校验提示信息被清掉
                this.OperationContext.Result.ComplexMessage.ErrorMessages.AddRange(msg.ErrorMessages);
                this.OperationContext.Result.ComplexMessage.WarningMessages.AddRange(msg.WarningMessages);
                this.OperationContext.Result.ComplexMessage.SuccessMessages.AddRange(msg.SuccessMessages);
                this.OperationContext.Result.IsSuccess = true;
                return;
            }

            ExistCheckLogEntry = this.OperationContext.HtmlForm.ExistsCheckLogEntry();

            DynamicObject setting = CheckSetting[0] as DynamicObject;
            //按单据进行处理
            foreach (var billData in dataEntities)
            {
                if (IsSubmit(setting, billData))
                {
                    //已提交审核的，直接退出
                    msg.WarningMessages.Add(Audit.GetMsg(OperationContext, billData, "已经提交审批，不需要再重复提交"));
                    continue;
                }

                SetSubmitInfo(setting, billData);

                msg.SuccessMessages.Add(Audit.GetMsg(OperationContext, billData, "已经成功提交审批"));
            }

            // 这里提示信息不能直接覆盖，而是要做合并，否则会导致业务插件中的校验提示信息被清掉
            this.OperationContext.Result.ComplexMessage.ErrorMessages.AddRange(msg.ErrorMessages);
            this.OperationContext.Result.ComplexMessage.WarningMessages.AddRange(msg.WarningMessages);
            this.OperationContext.Result.ComplexMessage.SuccessMessages.AddRange(msg.SuccessMessages);
            //this.OperationContext.Result.SimpleMessage = "操作完成";
            this.OperationContext.Result.IsSuccess = true;
            this.OperationContext.Result.IsShowMessage = true;
        }


        /// <summary>
        /// 设置提交信息
        /// </summary>
        /// <param name="billData"></param>
        private void SetSubmitInfo(DynamicObject billData)
        {
            var meta = this.OperationContext.HtmlForm;
            var fld = meta.GetField(meta.BizStatusFldKey);
            if (fld != null)
            {
                Audit.SetAuditFldValue(billData, fld.Id, BillStatus.D.ToString());
            }            
        }


        /// <summary>
        /// 设置提交信息
        /// </summary>
        /// <param name="setting"></param>
        /// <param name="billData"></param>
        private void SetSubmitInfo(DynamicObject setting, DynamicObject billData)
        {
            var statusFldKey = this.OperationContext.HtmlForm.BizStatusFldKey;// setting.GetValue<string>("fstatusfldkey", "").Trim();
            Audit.SetAuditFldValue(billData, statusFldKey, BillStatus.D.ToString());
            
            //预插入各个审批环节信息
            if (ExistCheckLogEntry)
            {
                var levels = setting["fentity"] as DynamicObjectCollection;
                var checkLogs = billData["fchecklogentry"] as DynamicObjectCollection;
                var checkLog= checkLogs.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                checkLog.SetValue("fcloguserid", this.OperationContext.UserContext.UserId);
                checkLog.SetValue("fchecklevelid", "");
                checkLog.SetValue("fclogcheckdate", DateTime.Now);
                checkLog.SetValue("fclogisagree", false);
                checkLog.SetValue("fclogcheckreason", "提交");
                checkLog.SetValue("fcheckleveldesc", "submit");
                checkLog.SetValue("fcloghandler", this.OperationContext.UserContext.UserId);
                checkLogs.Add(checkLog);
                foreach (var item in levels)
                {
                    if (!Audit.IsLevelFilter(this.OperationContext, item, billData))
                    {
                        continue;
                    }

                    var log = checkLogs.FirstOrDefault(f => f.GetValue<string>("fcloguserid", "") == this.OperationContext.UserContext.UserId.ToString()
                                                   && f.GetValue<string>("fchecklevelid", "") == item.GetValue<string>("Id", "0")
                                                   && f.GetValue<string>("fcloghandler", "").IsNullOrEmptyOrWhiteSpace());
                    if (log == null)
                    {
                        log = checkLogs.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                        var fuser = item.GetValue<string>("fuserdesc", "").Replace('，', ',').Replace('；', ',').Replace(';', ',');
                        if (fuser.IsNullOrEmptyOrWhiteSpace() || fuser[0].IsNullOrEmptyOrWhiteSpace())
                        {
                            fuser = item.GetValue<string>("froledesc", "").Replace('，', ',').Replace('；', ',').Replace(';', ',');
                        }
                        log.SetValue("fcloguserid", fuser);
                        log.SetValue("fchecklevelid", item.GetValue<string>("Id", "0"));
                        log.SetValue("fcheckleveldesc", item.GetValue<string>("flevelname", ""));

                        checkLogs.Add(log);
                    }
                } 
            }

        }

        /// <summary>
        /// 判断是否已经提交
        /// </summary>
        /// <param name="setting"></param>
        /// <param name="billData"></param>
        /// <returns></returns>
        private bool IsSubmit(DynamicObject setting, DynamicObject billData)
        {
            var statusFldKey = this.OperationContext.HtmlForm.BizStatusFldKey;// setting.GetValue<string>("fstatusfldkey", "").Trim();
            if (statusFldKey.IsNullOrEmptyOrWhiteSpace())
            {
                return false;
            }

            var status = billData.GetValue<string>(statusFldKey, "A");
            if (status.EqualsIgnoreCase(BillStatus.C.ToString())
                || status.EqualsIgnoreCase(BillStatus.D.ToString())
                || status.EqualsIgnoreCase(BillStatus.E.ToString()))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 处理提交操作通用校验规则
        /// </summary>
        /// <returns></returns>
        protected override List<IDataValidRule> PrepareValidationRules()
        {
            var rules = base.PrepareValidationRules();

            var errorMessage = string.Empty;
            rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return ValiStatus(newData, oldData, out errorMessage);
            }).WithMessage("{0}", (dyObj, key) => errorMessage));

            var mustRule = this.Container.GetValidRuleService(HtmlElementType.HtmlValidator_MustInput);
            if (mustRule != null)
            {
                mustRule.Initialize(this.OperationContext.UserContext, "Submit");
                rules.Add(mustRule);
            }

            return rules;
        }

        /// <summary>
        /// 状态校验
        /// </summary>
        /// <param name="newData"></param>
        /// <param name="oldData"></param>
        /// <param name="errorMessage"></param>
        /// <returns></returns>
        private bool ValiStatus(DynamicObject newData, DynamicObject oldData, out string errorMessage)
        {
            bool bPassed = true;
            HtmlForm formInfo = this.OperationContext.HtmlForm;
            var forbidFld = formInfo.GetField(formInfo.ForbidStatusFldKey);
            var statusFld = formInfo.GetField(formInfo.BizStatusFldKey);
            var cancelFld = formInfo.GetField(formInfo.CancelStatusFldKey);
            var numberFld = formInfo.GetField(formInfo.NumberFldKey);
            var numberVal = numberFld.DynamicProperty.GetValue<string>(newData);

            //未禁用
            bPassed = bPassed &&
                (forbidFld == null || forbidFld != null && !forbidFld.DynamicProperty.GetValue<bool>(newData));

            //状态=创建/重新审核
            bPassed = bPassed &&
            (statusFld == null || statusFld != null
                && (statusFld.DynamicProperty.GetValue<string>(newData).EqualsIgnoreCase(BillStatus.B.ToString())
                    || statusFld.DynamicProperty.GetValue<string>(newData).EqualsIgnoreCase(BillStatus.C.ToString())));

            //未作废
            bPassed = bPassed &&
                (cancelFld == null || cancelFld != null && !cancelFld.DynamicProperty.GetValue<bool>(newData));

            errorMessage = $"{numberFld.Caption}为【{numberVal}】的{formInfo.Caption} 不是创建/重新审核状态 或 已禁用 或 已作废，不允许提交！";

            return bPassed;
        }

        protected override void PrepareBusinessService(List<FormServiceDesc> lstOpServices)
        {
            base.PrepareBusinessService(lstOpServices);

            var formServiceLoaders = this.Container.GetService<IEnumerable<IFormServiceLoader>>();
            if (formServiceLoaders != null && formServiceLoaders.Any())
            {
                foreach (var formServiceLoader in formServiceLoaders)
                {
                    var syncServiceInst = formServiceLoader.CreateSyncService(this.UserCtx, this.OperationContext.HtmlForm, Enu_SyncTimePoint.SyncWhenSubmit, this.OperationContext.Option);
                    if (syncServiceInst != null)
                    {
                        lstOpServices.AddRange(syncServiceInst);
                    }
                }
            }
        }
    }
}