using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormMeta;
using Newtonsoft.Json.Linq;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 批改操作
    /// </summary>
    [InjectService("BatchModify")]
    public class BatchModify : AbstractOperationService
    {
        protected override void CheckPermission()
        {
            base.CheckPermission();
            var permissionService = this.Container.GetService<IPermissionService>();
            permissionService.CheckPermission(this.UserCtx, new MetaCore.PermData.PermAuth(this.UserCtx) { FormId = this.OperationContext.HtmlForm.Id, PermId = "fw_modify" });
        }

        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            var modifyDataStr = this.GetQueryOrSimpleParam<string>("modifyData");
            var modifyReason = this.GetQueryOrSimpleParam<string>("modifyReason");
            JObject modifyData = null;

            if (string.IsNullOrWhiteSpace(modifyDataStr)==false)
            {
                modifyData = JObject.Parse(modifyDataStr);
            }

            if (modifyData == null || modifyData.Count <= 0)
            {
                throw new BusinessException("无批改数据包!");
            }

            var fieldValueMap = new Dictionary<HtmlField, object>();
            var htmlForm = this.OperationContext.HtmlForm;

            foreach(var property in modifyData.Properties())
            {
                var field = htmlForm.GetField(property.Name);
                if (field == null || false == field.SupportBatchModify)
                {
                    continue;
                }
                var value = Convert.ChangeType(property.Value, field.GetFieldPropertyType());
                fieldValueMap[field] = value;
            }

            if (fieldValueMap == null || fieldValueMap.Count <= 0)
            {
                return;
            }

            ExtendedDataEntitySet dataEntitySet = new ExtendedDataEntitySet();
            dataEntitySet.Parse(this.UserCtx, dataEntities, htmlForm);
            var selectedRows = this.OperationContext.SelectedRows;
            var changed = false;

            foreach(var mapItem in fieldValueMap)
            {
                var field = mapItem.Key;
                var selectedKeys = new List<string>();
                if (field.IsBillHeadField)
                {
                    selectedKeys.AddRange(selectedRows.Select(x => x.PkValue));
                }
                else
                {
                    selectedKeys.AddRange(selectedRows.Where(x => string.Equals(x.EntityKey, field.EntityKey, StringComparison.OrdinalIgnoreCase)).Select(x => x.EntryPkValue));
                }
                var setDataEntities = dataEntitySet.FindByEntityKey(field.EntityKey);
                foreach(var setDataEntity in setDataEntities)
                {
                    var id = Convert.ToString(setDataEntity.DataEntity["id"]);
                    if (false==selectedKeys.Contains(id))
                    {
                        continue;
                    }
                    field.DynamicProperty.SetValue(setDataEntity.DataEntity, mapItem.Value);
                    changed = true;
                }
            }

            if (false == changed)
            {
                return;
            }

            var response = this.Gateway.InvokeBillOperation(this.UserCtx, htmlForm.Id, dataEntities, "save", new Dictionary<string, object> { { "execOpinion", modifyReason } });
            response?.ThrowIfHasError(true, $"保存{htmlForm.Caption}失败!");
        }
    }
}
