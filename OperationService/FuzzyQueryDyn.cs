using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JieNor.Framework.Consts;
using JieNor.Framework.IoC;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface.Profile;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject.QueryFilter;
using Newtonsoft.Json;
using System.Data;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 模糊查询：主要用于编辑界面的基础资料选择显示数据
    /// </summary>
    [InjectService("FuzzyQueryDyn")]
    public class FuzzyQueryDyn : QueryData
    {
        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem { get { return PermConst.PermssionItem_View; } }

        /// <summary>
        /// 验权表单标识
        /// </summary>
        protected override string PermFormId { get { return this.QueryHtmlForm?.Id ?? base.PermFormId; } }

        /// <summary>
        /// 是否只查数据
        /// </summary>
        protected virtual bool IsQueryDataOnly { get { return false; } }

        /// <summary>
        /// 当前表单模型
        /// </summary>
        protected HtmlForm HtmlForm { get { return this.OperationContext.HtmlForm; } }

        /// <summary>
        /// 当前要查询的基础资料表单模型
        /// </summary>
        protected override HtmlForm QueryHtmlForm
        {
            get
            {
                var targetFormId = this.GetQueryOrSimpleParam<string>("targetFormId");
                if (targetFormId.IsNullOrEmptyOrWhiteSpace())
                {
                    throw new BusinessException("请求参数 targetFormId 错误，请检查！");
                }
                var targetForm = this.MetaModelService.LoadFormModel(this.UserCtx, targetFormId);
                return targetForm;
            }
        }

        /// <summary>
        /// 预置过滤条件：未来若需要定制，则在此处可以发出插件事件由插件提供，目前只从模板进行解析
        /// </summary>
        protected string FilterString
        {
            get
            {
                var filter = this.LookUpField?.Filter ?? "";

                //允许业务插件自行设置过滤条件
                var ae = new OnCustomServiceEventArgs()
                {
                    EventName = "onAfterParseFilterString",
                    EventData = new Tuple<string, string>(this.FieldKey, filter),
                };
                this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", ae);
                if (ae.Cancel && !ae.Result.IsNullOrEmptyOrWhiteSpace())
                {
                    if (filter.IsNullOrEmptyOrWhiteSpace())
                    {
                        filter = " (" + ae.Result + ") ";
                    }
                    else
                    {
                        filter += " and (" + ae.Result + ") ";
                    }
                }
                return filter;
            }
        }

        /// <summary>
        /// 模糊查询列模型
        /// </summary>
        protected List<ColumnObject> FuzzyQueryColumns { get; set; } = new List<ColumnObject>();

        /// <summary>
        /// 模糊查询配置
        /// </summary>
        protected Setting FuzzySetting { get; set; }

        /// <summary>
        /// 操作执行过程
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (!this.IsQueryDataOnly)
            {
                if (this.LookUpField.EnableSrvPager)
                {
                    this.FuzzySetting = GetUserProfile();
                }
            }
            this.ServiceControlOption.SupportIdemotency = false;

            var activeEntityKey = "fbillhead";
            SqlBuilderParameter para = this.BuildQueryParameter(ref activeEntityKey);

            //先读取配置参数     
            if (this.FuzzySetting != null)
            {
                para.PageCount = this.FuzzySetting.FetchCount;
                para.PageIndex = this.PageIndex;
            }
            else
            {
                para.PageIndex = -1;
                para.PageCount = -1;
            }

            var matchFilter = this.GetFilter(this.FuzzyQueryColumns, this.FuzzySetting?.MatchRule ?? "like");
            para.SetFilter(matchFilter);

            para.FilterString = para.FilterString.JoinFilterString(this.FilterString);             
            //获取模糊查询数据
            var listQueryBuilder = this.OperationContext.Container.GetService<IListSqlBuilder>();
            var data = listQueryBuilder.GetFuzzyQueryData(this.OperationContext.UserContext, para);

            //允许插件干预列表数据
            var ae = new OnCustomServiceEventArgs()
            {
                EventName = "afterListData",
                EventData = data,
            };
            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", ae);
            data = ae.EventData as List<Dictionary<string, object>>;

            //设置返回的数据
            if (!this.IsQueryDataOnly)
            {
                //只有在第一页的情况下，才需要返回表格模型定义数据（Columns）
                if (para.PageIndex == 1 || para.PageIndex < 0)
                {
                    this.OperationContext.Result.SrvData = new
                    {
                        Columns = this.FuzzyQueryColumns,
                        Data = data,
                        Setting = this.FuzzySetting,
                    };
                }
                else
                {
                    this.OperationContext.Result.SrvData = new
                    {
                        Data = data,
                        Setting = this.FuzzySetting,
                    };
                }
            }
            else
            {
                this.OperationContext.Result.SrvData = new
                {
                    Data = data,
                };
            }
            this.OperationContext.Result.IsSuccess = true;
        }

        /// <summary>
        /// 构建列表查询字段
        /// </summary>
        /// <param name="para"></param>
        /// <param name="activeEntityKey"></param>
        protected override void BuildListQuerySelectField(SqlBuilderParameter para, ref string activeEntityKey)
        {
            var lstColumns = this.GetListColumns();
            foreach (var item in lstColumns)
            {
                para.SelectedFieldKeys.Add(item.Id);
                this.FuzzyQueryColumns.Add(item);
            }
        }

        /// <summary>
        /// 解析动态参数
        /// </summary>
        /// <param name="para"></param>
        protected override void ParseDynamicParam(SqlBuilderParameter para)
        {
            base.ParseDynamicParam(para);

            string jsonDynamicParam = this.GetQueryOrSimpleParam<string>("dynamicParam");
            if (jsonDynamicParam.IsNullOrEmptyOrWhiteSpace()) return;

            //将 Json 字符串转成对象
            DynamicQueryParam dynamicParam = JsonConvert.DeserializeObject<DynamicQueryParam>(jsonDynamicParam);
            if (dynamicParam != null && !dynamicParam.FilterString.IsNullOrEmptyOrWhiteSpace())
            {
                //拼接在 SqlBuilderParameter 的 FilterString 后面
                if (para.FilterString.IsNullOrEmptyOrWhiteSpace())
                {
                    para.FilterString = " ( {0} ) ".Fmt(dynamicParam.FilterString);
                }
                else
                {
                    para.FilterString = para.FilterString.JoinFilterString(dynamicParam.FilterString);
                }

                //如果有传递参数
                if (dynamicParam.Params != null)
                {
                    //基础资料字段所在的表单（比如：订货单 ydj_order）
                    HtmlForm parentHtmlForm = this.MetaModelService.LoadFormModel(this.UserCtx, dynamicParam.FormId);
                    if (parentHtmlForm == null) return;

                    foreach (var param in dynamicParam.Params)
                    {
                        //如果上面没有找到，再去基础资料字段所在的表单中查找（比如：订货单 ydj_order）
                        var field = parentHtmlForm.GetField(param.FieldId);

                        var dbType = DbType.String;
                        if (field != null) dbType = field.ElementType.ToDbType();

                        //添加参数
                        para.AddParameter(new SqlParam("@" + param.FieldId, dbType, param.PValue));
                    }
                }
            }
        }

        /// <summary>
        /// 获取过滤条件
        /// </summary>
        /// <param name="lstColumns"></param>
        /// <param name="matchRule"></param>
        /// <returns></returns>
        protected virtual List<FilterRowObject> GetFilter(List<ColumnObject> lstColumns, string matchRule)
        {
            List<FilterRowObject> lst = new List<FilterRowObject>();

            int index = 0;
            foreach (var item in lstColumns)
            {
                index++;

                FilterRowObject filter = new FilterRowObject();
                filter.Id = item.Id;

                filter.Logic = "or";

                if (index == 1)
                {
                    filter.LeftBracket = "(";
                    filter.Logic = "";
                }
                if (index == lstColumns.Count)
                {
                    filter.RightBracket = ")";
                }
                if (SearchKey.IsNullOrEmptyOrWhiteSpace())
                {
                    //比如前端没有录入数据，或者录入空格，则应该要查出数据才符合常理
                    filter.Operator = "like";
                }
                else
                {
                    filter.Operator = matchRule;
                }
                filter.Value = SearchKey;
                filter.RowIndex = index;

                lst.Add(filter);
            }

            return lst;
        }

        /// <summary>
        /// 获取返回的列表字段
        /// </summary>
        /// <returns></returns>
        protected List<ColumnObject> GetListColumns()
        {
            var lstColumns = new List<ColumnObject>();

            var noFld = this.QueryHtmlForm.GetField(this.QueryHtmlForm.NumberFldKey);
            if (noFld != null)
            {
                lstColumns.AddRange(noFld.ToListColumn(this.UserCtx));
            }
            var nameFld = this.QueryHtmlForm.GetField(this.QueryHtmlForm.NameFldKey);
            if (nameFld != null)
            {
                lstColumns.AddRange(nameFld.ToListColumn(this.UserCtx));
            }

            string fuzzyQueryFieldKeys = this.QueryHtmlForm.FuzzyQueryFieldKeys;
            if (!fuzzyQueryFieldKeys.IsNullOrEmptyOrWhiteSpace())
            {
                var fuzzyFlds = fuzzyQueryFieldKeys.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                foreach (string fuzzyFld in fuzzyFlds)
                {
                    var fuzFld = this.QueryHtmlForm.GetField(fuzzyFld);
                    if (!fuzFld.IsNullOrEmptyOrWhiteSpace())
                    {
                        lstColumns.AddRange(fuzFld.ToListColumn(this.UserCtx));
                    }
                }
            }

            return lstColumns;
        }

        /// <summary>
        /// 获取用户设置的模糊查询规则
        /// </summary>
        /// <returns></returns>
        private Setting GetUserProfile()
        {
            var userProfileService = this.Container.GetService<IUserProfile>();
            Setting setting = userProfileService.LoadUserProfile<Setting>(this.UserCtx, this.HtmlForm.Id, this.Category);
            if (setting == null)
            {
                setting = new Setting();
            }
            if (MatchRule.IsNullOrEmptyOrWhiteSpace() && (FetchCount.IsNullOrEmptyOrWhiteSpace() || FetchCount == 0))
            {
                //前端未传参数
                return setting;
            }

            if (setting.MatchRule.EqualsIgnoreCase(MatchRule) && setting.FetchCount == FetchCount)
            {
                //两个参数都一样
                return setting;
            }

            setting.MatchRule = MatchRule;
            setting.FetchCount = FetchCount;

            //如果界面传递的设置跟服务端不一样，则先保存
            userProfileService.SaveUserProfile(this.UserCtx, this.HtmlForm.Id, this.Category, setting);

            return setting;
        }

        /// <summary>
        /// 模糊查询的字段
        /// </summary>
        protected HtmlBizFieldValueField LookUpField
        {
            get
            {
                return this.OperationContext.HtmlForm.GetField(FieldKey) as HtmlBizFieldValueField;
            }
        }

        /// <summary>
        /// 类别
        /// </summary>
        protected string Category
        {
            get
            {
                return string.Format("FuzzyQueryDyn-{0}", FieldKey);
            }
        }

        /// <summary>
        /// 匹配规则
        /// </summary>
        protected string MatchRule
        {
            get
            {
                var key = GetQueryOrSimpleParam<string>("matchrule");
                return key;
            }
        }

        /// <summary>
        /// 模糊查询返回的记录数
        /// </summary>
        protected int FetchCount
        {
            get
            {
                var key = GetQueryOrSimpleParam<string>("fetchcount");
                int count = 0;
                int.TryParse(key, out count);
                return count;
            }
        }

        /// <summary>
        /// 当前要查询的数据页码
        /// </summary>
        protected int PageIndex
        {
            get
            {
                var key = GetQueryOrSimpleParam<string>("pageIndex");
                int pageIndex = 0;
                int.TryParse(key, out pageIndex);
                if (pageIndex <= 0) pageIndex = 1;
                return pageIndex;
            }
        }

        /// <summary>
        /// 模糊查询的 的关键字：自动去掉前后空格
        /// </summary>
        protected string SearchKey
        {
            get
            {
                var key = GetQueryOrSimpleParam<string>("searchkey");
                if (key == null)
                {
                    key = string.Empty;
                }
                return key.Trim();
            }
        }

        /// <summary>
        /// 当前模糊查询字段所关联的控制字段值
        /// </summary>
        protected string ControlFieldVal
        {
            get
            {
                var val = GetQueryOrSimpleParam<string>("ctlFieldVal");
                if (val == null)
                {
                    val = string.Empty;
                }
                return val.Trim();
            }
        }

        /// <summary>
        /// 模糊查询的字段标识（如销售订单上的部门字段标识）
        /// </summary>
        protected string FieldKey
        {
            get
            {
                var key = GetQueryOrSimpleParam<string>("fieldkey");
                if (key.IsNullOrEmptyOrWhiteSpace())
                {
                    throw new Exception(string.Format("模糊查询参数不正确，字段标识 {0} 不存在", key));
                }
                var field = this.HtmlForm.GetField(key);
                if (field == null)
                {
                    throw new Exception(string.Format("参数不正确，字段标识 {0} 不存在", key));
                }
                return key;
            }
        }

        /// <summary>
        /// 模糊匹配配置
        /// </summary>
        public class Setting
        {
            /// <summary>
            /// 匹配规则
            /// </summary>
            public string MatchRule { get; set; } = "like";

            /// <summary>
            /// 模糊查询返回的记录数，默认5条
            /// </summary>
            public int FetchCount { get; set; } = 5;

            /// <summary>
            /// 当前查询的页号，仅用于返回前端使用
            /// </summary>
            public int PageIndex { get; protected internal set; } = 1;

            /// <summary>
            /// 模糊查询的匹配规则（包含，左包含，右包含），默认左包含
            /// </summary>
            internal FieldOperatorObject GetMatchRule()
            {
                var key = "like";
                if (!MatchRule.IsNullOrEmptyOrWhiteSpace())
                {
                    key = MatchRule.ToLowerInvariant();
                }
                switch (key)
                {
                    case "like":
                        return new FieldOperatorObject() { Caption = "包含", Symbol = "like", IsCustom = false };
                    case "llike":
                        return new FieldOperatorObject() { Caption = "右包含", Symbol = "llike", IsCustom = false };
                    default:
                        return new FieldOperatorObject() { Caption = "左包含", Symbol = "rlike", IsCustom = false };
                }
            }
        }
    }
}