using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 保存操作预处理服务
    /// </summary>
    public interface IPrepareSaveDataService
    {
        /// <summary>
        /// 使用保存机制来预处理单据数据包
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="hForm"></param>
        /// <param name="dataEntities"></param>
        void PrepareDataEntity(UserContext userCtx, HtmlForm hForm, DynamicObject[] dataEntities, OperateOption option);

        /// <summary>
        /// 删除表格无效行
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="hForm"></param>
        /// <param name="dataEntities"></param>
        void RemoveEmptyOrInvalidRow(UserContext userCtx, HtmlForm hForm, DynamicObject[] dataEntities);
         



        }
}
