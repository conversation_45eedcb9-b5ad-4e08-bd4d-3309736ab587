using System;
using System.Text;
using System.Collections.Generic;

using Newtonsoft.Json.Linq;

using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FileServer;

namespace JieNor.Framework.AppService.AttachmentService
{
    /// <summary>
    /// 加载文件信息
    /// </summary>
    [InjectService]
    [FormId("bas_filedetail")]
    [OperationNo("loadfiledetail")]
    public class LoadFileDetail : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var id = this.GetQueryOrSimpleParam<string>("id");
            if (id.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("文件ID为空，无法获取文件信息！");
            }

            var metaSrv = this.Context.Container?.TryGetService<IMetaModelService>();
            var userProfileMeta = metaSrv?.LoadFormModel(this.Context, "bas_filedetail");
            var dm = this.Context.Container?.TryGetService<IDataManager>();
            dm.InitDbContext(this.Context, userProfileMeta.GetDynamicObjectType(this.Context));
            DynamicObject dataEntity = dm.Select(id) as DynamicObject;
            if (dataEntity == null)
            {
                throw new BusinessException("文件不存在，无法获取文件信息！");
            }

            var uiConverter = this.Context.Container.GetService<IUiDataConverter>();
            var billJsonData = uiConverter.CreateUIDataObject(this.Context, this.HtmlForm, dataEntity);

            ////加载附件路径
            //JObject uidata = billJsonData["uidata"] as JObject;
            //if (uidata != null)
            //{
            //    var response = this.Gateway.InvokeLocal(this.Context,
            //            new FileInfoDTO() { FileId = id, Detail = true, Thumbnail = false }) as DynamicDTOResponse;
            //    if (response != null)
            //    {
            //        Dictionary<string, FileDetail> fileDetail = response.OperationResult.SrvData as Dictionary<string, FileDetail>;
            //        if (fileDetail != null && fileDetail[id] != null)
            //        {
            //            uidata.Add("ffilepath", fileDetail[id].Url);
            //        }
            //    }
            //}

            this.Result.SrvData = new
            {
                fileDetail = billJsonData
            };
        }
    }
}