using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 表达式计算接口
    /// </summary>
    public interface IExpressionParser
    {
        /// <summary>
        /// 根据指定数据上下文计算表达式的值
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="expression"></param>
        /// <param name="dcRowObj"></param>
        /// <returns></returns>
        object Evaluate(UserContext userCtx, string expression, BizDynamicDataRow dcRowObj);

        /// <summary>
        /// 根据特定模型计算表达式值
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="hForm"></param>
        /// <param name="expression"></param>
        /// <param name="activeEntityKey"></param>
        /// <returns></returns>
        object Evaluate(UserContext userCtx, HtmlForm hForm, string expression, string activeEntityKey = "");
    }
}
