using JieNor.Framework.DataTransferObject.TestCase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.UnitTest
{

    /// <summary>
    /// 单元测试服务接口
    /// </summary>
    public interface IUnitTestCase
    {


        /// <summary>
        /// 获取所有的测试用例
        /// </summary> 
        List<TestCaseInfo> GetAllTestCase(UserContext ctx);


        /// <summary>
        /// 执行测试用例（全部）
        /// </summary> 
        List<TestCaseExeResult> ExcuteTestCase(UserContext ctx);


        /// <summary>
        /// 执行测试用例（指定的）
        /// </summary> 
        List<TestCaseExeResult> ExcuteTestCase(UserContext ctx, List<TestCaseInfo> testCase);



    }



}
